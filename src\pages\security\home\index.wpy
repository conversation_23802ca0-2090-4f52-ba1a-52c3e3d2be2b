<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { PRIMARY_COLOR } from '@/config/constant';


  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '中信湘雅',
      navigationBarBackgroundColor: '#fff'
    };

    data = {
    }

    onLoad(options){
      
    }

    onShow(){
    }

     // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      weburl: '',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navTo(activeIndex){
        console.log('112',activeIndex)
        wepy.navigateTo({ url: `/pages/dynamic/index/index?type=all&activeTypeIdx=${activeIndex}` });
      }
    };
  }
</script>
