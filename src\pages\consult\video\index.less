@import "../../../resources/style/mixins";
page{
  height:100%;
}
.page-inquiry-video {
  height: 100%;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  cover-view {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
  .live-player {
    // height: calc(~"99% - 100rpx");
    height: 100%;
    width: 100%;
    background-color: @hc-color-white;
    position: absolute;
  }
  .doc-item {
    position: absolute;
    z-index: 100;
    top: 0;
    left: 0;
    height: 184rpx;
    margin-bottom: 20rpx;
    // background-color: @hc-color-white;
    .doc-info {
      display: flex;
      padding: 20rpx 30rpx;
      height: 154rpx;
      align-items: center;
      .doc-img {
        /* display: flex; */
        width: 100rpx;
        height: 111rpx;
        border-radius:50%;
      }
      .text-box {
        padding-left: 30rpx;
        padding-right: 30rpx;
        .doc-name {
        color: @hc-color-title;
        font-size: 36rpx;
        }
        .doc-des {
          color: @hc-color-gray;
          line-height: 47rpx;
          font-size: 30rpx;
        }
      }
      .favor-item {
        width: 120rpx;
        height: 60rpx;
        border-radius: 12rpx;
        background-color: @hc-color-primary;
        color: @hc-color-white;
        font-size: 30rpx;
        text-align: center;
        line-height: 60rpx;
      }
      .favor-gray{
        background-color: #D3D3D3;
      }
    }
  }
  .cover-chat {
    width: 100%;
    height: calc(~"99% - 222rpx");
    position: absolute;
    top: 184rpx;
    left: 0;
    z-index: 100;
    color: @hc-color-white;
    font-size: 30rpx;
    .cover-content{
      position: absolute;
      bottom: 0;
      height: 320rpx;
      overflow-y: scroll;
      #chat-box {
        .chat-list{
          display: flex;
          position: relative;
          .img-box {
            .chat-img {
              width: 48rpx;
              height: 48rpx;
              margin: 0 22rpx 30rpx;
              border-radius: 50%;
            }
          }
          .tringle{
            border: 12rpx solid transparent;
            border-right-color: rgba(0, 0, 0, 0.28);
            position: absolute;
            top: 18rpx;
            left: 66rpx;
          }
          .chat-item {
            background-color:rgba(0, 0, 0, 0.28);
            border-radius:12rpx;
            margin:0 30rpx 20rpx 0;
            padding:8rpx 0;
            line-height: 60rpx;
            display:flex;
            align-items:center;
            max-width: 80%;
            .content {
              margin: 0 30rpx 0 12rpx;
              flex: 1;
              display: inline-block;
            }
            .msg {
              color: #FFE3AA;
            }
            .image{
              width: 40rpx;
              height: 33rpx;
              margin: 0 24rpx;
              display:inline-block;
            }
          }
        }
      }
    }
    .modal {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      .modal-body {
        width: 85%;
        box-sizing: border-box;
        border-radius: 12rpx;
        background: @hc-color-white;
        .modal-title {
          padding: 60rpx 0 30rpx;
          font-size: 36rpx;
          color: @hc-color-black;
          text-align: center;
          width: 100%;
          height: 133rpx;
          .chat-image {
            width: 138rpx;
            height: 133rpx;
            margin: 0 auto;
          }
        }
        .modal-content {
          padding: 0 26rpx 30rpx;
          overflow: auto;
          font-size: 34rpx;
          color: @hc-color-title;
          line-height: 1.5;
          text-align: center;
        }
        .modal-footer {
          border-top: 1rpx solid #e5e5e5;
          display: flex;
          .foot-type {
            flex: 1;
            box-sizing: border-box;
            text-align: center;
            color: @hc-color-text;
            font-size: 34rpx;
            height: 88rpx;
            line-height: 88rpx;
            width: auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:last-child {
              color: @hc-color-primary;
            }
          }
          .line{
            height: 88rpx;
            width: 1rpx;
            background: #d2d2d2;
          }
        }
      }
    }
  }
  .opts-close {
    position: absolute;
    bottom: 20rpx;
    right: 30rpx;
    height: 30rpx;
    width: 30rpx;
    background: rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    padding: 20rpx;
  }
  .footer {
    position: absolute;
    z-index: 100;
    bottom: 125rpx;
    left: 0;
    width: 100%;
    // height: 100rpx;
    // margin: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .foot-input {
      padding: 0 12rpx 0 20rpx;
      background: rgba(0, 0, 0, 0.28);
      border-radius: 50rpx;
      // width: 370rpx;
      flex:1;
      height: 70rpx;
      margin-right: 20rpx;
      line-height: 70rpx;
      display: flex;
      align-items: center;
      .image {
        width: 50rpx;
        height: 50rpx;
      }
      input{
        flex: 1;
      }
    }
    .foot-img,.foot-eye{
      width: 70rpx;
      height: 70rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.28);
      margin-right: 20rpx;
      display: flex;
      align-items: center;
      .image {
        width: 34rpx;
        height: 27rpx;
        margin: 0 auto;
      }
    }
    .foot-eye .image {
      width: 48rpx;
      height: 31rpx;
      margin: 0 auto;
    }
    .foot-view{
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .foot-hangup {
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
      overflow: hidden;
    }
    .foot-rotate{
      width: 70rpx;
      height: 70rpx;
      border-radius: 50%;
      overflow: hidden;
      padding: 20rpx;
      background: rgba(0, 0, 0, 0.6);
      .image{
        width: 100%;
        height: 100%;
      }
    }
  }
  .footer-opts {
    display: flex;
    justify-content: center;
    height: 240rpx;
    background: rgba(249, 249, 249, 1);
    .footer-opt-item {
      height: 100%;
      width: 120rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 27rpx;
      image {
        height: 120rpx;
        width: 120rpx;
        margin-bottom: 18rpx;
        margin-top: 37rpx;
      }
      text {
        font-size: 30rpx;
        color: rgba(155, 155, 155, 1);
        line-height: 42rpx;
      }
    }
  }
  .foot-img .image {
    height: 38rpx;
    width: 38rpx;
  }
  .live-pusher {
    width: 180rpx;
    height: 320rpx;
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    z-index: 10;
  }
  .modal {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    .modal-body {
      width: 85%;
      box-sizing: border-box;
      border-radius: 12rpx;
      background: @hc-color-white;
      .modal-title {
        padding: 60rpx 0 30rpx;
        font-size: 36rpx;
        color: @hc-color-black;
        text-align: center;
        width: 100%;
        height: 133rpx;
        .chat-image {
          width: 138rpx;
          height: 133rpx;
          margin: 0 auto;
        }
      }
      .modal-content {
        padding: 0 26rpx 30rpx;
        overflow: auto;
        font-size: 34rpx;
        color: @hc-color-title;
        line-height: 1.5;
        text-align: center;
      }
      .modal-footer {
        border-top: 1rpx solid #e5e5e5;
        display: flex;
        .foot-type {
          flex: 1;
          box-sizing: border-box;
          text-align: center;
          color: @hc-color-text;
          font-size: 34rpx;
          height: 88rpx;
          line-height: 88rpx;
          width: auto;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &:last-child {
            color: @hc-color-primary;
          }
        }
        .line{
          height: 88rpx;
          width: 1rpx;
          background: #d2d2d2;
        }
      }
    }
  }
  // 中间层信息
  .middle-layer{
    position: absolute;
    z-index: 5;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  // 画面切换
  .shrink{
    width: 180rpx;
    height: 320rpx;
    z-index: 10;
    top: 10rpx;
    right: 10rpx;
  }
  .magnify{
    height: 100%;
    width: 100%;
    z-index: 1;
    top: 0;
    right: 0;
  }
}