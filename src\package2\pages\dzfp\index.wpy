<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />
<script>
  import wepy from 'wepy';
  import md5 from "md5";
  import { DOMAIN, REQUEST_QUERY } from "@/config/constant";
  import Outpatient from "@/components/outpatient/index";
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '电子发票',
      navigationBarBackgroundColor: '#fff',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 挂号记录列表
      patientConfig: {
        infoShow: false,
        show: false,
        initUser: {}
      },
      currentPatient: {},
      orderList: []
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      outpatient: Outpatient,
      empty: Empty
    };

    props = {};

    onShow() {
      this.$broadcast("outpatient-get-patient");
    }

    events = {
      "outpatient-change-user": function(item = {}) {
        if (item) {
          this.patientConfig.infoShow = true;
          this.currentPatient = item;
          this.getOrderList(item);
        }
      }
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      // 开票、查看发票
      bindQueryEleck(item) {
        this.queryEleck(item);
      },
    };

    async getOrderList(item) {
      const { code, data = {} } = await Api.orderList({
        patCardNo: item.patCardNo,
        patName: item.patientName,
        msgKey: md5(item.patCardNo+'2024queryWaitInvoiceList')
      });
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      
      this.orderList = data.order || [];
      this.$apply();
    };

    async queryEleck(item) {
      if (item.status == '1') {
        wx.showLoading();
        //读取本地文件
        wx.downloadFile({
          // 示例 url，并非真实存在
          url: `${DOMAIN}/api/customize/queryHisEleck?_route=h${REQUEST_QUERY.platformId}&orderNo=${item.orderNo}&msgKey=${md5(item.orderNo+'2024pdfquandianpiaohis')}`,
          success: function (res) {
            const filePath = res.tempFilePath;
            wx.openDocument({
              filePath: filePath,
              showMenu: true,
              success: function (res) {
                wx.hideLoading();
              }
            })
          }
        });
      } else {
        const { code, data = {} } = await Api.dealInvoice({
          orderNo: item.orderNo,
          patName: this.currentPatient.patientName,
          msgKey: md5(item.orderNo+'dealInvoice2024his')
        });
        if (code !== 0) {
          return;
        }
        if (data.resultCode != 0) {
          wepy.showModal({
            title: '提示',
            content: '功能更新中，请稍后再试。',
            showCancel: false,
            confirmColor: '#30A1A6',
          });
          return;
        }else{
        
          setTimeout(() => { 
             wepy.showLoading({ title: '开票处理中...' }，duration);
             //
            //请求接口
            this.getOrderList(this.currentPatient);
            
          }, 5000);
          wepy.hideLoading();
        }
        //this.getOrderList(this.currentPatient);
      }
    };
    
  }
</script>
