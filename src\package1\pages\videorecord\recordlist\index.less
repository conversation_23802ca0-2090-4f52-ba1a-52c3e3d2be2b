@import "../../../../resources/style/mixins";

page{
  
}

.p-page{
  background-color: #f9f9f9;
  height: 100vh;
}

.m-list{
  overflow: hidden;
  padding-top: 20rpx;
  .list-item{
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    margin: 20rpx 0;
    // background-color: #fff;

  }
  .item-icon{
    width: 60rpx;
    height: 60rpx;
    overflow: hidden;
    border-radius: 50%;
    image{
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-main{
    flex: 1;
    margin-left: 30rpx;
  }
  .main-tit{
    font-size: 34rpx;
    color:@hc-color-title;
  }
  .main-txt{
    font-size: 30rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
  }
  .item-extra{
    text-align: right;
    .color-0{
      color: @hc-color-primary;
    }
    .color-1{
      color: @hc-color-warn;
    }
  }
  .extra-tit{
    font-size: 34rpx;
    color:@hc-color-text;
  }
  .extra-txt{
    font-size: 30rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
  }
  
  .list-item{
    .item-icon{
      background-color: @hc-color-warn;
    }
    &.list-item-success,&.list-item-lock{
      .item-icon{
        background-color: @hc-color-primary;
      }
      .extra-tit{
        color:@hc-color-warn;
      }
    }
    &.list-item-fail{
      .item-icon{
        background-color: @hc-color-error;
      }
    }
    &.list-item-cancel{
      .item-icon{
        background-color: @hc-color-text;
      }
    }
    &.list-item-abnormal{
      .item-icon{
        background-color: @hc-color-warn;
      }
    }
    background-color: #fff;
  }
  .unit-label{
    margin-left: 5px;
    display: inline-block;
    font-size: 24rpx;
    padding: 0 6rpx;
    color:#fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 4rpx;
  }
}

.title-btn{
  background-color: @hc-color-primary;
  color: #fff;
  border-radius: 40rpx;
  font-size: 32rpx;
  width: 80%;
  line-height: 70rpx;
  text-align: center;
  margin-left: 10%;
}
