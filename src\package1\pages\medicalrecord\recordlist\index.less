@import "../../../../resources/style/mixins";

page{
}
.m-date{
  position: relative;
  z-index: 1;
  padding: 20rpx 0;
  margin-top: 15rpx;
  text-align: center;
  background-color: #fff;
  box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.03);
  .date{
    display: inline-block;
    vertical-align: top;
    position: relative;
    padding: 0 30rpx;
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .arrow{
    position: absolute;
    right: 0;
    top: 50%;
    width: 15rpx;
    height: 15rpx;
    border-right: 4rpx solid #C7C7CC;
    border-bottom: 4rpx solid #C7C7CC;
    transform: translate(0, -75%) rotate(45deg);
  }
}
.m-record{
  border-radius: 4rpx;
  margin: 20rpx;
  padding: 0 25rpx 0 30rpx;
  background-color: #fff;

  .record-item{
    padding: 38rpx 0 38rpx 0;

    .record-title{
      color: @hc-color-title;
      font-size: 34rpx;
      font-weight: 600;
      .ellipsis();
    }
    .record-text{
      margin-top: 10rpx;
      color: @hc-color-text;
      font-size: 30rpx;
      .ellipsis();
    }
  }
}
