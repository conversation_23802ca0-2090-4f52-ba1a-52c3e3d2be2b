@import "../../../../resources/style/mixins";

.m-tab {
  background-color: @hc-color-white;
  padding: 0 5%;
  .tab-li {
    &:after {
      width: 1.5em;
    }
  }
  .unit-tab {
    position: relative;
    display: flex;
    background-color: @hc-color-white;
    
    & > .unit-tab-li {
      position: relative;
      z-index: 1;
      display: block;
      flex: 1;
      width: 150rpx;
      font-size: 32rpx;
      margin-right: 10rpx;
      text-align: center;
      color: @hc-color-title;
      height: 100rpx;
      line-height: 100rpx;
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      user-select: none;
      & > a {
        display: block;
        width: 100%;
        height: 100%;
        color: @hc-color-title;
      }
      &:after {
        content: " ";
        position: absolute;
        left: 50%;
        bottom: 0;
        display: block;
        width: 2.5em;
        height: 4rpx;
        background-color: @hc-color-primary;
        transform: translateX(-50%) scaleX(0);
        transition: all ease-out 0.2s 0.1s;
      }
      &.active {
        color: @hc-color-primary;
        font-weight: 600;
        &:after {
          transform: translateX(-50%) scale(1);
        }
        & > a {
          color: @hc-color-primary;
        }
      }
    }
  }
}
.item-box {
  // height: 200rpx;
  background-color: @hc-color-white;
  display: flex;
  align-items: center;
  border-top: 2rpx solid @hc-color-border;
  padding: 24rpx 30rpx;

    .image-box {
      width: 138rpx;
      height: 140rpx;
      image {
        width: 138rpx;
        height: 140rpx;
      }
    }
    .list-box {
      flex: 1;
      padding-right: 40rpx;
      .list-title {
        font-size: 34rpx;
        font-weight: 600;
        color: @hc-color-title;
      }
      .list-time {
        margin-top: 12rpx;
        font-size: 28rpx;
        color: @hc-color-gray;
        text{
          height: 40rpx;
          border-radius: 20rpx;
          border: 2rpx solid @hc-color-border;
          margin-right: 14rpx;
          display: inline-block;
          padding: 0 20rpx;
        }
      }
    }
  
}