<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy"
import { jsonToQueryString } from "@/utils/utils"

import * as Api from "./api"
export default class Agreement extends wepy.page {
  config = {
    navigationBarTitleText: "",
    navigationBarBackgroundColor: '#fff',
  }
  data = {
    options: {},
    isChecked: false
  }

  onLoad(options) {
    this.options = options
  }
  onShow() {}
  methods = {
    checkboxChange(e) {
      this.isChecked = !this.isChecked
    },
    beginApply() {
      this.onApply()
    }
  }

  async onApply(param = {}) {
    if (!this.isChecked) {
      return
    }

    const { isRepeat = "" } = param
    const params = { ...this.options, isRepeat }
    console.log("---params---", params)
    const { code, data, msg = "" } = await Api.save(params)
    if (code === 0) {
      wx.showToast({
        title: "申请成功",
        icon: "success",
        duration: 1500,
        success: () => {
          setTimeout(() => {
            wepy.navigateTo({
              url: `/package2/pages/medical/recorddetail/index?id=${data.id ||
                ""}`
            })
          }, 1500)
        }
      })
    } else if (code === 1) {
      wx.showModal({
        title: "温馨提示",
        content:
          "该条记录您有尚未结束的申请，不能重复申请，可以前往申请记录中查看订单状态。",
        cancelText: "取消",
        confirmText: "确定",
        confirmColor: "#3ECDB5",
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: "/package2/pages/medical/recordlist/index"
            })
            return
          }
          // wx.navigateBack()
        }
      })
    } else if (code === 2) {
      wx.showModal({
        title: "温馨提示",
        content: "该条记录您已提交过复印申请，是否再次申请？",
        cancelText: "取消",
        confirmText: "确定",
        confirmColor: "#3ECDB5",
        success: (res) => {
          if (res.confirm) {
            this.onApply({ isRepeat: 1 })
            return
          }
          // wx.navigateBack()
        }
      })
    } else {
      wx.showModal({
        title: "温馨提示",
        content: msg,
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3ECDB5"
      })
    }
  }
}
</script>


