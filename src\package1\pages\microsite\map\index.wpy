<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  export default class Map extends wepy.page {
    config = {
      navigationBarTitleText: '医院地图',
      usingComponents: {
        "map-route": "plugin://myPlugin/mapRoute"
      }
    };

    components = {
    };

    onLoad(options) {
      
    }

    data = {
     
    }

    methods = {
      regionchange(e) {
        console.log(e.type)
      },
      markertap(e) {
        console.log(e.markerId)
      },
      controltap(e) {
        console.log(e.controlId)
      }
    };
  }
</script>
