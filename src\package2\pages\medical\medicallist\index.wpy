<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy"
import Outpatient from "@/components/outpatient/index"
import { jsonToQueryString } from "@/utils/utils"

import * as Api from "./api"
export default class PrijectList extends wepy.page {
  config = {
    navigationBarTitleText: "病历复印预约",
    navigationBarBackgroundColor: '#fff',
  }
  components = {
    outpatient: Outpatient
  }
  data = {
    // url参数
    options: {},
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 就诊人列表
    outpatient: {},
    // 当前就诊人信息
    projectList: [
      { id: "1", value: "住院病历" },
      { id: "2", value: "试管病历" },
      { id: "3", value: "人工授精病历" }
    ]
  }
  onLoad() {}
  onShow() {
    const { patientId = "" } = this.$wxpage.options
    this.options = this.$wxpage.options
    this.$broadcast("outpatient-get-patient", { patientId })
    wepy.removeStorageSync("selectInfo")
  }
  events = {
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient
        this.outpatientConfig.infoShow = true
        this.$apply()
      }
    }
  }
  methods = {
    async onClick(item = {}) {
      const {
        patCardNo = "",
        patientName = "",
        patientId = "",
        patHisNo = ""
      } = this.options
      const { code, data = [] } = await Api.queryBlfycx({
        pid: patHisNo,
        queryType: item.value
      })
      if (code === 0) {
        const query = decodeURIComponent(
          jsonToQueryString({
            patientName,
            medicalCopyType: item.id,
            pid: patHisNo,
            grid: patCardNo,
            hospitalTimes: patientId
          })
        )
        wepy.navigateTo({
          url: `/package2/pages/medical/medicalinfo/index?${query}`
        })
      }
    },
    goToRecordList() {
      wepy.navigateTo({
        url: `/package2/pages/medical/recordlist/index`
      })
    }
  }
}
</script>


