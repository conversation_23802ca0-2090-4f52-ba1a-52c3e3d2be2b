@import "../../../resources/style/mixins";

.p-page {
  min-height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 10rpx; // 进一步减少底部内边距
}

// 收样列表样式
.sample-list-container {
  padding: 20rpx;
  
  .sample-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .sample-list-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
    
    .create-sample-btn {
      padding: 10rpx 20rpx;
      background-color: #2AAFFF;
      color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  
  .sample-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300rpx;
    
    .loading-icon {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #2AAFFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
  
  .sample-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400rpx;
    
    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
    }
    
    .create-sample-btn-empty {
      margin-top: 30rpx;
      padding: 15rpx 30rpx;
      background-color: #2AAFFF;
      color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  
  .sample-list {
    max-height: calc(100vh - 180rpx);
    
    .sample-item {
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 30rpx;
      padding: 30rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      .sample-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 20rpx;
        
        .sample-id {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        
        .sample-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .sample-item-content {
        .sample-info-row {
          display: flex;
          margin-bottom: 16rpx;
          line-height: 1.5;
          
          .sample-info-label {
            width: 180rpx;
            font-size: 28rpx;
            color: #666;
            flex-shrink: 0;
          }
          
          .sample-info-value {
            flex: 1;
            font-size: 28rpx;
            color: #333;
            word-break: break-all;
            
            .status-tag {
              display: inline-block;
              padding: 4rpx 12rpx;
              border-radius: 4rpx;
              font-size: 24rpx;
              
              &.status-pending {
                background-color: #FFF2F2;
                color: #FF5151;
              }
              
              &.status-completed {
                background-color: #E8F7FF;
                color: #2AAFFF;
              }
            }
          }
        }
      }
      
      .sample-item-footer {
        margin-top: 30rpx;
        display: flex;
        justify-content: flex-end;
        
        .sample-view-btn {
          padding: 12rpx 30rpx;
          background-color: #2AAFFF;
          color: #fff;
          border-radius: 30rpx;
          font-size: 28rpx;
        }
      }
    }
  }
}

// 报告出具页面样式
.report-page {
  padding: 10rpx 0; /* 增加顶部内边距 */
  margin-top: -5rpx; /* 减少顶部空白 */
}

.sample-card {
  margin: 10rpx 32rpx; /* 增加上下外边距 */
  background-color: #fff;
  border-radius: 16rpx;
  padding: 25rpx; /* 增加内边距 */
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx; /* 增加底部边距 */
}

.card-title {
  font-size: 26rpx; /* 减小字体 */
  color: #999;
  margin-bottom: 15rpx; /* 减少间距 */
}

.barcode-area {
  width: 100%;
  margin-bottom: 10rpx; /* 减少间距 */
  
  .barcode-image {
    width: 100%;
    height: 80rpx; /* 进一步降低高度 */
  }
}

.sample-code-text {
  font-size: 32rpx; /* 减小字体 */
  font-weight: 600;
  color: #333;
  letter-spacing: 2rpx;
  margin-bottom: 10rpx; /* 减少间距 */
}

.sample-info-text {
  font-size: 26rpx; /* 减小字体 */
  color: #333;
  margin-bottom: 8rpx; /* 减少间距 */
  font-weight: 500;
}

.scan-tip {
  font-size: 22rpx; /* 减小字体 */
  color: #666;
}

.process-timeline {
  margin: 15rpx 32rpx; /* 减少外边距 */
  background-color: #fff;
  border-radius: 16rpx;
  padding: 25rpx 30rpx; /* 增加内边距 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .process-item {
    display: flex;
    position: relative;
    padding-bottom: 35rpx; /* 增加间距 */

    &:last-child {
      padding-bottom: 0;
    }

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 25rpx; /* 调整连接线位置 */
      top: 50rpx; /* 调整连接线起点 */
      bottom: 0;
      width: 2rpx;
      background-color: #e0e0e0;
    }

    .process-dot {
      width: 50rpx; /* 增加圆点大小 */
      height: 50rpx; /* 增加圆点大小 */
      border-radius: 50%;
      background-color: #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 25rpx; /* 增加右边距 */
      z-index: 1;
      
      text {
        color: #fff;
        font-size: 26rpx; /* 增加字体 */
        font-weight: 500;
      }

      &.completed {
        background-color: #30A1A6;
      }
      
      &.pending {
        background-color: #e0e0e0;
      }
    }

    .process-content {
      flex: 1;

      .process-title {
        display: flex;
        align-items: center;
        font-size: 30rpx; /* 增加字体 */
        font-weight: 500;
        color: #333;
        
        .process-status {
          margin-left: 15rpx; /* 增加间距 */
          font-size: 24rpx; /* 增加字体 */
          color: #30A1A6;
          background-color: rgba(48, 161, 166, 0.1);
          padding: 4rpx 12rpx; /* 增加内边距 */
          border-radius: 4rpx;
        }
      }

      .process-time {
        font-size: 24rpx; /* 增加字体 */
        color: #999;
        margin-top: 6rpx; /* 增加上边距 */
      }
    }
  }
}

// 操作按钮通用样式
.action-button {
  width: 92%;
  height: 80rpx; /* 减小高度 */
  line-height: 80rpx; /* 减小行高 */
  text-align: center;
  background: linear-gradient(135deg, #30a1a6 0%, #00c6b8 100%);
  color: #ffffff;
  font-size: 30rpx; /* 减小字体 */
  border-radius: 40rpx; /* 减小圆角 */
  box-shadow: 0 6rpx 12rpx rgba(0, 185, 204, 0.2); /* 减小阴影 */
  margin: 15rpx auto 20rpx; /* 减少上边距，增加下边距 */
  transition: all 0.3s;
  
  &:active {
    opacity: 0.9;
    transform: scale(0.98);
  }
  
  &.disabled {
    background: linear-gradient(135deg, #b0b0b0 0%, #d0d0d0 100%);
    box-shadow: none;
    opacity: 0.8;
    pointer-events: none;
  }
}

.basic-info-form {
  background: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx 30rpx;
  padding: 10rpx 0 20rpx; // 减少内边距
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding-bottom: 90rpx; // 减少底部内边距，使其更紧凑

  .section-title {
    font-size: 32rpx; // 减小字体大小
    font-weight: 600;
    color: #333;
    padding: 20rpx 30rpx 10rpx; // 减少内边距
    border-left: 6rpx solid #30A1A6; // 减小边框宽度
    margin: 0 0 5rpx 0; // 减少下边距
    position: relative;
    display: flex;
    align-items: center;
    
    .title-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 10rpx;
      background: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M896 128H128c-35.2 0-64 28.8-64 64v640c0 35.2 28.8 64 64 64h768c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m0 704H128V192h768v640z" fill="%2330A1A6" p-id="2479"></path><path d="M224 384h576c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32zM224 544h576c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32zM224 704h384c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32z" fill="%2330A1A6" p-id="2480"></path></svg>') no-repeat center center;
      background-size: contain;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 30rpx;
      width: 100rpx;
      height: 3rpx;
      background: linear-gradient(90deg, #30A1A6 0%, rgba(47, 132, 139, 0.3) 100%);
    }
  }
  
  .form-intro {
    font-size: 26rpx;
    color: #999;
    padding: 0 30rpx 20rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx dashed #eee;
    
    .required {
      color: #f56c6c;
      font-weight: bold;
      margin: 0 4rpx;
    }
  }

  .form-item {
    margin-bottom: 16rpx; // 减少下边距
    padding: 16rpx 30rpx; // 减少内边距
    border-bottom: 1rpx solid #f0f0f0;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    &:last-child {
      border-bottom: none;
    }
    
    &.error {
      background-color: rgba(245, 108, 108, 0.05);
      
      .error-message {
        font-size: 24rpx;
        color: #f56c6c;
        margin-top: 10rpx;
        padding-left: 10rpx;
        display: flex;
        align-items: center;
        width: 100%;
        margin-left: 180rpx;
        
        &::before {
          content: "!";
          display: inline-block;
          width: 24rpx;
          height: 24rpx;
          line-height: 24rpx;
          text-align: center;
          background: #f56c6c;
          color: #fff;
          border-radius: 50%;
          margin-right: 8rpx;
          font-size: 20rpx;
          font-weight: bold;
        }
      }
      
      .form-input, .form-select {
        border: 1rpx solid #f56c6c;
      }
    }
    
    &.focused {
      background-color: rgba(47, 132, 139, 0.05);
    }
    
    .form-label {
      font-size: 28rpx; // 减小字体大小
      color: #333;
      width: 160rpx; // 减小标签宽度
      flex-shrink: 0;
      display: flex;
      align-items: center;
      
      .required {
        color: #f56c6c;
        margin-right: 8rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
    
    .input-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      height: 70rpx;
      line-height: 70rpx;
      border-radius: 8rpx;
      background-color: #f8f8fa;
      padding: 0 30rpx;
      border: 1rpx solid transparent;
      transition: all 0.3s;
    
    .input-icon {
      width: 40rpx;
      height: 40rpx;
        margin-right: 20rpx;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      
      &.name-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M512 512c94.293333 0 170.666667-76.373333 170.666667-170.666667s-76.373333-170.666667-170.666667-170.666666-170.666667 76.373333-170.666667 170.666666 76.373333 170.666667 170.666667 170.666667z m0-256c47.146667 0 85.333333 38.186667 85.333333 85.333333s-38.186667 85.333333-85.333333 85.333334-85.333333-38.186667-85.333333-85.333334 38.186667-85.333333 85.333333-85.333333zM512 597.333333c-113.92 0-341.333333 57.173333-341.333333 170.666667v85.333333h682.666666v-85.333333c0-113.493333-227.413333-170.666667-341.333333-170.666667z m0 85.333334c103.68 0 222.293333 45.653333 256 85.333333H256c32.426667-39.68 151.893333-85.333333 256-85.333333z" fill="%2399a9bf" p-id="2479"></path></svg>');
      }
      
      &.id-type-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M896 128H128c-35.2 0-64 28.8-64 64v640c0 35.2 28.8 64 64 64h768c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m0 704H128V192h768v640z" fill="%2399a9bf" p-id="2479"></path><path d="M224 384h576c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32zM224 544h576c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32zM224 704h384c17.6 0 32-14.4 32-32s-14.4-32-32-32H224c-17.6 0-32 14.4-32 32s14.4 32 32 32z" fill="%2399a9bf" p-id="2480"></path></svg>');
      }
      
      &.id-card-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M896 128H128c-35.2 0-64 28.8-64 64v640c0 35.2 28.8 64 64 64h768c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64z m0 704H128V192h768v640z" fill="%2399a9bf" p-id="2479"></path><path d="M320 320m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" fill="%2399a9bf" p-id="2480"></path><path d="M320 480c-88.4 0-160 71.6-160 160v64h320v-64c0-88.4-71.6-160-160-160zM640 384h192v64h-192zM640 512h192v64h-192zM640 640h192v64h-192z" fill="%2399a9bf" p-id="2481"></path></svg>');
      }
      
      &.age-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="%2399a9bf" p-id="2479"></path><path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z" fill="%2399a9bf" p-id="2480"></path></svg>');
      }
      
      &.gender-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M712.8 548.8c53.6-53.6 83.2-125 83.2-200.8 0-75.8-29.6-147.2-83.2-200.8S587.8 64 512 64c-75.8 0-147.2 29.6-200.8 83.2S228 272.2 228 348c0 75.8 29.6 147.2 83.2 200.8 44.2 44.2 99.8 72.6 160.6 81.6v153.4h-96v64h96v112h64v-112h96v-64h-96V630.4c60.8-9 116.4-37.4 160.6-81.6zM512 128c59.2 0 114.8 23 156.6 64.8S733.4 290.2 733.4 348c0 57.8-23 112.2-64.8 153.4-42.4 42.4-98.6 65.4-158.6 64.8-59.2-0.6-114.8-24.4-156.6-66.2S292 388.2 292 348c0-57.8 23-112.2 64.8-153.4C397.2 151 452.8 128 512 128z" fill="%2399a9bf" p-id="2479"></path></svg>');
      }
      
      &.phone-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M744.8 64H279.2c-26.6 0-48 21.4-48 48v800c0 26.6 21.4 48 48 48h465.6c26.6 0 48-21.4 48-48V112c0-26.6-21.4-48-48-48z m-16 816H295.2c-8.8 0-16-7.2-16-16V160c0-8.8 7.2-16 16-16h433.6c8.8 0 16 7.2 16 16v704c0 8.8-7.2 16-16 16z" fill="%2399a9bf" p-id="2479"></path><path d="M512 784m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z" fill="%2399a9bf" p-id="2480"></path></svg>');
      }
      
      &.height-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M840 128H184c-4.4 0-8 3.6-8 8v752c0 4.4 3.6 8 8 8h656c4.4 0 8-3.6 8-8V136c0-4.4-3.6-8-8-8z m-40 716H224V176h576v668z" fill="%2399a9bf" p-id="2479"></path><path d="M300 344h424v64H300zM300 544h424v64H300z" fill="%2399a9bf" p-id="2480"></path></svg>');
      }
      
      &.weight-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M512 128c-212 0-384 172-384 384s172 384 384 384 384-172 384-384-172-384-384-384z m0 690.8c-169.2 0-306.8-137.6-306.8-306.8S342.8 205.2 512 205.2 818.8 342.8 818.8 512 681.2 818.8 512 818.8z" fill="%2399a9bf" p-id="2479"></path><path d="M512 320c-16.8 0-32.8 2.8-48 8v48c0 8.8 7.2 16 16 16h64c8.8 0 16-7.2 16-16v-48c-15.2-5.2-31.2-8-48-8z" fill="%2399a9bf" p-id="2480"></path><path d="M512 384m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" fill="%2399a9bf" p-id="2481"></path><path d="M288 512c0 123.6 100.4 224 224 224s224-100.4 224-224c0-28.4-5.6-55.2-15.2-80l-55.2 32c5.6 15.2 8.8 31.2 8.8 48 0 88.4-71.6 160-160 160s-160-71.6-160-160c0-16.8 3.2-32.8 8.8-48l-55.2-32c-9.6 24.8-15.2 51.6-15.2 80z" fill="%2399a9bf" p-id="2482"></path></svg>');
      }
      
      &.occupation-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32z m-40 824H232V136h560v752z" fill="%2399a9bf" p-id="2479"></path><path d="M672 394h-46.9c-10.2 0-19.9 4.9-25.9 13.2L512 526.3 423.8 407.2c-6-8.3-15.6-13.2-25.9-13.2H352c-6.6 0-10.5 7.5-6.7 12.9l143.4 194.8c6.3 8.6 20.3 8.6 26.6 0l143.4-194.8c3.8-5.4-0.1-12.9-6.7-12.9zM512 824c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40z" fill="%2399a9bf" p-id="2480"></path></svg>');
      }
      
      &.education-icon {
        background-image: url('data:image/svg+xml;utf8,<svg t="1635739456646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2478" width="32" height="32"><path d="M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936L512 128z m0 128l255.936 192v384H704V576H320v256H192V448L512 256z" fill="%2399a9bf" p-id="2479"></path></svg>');
      }
    }
    
      .form-input,
      .unit-input,
      .form-select {
        flex: 1;
        height: 100%;
        line-height: inherit;
        background-color: transparent;
        border: none;
        padding: 0;
        font-size: 28rpx;
      color: #333;
      }
      
      .form-select,
      .unit-input {
      display: flex;
      align-items: center;
          justify-content: space-between;
      }
      
      .form-input::placeholder {
          color: #bbb;
        }
      }
      
    .form-item.focused .input-wrapper {
      background-color: #fff;
      border-color: #30A1A6;
      box-shadow: 0 0 0 3rpx rgba(48, 161, 166, 0.2);
    }
  }
  
  .divider {
    display: flex;
    align-items: center;
    margin: 20rpx 30rpx;
    color: #999;
    font-size: 26rpx;
    
    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1rpx;
      background-color: #eee;
    }
    
    .divider-text {
      padding: 0 20rpx;
    }
  }
}

.agreement-container {
  margin: 40rpx 30rpx 20rpx;
  text-align: center;
  
  .agreement-text {
    font-size: 28rpx;
    color: #30A1A6;
    text-decoration: underline;
    padding: 15rpx;
    display: inline-block;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 10rpx;
      left: 15rpx;
      right: 15rpx;
      height: 1rpx;
      background-color: #30A1A6;
      opacity: 0.6;
    }
    
    &:active {
      opacity: 0.8;
      background-color: rgba(48, 161, 166, 0.05);
      border-radius: 8rpx;
    }
  }
}

.step-process {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 20rpx 50rpx; /* 增加上下内边距 */
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    padding: 0 5rpx;

    &:not(:last-child):after {
      content: '';
      position: absolute;
      top: 25rpx;
      left: 50%;
      width: 90%;
      height: 3rpx;
      background-color: #e0e0e0;
    }

    .step-number {
      width: 60rpx; /* 增加圆圈大小 */
      height: 60rpx; /* 增加圆圈大小 */
      line-height: 60rpx; /* 调整行高匹配圆圈大小 */
      text-align: center;
      border-radius: 50%;
      background-color: #e0e0e0;
      color: #fff;
      font-size: 30rpx; /* 增加字体大小 */
      font-weight: 500;
      margin-bottom: 20rpx; /* 增加下方间距 */
      z-index: 1;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    }

    .step-title {
      font-size: 26rpx; /* 增加字体大小 */
      color: #999;
      white-space: nowrap;
      text-align: center;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.active {
      .step-number {
        background: linear-gradient(135deg, #30A1A6 0%, #2F848B 100%);
      }

      .step-title {
        color: #30A1A6;
        font-weight: 500;
      }

      &:not(:last-child):after {
        background: linear-gradient(90deg, #30A1A6 0%, rgba(47, 132, 139, 0.5) 100%);
      }
    }
  }
}

.question-cards {
  padding: 40rpx; /* 增加内边距 */

  .select-title {
    font-size: 36rpx; /* 增加字体大小 */
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-bottom: 50rpx; /* 增加下边距 */
  }

  .question-card {
    height: 360rpx;
    margin-bottom: 50rpx; /* 增加卡片之间的间距 */
    border-radius: 16rpx;
    padding: 0;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:active {
      transform: translateY(-10rpx);
      box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.18);
    }
    
    &.selected {
      transform: translateY(-5rpx);
      box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.18);
      
      &::after {
        content: '✓';
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background: #fff;
        color: #333;
        font-size: 24rpx;
        font-weight: bold;
        text-align: center;
        line-height: 40rpx;
      }
    }

    &.normal-version {
      background: linear-gradient(135deg, #30A1A6 0%, #2F848B 100%);
      color: #fff;
      
      .card-title {
        font-size: 60rpx; /* 大字体尺寸 */
        font-weight: 600;
        text-align: center;
        margin-bottom: 30rpx; /* 增加间距 */
        letter-spacing: 2rpx;
        color: #ffffff; 
        text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
      }
      
      .card-description {
        font-size: 32rpx; /* 大字体 */
        color: rgba(255, 255, 255, 0.9);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: -40rpx;
        right: -40rpx;
        width: 200rpx;
        height: 200rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
      }
      
      &.selected::after {
        color: #30A1A6;
      }
    }

    &.child-version {
      background: linear-gradient(135deg, #4E7FE2 0%, #3A67C4 100%);
      color: #fff;
      margin-bottom: 0;

      .card-title {
        font-size: 60rpx; /* 大字体尺寸 */
        font-weight: 600;
        text-align: center;
        margin-bottom: 30rpx; /* 增加间距 */
        letter-spacing: 2rpx;
        color: #ffffff;
        text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
      }
      
      .card-description {
        font-size: 32rpx; /* 大字体 */
        color: rgba(255, 255, 255, 0.9);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: -40rpx;
        left: -40rpx;
        width: 200rpx;
        height: 200rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: -40rpx;
        right: -40rpx;
        width: 180rpx;
        height: 180rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.05);
      }
      
      &.selected::after {
        content: '✓';
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        bottom: auto;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background: #fff;
        color: #4E7FE2;
        font-size: 24rpx;
        font-weight: bold;
        text-align: center;
        line-height: 40rpx;
      }
    }
  }
}

.btn-container {
  padding: 16rpx 30rpx; // 减少内边距
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100; // 增加z-index确保在最上层
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: calc(16rpx + constant(safe-area-inset-bottom)); // 适配iPhone X等机型
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));

  .btn-common {
    height: 80rpx; // 减少按钮高度
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 40rpx; // 调整圆角
    transition: all 0.3s;
    box-sizing: border-box;
  }

  .btn-next {
    .btn-common();
    background: linear-gradient(135deg, #30A1A6 0%, #2F848B 100%);
    color: #fff;
    box-shadow: 0 6rpx 16rpx rgba(47, 132, 139, 0.3);
    width: 100%; // 占满整行
    padding: 0 40rpx; // 增加左右内边距
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */

    &:active {
      transform: scale(0.98);
      box-shadow: 0 3rpx 8rpx rgba(47, 132, 139, 0.3);
    }
  }

  .btn-skip {
    .btn-common();
    background: #f5f5f5;
    color: #666;
    border: 1rpx solid #e0e0e0;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */

    &:active {
      background: #e8e8e8;
    }
  }

  .btn-single {
    width: 100%;
    padding: 0 40rpx; // 增加左右内边距
  }

  .btn-group-step2 {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .skip-btn {
      flex: 0 0 58%; /* 增加宽度比例，给更多文字空间 */
      margin-right: 10rpx;
      font-size: 28rpx; /* 稍微减小字体大小 */
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }

    .next-btn {
      flex: 0 0 38%; /* 减小宽度比例 */
      margin-left: 10rpx;
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
  }
}

/* 第三步：填写病史信息问卷样式 */
.survey-container {
  padding: 20rpx 20rpx 60rpx; // 减少顶部和侧边内边距，增加底部内边距
}

/* 为底部按钮留出空间的占位符 */
.survey-bottom-placeholder {
  height: 60rpx; // 进一步减少高度
  width: 100%;
}

.survey-card {
  border-radius: 12rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.survey-head {
  padding: 20rpx 30rpx 20rpx; // 减少顶部内边距
  background-color: #fff;
  margin-bottom: 10rpx; // 减少底部外边距
  border-radius: 12rpx 12rpx 0 0;
  box-shadow: none; // 移除阴影，避免与survey-card重复
}

.survey-title {
  font-size: 36rpx; // 稍微减小标题字体
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx; // 减少底部外边距
  line-height: 1.4;
}

.survey-des {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5; // 稍微减小行高
}

.survey-body {
  padding: 20rpx 30rpx 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.question-item {
  position: relative;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 填空题特殊样式 */
.question-item-fillblank {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.question-title {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 16rpx;
  margin-top: 20rpx;
  color: #333333;
  word-break: break-word;
}

/* 填空题标题特殊样式 */
.question-title-fillblank {
  margin: 0;
  padding-right: 4rpx;
  flex-shrink: 0;
}

/* 填空题样式 */
.fill-blank-title {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 1.8;
  font-size: 28rpx;
  font-weight: normal;
  flex: 1;
  word-break: break-word;
}

.fill-blank-text {
  display: inline;
  word-break: break-word;
  font-size: 28rpx;
  line-height: 1.8;
  white-space: nowrap;
  margin-right: 4rpx;
}

.fill-blank-input {
  display: inline-block;
  min-width: 120rpx;
  width: auto; /* 让宽度自适应内容 */
  max-width: 160rpx; /* 最大宽度限制 */
  height: 50rpx;
  line-height: 50rpx;
  border: none;
  border-bottom: 2rpx solid #3ECEB6;
  margin: 0 4rpx;
  padding: 0 4rpx;
  font-size: 28rpx;
  text-align: center;
  color: #3ECEB6;
  background-color: transparent;
  vertical-align: baseline;
  box-sizing: border-box;
  transition: border-bottom-color 0.3s;
  
  &:focus {
    border-bottom-color: #2BA99A;
    outline: none;
  }
  
  &::placeholder {
    color: #BBBBBB;
    font-size: 24rpx;
  }
}

.survey-form {
  background-color: #f5f5f5;
  padding-bottom: 60rpx; // 增加底部内边距，确保内容完全显示
}

/* 调整底部按钮的样式 */
.fixed-bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16rpx 30rpx; // 减少内边距
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  z-index: 100;
  padding-bottom: calc(16rpx + constant(safe-area-inset-bottom)); // 适配iPhone X等机型
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  
  .submit-btn {
    background-color: #2AAFFF;
    color: #fff;
    height: 80rpx; // 减少按钮高度
    line-height: 80rpx;
    border-radius: 40rpx; // 调整圆角
    font-size: 28rpx; // 减小字体大小
    font-weight: normal;
  }
}

// 添加媒体查询，适应不同屏幕尺寸
@media screen and (max-width: 375px) {
  .basic-info-form {
    .form-item {
      .form-label {
        width: 160rpx;
        font-size: 28rpx;
      }
      
      .error-message {
        margin-left: 160rpx;
      }
    }
  }
}

// 底部提交按钮样式
.btn-box {
  padding: 10rpx 40rpx 20rpx;

  .btn-next.btn-single {
    height: 88rpx;
    line-height: 88rpx;
    background-color: #30A1A6;
    color: #fff;
    text-align: center;
    border-radius: 44rpx;
    font-size: 28rpx; // 修改为28rpx，与"新增样本问卷"按钮一致
    font-weight: 500;
    box-shadow: 0 4rpx 16rpx rgba(48, 161, 166, 0.2);

    &:active {
      background-color: darken(#30A1A6, 10%);
    }
  }
}

// 固定底部按钮样式
.fixed-bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 0 40rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  
  .survey-btn {
    margin: 20rpx auto;
    
    &.submit-btn {
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(48, 161, 166, 0.3);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 6rpx rgba(48, 161, 166, 0.3);
      }
    }
  }
}

// 问卷调查样式 - 从surveytemp/index.less整合
.survey-head {
  padding: 30rpx 40rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.survey-title {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.survey-des {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.survey-body {
  padding: 0 30rpx;
}

.template-panle {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.mast-select {
  width: 18rpx;
  height: 18rpx;
  vertical-align: middle;
  margin-left: 5rpx;
}

.radio-group,
.checkbox-group {
  width: 100%;
  box-sizing: border-box;
}

.radio-group-item,
.checkbox-group {
  width: 100%;
  box-sizing: border-box;
}

.radio-group-item:last-child,
.checkbox-group {
  margin-bottom: 0;
}

.radio-group-item > view,
.checkbox > view {
  display: flex;
  align-items: center;
}

.radio-group-item label,
.checkbox text {
  margin-left: 12rpx;
  line-height: 1.5;
  flex: 1;
}

/* 二级内容容器 */
.secondary-content-container {
  margin-top: 16rpx;
  margin-left: 70rpx;
  width: calc(100% - 70rpx);
  box-sizing: border-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: -10rpx;
    top: 20rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
  }
  
  textarea {
    background-color: #fff;
    padding: 12rpx 0 10rpx;
    font-size: 28rpx;
    width: 100%;
    box-sizing: border-box;
    min-height: 80rpx;
    line-height: 1.4;
    border: none;
    border-bottom: 1rpx solid #e0e0e0;
    border-radius: 0;
    transition: border-color 0.3s ease;
    
    &:focus {
      border-bottom: 2rpx solid #3ECEB6;
    }
  }
  
  .empty-secondary-options {
    font-size: 28rpx;
    color: #999;
    padding: 10rpx 0;
  }
}

/* 二级多值填空容器 */
.secondary-fill-blank-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 1.8;
  font-size: 28rpx;
  font-weight: normal;
  width: 100%;
  word-break: break-word;

  .fill-blank-text {
    white-space: nowrap;
    margin-right: 4rpx;
  }

  .fill-blank-input {
    margin: 0 4rpx;
    vertical-align: baseline;
  }
}

.secondary-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 10rpx;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8rpx;
  padding: 16rpx;
  box-sizing: border-box;
}

.secondary-checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  width: 45%; /* 每行显示两个选项 */
  
  &:nth-child(2n) {
    margin-right: 0;
  }
  
  checkbox {
    transform: scale(0.85);
    margin-right: 6rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #555;
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
  }
}

.empty-secondary-options {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx 0;
  text-align: center;
  width: 100%;
}

.radio-group-ext-panel,
.checkbox-remark {
  margin-top: 16rpx;
  margin-left: 70rpx;
  width: calc(100% - 70rpx);
  box-sizing: border-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: -10rpx;
    top: 20rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
  }
}

.radio-group-ext-input-item {
  margin-bottom: 16rpx;
  width: 100%;
  box-sizing: border-box;
  
  label {
    display: block;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
  }
  
  textarea {
    background-color: #fff;
    padding: 12rpx 0 10rpx;
    font-size: 28rpx;
    width: 100%;
    box-sizing: border-box;
    min-height: 80rpx;
    line-height: 1.4;
    border: none;
    border-bottom: 1rpx solid #e0e0e0;
    border-radius: 0;
    transition: border-color 0.3s ease;
    
    &:focus {
      border-bottom: 2rpx solid #3ECEB6;
    }
  }
}

/* 附件上传 */
.file-upload-section {
  margin-top: 30rpx;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
}

.file-upload-title {
  margin-bottom: 20rpx;
  
  .file-upload-title-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    display: block;
    margin-bottom: 8rpx;
  }
  
  .file-upload-title-desc {
    font-size: 24rpx;
    color: #999;
    display: block;
  }
}

.file-upload-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10rpx;
}

.file-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  
  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .delete-img-btn, .delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    border-bottom-left-radius: 8rpx;
  }
  
  &.pdf-item {
    background-color: #f7f7f7;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
    box-sizing: border-box;
    
    .pdf-icon {
      background-color: #f44336;
      color: #fff;
      font-size: 20rpx;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
    }
    
    .pdf-name {
      font-size: 20rpx;
      color: #333;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
  }
}

.file-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;

  &:active {
    background-color: #f0f0f0;
    border-color: #2aafff;
  }

  .upload-icon-plus {
    font-size: 48rpx;
    color: #8c8c8c;
    font-weight: bold;
  }

  .upload-btn-text {
    font-size: 24rpx;
    color: #8c8c8c;
    margin-top: 8rpx;
  }
}

.file-name {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #eaeaea;
  
  .file-info {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
    
    .file-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 10rpx;
      flex-shrink: 0;
      background-color: #f0f0f0;
      border-radius: 4rpx;
      position: relative;
      
      &:before {
        content: '';
        position: absolute;
        top: 6rpx;
        left: 10rpx;
        right: 10rpx;
        height: 2rpx;
        background-color: #999;
      }
      
      &:after {
        content: '';
        position: absolute;
        top: 12rpx;
        left: 10rpx;
        right: 10rpx;
        height: 2rpx;
        background-color: #999;
      }
    }
    
    .file-text {
      font-size: 26rpx;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .delete-file {
    width: 40rpx;
    height: 40rpx;
    line-height: 36rpx;
    text-align: center;
    color: #ff4d4f;
    font-size: 32rpx;
    font-weight: bold;
    flex-shrink: 0;
    margin-left: 10rpx;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.mast-select {
  width: 20rpx;
  height: 20rpx;
  margin-left: 10rpx;
}

// 底部按钮样式
.submit-btn {
  width: 90%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background-color: #3ECEB6;
  color: #fff;
  font-size: 32rpx;
  text-align: center;
  margin: 0 auto;
  box-shadow: 0 6rpx 10rpx rgba(62, 206, 182, 0.2);
}

// 确保文本框内容可以完全显示
input {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
}

/* 确保所有textarea元素都有正确的样式 */
textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 12rpx 0 10rpx;
  background-color: #fff;
  font-size: 28rpx;
  min-height: 80rpx;
  line-height: 1.4;
  border: none;
  border-bottom: 1rpx solid #e0e0e0;
  border-radius: 0;
  transition: border-color 0.3s ease;
  
  &:focus {
    border-bottom: 2rpx solid #3ECEB6;
  }
  
  &::placeholder {
    color: #bbb;
    font-size: 26rpx;
  }
}

// 恢复底部按钮样式
.fixed-bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 0 40rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-btn {
  width: 90%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #3ECEB6 0%, #2BB3A0 100%);
  color: #fff;
  font-size: 32rpx;
  text-align: center;
  margin: 0 auto;
  box-shadow: 0 6rpx 10rpx rgba(62, 206, 182, 0.2);
}

// 确保页面滚动正常
page {
  height: 100%;
}

.p-page {
  min-height: 100%;
  background-color: #f5f5f5;
}

.survey-container {
  padding: 20rpx 20rpx 60rpx; // 减少顶部和侧边内边距，增加底部内边距
}

.survey-form {
  background-color: #f5f5f5;
  padding-bottom: 60rpx; // 增加底部内边距，确保内容完全显示
}

// 添加表单元素样式
.radio-group {
  display: flex;
  flex-direction: column;
}

.radio-group-item, .checkbox {
  display: flex;
  flex-direction: column;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #333;
  
  &.active {
    color: #3ECEB6;
  }
  
  > view:first-child {
    display: flex;
    align-items: center;
    
    radio, checkbox {
      transform: scale(0.9);
      margin-right: 10rpx;
    }
    
    label {
      line-height: 1.6;
    }
  }
}

.secondary-content-container {
  margin-top: 16rpx;
  margin-left: 70rpx;
  width: calc(100% - 70rpx);
  box-sizing: border-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: -10rpx;
    top: 20rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
  }
}

.secondary-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 10rpx 0;
  margin-top: 10rpx;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8rpx;
  padding: 16rpx;
  box-sizing: border-box;
}

.secondary-checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  width: 45%; /* 每行显示两个选项 */
  
  &:nth-child(2n) {
    margin-right: 0;
  }
  
  checkbox {
    transform: scale(0.85);
    margin-right: 6rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #555;
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
  }
}

.radio-group-ext-panel, .checkbox-remark {
  margin-top: 16rpx;
  margin-left: 70rpx;
  width: calc(100% - 70rpx);
  box-sizing: border-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: -10rpx;
    top: 20rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
  }
}

.radio-group-ext-input-item {
  margin-bottom: 16rpx;
  width: 100%;
  box-sizing: border-box;
  
  label {
    display: block;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
  }
}

.mast-select {
  width: 20rpx;
  height: 20rpx;
  margin-left: 10rpx;
}

// 文件预览样式
.file-preview {
  margin-top: 20rpx;
  width: 100%;
  
  .image-preview {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    overflow: hidden;
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    
    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .delete-img-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      border-radius: 0 0 0 8rpx;
      font-size: 30rpx;
      z-index: 2;
    }
  }
}

// 图片内容区域
.img-content {
  padding: 20rpx;
  background-color: #f9f9f9;
  margin: 20rpx 0;
  border-radius: 8rpx;
  
  .content {
    .content-view {
      margin-bottom: 20rpx;
      
      .time {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 10rpx;
      }
      
      .img-box-content {
        display: flex;
        flex-wrap: wrap;
        
        .file-item {
          position: relative;
          width: 140rpx;
          height: 140rpx;
          margin: 10rpx;
          border-radius: 8rpx;
          overflow: hidden;
          
          .img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .delete-img-btn, .delete-btn {
            position: absolute;
            top: 0;
            right: 0;
            width: 40rpx;
            height: 40rpx;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            font-size: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
          }
          
          &.pdf-item {
            background-color: #fff;
            border: 1px solid #eee;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10rpx;
            box-sizing: border-box;
            
            .pdf-icon {
              background-color: #f44336;
              color: #fff;
              font-size: 20rpx;
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 10rpx;
            }
            
            .pdf-name {
              font-size: 20rpx;
              color: #333;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 100%;
            }
          }
        }
      }
    }
  }
  
  .update-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    background-color: #ffffff;
    color: #2AAFFF;
    font-size: 28rpx;
    border-radius: 8rpx;
    margin-top: 20rpx;
    border: 1px dashed #2AAFFF;
  }
}

.content-view {
  margin-bottom: 16rpx;
  padding: 32rpx 32rpx 0;
  background: #FFF;
  border-radius: 8rpx;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
}

.img-box-content {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  
  .file-item {
    margin: 10rpx;
    position: relative;
    width: 160rpx;
    height: 160rpx;
    overflow: hidden;
    border-radius: 8rpx;
    
    &.pdf-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f8f8f8;
      border: 1px solid #e0e0e0;
      padding: 10rpx;
      box-sizing: border-box;
      
      .pdf-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #f44336;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: bold;
        border-radius: 8rpx;
        margin-bottom: 10rpx;
      }
      
      .pdf-name {
        font-size: 22rpx;
        color: #333;
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }
      
      .delete-btn {
        position: absolute;
        top: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border-radius: 0 0 0 8rpx;
        font-size: 30rpx;
      }
    }
    
    .img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .delete-img-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      border-radius: 0 0 0 8rpx;
      font-size: 30rpx;
      z-index: 2;
    }
  }
}

// 底部按钮
.bottom-btn {
  position: fixed;
  padding: 0 32rpx;
  bottom: 48rpx;
  left: 0;
  right: 0;
  width: 100%;
    display: flex;
    justify-content: space-between;
  z-index: 100;
  
  .save {
    margin: 0 24rpx 0 32rpx;
  }
  
  .save, .cancel {
    flex: 1;
    height: 100rpx;
    line-height: 100rpx;
    color: #3ECEB6;
    font-size: 34rpx;
    font-weight: 600;
    text-align: center;
    border-radius: 76rpx;
    border: 1rpx solid #3ECEB6;
    background-color: #fff;
  }
  
  .cancel {
    color: rgba(0, 0, 0, 0.70);
    margin-right: 32rpx;
    background: rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(0, 0, 0, 0.04);
  }
}

/* 步骤4: 签署知情同意书 */
.consent-form-container {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  box-sizing: border-box;

  .consent-icon-wrapper {
    width: 160rpx;
    height: 160rpx;
    background-color: #f0f9f5;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40rpx;

    .consent-icon {
      width: 90rpx;
      height: 90rpx;
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M20%2C10%20C15%2C10%2010%2C15%2010%2C20%20V80%20C10%2C85%2015%2C90%2020%2C90%20H60%20V70%20C60%2C65%2065%2C60%2070%2C60%20H90%20V20%20C90%2C15%2085%2C10%2080%2C10%20H20%20Z%22%20fill%3D%22none%22%20stroke%3D%22%2300c6b8%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cline%20x1%3D%2225%22%20y1%3D%2235%22%20x2%3D%2275%22%20y2%3D%2235%22%20stroke%3D%22%2300c6b8%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Cline%20x1%3D%2225%22%20y1%3D%2250%22%20x2%3D%2260%22%20y2%3D%2250%22%20stroke%3D%22%2300c6b8%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpath%20d%3D%22M55%2C75%20L70%2C90%20L95%2C65%22%20fill%3D%22none%22%20stroke%3D%22%2300c6b8%22%20stroke-width%3D%228%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }

  .consent-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 24rpx;
  }

  .consent-description {
    font-size: 28rpx;
    color: #888888;
    text-align: center;
    line-height: 1.6;
    margin-bottom: 80rpx;
  }

  .sign-consent-btn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    background: #30a1a6;
    color: #ffffff;
    font-size: 32rpx;
    border-radius: 48rpx;
    box-shadow: 0 8rpx 16rpx rgba(0, 185, 204, 0.2);
    transition: all 0.3s;

    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }
  
  // 已签署的PDF文件卡片
  .signed-pdf-card {
    width: 100%;
    background-color: #f8f8f8;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    
    &:active {
      background-color: #f0f0f0;
    }
    
    .pdf-icon {
      width: 80rpx;
      height: 80rpx;
      background-color: #f44336;
      border-radius: 10rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;
      
      .pdf-icon-text {
        color: #ffffff;
        font-size: 24rpx;
        font-weight: bold;
      }
    }
    
    .pdf-info {
      flex: 1;
      
      .pdf-title {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 6rpx;
      }
      
      .pdf-desc {
        font-size: 24rpx;
        color: #999999;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6rpx;
        
        .pdf-sign-status {
          color: #30a1a6;
          font-weight: 500;
          padding: 2rpx 10rpx;
          background-color: rgba(48, 161, 166, 0.1);
          border-radius: 8rpx;
        }
      }
    }
    
    .pdf-action {
      font-size: 28rpx;
      color: #30a1a6;
    }
  }
  
  // 查看报告按钮
  .view-report-btn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    background: linear-gradient(135deg, #30a1a6 0%, #00c6b8 100%);
    color: #ffffff;
    font-size: 32rpx;
    border-radius: 48rpx;
    box-shadow: 0 8rpx 16rpx rgba(0, 185, 204, 0.2);
    transition: all 0.3s;
    
    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }
  
  .test-btn {
    margin-top: 40rpx;
    font-size: 28rpx;
    color: #999999;
    padding: 16rpx 32rpx;
    border: 1rpx solid #dddddd;
    border-radius: 40rpx;

    &:active {
      background-color: #f0f0f0;
    }
  }
}

  &.survey-bottom-bar {
    margin-top: 0; // 移除顶部边距
    padding-bottom: 10rpx; // 调整底部内边距
    box-shadow: 0 -4rpx 12rpx rgba(0,0,0,0.08); // 增强阴影效果
  }

// 底部占位符，为底部按钮留出空间
.bottom-bar-placeholder {
  height: 30rpx; // 减少占位符高度
  width: 100%;
}

// 上传区域样式
.upload-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  
  .upload-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 20rpx;
  }
  
  .upload-content {
    margin-top: 20rpx;
  }
  
  .upload-hint {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

// 文件上传内容区域
.file-upload-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10rpx;
}

// 文件项
.file-item {
    position: relative;
  width: 150rpx;
  height: 150rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  
  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .delete-img-btn, .delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    border-bottom-left-radius: 8rpx;
  }
  
  &.pdf-item {
    background-color: #f7f7f7;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
    box-sizing: border-box;
    
    .pdf-icon {
      background-color: #f44336;
      color: #fff;
      font-size: 20rpx;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
    }
    
    .pdf-name {
      font-size: 20rpx;
      color: #333;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
  }
}

// 上传按钮
.file-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;

  &:active {
    background-color: #f0f0f0;
    border-color: #2aafff;
  }

  .upload-icon-plus {
    font-size: 48rpx;
    color: #8c8c8c;
    font-weight: bold;
  }

  .upload-btn-text {
    font-size: 24rpx;
    color: #8c8c8c;
    margin-top: 8rpx;
  }
}

.form-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
  
  .select-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    &.placeholder {
      color: #999;
    }
  }
  
  .select-arrow {
    width: 16rpx;
    height: 16rpx;
    border-right: 2rpx solid #999;
    border-bottom: 2rpx solid #999;
    transform: rotate(45deg);
    margin-left: 10rpx;
  }
}

.nation-icon {
  background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%23cccccc%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%23cccccc%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C/svg%3E');
}

.form-item.focused {
  .input-wrapper {
    border-color: #3ECEB6;
    box-shadow: 0 0 0 2px rgba(62, 206, 182, 0.2);
  }
  .input-icon {
    &.name-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2235%22%20r%3D%2215%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M20%2C85%20C20%2C65%2080%2C65%2080%2C85%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.age-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2235%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpolyline%20points%3D%2250%2C25%2050%2C50%2070%2C60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.gender-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2235%22%20cy%3D%2235%22%20r%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M35%2C55%20V85%20M20%2C70%20H50%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Ccircle%20cx%3D%2265%22%20cy%3D%2265%22%20r%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M80%2C30%20L50%2C60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.height-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M50%2C15%20V85%20M35%2C20%20L50%2C15%20L65%2C20%20M35%2C80%20L50%2C85%20L65%2C80%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M20%2C50%20H80%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-dasharray%3D%2210%2C10%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.weight-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2230%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M50%2C20%20A30%2C30%200%200%2C1%2075%2C35%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M50%2C20%20L50%2C10%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.id-type-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.nation-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.id-card-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Crect%20x%3D%2215%22%20y%3D%2225%22%20width%3D%2270%22%20height%3D%2250%22%20rx%3D%225%22%20ry%3D%225%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M25%2C55%20H45%20M25%2C65%20H55%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Crect%20x%3D%2260%22%20y%3D%2235%22%20width%3D%2215%22%20height%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.phone-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M30%2C15%20H70%20C75%2C15%2080%2C20%2080%2C25%20V75%20C80%2C80%2075%2C85%2070%2C85%20H30%20C25%2C85%2020%2C80%2020%2C75%20V25%20C20%2C20%2025%2C15%2030%2C15%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M40%2C70%20H60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.occupation-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M20%2C30%20H80%20V75%20C80%2C80%2075%2C85%2070%2C85%20H30%20C25%2C85%2020%2C80%2020%2C75%20V30%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M35%2C30%20V20%20C35%2C15%2040%2C10%2045%2C10%20H55%20C60%2C10%2065%2C15%2065%2C20%20V30%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.education-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M10%2C70%20L50%2C40%20L90%2C70%20L50%2C100%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M20%2C65%20V40%20C20%2C20%2050%2C0%2050%2C0%20C50%2C0%2080%2C20%2080%2C40%20V65%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
  }
}
.form-item.error {
  .input-wrapper {
    border-color: #FA5151;
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 8rpx 30rpx; // 进一步减少上下内边距
  padding-bottom: calc(8rpx + constant(safe-area-inset-bottom)); // 适配iPhone X等机型
  padding-bottom: calc(8rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 999;
  
  display: flex; /* 添加flex布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  box-sizing: border-box; /* 确保内边距计算正确 */

  .action-btn {
    height: 76rpx; // 减少按钮高度
    line-height: 76rpx;
    text-align: center;
    border-radius: 38rpx; // 调整圆角
    font-weight: 500;
  }
  
  .next-btn {
    background: #30a1a6;
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(48, 161, 166, 0.3);
    width: 100%; // 占满整行
    font-size: 28rpx; // 修改为28rpx，与"新增样本问卷"按钮一致
    
    &:active {
      opacity: 0.9;
    }
  }

  .skip-btn {
    background-color: #f7f7f7;
    color: #666;
    border: 1rpx solid #eee;
    font-size: 28rpx; // 修改为28rpx，与"新增样本问卷"按钮一致
  }
  
  .btn-group { /* 这个是旧的通用btn-group，保留不动 */
    display: flex;
    justify-content: space-between;
    
    .skip-btn, .next-btn {
      width: 48%;
    }
  }

  .btn-group-step2 { /* 调整针对步骤2的按钮组样式 */
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .skip-btn {
      flex: 0 0 58%; /* 增加宽度比例，给更多文字空间 */
      margin-right: 10rpx;
      /* 保留第二步按钮的当前文字大小 */
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }

    .next-btn {
      flex: 0 0 38%; /* 减小宽度比例 */
      margin-left: 10rpx;
      /* 保留第二步按钮的当前文字大小 */
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
  }
  
  &.survey-bottom-bar {
    margin-top: 0; // 移除顶部边距
    padding-bottom: 10rpx; // 调整底部内边距
    box-shadow: 0 -4rpx 12rpx rgba(0,0,0,0.08); // 增强阴影效果
  }
}

// 底部占位符，为底部按钮留出空间
.bottom-bar-placeholder {
  height: 30rpx; // 减少占位符高度
  width: 100%;
}

// 上传区域样式
.upload-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  
  .upload-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 20rpx;
  }
  
  .upload-content {
    margin-top: 20rpx;
  }
  
  .upload-hint {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

// 文件上传内容区域
.file-upload-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10rpx;
}

// 文件项
.file-item {
    position: relative;
  width: 150rpx;
  height: 150rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  
  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .delete-img-btn, .delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    border-bottom-left-radius: 8rpx;
  }
  
  &.pdf-item {
    background-color: #f7f7f7;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
    box-sizing: border-box;
    
    .pdf-icon {
      background-color: #f44336;
      color: #fff;
      font-size: 20rpx;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
    }
    
    .pdf-name {
      font-size: 20rpx;
      color: #333;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
  }
}

// 上传按钮
.file-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;

  &:active {
    background-color: #f0f0f0;
    border-color: #2aafff;
  }

  .upload-icon-plus {
    font-size: 48rpx;
    color: #8c8c8c;
    font-weight: bold;
  }

  .upload-btn-text {
    font-size: 24rpx;
    color: #8c8c8c;
    margin-top: 8rpx;
  }
}

.form-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
  
  .select-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    &.placeholder {
      color: #999;
    }
  }
  
  .select-arrow {
    width: 16rpx;
    height: 16rpx;
    border-right: 2rpx solid #999;
    border-bottom: 2rpx solid #999;
    transform: rotate(45deg);
    margin-left: 10rpx;
  }
}

.nation-icon {
  background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%23cccccc%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%23cccccc%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C/svg%3E');
}

.form-item.focused {
  .input-wrapper {
    border-color: #3ECEB6;
    box-shadow: 0 0 0 2px rgba(62, 206, 182, 0.2);
  }
  .input-icon {
    &.name-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2235%22%20r%3D%2215%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M20%2C85%20C20%2C65%2080%2C65%2080%2C85%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.age-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2235%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpolyline%20points%3D%2250%2C25%2050%2C50%2070%2C60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.gender-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2235%22%20cy%3D%2235%22%20r%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M35%2C55%20V85%20M20%2C70%20H50%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Ccircle%20cx%3D%2265%22%20cy%3D%2265%22%20r%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M80%2C30%20L50%2C60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.height-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M50%2C15%20V85%20M35%2C20%20L50%2C15%20L65%2C20%20M35%2C80%20L50%2C85%20L65%2C80%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M20%2C50%20H80%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-dasharray%3D%2210%2C10%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.weight-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Ccircle%20cx%3D%2250%22%20cy%3D%2250%22%20r%3D%2230%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M50%2C20%20A30%2C30%200%200%2C1%2075%2C35%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M50%2C20%20L50%2C10%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.id-type-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.nation-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M25%2C15%20C20%2C15%2015%2C20%2015%2C25%20V75%20C15%2C80%2020%2C85%2025%2C85%20H75%20C80%2C85%2085%2C80%2085%2C75%20V40%20L60%2C15%20H25%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3Cpolyline%20points%3D%2260%2C15%2060%2C40%2085%2C40%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.id-card-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Crect%20x%3D%2215%22%20y%3D%2225%22%20width%3D%2270%22%20height%3D%2250%22%20rx%3D%225%22%20ry%3D%225%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%2F%3E%3Cpath%20d%3D%22M25%2C55%20H45%20M25%2C65%20H55%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3Crect%20x%3D%2260%22%20y%3D%2235%22%20width%3D%2215%22%20height%3D%2220%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.phone-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M30%2C15%20H70%20C75%2C15%2080%2C20%2080%2C25%20V75%20C80%2C80%2075%2C85%2070%2C85%20H30%20C25%2C85%2020%2C80%2020%2C75%20V25%20C20%2C20%2025%2C15%2030%2C15%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M40%2C70%20H60%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.occupation-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M20%2C30%20H80%20V75%20C80%2C80%2075%2C85%2070%2C85%20H30%20C25%2C85%2020%2C80%2020%2C75%20V30%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M35%2C30%20V20%20C35%2C15%2040%2C10%2045%2C10%20H55%20C60%2C10%2065%2C15%2065%2C20%20V30%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
    &.education-icon {
      background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20100%20100%22%3E%3Cpath%20d%3D%22M10%2C70%20L50%2C40%20L90%2C70%20L50%2C100%20Z%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%2F%3E%3Cpath%20d%3D%22M20%2C65%20V40%20C20%2C20%2050%2C0%2050%2C0%20C50%2C0%2080%2C20%2080%2C40%20V65%22%20fill%3D%22none%22%20stroke%3D%22%233ECEB6%22%20stroke-width%3D%226%22%20stroke-linejoin%3D%22round%22%20stroke-linecap%3D%22round%22%2F%3E%3C%2Fsvg%3E');
    }
  }
}
.form-item.error {
  .input-wrapper {
    border-color: #FA5151;
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16rpx 40rpx; // 增加内边距，与新增样本问卷按钮一致
  padding-bottom: calc(16rpx + constant(safe-area-inset-bottom)); // 适配iPhone X等机型
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 999;
  
  display: flex; /* 添加flex布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  box-sizing: border-box; /* 确保内边距计算正确 */

  .action-btn {
    height: 88rpx; // 增加按钮高度，与新增样本问卷按钮一致
    line-height: 88rpx; // 与高度保持一致
    text-align: center;
    border-radius: 44rpx; // 圆角与高度的一半保持一致
    font-weight: 500;
    font-size: 32rpx; // 增大字体大小，与新增样本问卷按钮一致
    transition: all 0.2s; // 添加过渡效果，使动画更加流畅
    
    &:active {
      opacity: 0.85; // 增强点击效果的反馈
      transform: scale(0.98); // 添加轻微的缩放效果
      transition: all 0.1s; // 添加过渡效果
    }
  }
  
  .next-btn {
    background: #30a1a6;
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(48, 161, 166, 0.3);
    width: 100%; // 占满整行
  }

  .save-btn {
    background: linear-gradient(135deg, #35B8BE, #1F7A82);
    box-shadow: 0 8rpx 24rpx rgba(46, 169, 154, 0.3);
    color: #fff;
    width: 100%;
  }

  .skip-btn {
    background-color: #f7f7f7;
    color: #666;
    border: 2rpx solid rgba(48, 161, 166, 0.3); /* 添加浅绿色边框 */
  }
  
  .btn-group { /* 这个是旧的通用btn-group，保留不动 */
    display: flex;
    justify-content: space-between;
    
    .skip-btn, .next-btn {
      width: 48%;
    }
  }

  .btn-group-step2 { /* 调整针对步骤2的按钮组样式 */
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .skip-btn {
      flex: 0 0 58%; /* 增加宽度比例，给更多文字空间 */
      margin-right: 10rpx;
      /* 保留第二步按钮的当前文字大小 */
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }

    .next-btn {
      flex: 0 0 38%; /* 减小宽度比例 */
      margin-left: 10rpx;
      /* 保留第二步按钮的当前文字大小 */
      white-space: nowrap; /* 防止文字换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
  }
  
  &.survey-bottom-bar {
    box-shadow: 0 -4rpx 12rpx rgba(0,0,0,0.08); // 增强阴影效果
  }
}

// 底部占位符，为底部按钮留出空间
.bottom-bar-placeholder {
  height: 30rpx; // 减少占位符高度
  width: 100%;
}

// 功能按钮卡片样式
.function-card {
  margin: 10rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.function-buttons {
  display: flex;
  justify-content: space-around;
}

.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* 添加相对定位，用于放置状态标志 */
  width: 33.33%;
  padding: 15rpx 0;
}

/* 状态标志样式 */
.status-badge {
  position: absolute;
  top: 0;
  right: 5rpx;
  background-color: #FF5151;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  transform: translateY(-50%);
  z-index: 2;
  white-space: nowrap;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-bottom: 10rpx;
  background-size: 60% 60%;
  background-position: center;
  background-repeat: no-repeat;
}

.function-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

// 知情同意书弹窗样式
.consent-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.consent-modal {
  width: 650rpx;
  max-width: 90%;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.consent-modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.consent-modal-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #30a1a6 0%, #00c6b8 100%);
  border-radius: 3rpx;
}

.consent-modal-list {
  max-height: 600rpx;
  overflow-y: auto;
  padding: 24rpx;
}

.consent-file-item {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.consent-file-item:last-child {
  margin-bottom: 0;
}

.consent-file-item:active {
  background-color: #f0f0f0;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(244, 67, 54, 0.2);
}

.file-info {
  flex: 1;
  position: relative;
}

.file-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  padding-right: 80rpx;
}

.file-meta {
  font-size: 24rpx;
  color: #999;
}

.sign-time {
  display: block;
  line-height: 1.5;
}

.file-status-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(48, 161, 166, 0.1);
  color: #30a1a6;
  font-size: 24rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.file-action {
  color: #30a1a6;
  font-size: 28rpx;
  font-weight: 500;
  padding: 10rpx 20rpx;
  border-left: 1rpx solid #eee;
  margin-left: 20rpx;
}

.consent-file-item.empty {
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.consent-modal-close {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background: linear-gradient(90deg, #30a1a6 0%, #00c6b8 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

// 基础信息弹窗样式
.basicinfo-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.basicinfo-modal {
  width: 650rpx;
  max-width: 90%;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.basicinfo-modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.basicinfo-modal-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #30a1a6 0%, #00c6b8 100%);
  border-radius: 3rpx;
}

.basicinfo-table {
  max-height: 600rpx;
  overflow-y: auto;
  padding: 0;
}

.basicinfo-row {
  display: flex;
  flex-direction: row;
  padding: 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.basicinfo-row:last-child {
  border-bottom: none;
}

.basicinfo-label {
  width: 30%;
  font-size: 28rpx;
  color: #666;
  padding: 24rpx 20rpx;
  background-color: #fafafa;
  display: flex;
  align-items: center;
}

.basicinfo-value {
  width: 70%;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  padding: 24rpx 20rpx;
  word-break: break-all;
}

.basicinfo-modal-close {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background: linear-gradient(90deg, #30a1a6 0%, #00c6b8 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.basic-info-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12z"/><path d="M4 0h16v2H4z"/><path d="M7 10h10v2H7z"/><path d="M7 14h7v2H7z"/></svg>');
}

.survey-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/></svg>');
}

.consent-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M18 2h-8L4.02 8 4 20c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-6 6h-2V4h2v4zm3 0h-2V4h2v4zm3 0h-2V4h2v4z"/></svg>');
}

.question-cards {
  padding: 40rpx; /* 增加内边距 */

  .select-title {
    font-size: 36rpx; /* 增加字体大小 */
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-bottom: 50rpx; /* 增加下边距 */
  }

  .question-card {
    height: 360rpx;
    margin-bottom: 50rpx; /* 增加卡片之间的间距 */
    border-radius: 16rpx;
    padding: 0;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

