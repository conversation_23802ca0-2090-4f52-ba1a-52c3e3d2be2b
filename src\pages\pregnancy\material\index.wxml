<view class="material-container">
	<view class="material-container-top">
		<view class="material-name">{{name}}</view>
		<view class="material-number">PID:{{pid}}</view>
	</view>
	<view class="info-container">
		<view class="info-list">
			<view class="info-title">基本信息</view>
			<view class="info-tips">请核对一下基本信息是否正确</view>
		</view>
		<view class="info-list">
			<view class="info-lable">男方姓名</view>
			<view class="info-value">{{personData.manName || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">年龄</view>
			<view class="info-value">{{personData.manAge || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">身份证号</view>
			<view class="info-value">{{tuoMingmanIdNo || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">女方姓名</view>
			<view class="info-value">{{personData.womanName || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">年龄</view>
			<view class="info-value">{{personData.womanAge || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">身份证号</view>
			<view class="info-value">{{tuoMingWonmentIdNo || ""}}</view>
		</view>
		<view class="info-list">
			<view class="info-lable">联系地址</view>
			<view class="info-value">{{personData.address || ""}}</view>
		</view>
		<!-- <view class="info-list">
      <view class="info-lable">联系电话（男方）</view>
      <view class="info-value">{{personData.manMobile || ""}}</view>
    </view>
    <view class="info-list">
      <view class="info-lable">联系电话（女方）</view>
      <view class="info-value">{{personData.womanMobile || ""}}</view>
    </view> -->
	</view>
	<view class="info-photo">
		<view class="photo-title">资料上传</view>
		<view class="photo-des">
			<span class="xh">*</span>
			请上传女方身份证正反面照片
		</view>
		<view class="m-upload-list">
			<block wx:for="{{imageObj[1]}}" wx:key="index">
				<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'idCard')">
					<!-- <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/close.png" class="delete" @tap="delete({{item}})"/> -->
					<image
					 class="m-upload-image"
					 src="{{item}}"
					 @tap="previewImage({{item}}, 1)"
					/>
					<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" @tap="delete({{item}},1)" />
				</view>
			</block>
			<!-- <view wx:if="{{showIdCardImgAdd}}" class="m-upload-item m-upload-icon" @tap="chooseImage(1)">+</view> -->
			<view class="m-upload-item m-upload-icon" @tap="chooseImage(1)">+</view>
		</view>
		<view class="photo-des">
			<span class="xh">*</span>
			请上传结婚证照片
		</view>
		<view class="m-upload-list">
			<block wx:for="{{imageObj[2]}}" wx:key="index">
				<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'marry')">
					<image
					 class="m-upload-image"
					 src="{{item}}"
					 @tap="previewImage({{item}}, 2)"
					/>
					<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" @tap="delete({{item}},2)" />
				</view>
			</block>
			<!-- <view wx:if="{{showMarryImgAdd}}" class="m-upload-item m-upload-icon" @tap="chooseImage(2)">+</view> -->
			<view class="m-upload-item m-upload-icon" @tap="chooseImage(2)">+</view>
		</view>
		<view class="photo-des">
			<span class="xh">*</span>
			{{otherIntroduce || '其他资料'}}</view>
		<view class="m-upload-list">
			<block wx:for="{{imageObj[3]}}" wx:key="index">
				<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'other')">
					<image
					 class="m-upload-image"
					 src="{{item}}"
					 @tap="previewImage({{item}}, 3)"
					/>
					<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" @tap="delete({{item}},3)" />
				</view>
			</block>
			<view class="m-upload-item m-upload-icon" @tap="chooseImage(3)">+</view>
		</view>

		<block wx:if="{{ouContent}}">
			<view class="ouContent">
				<span class="xh">*</span>
				<rich-text nodes="{{ouContent}}" />
			</view>
			<textarea
			 class="ouContent-textarea"
			 value="{{ouContentInfo}}"
			 placeholder="请输入您的回答"
			 name="textarea"
			 maxlength="500"
			 @input="onOuContentInfoInputValue"
			/>
			</block>
	</view>
	<button class="button" @tap="confirm">确定</button>
</view>

