@import "../../../../resources/style/mixins.less";

@fixed-height: 112rpx;

page {
    background-color: #fff;
    padding-bottom: @fixed-height;
    .g-head {
        background-color: #fff;
        .m-video-content {
            position: relative;
            width: 100%;
            height: 420rpx;
            color: #fff;
            .video-play {
                width: 100%;
                height: 100%;
            }
            .u-tt {
                position: absolute;
                top: 44rpx;
                left: 29rpx;
                max-width: 690rpx;
                font-size: 42rpx;
                line-height: 59rpx;
                overflow: hidden;
            }
            .u-plays {
                position: absolute;
                bottom: 37rpx;
                left: 33rpx;
                font-size: 24rpx;
                line-height: 33rpx;
                overflow: hidden;
            }
        }
        .m-video-operation {
            display: -webkit-flex;
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            height: 40rpx;
            padding: 30rpx 0;
            color: #989898;
            align-items: flex-end;
            .u-item {
                display: flex;
                flex-direction: row;
                width: 33.3%;
                justify-content: center;
                &.btn-share {
                    .clearButton();
                    margin: 0;
                    padding: 0;
                    width: 33.3%;
                }
                .img-like, .img-share {
                    width: 36rpx;
                    height: 36rpx;
                }
                .img-comments {
                    width: 38rpx;
                    height: 38rpx;
                }
                .u-num {
                    margin-left: 10rpx;
                    font-size: 28rpx;
                    color: #989898;
                    &.active {
                        color: #3ECEB6;
                    }
                }
            }
        }
    }
    .comm-doctor-info {
        background: #fff;
    }
    .g-wrap-gray {
        width: 100%;
        height: 18rpx;
        background-color: #EEF1F3;
    }
    .g-body {
        background-color: #fff;
        padding: 29rpx 53rpx;
        .comments-num {
            color: #373737;
            font-size: 26rpx;
            margin-bottom: 23rpx;
        }
        .item {
            margin-bottom: 20rpx;
            font-size: 30rpx;
            .comments-name {
                color: #2D2D2D;
            }
            .reply-name {
                color: #264D68;
            }
        }
        .loadMore {
            color: #3ECEB6;
            margin-top: 24rpx;
        }
    }
    .g-foot-box {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        .g-foot {
            width: 100%;
            height: @fixed-height;
            padding: 16rpx 20rpx;
            box-sizing: border-box;
            background-color: #F3F3F3;
            display: -webkit-flex;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .u-input {
                width: 564rpx;
                height: 80rpx;
                padding-left: 30rpx;
                background: #fff;
                .input-placeholder {
                    color: #B2B2B2;
                }
            }
            .u-btn {
                .clearButton();
                width: 96rpx;
                height: 60rpx;
                margin: 0;
                line-height: 60rpx;
                font-size: 28rpx;
                border-radius: 6rpx;
                background-color: #3ECEB6;
                color: #fff;
                text-align: center;
                padding-left: 0;
                padding-right: 0;
            }
        }
    }
}