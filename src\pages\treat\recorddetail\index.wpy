<style lang="less" src="./index.less"></style>

<template lang="wxml" src="./index.wxml">

</template>

<script>
import wepy from "wepy";
import {
  TYPE_MAP,
  CODE_TYPE
} from "@/config/constant";
import DetailStatus from "@/components/detailstatus/index";
import RefundList from "@/components/refundlist/index";
import * as Utils from "@/utils/utils";
import BasicDetail from "./com/basicDetail";
import PayDetail from "./com/payDetail";
import MedDetail from "./com/medDetail";
import GuideDetail from "./com/guideDetail";
import * as Api from "./api";
const NORMAL_MAP = ["S", "F"];
export default class PageWidget extends wepy.page {
  async getPayDetail(param) {
    try {
      const { code, data } = await Api.orderListPay(param);
      if (code === 0 && data.resultList) {
        // 根据传递的 orderId 查找对应记录
        const targetOrder = data.resultList.find(item =>
          item.costHeadID.toString() === param.orderId
        );
        if (!targetOrder) {
          console.error('未找到对应订单');
          return;
        }
        this.dataFrom = 'success';
        this.detailData = this.formatPayData(targetOrder);
        this.statusConfig = this.getStatus();
      }
    } finally {
      this.$apply();
    }
  }
  async orderDetail(item = {}) {

    this.dataFrom = 'normal';
  }
  formatPayData(item) {
    let totalAmtSum = 0; // 总金额（元）
    let promFeeSum = 0; // 优惠金额（元）
    // 遍历items计算总和
    if (item.items && item.items.length > 0) {
      item.items.forEach(i => {
        totalAmtSum += Number(i.totalAmt) || 0;
        promFeeSum += Number(i.promFee) || 0;
      });
    }

    return {
      patCardNo: item.cardNo,
      orderId: item.orderId,
      status: 'S',
      payStatues: 'S', // 支付状态
      totalFees: totalAmtSum * 100, // 总金额
      totalPromFee: promFeeSum * 100 || 0,// 优惠总金额
      totalRealFees: (totalAmtSum - promFeeSum) * 100, // 实付金额

      statusName: '缴费成功',
      patientName: item.patName, //用户姓名
      //totalRealFee: item.totalFee * 100, // 保持分单位、总金额
      deptName: item.deptName, // 就诊科室
      doctorName: item.doctorName, // 医生姓名
      cardNo: item.cardNo, // 就诊卡号
      costHeadID: item.costHeadID, // 医院单号
      costDate: item.costDate,
      itemList: (item.items && item.items.map(i => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemType: i.itemType,
        totalAmt: i.totalAmt * 100, //总金额
        itemPrice: i.price * 100,
        totalFee: i.totalAmt * 100, //优惠金额
        spec: i.spec, // 规格信息
        showAmount: i.showAmount // 展示数量
      }))) || [] // 确保默认空数组
      
    };
 
  }
  config = {
    navigationBarTitleText: "门诊缴费详情"
  };
  data = {
    // 订单详情
    detailData: {},
    // 导诊信息
    guideInfo: [],
    // 导诊信息是否展开
    guideIsExpand: true,
    // 顶部状态配置
    statusConfig: {},
    // 退款列表
    refundList: [],
    // 订单状态是否异常，用来确定是否需要重发
    isAbnormal: false,
    // 缴费信息是否展开,默认S状态收起
    payIsExpand: true,
    // 条形码格式
    codeType: "",
    // 处方单是否展开
    medIsExpand: true,
    dataFrom: 'normal'
  };
  components = {
    "detail-status": DetailStatus,
    "refund-list": RefundList,
    "basic-detail": BasicDetail,
    "pay-detail": PayDetail,
    "med-detail": MedDetail,
    "guide-detail": GuideDetail
  };
  onLoad(options) {
    this.codeType = CODE_TYPE;

    if (options.orderId && !options.cardNo) {
      this.orderDetail(options);
      this.getGuideinfo(options);
      return;
    }

    if (options.cardNo) {
      const param = {
        cardNo: decodeURIComponent(options.cardNo),
        patName: decodeURIComponent(options.patName),
        parterId: options.parterId,
        passwd: decodeURIComponent(options.passwd),
        orderId: options.orderId
      };
      this.getPayDetail(param);
      return;
    }
  }
  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };
  methods = {
    /**
     * 重发订单状态查询
     */
    bindRetryOrder() {
      this.retryOrder();
    }
  };
  async getGuideinfo(item = {}) {
    const {
      orderId = ""
    } = item;
    const {
      code,
      data = {}
    } = await Api.guideinfo({
      orderId
    });
    if (code != 0) {
      return;
    }
    const {
      items = []
    } = data;
    this.guideInfo = items;
    this.$apply();
  }
  async orderDetail(item = {}) {
    const {
      orderId = ""
    } = item;
    const {
      code,
      data = {},
      msg
    } = await Api.orderDetail({
      orderId
    });
    if (code !== 0) {
      return;
    }
    const {
      status = "", itemList = []
    } = data;
    itemList.forEach(item => {
      item.itemPrice = Number(item.itemPrice) * 100;
      item.totalFee = Number(item.totalFee) * 100;
    })
    this.detailData = {
      ...data,
      // 字段重映射
      patientName: data.patientName || data.patName,
      deptName: data.deptName || data.department,
      doctorName: data.doctorName || data.docName,
    };

    this.dataFrom = 'normal';
    this.refundList = data.refundList || [];
    this.statusConfig = this.getStatus() || {};

    if (NORMAL_MAP.indexOf(status) === -1) {
      this.isAbnormal = true;
    }
    if (status === "S") {
      this.payIsExpand = false;
    } else {
      this.payIsExpand = true;
    }
    this.$apply();
  }
  /**
   * 获取订单描述文案
   */
  getStatus() {
    const {
      status,
      refundStatus
    } = this.detailData;
    let stsObj = {};
    // 需要转成map
    if (status == "S") {
      stsObj = {
        statusName: "门诊缴费成功",
        text: "请凭手机缴费页面和处方单，及时到对应科室执行。如需打印发票，请到收费窗口咨询。"
      };
    } else if (status == "F") {
      stsObj = {
        statusName: "门诊缴费失败",
        text: "门诊缴费失败，您的退款申请已受理，退款金额将于1-7个工作日自动原路返回到您的支付账户。"
      };
    } else if (status == "P") {
      stsObj = {
        statusName: "付款完成，调用医院支付接口中",
        text: ""
      };
    } else if (status == "H" || status == "Z") {
      stsObj = {
        statusName: "门诊缴费异常",
        text: `非常抱歉，給您带来不便。请您前往挂号缴费窗口，工作人员将为您核实缴费情况；或者您也可以或致电我们客服为您办理 （400-811-9893）。`
      };
    } else if (status == undefined) {
      stsObj = {
        statusName: "",
        text: ""
      };
    } else {
      stsObj = {
        statusName: "门诊缴费异常",
        text: `非常抱歉，給您带来不便。请您前往挂号缴费窗口，工作人员将为您核实缴费情况；或者您也可以或致电我们客服为您办理 （400-811-9893）。`
      };
    }
    return {
      ...stsObj,
      status,
      hasRefund: refundStatus == 1 || refundStatus == 2
    };
  }
  /**
   * 重发订单状态查询
   */
  async retryOrder() {
    const {
      orderId = ""
    } = this.detailData;
    const type = "MZJF";
    const {
      code
    } = await Api.manualNotify({
      orderId,
      bizType: TYPE_MAP[type] || "default"
    });
    if (code !== 0) {
      return;
    }
    wepy.redirectTo({
      url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&time=15&from=detail`
    });
  }
}
</script>
