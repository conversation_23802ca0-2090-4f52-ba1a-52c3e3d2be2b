@import "../../../../resources/style/mixins";

.p-site-deptdst {
	min-height: 100vh;
	.m-list {
		position: relative;
    margin-bottom: 24rpx;
		background-color: #fff;
		padding: 24rpx 0 24rpx 24rpx;
		overflow: hidden;
		.list-box {
			display: flex;
			overflow-x: scroll;
			overflow-y: hidden;
		}
		.list {
			flex: 1;
			display: flex;
			margin-left: -20rpx;
			margin-bottom: -20rpx;
			padding: 24rpx;
			box-sizing: border-box;
		}
		.list-item {
			white-space: nowrap;
			padding: 8rpx 24rpx;
			line-height: 42rpx;
			margin-left: 16rpx;
			text-align: center;
			color: #3F969D;
			font-size: 28rpx;
			border-radius: 4rpx;
			border: 2rpx solid rgba(0, 0, 0, 0.10);
			
			&.active {
				color: #fff;
				background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
			}
		}
		.list-opt {
			display: block;
			position: absolute;
			right: 0;
			top: 0;
			bottom: 0;
			width: 88rpx;
			border: 2rpx solid @hc-color-border;
			box-shadow: -2rpx 0 1rpx 0 rgba(0, 0, 0, 0.06);
			background-color: rgba(255, 255, 255, 1);
      z-index: 2;
      
      .icon-img{
        width: 36rpx;
        height: 36rpx;
        position: absolute;
        left: 50%;
        top: 42rpx;
        margin-left: -18rpx;
        transform: rotate(180deg);
      }

			&.active {
        .icon-img{
          transform: rotate(0);
        }
			}
		}
		
		&.active {
			.list-box {
				overflow: hidden;
			}
			.list {
				display: flex;
				flex-wrap: wrap;
			}
		}
	}
	.m-detail {
    margin: 24rpx;
    .m-nofloor{
      text-align: center;
      font-size: 28rpx;
    }
    .detail-item{
      padding: 32rpx;
      margin: 16rpx 0;
      background-color: #fff;
      border-radius: 8rpx;
      box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
    }

		.item-num {
      position: relative;
      display: flex;
      align-items: center;
      padding-bottom: 24rpx;
      margin-bottom: 24rpx;
			font-size: 32rpx;
			color: @hc-color-title;
      font-weight: bold;
      border-bottom: 2rpx solid rgba(0, 0, 0, 0.10);
      .shu{
        margin-right: 8rpx;
        width: 8rpx;
        height: 24rpx;
        border-radius: 13rpx;
        background: #3F969D;
      }
		}
		.item-text {
			flex: 1;
			font-size: 32rpx;
			color: rgba(0, 0, 0, 0.7);
			margin-right: 0 24rpx 24rpx;
      position: relative;
      &:last-child{
        margin-right: 0;
      }
		}
	}
}
