<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy"
import { validator, jsonToQueryString } from "@/utils/utils"

import * as Api from "./api"
export default class PrijectList extends wepy.page {
  config = {
    navigationBarTitleText: "病历复印",
    navigationBarBackgroundColor: '#fff',
  }
  data = {
    medicalCopyTypeList: {
      1: "住院病历",
      2: "试管病历",
      3: "人工授精病历"
    },
    medicalCopyType: "",
    infoList: [],
    wayList: [
      "医疗报销",
      "产检",
      "外院中医就诊",
      "外院专科就诊",
      "外院生殖科就诊"
    ],
    copyZYContentArray: [
      { value: "出院记录", isCheck: false },
      { value: "检验报告单", isCheck: false },
      { value: "检查报告单", isCheck: false },
      { value: "手术报告单", isCheck: false },
      { value: "体温单", isCheck: false }
    ],
    copyOthContentArray: [
      { value: "检验报告单", isCheck: false },
      { value: "检查报告单", isCheck: false },
      { value: "手术报告单", isCheck: false },
      { value: "其他", isCheck: false }
    ],
    copyPurpose: "",
    copyNum: 1,
    remark: "",
    radioArray: [
      { label: "快递到家", value: 1 },
      { label: "来院自取", value: 2 }
    ],

    sexRadioArray: [
      { value: "男方", isCheck: false },
      { value: "女方", isCheck: false }
    ],
    receiveType: 1,
    copyObject: "",
    copyContent: "",
    addresseeInfo: {
      userName: "",
      mobile: "",
      provinceName: "",
      cityName: "",
      areaName: "",
      addressDetail: ""
    },
    selectInfoIdx: 0,
    userInfo: []
  }
  onLoad(options) {
    this.medicalCopyType = options.medicalCopyType || "1"
    this.copyPurpose = options.copyPurpose || ""
    this.remark = options.remark || ""
    this.receiveType = options.receiveType || 1
    this.routeParams = options
    try {
      if (options.addresseeInfo) {
        this.addresseeInfo = JSON.parse(options.addresseeInfo)
      }
      if (options.copyContent) {
        const copyContent = options.copyContent.split(";")
        this.copyContent = options.copyContent
        const id =
          options.medicalCopyType == 1
            ? "copyZYContentArray"
            : "copyOthContentArray"
        this[id].map((item) => {
          item.isCheck = copyContent.includes(item.value)
        })
      }

      if (options.copyObject) {
        const copyObject = options.copyObject.split(";")
        this.copyObject = options.copyObject
        this.sexRadioArray.map((item) => {
          item.isCheck = copyObject.includes(item.value)
        })
      }
      this.$apply()
    } catch (error) {}

    const userInfo = wepy.getStorageSync("selectInfo") || []
    if (userInfo.length) {
      wepy.removeStorageSync("selectInfo")
      this.userInfo = userInfo
    } else {
      this.queryBlfycx(options)
    }
    this.$apply()
  }
  onShow() {}

  async queryBlfycx(item) {
    const { pid = "", medicalCopyType = "" } = item
    const { code, data } = await Api.queryBlfycx({
      pid,
      queryType: this.medicalCopyTypeList[medicalCopyType]
    })
    if (code === 0) {
      this.userInfo = data
      this.$apply()
    }
  }

  methods = {
    bindPickerChangeVal(e) {
      this.selectInfoIdx = e.detail.value
    },
    bindPickerChange(e) {
      this.copyPurpose = this.wayList[e.detail.value]
    },
    add() {
      this.copyNum = this.copyNum + 1
    },
    reduce() {
      if (this.copyNum === 1) return
      this.copyNum = this.copyNum - 1
    },
    inputTriggerOtherContent(e) {
      const { value } = e.detail
      this.remark = value
      this.$apply()
    },

    userInfoRadioChange(e) {
      this.selectInfoIdx = Number(e.detail.value)
      this.$apply()
    },
    postRadioChange(id, e) {
      let newVal
      if (id === "receiveType") {
        newVal = Number(e.detail.value)
      } else {
        newVal = e.detail.value
      }
      this[id] = newVal
      this.$apply()
    },

    checkValue(e) {
      const { id = "" } = e.currentTarget.dataset || {}
      const copyContent = e.detail.value
      this[id] = copyContent.join(";")
      this.$apply()
      console.log(`----${id}----`, this[id])
    },

    onAddressInputValue(e) {
      const { key } = e.target.dataset || {}
      this.addresseeInfo = {
        ...this.addresseeInfo,
        [key]: e.detail.value
      }
      this.$apply()
    },

    bindRegionChange(e) {
      const regionArray = e.detail.value
      this.addresseeInfo = {
        ...this.addresseeInfo,
        provinceName: regionArray[0],
        cityName: regionArray[1],
        areaName: regionArray[2]
      }
    },
    submit() {
      const {
        medicalCopyType,
        copyPurpose,
        copyContent,
        copyObject,
        copyNum,
        remark,
        receiveType,
        addresseeInfo,
        routeParams,
        userInfo,
        selectInfoIdx
      } = this
      const {
        jzid = "",
        ryks = "",
        ryrq = "",
        zyh = "",
        zljd = "",
        zqxx = ""
      } = userInfo[selectInfoIdx]
      let errorMsg = ""
      if (!copyPurpose) {
        errorMsg = "请选择复印用途"
      } else if (!copyContent) {
        errorMsg = "请选择复印内容"
      } else if (!copyObject) {
        errorMsg = "请选择复印对象"
      } else if (
        receiveType == 1 &&
        Object.values(addresseeInfo).some((v) => v === "")
      ) {
        errorMsg = "请完善您的邮寄信息"
      } else if (
        receiveType == 1 &&
        addresseeInfo.mobile &&
        !validator.mobile(addresseeInfo.mobile).ret
      ) {
        errorMsg = "手机号码格式有误"
      }
      if (errorMsg) {
        wx.showModal({
          title: "提示",
          content: errorMsg,
          showCancel: false,
          confirmText: "确定",
          confirmColor: "#3CC51F"
        })
        return
      }
      let params = {
        ...routeParams,
        medicalCopyType,
        copyPurpose,
        copyObject,
        copyContent,
        copyNum,
        remark,
        receiveType
      }

      if (medicalCopyType == 1) {
        params = {
          ...params,
          jzid,
          deptName: ryks,
          inHospitalDate: ryrq,
          zyh
        }
      } else {
        params = {
          ...params,
          jzid,
          doctorName: zljd,
          cyclesNum: zqxx
        }
      }

      if (receiveType == 1) {
        params = {
          ...params,
          addresseeInfo: JSON.stringify(addresseeInfo)
        }
      } else {
        delete params.addresseeInfo
      }

      const query = decodeURIComponent(jsonToQueryString(params))
      console.log("----params----", params)
      // return
      wepy.navigateTo({
        url: `/package2/pages/medical/agreement/index?${query}`
      })
    }
  }
}
</script>