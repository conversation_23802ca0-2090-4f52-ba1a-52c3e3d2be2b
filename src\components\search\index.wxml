<view class="g-search">
  <view class="m-serach-seat" wx:if="{{searchFocus}}"></view>
  <view class="m-search {{searchFocus ? 'active' : ''}}">
    <view class="search-ipt">
      <view class="ipt-icon"></view>
      <input
        class="ipt"
        placeholder="搜索医生、科室"
        @focus="bindSearchFocus"
        @input="bindSearchInput"
        value="{{searchValue}}"
      />
      <!-- <view wx:if="{{searchFocus}}" class="ipt-icon-cancel" @tap="bindSearchBlur">
        <icon type="cancel" size="14" color="#888888"></icon>
      </view> -->
    </view>
    <slot name="extra"></slot>
    <view class="extra-cancel" wx:if="{{searchFocus}}" @tap="bindSearchBlur">取消</view>
  </view>

  <view class="m-search-content" wx:if="{{searchFocus}}">
    <block wx:if="{{deptList.length == 0 && doctorList.length == 0}}">
      <empty>
        <block slot="text">暂未查询到相关信息</block>
      </empty>
    </block>
    <block wx:else>
      <view class="search-content">
        <block wx:if="{{doctorList.length > 0}}">
          <view class="content">
            <view class="content-title">医生结果</view>
            <view class="content-list">
              <view
                class="list-item"
                wx:for="{{doctorList || []}}"
                wx:for-index="index"
                wx:for-item="item"
                wx:key="index"
                wx:if="{{index < 3}}"
                @tap="bindToDocInfo({{item}})"
              >
                <view class="item-image">
                  <image
                    mode="widthFix"
                    src="{{item.doctorImg || ('REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}"
                  ></image>
                </view>
                <view class="item-info">
                  <view class="info-title">
                    <text class="title">{{item.doctorName}}</text>
                    <!-- <text class="info-status no-source" wx:if="{{item.doctorRemark === '已满诊,可预约'}}">暂无号源</text>
                    <text class="info-status has-source" wx:else>有号源</text> -->
                  </view>
                  <view class="info-text">{{item.deptName}} | {{item.doctorTitle}}</view>
                  <!-- <view class="info-address">
                    <text>看诊地点：</text>
                    <text class="info-address-base1">麓谷总院</text>
                    <text class="info-address-base2">开福分院</text>
                  </view> -->
                  <view class="info-des">{{item.doctorSkill || ''}}</view>
                </view>
              </view>
              <view
                wx:if="{{doctorList.length > 3}}"
                class="list-item"
                @tap="bindMore"
              >
                <view class="item-info">
                  <view class="info-more">查看更多</view>
                </view>
                <!-- <view class="item-arrow"></view> -->
              </view>
            </view>
          </view>
        </block>
        <block wx:if="{{deptList.length > 0}}">
          <view class="content">
            <view class="content-title">科室结果</view>
            <view class="content-list">
              <view
                class="list-item"
                wx:for="{{deptList || []}}"
                wx:for-index="index"
                wx:for-item="item"
                wx:key="index"
                wx:if="{{index < 3}}"
                @tap="bindToDocList({{item}})"
              >
                <!-- <view class="item-icon">
                  <image
                    mode="widthFix"
                    src="{{item.doctorImg || ('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/dept.png')}}"
                  ></image>
                </view> -->
                <view class="item-info flex-between">
                  <view class="info-deptinfo">{{item.deptName}}</view>
                  <view class="item-arrow"></view>
                </view>
              </view>

              <view
                wx:if="{{deptList.length > 3}}" class="list-item"
                @tap="bindMore"
              >
                <view class="item-info">
                  <view class="info-more">查看更多</view>
                </view>
                <!-- <view class="item-arrow"></view> -->
              </view>
            </view>
          </view>
        </block>
      </view>
    </block>
  </view>
</view>