<form bindsubmit="formSubmit" report-submit='true'>
  <view class="bindcard-list">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title require">姓名</text>
      </view>
      <view class="listitem-body">
        <input class="m-content" placeholder="请输入姓名" id="userName" @input="inputTrigger" @focus="resetThisError" value="{{options.userName}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title require">手机号码</text>
      </view>
      <view class="listitem-body">
        <input class="m-content" placeholder="请输入手机号码" id="mobile" @input="inputTrigger" @focus="resetThisError" value="{{options.mobile}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head" >
        <view class="list-title require">
          所在地区
        </view>
      </view>
      <picker class="listitem-body" mode="region" bindchange="changeCity">
        <input class="m-content" placeholder="请选择所在地区" disabled value="{{address}}" />
      </picker>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title require">详细地址</text>
      </view>
      <view class="listitem-body">
        <input class="m-content" placeholder="请输入详细地址" id="addressDetail" @input="inputTrigger" @focus="resetThisError" value="{{options.addressDetail}}"/>
      </view>
    </view>
  </view>
    <view class="afterscan-operbtnbox">
      <button class="binduser-btn_line" formType="submit">保存</button>
    </view>
</form>