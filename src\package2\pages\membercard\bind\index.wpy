<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { validator } from "@/utils/utils";
import Outpatient from "@/components/outpatient/index";
import TopTip from "@/components/toptip/index";
import { CURSOR_SPACING } from "@/config/constant";
import * as Api from "../api";

export default class BindCard extends wepy.page {
  config = {
    navigationBarTitleText: "绑定会员卡",
    navigationBarBackgroundColor: "#fff",
  };

  components = {
    outpatient: Outpatient,
    toptip: TopTip,
  };

  onShow() {
    this.$broadcast("outpatient-get-patient");
  }

  data = {
    CURSOR_SPACING,
    patientConfig: {
      infoShow: false,
      show: false,
      initUser: {},
    },
    currentPatient: {},

    placeholderColor: "",
    errorColor: "red",

    errorElement: {}, // 发生错误的元素
    hasErr: true, // 是否存在校验错误
    toptip: "",
    vipCardNo: "",
    vipPassWord: "",
  };

  validator(id) {
    let validate = {
      vipCardNo: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{1,7}$/,
        errTip: "请输入卡号",
      },
      vipPassWord: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{1,6}$/,
        errTip: "请输入密码",
      },
    };
    const value = this.getFormData();

    let hasErr = false;
    for (let o in value) {
      const obj = validate[o];
      if (obj && obj.regexp) {
        let thisErr = false;
        if (typeof obj.regexp === "function") {
          const retObj = obj.regexp(value);
          if (!retObj.ret) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        } else {
          if (
            typeof obj.regexp.test === "function" &&
            !obj.regexp.test(value[o])
          ) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        }
        if (
          (!id && hasErr) ||
          (obj.errTarget && obj.errTarget == id && thisErr)
        ) {
          // 提交时弹框提示
          this.errorElement[obj.errTarget || o] = true;
          this.toptip = obj.errTip || "";
          const errTimer = setTimeout(() => {
            this.toptip = "";
            this.$apply();
            clearTimeout(errTimer);
          }, 2000);
          break;
        }
      }
    }

    return hasErr;
  }

  getFormData() {
    const { vipCardNo, vipPassWord } = this;
    return {
      vipCardNo,
      vipPassWord,
    };
  }

  async bindcard() {
    const value = this.getFormData();
    const showModalRes = await wepy.showModal({
      title: "绑卡提示",
      content:
        "是否确认将会员卡【" +
        value.vipCardNo +
        "】绑定到【" +
        this.currentPatient.patientName +
        "】？",
      cancelText: "取消",
      confirmText: "确定",
      confirmColor: "#2F848B",
    });
    if (showModalRes.cancel) {
      return false;
    }

    wepy.showLoading();

    const param = {
      vipCardNo: value.vipCardNo,
      vipPassWord: value.vipPassWord,
      patId: this.currentPatient.patInNo,
    };
    const { code, data } = await Api.vipActive(param);
    wepy.hideLoading();
    if (code == 0 && data.resultCode == 0) {
      wx.showModal({
        title: "温馨提示",
        content: "绑定成功",
        showCancel: false,
      }).then(() => {
        this.$apply();
        wepy.navigateBack({
          delta: 1,
          fail: (res) => {
            wepy.redirectTo({
              url: `/package2/pages/membercard/index`,
            });
          },
        });
      });
    } else {
      await wepy.showModal({
        title: "提示",
        content: data.resultMessage || "绑定失败，请尝试重试",
        showCancel: false,
      });
    }
    this.$apply();
  }

  methods = {
    bind() {
      this.hasErr = this.validator();
      if (this.hasErr) {
        return false;
      }
      this.bindcard();
    },
    formSubmit(e) {},
    inputTrigger(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      this[id] = value;
    },
    resetThisError(e) {
      const { id } = e.currentTarget;
      this.errorElement[id] = false;
    },
    goBack() {
      wepy.navigateBack({
        delta: 1,
      });
      wepy.navigateBack({
        delta: 1,
        fail: (res) => {
          wepy.reLaunch({
            url: "/pages/home/<USER>",
          });
        },
      });
    },
  };

  events = {
    "outpatient-change-user": function (item = {}) {
      if (item) {
        this.patientConfig.infoShow = true;
        this.currentPatient = item;
      }
    },
  };
}
</script>
