@import "../../../resources/style/mixins";

page {
  background-color: @hc-color-bg;
  min-height: 100vh;
}

.dyna-tab {
  min-width: 100%;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 16rpx;

  .dyna-tabitem {
    flex-basis: 50%;

    .dyna-tabitem-content {
      height: 206rpx;
      font-weight: 600;
      font-size: 28rpx;
      justify-content: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #ffffff;
      border-radius: 24rpx;
      color: @hc-color-text;
      margin: 12rpx;
      text-align: center;
      .icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
        margin-bottom: 12rpx;
      }
    }
  }
}
