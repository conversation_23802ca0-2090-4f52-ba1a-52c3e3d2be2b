@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.doctorRemark {
  // font-size: 30rpx !important;
  font-weight: bold;
  color: @hc-color-title !important;
}
.m-info {
  position: relative;
  padding: 40rpx 24rpx 32rpx;
  background: url(REPLACE_IMG_DOMAIN/his-miniapp/images/top-bg.png) no-repeat;
  background-size: cover;
  background-position: center;
  .info-box {
    position: relative;
    z-index: 1;
  }
  .info {
    position: relative;
    display: flex;
    align-items: center;
  }
  .info-bg {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    image {
      width: 100%;
      vertical-align: top;
    }
  }
  .info-hd {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 124rpx;
    height: 124rpx;
    overflow: hidden;
    border-radius: 50%;
    flex: 0 0 1;

    image {
      height: 100%;
      vertical-align: top;
    }
  }
  .info-bd {
    margin-left: 24rpx;
    flex: 1;
    overflow: hidden;
    color: #fff;
  }
  .bd-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .tit-lt {
    width: 380rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 48rpx;
    font-weight: 600;
  }
  .tit-rt {
    display: flex;
  }
  .rt-icon {
    font-size: 56rpx;
    line-height: 1;
    margin-left: 30rpx;

    &.active {
      color: @hc-color-warn;
    }
  }
  .bd-txt {
    font-size: 30rpx;
    margin-top: 8rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 28rpx;
    font-weight: 500;
  }
  .info-des {
    font-size: 26rpx;
    margin-top: 24rpx;
    color: rgba(255, 255, 255, 0.70);
    font-family: PingFang SC;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .abs-icon-box {
    display: flex;
  }
  .abs-icon {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 48rpx;
    height: 48rpx;
    margin-left: 32rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.20);

    image {
      width: 32rpx;
      height: 32rpx;
    }
    .share-btn {
      position: absolute;
      left: 0;
      top: 0;
      width: 48rpx;
      height: 48rpx;
      z-index: 1;
      opacity: 0;
    }
  }
}

.m-tab {

  .tab-li {
    &:after {
      width: 1.5em;
    }
  }
}

.date-container {
  margin: 24rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: auto;
}
.m-date-info {
  display: flex;
  background-color: #fff;
  justify-content: space-between;
  line-height: 88rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  color: @hc-color-text;
  align-items: center;
  border-radius: 24rpx 24rpx 0 0;

  .info-title{
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
    font-weight: 600;
  }

  .m-date-rt{
    display: flex;
  }

  .info-lt {
    flex: 1;
    font-weight: 600;
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.70);
  }
  .info-rt {
    display: none;
    line-height: 1;
    .rt-text {
      color: rgba(0, 0, 0, 0.4);
      font-size: 24rpx;
    }
    .arrow-icon{
      margin-left: 16rpx;
      width: 24rpx;
      height: 24rpx;
      &.top {
        transform: translateY(4rpx) rotate(-90deg);
      }
    }
    .rt-arrow {
      margin-left: 20rpx;
      width: 15rpx;
      height: 15rpx;
      border-right: 3rpx solid #c2c9c9;
      border-bottom: 3rpx solid #c2c9c9;
      &.bottom {
        transform: translateY(-4rpx) rotate(45deg);
      }
     
    }
    &.active {
      display: flex;
      align-items: center;
    }
  }
}

//日期展开模式
.m-date {
  display: none;
  background-color: #fff;
  // border-top: 2rpx solid @hc-color-border;

  .m-date-box{
      display: flex;
      flex-wrap: wrap; // 确保换行
      width: 100%;
      background: rgba(240, 247, 248, 0.38);
      border-top: 1rpx solid rgba(0, 0, 0, 0.07);
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
    .date-box-list{
      flex: 0 0 14.28%; // 7项/行
      max-width: 14.28%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16rpx 8rpx; // 减小横向间距
      border-right: 1rpx solid rgba(0, 0, 0, 0.07);
      &.last-child{
        border: none;
      }
        &:nth-child(7n) 
        {
        border-right: none;
      }

      .date-item{
        padding: 0;
        //margin: 0;
        margin-bottom: 4rpx;
        font-size: 22rpx;
        color: rgba(0, 0, 0, 0.40);
      }
      .week-item{
        color: rgba(0, 0, 0, 0.90);
        font-size: 26rpx;
        font-weight: 600;
      }
      .source-item{
        margin-top: 8rpx;
        color: rgba(0, 0, 0, 0.40);
        font-size: 20rpx;
        &.on{
          color: #2D666F;
        }
      }
      &.active{
        background: linear-gradient(180deg, #30A1A6 0%, #2F848B 100%);
        & > view, .source-item{
          color: #fff;
        }
      }
    }
  }

  &.active {
    display: block;
  }
  .date-box {
    overflow: hidden;
  }
  .week {
    padding: 16rpx 0;
    display: flex;
    // border-bottom: 2rpx solid @hc-color-border;
  }
  .week-item {
    flex-basis: 13%;
    text-align: center;
    box-sizing: border-box; 
    font-size: 28rpx;
    margin: 0;
    flex-shrink: 0;
    padding: 0 4rpx; // 增加左右间距
    // color: @hc-color-text;
    color: rgba(0, 0, 0, 0.4);
  }
  .date {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 10rpx;
  }
  .date-item {
    flex-basis: 14.28%;
    text-align: center;
    margin-bottom: 10rpx;
    padding-top: 15rpx;
  }
  .item-box {
    width: 66rpx;
    height: 68rpx;
    padding: 12rpx 8rpx;
    margin: 0 auto;
  }
  .item-day {
    font-size: 34rpx;
    color: @hc-color-title;
    line-height: 36rpx;
  }
  .item-status {
    font-size: 18rpx;
    line-height: 24rpx;
    margin-top: 8rpx;
    // color: @hc-color-text;
    color: rgba(0, 0, 0, 0.4);
    &.on {
      color: @hc-color-primary;
    }
  }
  .date-item {
    &.on {
      .item-day {
        // color: @hc-color-primary;
      }
    }
    &.active {
      .item-box {
        border-radius: 16rpx;
        background-color: @hc-color-primary;
        color: #fff;
      }
      .item-day {
        color: #fff;
      }
      .item-status {
        color: #fff;
      }
    }
  }
}

.m-list {
  padding-bottom: 20rpx;
  .list-item {
    display: block;
    padding: 0 32rpx;
    background-color: #fff;
  }
  .item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid @hc-color-border;
    overflow: hidden;
    min-height: 56rpx;
    .arrow-icon{
      width: 24rpx;
      height: 24rpx;
    }
  }
  .list-item:last-child {
    .item {
      border-bottom: none;
    }
  }
  .item-hd {
    flex: 1;
    color: @hc-color-title;
    font-size: 28rpx;
    .circle {
      display: inline-block;
      width: 90rpx;
      height: 90rpx;
      border-radius: 50%;
      text-align: center;
      line-height: 90rpx;
      border: 1rpx solid transparent;
      font-size: 24rpx;
      margin-right: 30rpx;
    }
    .can {
      color: @hc-color-primary;
      border-color: @hc-color-primary;
    }
    .can-not {
      color: @hc-color-border;
      border-color: @hc-color-border;
    }
    .exist {
      // color: @hc-color-title;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.7);
    }
    .no-exist {
      // color: @hc-color-text;
      color: rgba(0, 0, 0, 0.4);
    }
  }
  .item-bd {
    color: @hc-color-warn;
    font-size: 24rpx;
    margin-left: 20rpx;
    position: relative;
    .money {
      padding: 0 32rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #CC8F24;
    }
    .exist {
      color: #2D666F;
      font-size: 32rpx;
      .exit-total{
        color: rgba(0, 0, 0, 0.7);
      }
    }
    .no-exist {
      border-radius: 56rpx;
      padding: 8rpx 24rpx;
      font-weight: 600;
      font-size: 26rpx;
      background: rgba(0, 0, 0, 0.04);
      color: rgba(0, 0, 0, 0.4);
      width: 110rpx;
      display: inline-block;
      text-align: center;
    }
  }
  .item-ft {
    position: absolute;
    top: 7px;
    right: -2rpx;
    width: 15rpx;
    height: 15rpx;
    border-right: 3rpx solid rgba(0, 0, 0, 0.2);
    border-bottom: 3rpx solid rgba(0, 0, 0, 0.2);
    transform: translateX(-8rpx) rotate(-45deg);
  }
  .list-item {
    &:active:not(.disabled) {
      background-color: #ddd;
    }
    &.disabled {
      .item-hd,
      .item-bd {
        color: @hc-color-text;
      }
      .item-ft {
        visibility: hidden;
      }
    }
  }
}
//介绍
.m-intro {
  margin: 24rpx 0;
  &.active {
    display: block;
  }
  .intro-box {
    // background-color: #fff;
    // padding: 40rpx 30rpx 40rpx 30rpx;
    padding: 0 24rpx;
  }
  .intro-tit {
    text-align: center;
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .intro-item {
    padding: 24rpx;
    margin-bottom: 16rpx;
    border-radius: 8rpx;
    background: #FFF;
  }
  .item-tit {
    margin-bottom: 24rpx;
    color: rgba(0, 0, 0, 0.90);
    font-weight: 600;
    font-size: 28rpx;
  }
  .item-text {
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.70);
    line-height: 42rpx;
  }
}

.unit-tab {
  position: relative;
  display: flex;
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8rpx;
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.80);

  & > .unit-tab-li {
    position: relative;
    z-index: 1;
    display: block;
    padding: 12rpx 16rpx;
    flex: 1;
    width: 0%;
    font-size: 30rpx;
    text-align: center;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    user-select: none;
    font-weight: 600;
    & > a {
      display: block;
      width: 100%;
      height: 100%;
      color: @hc-color-title;
    }
    // &:after {
    //   content: " ";
    //   position: absolute;
    //   left: 50%;
    //   bottom: 0;
    //   display: block;
    //   width: 2.5em;
    //   height: 4rpx;
    //   background-color: @hc-color-primary;
    //   transform: translateX(-50%) scaleX(0);
    //   transition: all ease-out 0.2s 0.1s;
    // }
    &.active {
      color: #2D666F;
      background-color: #fff;
      // &:after {
      //   transform: translateX(-50%) scale(1);
      // }
      & > a {
        color: @hc-color-primary;
      }
    }
  }
}

//挂号确认弹窗
.m-reg-docinfo-popup {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  z-index: -9;
  visibility: hidden;

  .reg-docinfo-popup-mask {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0);
    transition: background 0.3s;
    z-index: 0;
  }
  .reg-docinfo-popup {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    background-color: #fff;
    transition: transform 0.3s 0.1s;
    transform: translateY(100%);
    border-radius: 8rpx 8rpx 0 0;
  }
  &.active {
    z-index: 999;
    visibility: visible;
    .reg-docinfo-popup-mask {
      background-color: rgba(0, 0, 0, 0.6);
    }
    .reg-docinfo-popup {
      transform: translateY(0%);
    }
  }
  .popup-form-box {
    max-height: 1000rpx;
    overflow-y: auto;
  }
  .popup-item {
    // border-top: 2rpx solid @hc-color-border;
    // margin-left: 30rpx;
    margin: 0 32rpx;
    &:first-child {
      border-top: 0;
    }
  }
  .info {
    display: flex;
    align-items: center;
    // padding: 45rpx 30rpx 45rpx 0;
    padding: 40rpx 0;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  }
  .info-hd {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 124rpx;
    height: 124rpx;
    border-radius: 50%;
    overflow: hidden;

    image {
      vertical-align: top;
      border-radius: 50%;
    }
  }
  .info-bd {
    flex: 1;
  }
  .info-bd-item {
    display: flex;
    font-size: 36rpx;
    margin-bottom: 8rpx;
    margin-left: 24rpx;
    font-weight: 600;
    .bd-item-title{
      margin-right: 18rpx;
    }
    
  }
  .info-bd-small{
    display: flex;
    margin-left: 24rpx;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.70);
    .info-bd-text{
      font-weight: 600;
      color: rgba(204, 143, 36, 1);
    }
    &.last-child{
      margin-top: 8rpx;
    }
  }
  .info-bd-label {
    color: @hc-color-title;
    word-break: keep-all;
  }
  .info-bd-text {
    flex: 1;
  }
  .opt {
    padding: 30rpx 0;
  }
  .opt-tit {
    font-size: 34rpx;
    .textBreak();
  }
  .opt-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 32rpx;
    .list-box{
      margin-right: 24rpx;
      &.last-child{
        margin: 0;
      }
    }
  }
  .opt-list-item {
    margin-left: 32rpx;
    margin-bottom: 24rpx;
  }
  .opt-list-item-add {
    width: 120rpx;
    border-radius: 100px;
    background: rgba(48, 161, 166, 0.10);
    font-size: 56rpx;
    text-align: center;
    color: rgba(0, 0, 0, 0.5);
    & > view{
      color: rgba(48, 161, 166, 1);
      margin-top: -8rpx;
    }
  }

  .popup-bottom-btn {
    bottom: 48rpx;
    width: 100%;
    padding: 34rpx 32rpx 24rpx;
    text-align: center;
    box-sizing: border-box;
    .popup-submit {
      padding: 24rpx;
      color: @hc-color-white;
      font-size: 34rpx;
      font-weight: 600;
      border-radius: 62rpx;
      background: var(--linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
      margin-bottom: 24rpx;

      &.disabled {
        background-color: #dddddd;
      }
    }
    .popup-cancel {
      line-height: 100rpx;
      color: @hc-color-title;
      font-size: 34rpx;
      font-weight: 600;
      background: rgba(0, 0, 0, 0.04);
      width: 100%;
      border-radius: 24rpx;
    }
  }

  .popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 30rpx 30rpx;
    font-size: 36rpx;
    color: @hc-color-border;
    z-index: 9;
    image {
      width: 36rpx;
      height: 36rpx;
    }
  }
  .unit-label {
    padding: 16rpx 20rpx;
    background: #f8f9fb;
    color: @hc-color-title;
    border-radius: 100rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 600;

    &.active {
      // border: 2rpx solid @hc-color-primary;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      color: #fff;
    }
  }
  .unit-label-text {
    display: inline-block;
    vertical-align: top;
    width: 2em;
    white-space: nowrap;
    text-overflow: clip;
    overflow: hidden;
  }
  .unit-select {
    padding: 0 30rpx;
    line-height: 70rpx;
    border: 2rpx solid @hc-color-border;
    color: @hc-color-text;
    border-radius: 70rpx;
    text-align: center;
    font-size: 30rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.active {
      border: 2rpx solid @hc-color-primary;
      background-color: @hc-color-primary;
      color: #fff;
    }
  }
  .m-top-tips {
    display: none;
    position: fixed;
    right: 0;
    left: 0;
    padding: 40rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 6rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-bottom: 2rpx solid @hc-color-border;
    z-index: 1000;
    color: @hc-color-warn;
    align-items: center;

    &.active {
      display: flex;
    }

    .tips-icon {
      line-height: 1;
      font-size: 60rpx;
    }
    .tips-text {
      flex: 1;
      font-size: 30rpx;
      margin-left: 30rpx;
    }
  }
}

.has-wait {
  border-radius: 56rpx;
  padding: 8rpx 24rpx;
  font-weight: 600;
  font-size: 26rpx;
  background: @hc-color-primary;
  color: @hc-color-white;
  width: 110rpx;
  display: inline-block;
  text-align: center;
  margin-right: 16rpx;
}

.unit-color-title {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.70);
  .title{
    margin-right: 16rpx;
    color: rgba(0, 0, 0, 0.90);
    font-size: 28rpx;
    font-weight: 600;
  }
}
.unit-color-text {
  color: @hc-color-text;
}
.unit-color-warn {
  color: @hc-color-warn;
}
.unit-fs-title {
  font-size: 36rpx;
}
.unit-fs-text {
  font-size: 28rpx;
}
.unit-none {
  display: none;
}

.ad-docinfo {
  .list-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    margin-bottom: 20rpx;
  }
  .item-bd {
    flex: 1;
    display: flex;
    align-items: center;
    padding-right: 30rpx;
  }
  .bd-main {
    flex: 1;
  }
  .main-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .main-txt {
    margin-top: 5rpx;
    font-size: 24rpx;
    color: @hc-color-text;
  }
  .bd-extra {
    font-size: 34rpx;
    color: @hc-color-warn;
  }
  .item-ft {
    image {
      width: 50rpx;
      height: 50rpx;
      vertical-align: top;
    }
  }
  .help-icon {
    margin-left: 10rpx;
    width: 30rpx;
    height: 30rpx;
    background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/ad/ad-help.png")
      no-repeat 50% 50%;
    background-size: 100% 100%;
    display: inline-block;
  }
}

// 弹窗提示
.desc-modal-mask {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  .desc-modal {
    width: 670rpx;
    border-radius: 24rpx;
    background-color: @hc-color-white;

    .desc-title {
      text-align: center;
      height: 48rpx;
      padding: 40rpx 0 32rpx;
      font-size: 34rpx;
      font-weight: 600;
      color: @hc-color-title;
    }

    .desc-content {
      width: 100%;
      max-height: 700rpx;
      // min-height: 200rpx;
      box-sizing: border-box;
      padding: 0 32rpx 32rpx 32rpx;
      color: @hc-color-info;
    }
    .desc-content-info {
      text-align: center;
      line-height: 1.7;
    }
    .address {
      font-weight: 600;
      color: @hc-color-title;
    }
    .phone {
      color: @hc-color-assist;
    }
    .desc-footer {
      height: 96rpx;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      // border-top: 2rpx solid @hc-color-border;
      > view {
        flex: 1;
        font-size: 34rpx;
        font-weight: 500;
        color: @hc-color-gray;
        text-align: center;
        margin: 24rpx 0;

        & + view {
          border-left: 2rpx solid @hc-color-border;
        }
      }

      .agree {
        color: @hc-color-primary;
      }
    }
  }
}
