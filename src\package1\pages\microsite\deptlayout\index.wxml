<view class="p-page">

  <view class="p-site-deptdst" wx:if="{{buildList.length > 0}}">
    <view class="m-list">
      <view class="list-box">
        <view class="list">
          <block wx:for="{{buildList}}" wx:for-index="idx">
            <view class="list-item {{tabIndex === idx ? 'active' : ''}}" @tap="changeBuild({{item.buildId}}, {{idx}})">{{item.buildName}}</view>
          </block>
        </view>
      </view>
    </view>
    <view class="m-detail">
      <view class="detail-box">
        <block wx:for="{{floorList}}" wx:for-index="idx">
          <view class="detail-item">
            <view class="item-num"><view class="shu" />{{item.floorName}}</view>
            <view class="item-text">{{item.floorDept || '暂无科室'}}</view>
          </view>
        </block>
      </view>
      <view class="m-nofloor" hidden="{{floorList.length > 0}}">未查询到楼层信息</view>
    </view>
  </view>

  <empty :config.sync="emptyConfig">
    <block slot="text">暂未查询到科室信息</block>
  </empty>
</view>
