<!--  -->
<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveDetailMixin from '@/mixins/live/detailMixin';
import { post } from '@/utils/request';
import DoctorInfo from '../comm/doctorInfo';
import * as Api from './api';

const ERROR_OK = 0;
export default class VideoDetail extends wepy.page {
  config = {
    navigationBarTitleText: '视频详情'
  };

  // 在mixin中处理了对视频详情的请求
  mixins = [LiveDetailMixin];

  data = {
    id: '', // 视频id
    likes: 0, // 点赞数
    inputValue: '', // 评论内容
    comments: 0, // 总评论数
    commentsList: [], // 评论列表
    focus: false, // 评论框focus状态
    pageNum: 1,
    hasMoreData: false,
    likeEventStatus: true,
    videoPlayed: false
  };

  components = {
    'doctor-info': DoctorInfo
  };

  methods = {
    likeToggle(liveId) {
      this.likeToggleEvent(liveId);
    },
    onInput(e) {
      this.inputValue = e.detail.value;
    },
    onBlur() {
      this.focus = false;
    },
    goComment() {
      this.focus = true;
    },
    getMoreComments(id, pageNum) {
      this.getCommentsList(id, pageNum);
    },
    send(liveId, comment) {
      this.sendEvent(liveId, comment);
    },
    onVideoError(e) {
      console.log(e)
      wx.showModal({
        title: '视频播放失败', //提示的标题,
        content: '请返回重试', //提示的内容,
        showCancel: false,
        confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
        confirmColor: '#3CC51F', //确定按钮的文字颜色,
        success: res => {
          if (res.confirm) {
            wepy.navigateBack({
              delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
            });
          }
        }
      });
    },
    onVideoPlay() {
      if (!this.videoPlayed) {
        this.reqVideoPlay();
      }
    }
  };

  onShareAppMessage() {
    return {
      title: `直播`,
      path: `/package1/pages/live/video/index?id=${this.id}`
    }
  }

  events = {};
  async reqVideoPlay() {
    const { code, data } = await Api.onPlay({ liveId: this.id });
    if (code == 0) {
      this.videoPlayed = true;
      this.$apply();
    }
  }
  async sendEvent(liveId, comment) {
    if (comment.trim() === '') {
      // 评论为空
      const TOAST_TT = '请输入评论';
      const TOAST_ICON = 'none';
      this.showToast(TOAST_TT, TOAST_ICON);
      return;
    }
    if (this.isSending) {
      return;
    }
    this.isSending = true;
    const { code, msg } = await Api.onComments({ liveId, comment });
    this.isSending = false;
    if (code != ERROR_OK) {
      this.showModal(msg);
      return;
    } else {
      const SUCC_MSG = '评论需审核通过后显示';
      this.showModal(SUCC_MSG);
      this.inputValue = '';
      // this.getCommentsList(liveId);
    }
    this.$apply();
  }
  async getCommentsList(liveId, pageNum = 1) {
    const { code, msg, data } = await Api.getList({ liveId, pageNum });
    if (code != ERROR_OK) {
      this.showModal(msg);
      return;
    } else {
      const oldList = pageNum == 1 ? [] : this.commentsList;
      this.commentsList = [...oldList, ...data.recordList];
      this.comments = data.totalCount;
      this.hasMoreData = data.currentPage < data.pageCount ? true : false;
    }
    this.$apply();
  }
  async likeToggleEvent(liveId) {
    if (!this.likeEventStatus) {
      return;
    }
    this.likeEventStatus = false;
    if (!this.isLike) {
      // 点赞
      console.log('liveId',liveId)
      const { code } = await Api.onLike({ liveId });
      if (code == ERROR_OK) {
        this.isLike = !this.isLike;
        this.likes++;
        this.likeEventStatus = true;
      }
    } else {
      // 取消点赞
      const { code } = await Api.onUnLike({ liveId });
      if (code == ERROR_OK) {
        this.isLike = !this.isLike;
        this.likes--;
        this.likeEventStatus = true;
      }
    }
    this.$apply();
  }
  showModal(msg) {
    wx.showModal({
      title: '提示',
      content: msg || '出错啦',
      showCancel: false
    });
  }
  showToast(title = '出错了', icon = 'none') {
    wx.showToast({
      title,
      icon
    });
  }

  watch = {
    liveDetail(newVal = {}) {
      const { liveStatus, id, likes, comments, islikes } = newVal;
      // if (liveStatus != ERROR_OK) {
      //   // 错误处理 返回视频列表
      //   wx.navigateBack({ delta: 1 });
      // }
      this.id = id;
      this.likes = likes;
      this.comments = comments;
    }
  };

  computed = {};

  onLoad(options = {}) {
    const { id } = options;
    this.id = id;
    console.log('id1', id)
    this.getCommentsList(id);
  }

  onShow() {}
}
</script>
