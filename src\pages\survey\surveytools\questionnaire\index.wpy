<template>
  <view class="questionnaire-page">
    <!-- 问卷标题 -->
    <view class="questionnaire-header">
      <view class="questionnaire-title">{{quesInfo.examTitle }}</view>
      <view class="questionnaire-description">
        <view style="text-indent: 2em; line-height: 1.6; color: #333; margin-bottom: 15px;">
          {{quesInfo.examDesc }}
        </view>
      </view>
    </view>

    <!-- 问卷内容 -->
    <view class="questionnaire-content">
      <block wx:for="{{titleList}}" wx:for-index="subjectListIdx" wx:key="key" wx:for-item="question">
        <!-- 单选题 -->
        <block wx:if="{{question.questionsType == '0'}}">
          <view class="question-item" id="title-{{question.titleId}}">
            <view class="question-title">
              {{question.titleNum}}、{{question.questionsTitle}}（单选）
              <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
            </view>
            <radio-group class="radio-group" data-title-id="{{question.titleId}}" data-questions-type="{{question.questionsType}}" bindchange="bindValueChange">
              <view class="radio-group-item {{item.checked ? 'active' : ''}}" wx:for="{{question.optionList}}" wx:for-index="optionsIdx" wx:for-item="item" wx:key="optionNum">
                <view>
                  <radio checked="{{item.checked}}" value="{{item.optionNum}}" color="#3ECEB6" disabled="{{mode === 'view'}}" />
                  <label>{{item.optionContent}}</label>
                </view>
                
                <!-- 单选题中的二级文本框 -->
                <view wx:if="{{item.optionType === '1' && item.checked}}" class="secondary-content-container">
                  <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true" disabled="{{mode === 'view'}}"></textarea>
                </view>
                
                <!-- 单选题中的二级选项复选框 -->
                <view wx:if="{{item.optionType === '2' && item.checked}}" class="secondary-content-container">
                  <checkbox-group class="secondary-checkbox-group" data-title-id="{{question.titleId}}" data-option-num="{{item.optionNum}}" bindchange="bindSecondOptionChange">
                    <block wx:if="{{item.secondOptionArray && item.secondOptionArray.length > 0}}">
                      <block wx:for="{{item.secondOptionArray}}" wx:for-item="secondOption" wx:for-index="secondIdx" wx:key="secondIdx">
                        <view class="secondary-checkbox-item">
                          <checkbox value="{{secondOption}}" color="#3ECEB6" disabled="{{mode === 'view'}}" />
                          <text>{{secondOption}}</text>
                        </view>
                      </block>
                    </block>
                    <view wx:else class="empty-secondary-options">
                      暂无二级选项
                    </view>
                  </checkbox-group>
                </view>
                
                <!-- 单选题中的二级多值填空 -->
                <view wx:if="{{item.optionType === '3' && item.checked}}" class="secondary-content-container">
                  <view class="secondary-fill-blank-container">
                    <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                      <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                      <view wx:else class="fill-blank-input-wrapper">
                        <input
                          class="fill-blank-input"
                          type="text"
                          placeholder=""
                          data-title-id="{{question.titleId}}"
                          data-option-num="{{item.optionNum}}"
                          data-index="{{part.index}}"
                          data-unique-id="{{part.uniqueId}}"
                          value="{{part.value}}"
                          bindinput="bindSecondFillBlankChange"
                          bindfocus="bindSecondFillBlankFocus"
                          maxlength="10"
                          disabled="{{mode === 'view'}}"
                          style="width: {{part.inputWidth || '60rpx'}};"
                        />
                        <view class="fill-blank-underline" style="width: {{part.inputWidth || '60rpx'}};"></view>
                      </view>
                    </block>
                  </view>
                </view>
              </view>
            </radio-group>

            <!-- 附件上传 -->
            <view wx:if="{{question.fileFlg == '1'}}" class="file-upload-section">
              <view class="file-upload-title">
                <text class="file-upload-title-text">{{question.fileUploadDescribe || '请上传相关附件'}}</text>
                <text class="file-upload-title-desc">支持上传图片、PDF等文件格式</text>
              </view>
              <view class="file-upload-content">
                <!-- 文件预览 -->
                <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                  <!-- 图片项 -->
                  <view class="file-item" wx:if="{{file.type === 'image'}}">
                    <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                    <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                  <!-- PDF项 -->
                  <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                    <view class="pdf-icon">PDF</view>
                    <view class="pdf-name">{{file.fileName}}</view>
                    <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                </block>

                <!-- 上传按钮 -->
                <view wx:if="{{question.imageCount < 9 && mode === 'edit'}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                  <view class="upload-icon-plus">+</view>
                  <text class="upload-btn-text">点击上传</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 多选题 -->
        <block wx:if="{{question.questionsType == '1'}}">
          <view class="question-item" id="title-{{question.titleId}}">
            <view class="question-title">
              {{question.titleNum}}、{{question.questionsTitle}}（多选）
              <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
            </view>
            <checkbox-group class="checkbox-group" data-title-id="{{question.titleId}}" data-questions-type="{{question.questionsType}}" bindchange="bindCheckboxChange">
              <view class="checkbox-group-item {{item.checked ? 'active' : ''}}" wx:for="{{question.optionList}}" wx:for-index="optionsIdx" wx:for-item="item" wx:key="optionNum">
                <view>
                  <checkbox checked="{{item.checked}}" value="{{item.optionNum}}" color="#3ECEB6" disabled="{{mode === 'view'}}" />
                  <label>{{item.optionContent}}</label>
                </view>
                
                <!-- 多选题中的二级文本框 -->
                <view wx:if="{{item.optionType === '1' && item.checked}}" class="secondary-content-container">
                  <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true" disabled="{{mode === 'view'}}"></textarea>
                </view>
                
                <!-- 多选题中的二级选项复选框 -->
                <view wx:if="{{item.optionType === '2' && item.checked}}" class="secondary-content-container">
                  <checkbox-group class="secondary-checkbox-group" data-title-id="{{question.titleId}}" data-option-num="{{item.optionNum}}" bindchange="bindSecondOptionChange">
                    <block wx:if="{{item.secondOptionArray && item.secondOptionArray.length > 0}}">
                      <block wx:for="{{item.secondOptionArray}}" wx:for-item="secondOption" wx:for-index="secondIdx" wx:key="secondIdx">
                        <view class="secondary-checkbox-item">
                          <checkbox value="{{secondOption}}" color="#3ECEB6" disabled="{{mode === 'view'}}" />
                          <text>{{secondOption}}</text>
                        </view>
                      </block>
                    </block>
                    <view wx:else class="empty-secondary-options">
                      暂无二级选项
                    </view>
                  </checkbox-group>
                </view>
                
                <!-- 多选题中的二级多值填空 -->
                <view wx:if="{{item.optionType === '3' && item.checked}}" class="secondary-content-container">
                  <view class="secondary-fill-blank-container">
                    <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                      <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                      <view wx:else class="fill-blank-input-wrapper">
                        <input
                          class="fill-blank-input"
                          type="text"
                          placeholder=""
                          data-title-id="{{question.titleId}}"
                          data-option-num="{{item.optionNum}}"
                          data-index="{{part.index}}"
                          data-unique-id="{{part.uniqueId}}"
                          value="{{part.value}}"
                          bindinput="bindSecondFillBlankChange"
                          bindfocus="bindSecondFillBlankFocus"
                          maxlength="10"
                          disabled="{{mode === 'view'}}"
                          style="width: {{part.inputWidth || '60rpx'}};"
                        />
                        <view class="fill-blank-underline" style="width: {{part.inputWidth || '60rpx'}};"></view>
                      </view>
                    </block>
                  </view>
                </view>
              </view>
            </checkbox-group>

            <!-- 附件上传 -->
            <view wx:if="{{question.fileFlg == '1'}}" class="file-upload-section">
              <view class="file-upload-title">
                <text class="file-upload-title-text">{{question.fileUploadDescribe || '请上传相关附件'}}</text>
                <text class="file-upload-title-desc">支持上传图片、PDF等文件格式</text>
              </view>
              <view class="file-upload-content">
                <!-- 文件预览 -->
                <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                  <!-- 图片项 -->
                  <view class="file-item" wx:if="{{file.type === 'image'}}">
                    <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                    <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                  <!-- PDF项 -->
                  <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                    <view class="pdf-icon">PDF</view>
                    <view class="pdf-name">{{file.fileName}}</view>
                    <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                </block>

                <!-- 上传按钮 -->
                <view wx:if="{{question.imageCount < 9 && mode === 'edit'}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                  <view class="upload-icon-plus">+</view>
                  <text class="upload-btn-text">点击上传</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 填空题 -->
        <block wx:if="{{question.questionsType == '18'}}">
          <view class="question-item" id="title-{{question.titleId}}">
            <view class="question-title">
              {{question.titleNum}}、{{question.questionsTitle}}（填空）
              <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
            </view>

            <block wx:for="{{question.optionList}}" wx:for-item="option" wx:key="optionNum">
              <view class="fill-blank-container">
                <block wx:for="{{option.contentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                  <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                  <view wx:else class="fill-blank-input-wrapper">
                    <input
                      class="fill-blank-input"
                      type="text"
                      placeholder=""
                      data-title-id="{{question.titleId}}"
                      data-option-num="{{option.optionNum}}"
                      data-index="{{part.index}}"
                      data-unique-id="{{part.uniqueId}}"
                      value="{{part.value}}"
                      bindinput="bindFillBlankChange"
                      bindfocus="bindFillBlankFocus"
                      bindblur="bindFillBlankBlur"
                      maxlength="50"
                      disabled="{{mode === 'view'}}"
                      style="width: {{part.inputWidth || '80rpx'}};"
                    />
                    <view class="fill-blank-underline" style="width: {{part.inputWidth || '80rpx'}};"></view>
                  </view>
                </block>
              </view>
            </block>

            <!-- 附件上传区域 -->
            <view wx:if="{{question.fileFlg === '1'}}" class="file-upload-area">
              <view class="file-list">
                <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                  <!-- 图片项 -->
                  <view class="file-item" wx:if="{{file.type === 'image'}}">
                    <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                    <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                  <!-- PDF项 -->
                  <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                    <view class="pdf-icon">PDF</view>
                    <view class="pdf-name">{{file.fileName}}</view>
                    <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}" wx:if="{{mode === 'edit'}}">×</view>
                  </view>
                </block>

                <!-- 上传按钮 -->
                <view wx:if="{{question.imageCount < 9 && mode === 'edit'}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                  <view class="upload-icon-plus">+</view>
                  <text class="upload-btn-text">点击上传</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>

    <!-- 底部按钮 -->
    <view class="questionnaire-bottom" wx:if="{{mode === 'edit'}}">
      <view class="submit-btn" bindtap="submitQuestionnaire">提交问卷并签署知情同意书</view>
    </view>
  </view>
</template>

<script>
import wepy from 'wepy'
import * as Api from '../api'

export default class Questionnaire extends wepy.page {
  config = {
    navigationBarTitleText: '问卷调查'
  }

  data = {
    id: '', // 订单ID
    surveyId: '', // 问卷ID
    questionType: '1', // 问卷类型
    mode: 'edit', // 模式：edit-编辑，view-查看
    titleList: [], // 问卷题目列表
    readonly: 0, // 是否只读
    questionUserId: '', // 问卷用户ID
    quesInfo: {}, // 问卷基本信息
    startExpandIdx: 0, // 开始展开的索引
    isSpecialDis: false // 是否特殊疾病
  }

  onLoad(options) {
    const { id, surveyId, questionType, mode } = options
    
    this.id = id || ''
    this.surveyId = surveyId || ''
    this.questionType = questionType || '1'
    this.mode = mode || 'edit'
    
    // 设置只读状态
    this.readonly = this.mode === 'view' ? 1 : 0
    
    // 设置导航栏标题
    const title = this.mode === 'view' ? '问卷详情' : '问卷调查'
    wepy.setNavigationBarTitle({ title })
    

    
    // 加载问卷数据
    this.loadQuestionnaireData()
    
    this.$apply()
  }

  // 加载问卷数据
  async loadQuestionnaireData() {
    try {
      wepy.showLoading({ title: '加载中...', mask: true })

      if (this.mode === 'view') {
        // 查看模式：加载已填写的问卷数据
        await this.loadFilledQuestionnaire()
      } else {
        // 编辑模式：加载空白问卷
        await this.loadBlankQuestionnaire()
      }

      wepy.hideLoading()
    } catch (error) {
      wepy.hideLoading()
      console.error('加载问卷数据失败:', error)
      wepy.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  }

  // 加载空白问卷
  async loadBlankQuestionnaire() {
    try {
      // 使用正确的API参数格式
      const res = await Api.getSurveyDetail({ id: this.surveyId })

      if (res && res.code === 0 && res.data) {
        this.quesInfo = res.data || {}
        this.titleList = res.data.titleList || []
        this.startExpandIdx = res.data.startExpandIdx || 0
        this.isSpecialDis = res.data.isSpecialDis || false

        // 如果返回了问卷ID，保存它
        if (res.data.id) {
          this.surveyId = res.data.id
        }

        this.initializeQuestionnaireData()
        this.$apply()
      } else {
        wepy.showToast({
          title: res.msg || '获取问卷详情失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载空白问卷失败:', error)
      throw error
    }
  }

  // 加载已填写的问卷
  async loadFilledQuestionnaire() {
    try {
      // 使用getSurveyAnswer接口加载已填写的问卷数据
      const res = await Api.getSurveyAnswer({
        questionUserId: this.questionUserId || '',
      
        id: this.surveyId,
        examId: this.surveyId
      })

      if (res && res.code === 0 && res.data) {
        // 处理问卷基本信息
        this.quesInfo = res.data || {}
        this.titleList = res.data.titleList || []
        this.titleList = res.data.titleList || []

        // 处理已填写的答案
        if (res.data.answerContentList && res.data.answerContentList.length > 0) {
          this.fillAnswersFromResponse(res.data.answerContentList)
        }

        this.initializeQuestionnaireData()
        this.$apply()
      } else {
        wepy.showToast({
          title: res.msg || '获取问卷答案失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载已填写问卷失败:', error)
      throw error
    }
  }

  // 初始化问卷数据
  initializeQuestionnaireData() {
    if (!this.titleList) return

    this.titleList.forEach(question => {
      if (question.optionList) {
        question.optionList.forEach(option => {
          // 初始化二级选项
          if (option.optionType === '2' && option.secondOptionContent) {
            if (option.secondOptionContent.indexOf(',') !== -1) {
              option.secondOptionArray = option.secondOptionContent.split(',')
            } else {
              option.secondOptionArray = [option.secondOptionContent]
            }
            option.selectedSecondOptions = option.selectedSecondOptions || []
          }

          // 初始化二级多值填空
          if (option.optionType === '3' && option.secondOptionContent) {
            option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent)
            option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length
            option.secondAnswerValues = option.secondAnswerValues || new Array(option.secondInputCount).fill('')
          }
        })
      }

      // 初始化填空题
      if (question.questionsType === '18' && question.optionList) {
        question.optionList.forEach(option => {
          // 解析填空题内容
          if (option.optionContent) {
            option.contentParts = this.parseSecondFillBlankContent(option.optionContent)
            option.inputCount = option.contentParts.filter(part => part.isInput).length
            option.answerValues = option.answerValues || new Array(option.inputCount).fill('')

            // 为每个输入框设置初始宽度
            option.contentParts.forEach(part => {
              if (part.isInput) {
                const calculateWidth = (val) => {
                  if (!val || val.length === 0) return '80rpx'
                  let charCount = 0
                  for (let i = 0; i < val.length; i++) {
                    const char = val.charAt(i)
                    if (/[\u4e00-\u9fa5]/.test(char)) {
                      charCount += 2
                    } else {
                      charCount += 1
                    }
                  }
                  // 计算宽度，考虑屏幕宽度限制
                  const maxWidth = Math.min(400, 670)
                  const width = Math.max(80, Math.min(maxWidth, charCount * 28 + 20))
                  return width + 'rpx'
                }
                part.inputWidth = calculateWidth(part.value || '')
              }
            })
          }
        })
      }

      // 初始化图片数量
      question.imageCount = (question.uploadedFiles || []).filter(f => f.type === 'image').length
    })
  }

  // 从API响应填充答案到问卷
  fillAnswersFromResponse(answerContentList) {
    if (!answerContentList || !this.titleList) return

    // 遍历答案列表
    answerContentList.forEach(answer => {
      const question = this.titleList.find(q => q.titleId === answer.titleId)
      if (question) {
        // 处理填空题
        if (question.questionsType === '18' && question.optionList) {
          const option = question.optionList.find(o => o.optionNum === answer.optionNum)
          if (option && answer.answerContent) {
            // 填充填空题答案
            option.answerValues = answer.answerContent.split(',')

            // 同步到contentParts
            if (option.contentParts) {
              let valueIndex = 0
              option.contentParts.forEach(part => {
                if (part.isInput && valueIndex < option.answerValues.length) {
                  part.value = option.answerValues[valueIndex]
                  // 根据已有内容设置宽度
                  const calculateWidth = (val) => {
                    if (!val || val.length === 0) return '80rpx'
                    let charCount = 0
                    for (let i = 0; i < val.length; i++) {
                      const char = val.charAt(i)
                      if (/[\u4e00-\u9fa5]/.test(char)) {
                        charCount += 2
                      } else {
                        charCount += 1
                      }
                    }
                    // 计算宽度，考虑屏幕宽度限制
                    const maxWidth = Math.min(400, 670)
                    const width = Math.max(80, Math.min(maxWidth, charCount * 28 + 20))
                    return width + 'rpx'
                  }
                  part.inputWidth = calculateWidth(part.value || '')
                  valueIndex++
                }
              })
            }
          }
        }
        // 处理单选题和多选题
        else if (question.optionList) {
          const option = question.optionList.find(o => o.optionNum === answer.optionNum)
          if (option) {
            option.checked = true

            // 填充二级答案
            if (answer.secondAnswerContent) {
              if (option.optionType === '1') {
                option.secondAnswerContent = answer.secondAnswerContent
              } else if (option.optionType === '2') {
                option.selectedSecondOptions = answer.secondAnswerContent.split(',')
              } else if (option.optionType === '3') {
                option.secondAnswerValues = answer.secondAnswerContent.split(',')
                // 同步到contentParts
                if (option.secondContentParts) {
                  let valueIndex = 0
                  option.secondContentParts.forEach(part => {
                    if (part.isInput && valueIndex < option.secondAnswerValues.length) {
                      part.value = option.secondAnswerValues[valueIndex]
                      valueIndex++
                    }
                  })
                }
              }
            }
          }
        }
      }
    })
  }

  // 填充答案到问卷（保留原方法以兼容）
  fillAnswersToQuestionnaire(answerData) {
    this.fillAnswersFromResponse(answerData)
  }

  // 解析二级多值填空内容
  parseSecondFillBlankContent(content) {
    if (!content) return []

    const parts = []
    let currentIndex = 0
    let inputIndex = 0

    const regex = /\{val\}/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index)
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          })
        }
      }

      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      })

      inputIndex++
      currentIndex++
      lastIndex = regex.lastIndex
    }

    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex)
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        })
      }
    }

    return parts
  }

  // 验证问卷
  validateSurvey() {
    // 检查必填题是否已填写
    let isValid = true
    let firstErrorTitleId = ''
    let firstErrorTitleNum = ''
    let errorMessage = '请完成所有必填题目'



    if (this.titleList && this.titleList.length > 0) {
      for (let i = 0; i < this.titleList.length; i++) {
        const question = this.titleList[i]

        console.log(`检查题目 ${question.titleId}: ${question.questionsTitle}, 题号: ${question.titleNum}, 是否必填: ${question.required === 1 || question.required === '1' ? '是' : '否'}, required值: ${question.required}`)

        // 检查必填题 - 兼容字符串"1"和数字1
        if (question.required === 1 || question.required === '1') {
          let hasAnswer = false
          let hasSecondaryError = false


          // 填空题（多空填值）验证
          if (question.questionsType === '18') {
            if (question.optionList && question.optionList.length > 0) {
              const option = question.optionList[0] // 通常填空题只有一个选项

              if (option && option.answerValues) {
                // 假设所有空都已填写，然后检查
                hasAnswer = true

                // 检查每个输入框是否都已填写
                for (let i = 0; i < option.answerValues.length; i++) {
                  const value = option.answerValues[i]
                  if (!value || value.trim() === '') {
                    hasAnswer = false
                    errorMessage = `请填写题号 ${question.titleNum} 第${i + 1}个空`
                    console.log(`填空题 ${question.titleId} 第${i + 1}个空未填写`)
                    break
                  }
                }
              } else {
                hasAnswer = false
                errorMessage = `请完成必填题 ${question.titleNum}`
                console.log(`填空题 ${question.titleId} 没有答案数据`)
              }
            }
          }
          // 单选题验证
          else if (question.questionsType === '0') {
            if (question.optionList && question.optionList.length > 0) {
              const checkedOptions = question.optionList.filter(option => option.checked)
              hasAnswer = checkedOptions.length > 0

              if (hasAnswer) {
                // 检查选中选项的二级内容
                for (const option of checkedOptions) {
                  if (option.optionType === '1' && (!option.secondAnswerContent || option.secondAnswerContent.trim() === '')) {
                    hasSecondaryError = true
                    secondaryErrorType = 'text'
                    errorMessage = `请填写题号 ${question.titleNum} 的输入框`
                    console.log(`单选题 ${question.titleId} 选项 ${option.optionNum} 的二级输入框未填写`)
                    break
                  } else if (option.optionType === '2' && (!option.selectedSecondOptions || option.selectedSecondOptions.length === 0)) {
                    hasSecondaryError = true
                    secondaryErrorType = 'checkbox'
                    errorMessage = `请选择题号 ${question.titleNum} 的选项`
                    console.log(`单选题 ${question.titleId} 选项 ${option.optionNum} 的二级选择框未选择`)
                    break
                  } else if (option.optionType === '3' && option.secondAnswerValues) {
                    // 检查每个填空是否都已填写
                    for (let i = 0; i < option.secondAnswerValues.length; i++) {
                      const val = option.secondAnswerValues[i]
                      if (!val || val.trim() === '') {
                        hasSecondaryError = true
                        secondaryErrorType = 'text'
                        // 获取选项的显示文本，如果没有则使用选项编号
                        const optionText = option.optionContent || `选项${option.optionNum}`
                        errorMessage = `请填写题号 ${question.titleNum} "${optionText}" 第${i + 1}个空`
                        console.log(`单选题 ${question.titleId} 选项 ${option.optionNum} 的第${i + 1}个填空未填写`)
                        break
                      }
                    }
                    if (hasSecondaryError) break
                  }
                }
              } else {
                console.log(`单选题 ${question.titleId} 未选择`)
              }
            }
          }
          // 多选题验证
          else if (question.questionsType === '1') {
            if (question.optionList && question.optionList.length > 0) {
              const checkedOptions = question.optionList.filter(option => option.checked)
              hasAnswer = checkedOptions.length > 0

              if (hasAnswer) {
                // 检查选中选项的二级内容
                for (const option of checkedOptions) {
                  if (option.optionType === '1' && (!option.secondAnswerContent || option.secondAnswerContent.trim() === '')) {
                    hasSecondaryError = true
                    secondaryErrorType = 'text'
                    errorMessage = `请填写题号 ${question.titleNum} 的输入框`
                    console.log(`多选题 ${question.titleId} 选项 ${option.optionNum} 的二级输入框未填写`)
                    break
                  } else if (option.optionType === '2') {
                    // 检查二级多选：如果一级选中了，二级至少要选一个
                    if (!option.selectedSecondOptions || option.selectedSecondOptions.length === 0) {
                      hasSecondaryError = true
                      secondaryErrorType = 'checkbox'
                      errorMessage = `请选择题号 ${question.titleNum} "${option.optionContent}" 的二级选项`
                      console.log(`多选题 ${question.titleId} 选项 ${option.optionNum} 的二级选择框未选择`)
                      break
                    }
                  } else if (option.optionType === '3' && option.secondAnswerValues) {
                    // 检查每个填空是否都已填写
                    for (let i = 0; i < option.secondAnswerValues.length; i++) {
                      const val = option.secondAnswerValues[i]
                      if (!val || val.trim() === '') {
                        hasSecondaryError = true
                        secondaryErrorType = 'text'
                        // 获取选项的显示文本，如果没有则使用选项编号
                        const optionText = option.optionContent || `选项${option.optionNum}`
                        errorMessage = `请填写题号 ${question.titleNum} "${optionText}" 第${i + 1}个空`
                        console.log(`多选题 ${question.titleId} 选项 ${option.optionNum} 的第${i + 1}个填空未填写`)
                        break
                      }
                    }
                    if (hasSecondaryError) break
                  }
                }
              } else {
                console.log(`多选题 ${question.titleId} 未选择`)
              }
            }
          }

          if (!hasAnswer || hasSecondaryError) {
            isValid = false

            // 记录第一个错误的题目ID和题号
            if (!firstErrorTitleId) {
              firstErrorTitleId = question.titleId
              firstErrorTitleNum = question.titleNum

              // 如果在检查二级内容时已经设置了具体的错误信息，就使用它
              // 否则设置默认的错误信息
              if (!hasSecondaryError) {
                errorMessage = `请完成必填题 ${firstErrorTitleNum}`
              }
              // 如果hasSecondaryError为true，errorMessage已经在上面的循环中设置了
            }
          }
        }
      }
    }

    if (!isValid) {
      // 提示用户第一个未完成的必填项
      wepy.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })

      // 滚动到该题目位置
      if (firstErrorTitleId) {
        wx.pageScrollTo({
          selector: `#title-${firstErrorTitleId}`,
          duration: 300,
          offsetTop: -20 // 适当的偏移量
        })
      }
    }

    return isValid
  }

  // 收集问卷答案
  collectSurveyAnswers() {
    const surveyData = []

    this.titleList.forEach(question => {
      const { titleId, questionsType, optionList = [] } = question

      if (questionsType === '0' || questionsType === '1') {
        // 单选题和多选题
        const checkedOptions = optionList.filter(option => option.checked)

        checkedOptions.forEach(option => {
          const answerItem = {
            titleId: titleId,
            optionNum: option.optionNum
          }

          // 处理二级答案
          if (option.optionType === '1' && option.secondAnswerContent) {
            answerItem.secondAnswerContent = option.secondAnswerContent
          } else if (option.optionType === '2' && option.selectedSecondOptions && option.selectedSecondOptions.length > 0) {
            answerItem.secondAnswerContent = option.selectedSecondOptions.join(',')
          } else if (option.optionType === '3' && option.secondAnswerValues && option.secondAnswerValues.length > 0) {
            const hasValue = option.secondAnswerValues.some(val => val && val.trim() !== '')
            if (hasValue) {
              answerItem.secondAnswerContent = option.secondAnswerValues.join(',')
            }
          }

          surveyData.push(answerItem)
        })

        // 处理题目级别的附件
        if (question.uploadedFiles && question.uploadedFiles.length > 0) {
          const filePaths = question.uploadedFiles.map(file => file.url).join(',')

          // 如果有选中的选项，将附件添加到第一个选中选项
          if (checkedOptions.length > 0) {
            const firstAnswer = surveyData[surveyData.length - checkedOptions.length]
            if (firstAnswer) {
              firstAnswer.filePath = filePaths
            }
          } else {
            // 如果没有选中选项但有附件，创建一个空答案项来保存附件
            const answerItem = {
              titleId: titleId,
              optionNum: '',
              filePath: filePaths
            }
            surveyData.push(answerItem)
          }
        }
      } else if (questionsType === '18') {
        // 填空题
        optionList.forEach(option => {
          if (option.answerValues && option.answerValues.length > 0) {
            // 检查是否有填写内容
            const hasContent = option.answerValues.some(val => val && val.trim() !== '')

            if (hasContent) {
              const answerItem = {
                titleId: titleId,
                optionNum: option.optionNum,
                answerContent: option.answerValues.join(','),
                type: '18'
              }

              // 添加附件路径
              if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                const filePaths = question.uploadedFiles.map(file => file.url).join(',')
                answerItem.filePath = filePaths
              }

              surveyData.push(answerItem)
            }
          }
        })

        // 如果没有填写内容但有附件，也要保存
        if (question.uploadedFiles && question.uploadedFiles.length > 0) {
          const hasAnswers = surveyData.some(item => item.titleId === titleId)
          if (!hasAnswers) {
            const answerItem = {
              titleId: titleId,
              optionNum: '',
              answerContent: '',
              type: '18',
              filePath: question.uploadedFiles.map(file => file.url).join(',')
            }
            surveyData.push(answerItem)
          }
        }
      }
    })

    return surveyData
  }

  // 处理题目级别的附件（保留原有逻辑）
  processQuestionFiles(question, surveyData) {
    if (question.uploadedFiles && question.uploadedFiles.length > 0) {
      const filePaths = question.uploadedFiles.map(file => file.url).join(',')

      // 如果当前题目已有答案，将附件添加到最后一个答案
      if (surveyData.length > 0) {
        const lastAnswer = surveyData[surveyData.length - 1]
        if (lastAnswer.titleId === question.titleId) {
          lastAnswer.filePath = filePaths
        }
      } else {
        // 如果没有答案但有附件，创建一个空答案项
        surveyData.push({
          titleId: question.titleId,
          optionNum: '',
          answerContent: '',
          type: '0',
          filePath: filePaths
        })
      }
    }
  }

  methods = {
    /**
     * 单选题选项变化
     */
    bindValueChange(e) {
      const { titleId, questionsType } = e.currentTarget.dataset
      const value = e.detail.value



      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        // 清除所有选项的选中状态
        this.titleList[titleIndex].optionList.forEach(option => {
          option.checked = false
        })

        // 设置选中的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(value))
        if (optionIndex !== -1) {
          this.titleList[titleIndex].optionList[optionIndex].checked = true

          const option = this.titleList[titleIndex].optionList[optionIndex]

          // 处理二级选项初始化
          if (option.optionType === '2' && option.secondOptionContent) {
            if (option.secondOptionContent.indexOf(',') !== -1) {
              option.secondOptionArray = option.secondOptionContent.split(',')
            } else {
              option.secondOptionArray = [option.secondOptionContent]
            }
            option.selectedSecondOptions = []
          }

          if (option.optionType === '3' && option.secondOptionContent) {
            option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent)
          }
        }

        this.$apply()
      }
    },

    /**
     * 多选题选项变化
     */
    bindCheckboxChange(e) {
      const { titleId, questionsType } = e.currentTarget.dataset
      const values = e.detail.value



      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        // 更新选项状态
        this.titleList[titleIndex].optionList.forEach(option => {
          const isChecked = values.includes(String(option.optionNum))
          option.checked = isChecked

          if (isChecked) {
            // 处理二级选项初始化
            if (option.optionType === '2' && option.secondOptionContent) {
              if (option.secondOptionContent.indexOf(',') !== -1) {
                option.secondOptionArray = option.secondOptionContent.split(',')
              } else {
                option.secondOptionArray = [option.secondOptionContent]
              }
              option.selectedSecondOptions = []
            }

            if (option.optionType === '3' && option.secondOptionContent) {
              option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent)
            }
          }
        })

        this.$apply()
      }
    },

    /**
     * 二级文本框内容变化
     */
    bindSecondAnswerChange(e) {
      const { titleId, optionnum } = e.currentTarget.dataset
      const value = e.detail.value

      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => item.optionNum === optionnum)
        if (optionIndex !== -1) {
          this.titleList[titleIndex].optionList[optionIndex].secondAnswerContent = value
          this.$apply()
        }
      }
    },

    /**
     * 二级复选框变化
     */
    bindSecondOptionChange(e) {
      const { titleId, optionNum } = e.currentTarget.dataset
      const values = e.detail.value

      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum))
        if (optionIndex !== -1) {
          this.titleList[titleIndex].optionList[optionIndex].selectedSecondOptions = values
          this.$apply()
        }
      }
    },

    /**
     * 二级多值填空输入框获取焦点
     */
    bindSecondFillBlankFocus(e) {
      // 焦点处理逻辑
    },

    /**
     * 二级多值填空输入框内容变化
     */
    bindSecondFillBlankChange(e) {
      const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset
      const value = e.detail.value

      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum))
        if (optionIndex !== -1) {
          const option = this.titleList[titleIndex].optionList[optionIndex]

          if (uniqueId && option.secondContentParts) {
            const partIndex = option.secondContentParts.findIndex(part => part.uniqueId === uniqueId)
            if (partIndex !== -1) {
              option.secondContentParts[partIndex].value = value
              // 计算并设置输入框宽度
              const calculateWidth = (val) => {
                if (!val || val.length === 0) return '60rpx'
                let charCount = 0
                for (let i = 0; i < val.length; i++) {
                  const char = val.charAt(i)
                  if (/[\u4e00-\u9fa5]/.test(char)) {
                    charCount += 2
                  } else {
                    charCount += 1
                  }
                }
                // 二级填空题的最大宽度限制
                const maxWidth = Math.min(300, 500)
                const width = Math.max(60, Math.min(maxWidth, charCount * 24 + 20))
                return width + 'rpx'
              }
              option.secondContentParts[partIndex].inputWidth = calculateWidth(value)

              if (!option.secondAnswerValues) {
                option.secondAnswerValues = new Array(option.secondInputCount || 0).fill('')
              }

              const targetIndex = option.secondContentParts[partIndex].index
              while (option.secondAnswerValues.length <= targetIndex) {
                option.secondAnswerValues.push('')
              }

              option.secondAnswerValues[targetIndex] = value || ''
              this.$apply()
            }
          }
        }
      }
    },

    /**
     * 提交问卷
     */
    async submitQuestionnaire() {
      try {
        // 验证问卷
        if (!this.validateSurvey()) {
          return
        }

        wepy.showLoading({ title: '提交中...', mask: true })

        // 收集问卷答案
        const surveyData = this.collectSurveyAnswers()

        // 格式化答案数据，与原来的提交格式保持一致
        const formattedAnswers = surveyData.map(item => {
          // 基本结构 - 只包含必要的字段
          const formattedItem = {
            titleId: parseInt(item.titleId) || 0,
            optionNum: item.optionNum
          }

          // 填空题才添加answerContent字段
          if (item.type === '18' && item.answerContent) {
            formattedItem.answerContent = item.answerContent
          }

          // 如果有二级选项内容，添加secondAnswerContent字段
          if (item.secondAnswerContent) {
            formattedItem.secondAnswerContent = item.secondAnswerContent
          }

          // 如果有文件路径，添加filePath字段
          if (item.filePath) {
            formattedItem.filePath = item.filePath
          }

          return formattedItem
        })

        // 准备请求参数
        const params = {
          JsonToAnswer: JSON.stringify(formattedAnswers),
          healthSampleOrderId: this.id,
          examId: this.surveyId
        }

        console.log('提交问卷参数:', params)

        // 调用保存问卷API
        const res = await Api.saveQuestion(params)

        wepy.hideLoading()

        if (res && res.code === 0) {
          wepy.showToast({
            title: '问卷提交成功',
            icon: 'success'
          })

          // 提交成功后返回主页面，主页面会自动更新问卷状态
          setTimeout(() => {
            wepy.navigateBack({
              success: () => {
                // 通知主页面刷新数据
                const pages = getCurrentPages()
                if (pages.length > 1) {
                  const prevPage = pages[pages.length - 2]
                  if (prevPage && prevPage.methods && prevPage.methods.loadOrderDetailForReport) {
                    prevPage.methods.loadOrderDetailForReport.call(prevPage)
                  }
                }
              }
            })
          }, 1500)
        } else {
          wepy.showToast({
            title: res.msg || '提交失败',
            icon: 'none'
          })
        }
      } catch (error) {
        wepy.hideLoading()
        console.error('提交问卷失败:', error)
        wepy.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      }
    },

    /**
     * 填空题输入变化
     */
    bindFillBlankChange(e) {
      const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset
      const value = e.detail.value



      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)
      if (titleIndex !== -1) {
        const question = this.titleList[titleIndex]
        if (question.optionList) {
          const optionIndex = question.optionList.findIndex(opt => opt.optionNum === optionNum)
          if (optionIndex !== -1) {
            const option = question.optionList[optionIndex]

            // 确保 answerValues 数组存在
            if (!option.answerValues) {
              option.answerValues = []
            }

            // 更新对应位置的值
            if (index !== undefined) {
              option.answerValues[index] = value
            }

            // 同步更新 contentParts 中的值
            if (option.contentParts && uniqueId) {
              const part = option.contentParts.find(p => p.uniqueId === uniqueId)
              if (part) {
                part.value = value
                // 计算输入框宽度，根据内容长度动态调整
                const calculateWidth = (val) => {
                  if (!val || val.length === 0) return '80rpx'
                  let charCount = 0
                  for (let i = 0; i < val.length; i++) {
                    const char = val.charAt(i)
                    if (/[\u4e00-\u9fa5]/.test(char)) {
                      charCount += 2
                    } else {
                      charCount += 1
                    }
                  }
                  // 计算宽度，考虑屏幕宽度限制
                  // 屏幕宽度约750rpx，减去边距约80rpx，可用宽度约670rpx
                  const maxWidth = Math.min(400, 670)
                  const width = Math.max(80, Math.min(maxWidth, charCount * 28 + 20))
                  return width + 'rpx'
                }
                part.inputWidth = calculateWidth(value)
              }
            }

            this.$apply()
          }
        }
      }
    },

    /**
     * 填空题获得焦点
     */
    bindFillBlankFocus(e) {
      const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset
      console.log('填空题获得焦点:', { titleId, optionNum, index, uniqueId })
    },

    /**
     * 填空题失去焦点
     */
    bindFillBlankBlur(e) {
      const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset
      console.log('填空题失去焦点:', { titleId, optionNum, index, uniqueId })
    },

    /**
     * 选择文件
     */
    chooseFile(e) {
      const { titleId } = e.currentTarget.dataset
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)

      let uploadedCount = 0
      if (titleIndex !== -1 && this.titleList[titleIndex].uploadedFiles) {
        uploadedCount = this.titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length
      }

      // 保存this引用，避免作用域问题
      const self = this

      // 使用与原页面相同的选择方式
      wx.showActionSheet({
        itemList: ['拍摄', '从相册选择', '上传PDF文件'],
        success: (res) => {
          const tapIndex = res.tapIndex
          if (tapIndex === 0 || tapIndex === 1) {
            // 选择图片
            wx.chooseImage({
              count: 9 - uploadedCount,
              sizeType: ['compressed'],
              sourceType: tapIndex === 0 ? ['camera'] : ['album'],
              success: (res) => {
                const tempFilePaths = res.tempFilePaths
                if (!tempFilePaths || tempFilePaths.length === 0) return

                // 直接在这里处理图片上传，使用内联方式避免作用域问题
                const uploadNext = (idx) => {
                  if (idx >= tempFilePaths.length) {
                    // 内联更新图片数量逻辑
                    if (titleIndex !== -1) {
                      const imageCount = (self.titleList[titleIndex].uploadedFiles || []).filter(f => f.type === 'image').length
                      self.titleList[titleIndex].imageCount = imageCount
                    }
                    self.$apply()
                    wx.showToast({ title: '上传成功', icon: 'success' })
                    return
                  }

                  wx.showLoading({ title: '上传中...', mask: true })

                  Api.uploadFile(tempFilePaths[idx], 'image')
                    .then(uploadRes => {
                      wx.hideLoading()
                      if (uploadRes && uploadRes.code === 0 && uploadRes.data && uploadRes.data.url) {
                        if (titleIndex !== -1) {
                          if (!self.titleList[titleIndex].uploadedFiles) {
                            self.titleList[titleIndex].uploadedFiles = []
                          }
                          self.titleList[titleIndex].uploadedFiles.push({
                            url: uploadRes.data.url,
                            type: 'image',
                            fileName: '图片' + (self.titleList[titleIndex].uploadedFiles.length + 1)
                          })
                          // 内联更新图片数量逻辑
                          const imageCount = (self.titleList[titleIndex].uploadedFiles || []).filter(f => f.type === 'image').length
                          self.titleList[titleIndex].imageCount = imageCount
                          self.$apply()
                        }
                        uploadNext(idx + 1)
                      } else {
                        wx.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' })
                      }
                    })
                    .catch(error => {
                      wx.hideLoading()
                      console.error('上传图片失败:', error)
                      wx.showToast({ title: '上传失败，请重试', icon: 'none' })
                    })
                }

                uploadNext(0)
              }
            })
          } else if (tapIndex === 2) {
            // 选择PDF文件
            wx.chooseMessageFile({
              count: 1,
              type: 'file',
              success: (res) => {
                const file = res.tempFiles[0]
                if (file && file.path) {
                  // 直接在这里处理PDF上传，使用内联方式避免作用域问题
                  wx.showLoading({ title: '上传中...', mask: true })

                  Api.uploadFile(file.path, 'pdf')
                    .then(uploadRes => {
                      wx.hideLoading()
                      if (uploadRes && uploadRes.code === 0 && uploadRes.data && uploadRes.data.url) {
                        if (titleIndex !== -1) {
                          if (!self.titleList[titleIndex].uploadedFiles) {
                            self.titleList[titleIndex].uploadedFiles = []
                          }
                          self.titleList[titleIndex].uploadedFiles.push({
                            url: uploadRes.data.url,
                            type: 'pdf',
                            fileName: file.name || 'PDF文件' + (self.titleList[titleIndex].uploadedFiles.length + 1)
                          })
                          // 内联更新图片数量逻辑
                          const imageCount = (self.titleList[titleIndex].uploadedFiles || []).filter(f => f.type === 'image').length
                          self.titleList[titleIndex].imageCount = imageCount
                          self.$apply()
                          wx.showToast({ title: '上传成功', icon: 'success' })
                        }
                      } else {
                        wx.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' })
                      }
                    })
                    .catch(error => {
                      wx.hideLoading()
                      console.error('上传PDF失败:', error)
                      wx.showToast({ title: '上传失败，请重试', icon: 'none' })
                    })
                }
              }
            })
          }
        }
      })
    },



    /**
     * 查看文件
     */
    viewFile(e) {
      const { url } = e.currentTarget.dataset

      if (url.toLowerCase().includes('.pdf')) {
        // PDF文件，跳转到webview查看
        wx.navigateTo({
          url: `/pages/webview/index?url=${encodeURIComponent(url)}`
        })
      } else {
        // 图片文件，使用预览
        wx.previewImage({
          current: url,
          urls: [url]
        })
      }
    },

    /**
     * 删除文件
     */
    deleteFile(e) {
      const { url, titleId } = e.currentTarget.dataset
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId)

      if (titleIndex !== -1 && this.titleList[titleIndex].uploadedFiles) {
        this.titleList[titleIndex].uploadedFiles = this.titleList[titleIndex].uploadedFiles.filter(file => file.url !== url)
        // 内联更新图片数量逻辑
        const imageCount = (this.titleList[titleIndex].uploadedFiles || []).filter(f => f.type === 'image').length
        this.titleList[titleIndex].imageCount = imageCount
        this.$apply()
        wx.showToast({ title: '删除成功', icon: 'success' })
      }
    }
  }
}
</script>

<style lang="less">
@import '../index.less';

/* 使用与原病史信息步骤一致的样式 */
.questionnaire-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 问卷头部样式 - 与survey-head保持一致 */
.questionnaire-header {
  padding: 30rpx 40rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.questionnaire-title {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.questionnaire-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 问卷内容容器 - 与survey-container保持一致 */
.questionnaire-content {
  padding: 20rpx 20rpx 60rpx;
}

/* 问题项样式 - 与template-panle保持一致 */
.question-item {
  position: relative;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.question-title {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 16rpx;
  margin-top: 20rpx;
  color: #333333;
  word-break: break-word;
}

/* 单选题和多选题组样式 - 与原样式保持一致 */
.radio-group,
.checkbox-group {
  width: 100%;
  box-sizing: border-box;
}

.radio-group-item,
.checkbox-group-item {
  display: flex;
  flex-direction: column;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #333;

  &.active {
    color: #3ECEB6;
  }

  > view:first-child {
    display: flex;
    align-items: center;

    radio, checkbox {
      transform: scale(0.9);
      margin-right: 10rpx;
    }

    label {
      line-height: 1.6;
    }
  }
}

/* 二级选项容器样式 - 与原样式保持一致 */
.secondary-content-container {
  margin-top: 16rpx;
  margin-left: 70rpx;
  width: calc(100% - 70rpx);
  box-sizing: border-box;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -10rpx;
    top: 20rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
  }

  textarea {
    background-color: #fff;
    padding: 12rpx 0 10rpx;
    font-size: 28rpx;
    width: 100%;
    box-sizing: border-box;
    min-height: 80rpx;
    line-height: 1.4;
    border: none;
    border-bottom: 1rpx solid #e0e0e0;
    border-radius: 0;
    transition: border-color 0.3s ease;

    &:focus {
      border-bottom: 2rpx solid #3ECEB6;
    }
  }
}

/* 二级复选框组样式 - 与原样式保持一致 */
.secondary-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 10rpx 0;
  margin-top: 10rpx;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8rpx;
  padding: 16rpx;
  box-sizing: border-box;
}

.secondary-checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
  width: 45%;

  &:nth-child(2n) {
    margin-right: 0;
  }

  checkbox {
    transform: scale(0.85);
    margin-right: 6rpx;
  }

  text {
    font-size: 28rpx;
    color: #555;
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
  }
}

.empty-secondary-options {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx 0;
  text-align: center;
  width: 100%;
}

/* 必填标识样式 - 与原样式保持一致 */
.mast-select {
  width: 18rpx;
  height: 18rpx;
  vertical-align: middle;
  margin-left: 5rpx;
}

/* 底部按钮样式 - 与fixed-bottom-btn保持一致 */
.questionnaire-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 0 40rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-btn {
  margin: 20rpx auto;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(48, 161, 166, 0.3);
  transition: all 0.3s ease;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  width: 90%;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 6rpx rgba(48, 161, 166, 0.3);
  }
}

/* 附件上传样式 - 与原样式保持一致 */
.file-upload-section {
  margin-top: 30rpx;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
}

.file-upload-title {
  margin-bottom: 20rpx;
}

.file-upload-title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.file-upload-title-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.file-upload-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10rpx;
}

.file-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
  border: 2rpx solid #e0e0e0;
}

.file-item .img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-img-btn, .delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
}

.pdf-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  background: #fff;
}

.pdf-icon {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff6b6b;
  background: #ffe0e0;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}

.pdf-name {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.file-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;

  &:active {
    background-color: #f0f0f0;
    border-color: #2aafff;
  }

  .upload-icon-plus {
    font-size: 48rpx;
    color: #8c8c8c;
    font-weight: bold;
  }

  .upload-btn-text {
    font-size: 24rpx;
    color: #8c8c8c;
    margin-top: 8rpx;
  }
}

/* 填空题样式 */
.fill-blank-container {
  line-height: 1.8;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
  /* 使用flex布局支持自动换行 */
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  word-break: break-all;
}

.fill-blank-text {
  /* 改为flex项目，支持自动换行 */
  display: inline-flex;
  align-items: baseline;
  flex-shrink: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.fill-blank-input-wrapper {
  /* 改为flex项目，支持自动换行 */
  display: inline-flex;
  position: relative;
  align-items: baseline;
  margin: 0 4rpx;
  flex-shrink: 0;
  /* 当输入框过长时允许换行 */
  max-width: 100%;
}

.fill-blank-input {
  background: transparent;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  padding: 0 4rpx;
  min-width: 80rpx;
  height: 40rpx;
  line-height: 40rpx;
  vertical-align: baseline;
  transition: all 0.3s ease;
  /* 确保输入框宽度能够动态调整 */
  box-sizing: border-box;

  &:focus {
    color: #3ECEB6;
  }
}

.fill-blank-underline {
  position: absolute;
  bottom: 2rpx;
  left: 0;
  height: 2rpx;
  background-color: #333;
  transition: all 0.3s ease;
  /* 确保下划线跟随输入框宽度 */
  min-width: 80rpx;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background-color: #3ECEB6;
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }
}

.fill-blank-input:focus + .fill-blank-underline::after {
  transform: scaleX(1);
}

/* 二级填空题样式 */
.secondary-fill-blank-container {
  line-height: 1.6;
  font-size: 26rpx;
  color: #555;
  padding: 10rpx 0;
  /* 使用flex布局支持自动换行 */
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  word-break: break-all;

  .fill-blank-text {
    font-size: 26rpx;
    /* 改为flex项目，支持自动换行 */
    display: inline-flex;
    align-items: baseline;
    flex-shrink: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .fill-blank-input-wrapper {
    /* 改为flex项目，支持自动换行 */
    display: inline-flex;
    position: relative;
    align-items: baseline;
    margin: 0 4rpx;
    flex-shrink: 0;
    /* 当输入框过长时允许换行 */
    max-width: 100%;
  }

  .fill-blank-input {
    font-size: 26rpx;
    height: 36rpx;
    line-height: 36rpx;
    min-width: 60rpx;
    /* 确保输入框宽度能够动态调整 */
    box-sizing: border-box;
  }

  .fill-blank-underline {
    height: 1rpx;
    /* 确保下划线跟随输入框宽度 */
    min-width: 60rpx;
  }
}
</style>
