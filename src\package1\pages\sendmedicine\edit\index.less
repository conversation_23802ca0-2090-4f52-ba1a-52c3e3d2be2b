@import "../../../../resources/style/mixins.less";

page{
  position: relative;
  font-size: 30rpx;
  line-height: 1.5;
  color: @hc-color-title;
  background-color: @hc-color-bg;
  font-family: PingFangSC-Regular, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}


.bindcard-list{
  background: #fff;
  .bindcard-listitem{
    padding: 30rpx;
    display: flex;
    position: relative;

    &.bindcard-listitem_none{
      display: none;
    }

    &:before{
      content: ' ';
      position: absolute;
      left: 30rpx;
      right: 0;
      top: 0;
      height: 1rpx;
      border-top: 2rpx solid @hc-color-border;
    }
    &:first-child:before{
      display: none;
    }

    .listitem-head{
      width: 240rpx;
      display: flex;
      align-items: center;
      .textBreak();

      .list-title{
        flex: 1;
        font-size: 30rpx;
        color: @hc-color-title;
        padding-right: 12rpx;
        position: relative;
        line-height: 1;
        height: 100%;

        &.require {
          position: relative;
          &:after {
            content: '*';
            color: #F76361;
            font-size: 32rpx;
          }
        }

        &.list-title_select:before{
          content: ' ';
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          border-bottom: 10rpx solid @hc-color-title;
          border-right: 10rpx solid @hc-color-title;
          border-top: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body{
      flex: 1;
      padding-left: 30rpx;
      position: relative;
      .textBreak();
      .m-verifycode-btn {
        position: absolute;
        top: 0;
        right: 10rpx;
        color: #3ECEB6;
        height: 1.4rem;
        line-height: 1.4rem;
        &:active {
          color: #666;
        }
      }
    }
  }

  .listitem_accest{
    color: red;
  }

  .listitem_accest .listitem-body:before{
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.afterscan-operbtnbox{
  margin: 42rpx 40rpx;
  .binduser-btn_line{
    background: @hc-color-primary;
    color: #fff;
    border-radius: 10rpx;
    text-align: center;
    font-size: 36rpx;
    //padding: 22rpx 0;
  }
}