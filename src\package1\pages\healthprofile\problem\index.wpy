<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Moment from 'moment';
  import TopTip from '@/components/toptip/index';
  import { validator } from '@/utils/utils';
  import * as Api from './api';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '是否有问题',
    };

    components = {
      toptip: TopTip,
    };
    
    onLoad(options) {
      const { sex = 'F', pid = '', healthId = '', type = '' } = options || {};
      if (sex) {
        const title = `${sex === 'F' ? '妇科' : '男科'}是否有问题`
        wepy.setNavigationBarTitle({ title });
      }
      this.sex = sex;
      this.pid = pid;
      this.healthId = healthId;
      this.type = type;
    }

    onShow() {
      this.cardList = [];
      this.getMaritalInfo();
      this.queryDiseaseList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      femDiseaseList: [],
      manDiseaseList: [],
      femDisease: [],
      diseaseList: [],
      manDisease: [],
      currentTab: '1',
      checkBoxs: [],
      questionStatus: '1',  //有无问题
      questionRel: [],    // 上传选中的一些疾病id
      questionFaker: [],   //上传id的一个容器

      options: {},
      placeholderColor: '',
      errorColor: 'red',
      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      type: '',
      patientName: '',
      idNo: '',
      pid: '',
      sex: 'F',
      healthId: '',
      isUpdateInfo: false,
    };
     // 查询疾病
    async queryDiseaseList(){
      console.log('history', this)
      const { sex = '' } = this;
      console.log("sex", sex)
      let type = '';
      if( sex == 'F' ){
        type = "f_disease_type";
      }else if( sex == 'M' ) {
        type = "m_disease_type";
      }else{
        type = "anamnesis_record";
      }
      // const param = 
      const { code, data } = await Api.queryDiseaseList({type});
      data.forEach((item, index) => {
        return item.isChecked = false;
      })
      this.diseaseList = data;
      this.getChecked();
      this.$apply();
    }
    //筛选选中的
    getChecked() {
      const { diseaseList = [] } = this;
      let  { questionRel = [], questionStatus = '' } = this;
      console.log('getChecked', questionRel)
      if(questionStatus == 0 && questionRel.length != 0) {
        questionRel = [...new Set(questionRel.split(','))];
        for (let i = 0; i < diseaseList.length; i++) {
          for(let j = 0; j < questionRel.length; j++) {
            if(diseaseList[i].id == questionRel[j]){
              diseaseList[i].isChecked = true ;
            }
          }
          this.diseaseList = diseaseList;
        }
      }
      if (questionStatus) {
        this.currentTab = Number(questionStatus);
      }
      
      this.$apply();
    }

    //上传选中的数据
    getDiseasse(){
      const { sex = '', questionStatus = '', diseaseList = [] } = this;
      // let { questionRel = [] } = this;
      let questionFaker = [];
      let questionRel = '';
      if (questionStatus == 1) {
        for(let i = 0; i < diseaseList.length; i++) {
          if( diseaseList[i].isChecked ){
            let ques = diseaseList[i].id;
            questionFaker.push(ques);
          }
        }
        questionFaker = [...new Set(questionFaker)];
        questionRel = questionFaker.join(',');
        this.questionFaker = questionFaker;
      } else if (questionStatus == 0) {
        let questionFaker = [], questionRel = '';
        this.questionFaker = questionFaker;
      } else if (questionStatus == '2') {
        let questionFaker = [], questionRel = '';
        this.questionFaker = questionFaker;
      }
      this.questionRel = questionRel;
      this.$apply();
      return { questionRel, questionStatus };
    }

    async getMaritalInfo(){
      let patientInfo = {};
      if (this.sex === 'F') {
        patientInfo = this.$parent.femalInfo || {};
      } else {
        patientInfo = this.$parent.manInfo || {};
      }
      console.log('patientInfo', patientInfo);
      const { basicInfo = {}, periodRecord: infoObj = {}, pid = '', healthId = '' } = patientInfo;
      let { questionRel = '', questionStatus = 0 } = patientInfo;
      // this.periodRecord = infoObj;
      if(questionRel == "null"){
        console.log('11111111')
        questionRel = new Array();
        questionRel = [];
        console.log('questionRel', questionRel)
      } else {
        this.questionRel = questionRel;
      }
      this.questionStatus = questionStatus;

      // 设置值
      if (basicInfo.name && basicInfo.idNo) {
        this.name = basicInfo.name || '';
        this.idNo	= basicInfo.idNo || '';
      }
      console.log('设置picker内容')
      if (infoObj.yjzq) {
        this.yjzq = infoObj.yjzq || '';
        this.yjts	= infoObj.yjts || '';
        this.ccnl	= infoObj.ccnl || '';
        this.jlqk = infoObj.jlqk || '';
        this.mcyjsj = infoObj.mcyjsj || '';
        this.healthId = healthId || '';

        // 设置picker内容
        const processList = ['yjzq', 'yjts', 'ccnl', 'jlqk'];
        processList.forEach((name) => {
          const value = infoObj[name];
          const list = this[`${name}List`];
          if (value && list && list.length > 0) {
            list.forEach((item, index) => {
              if (item === value) {
                this[`${name}Index`] = index;
              }
            });
          }
        });
      }
      this.$apply();
    }

    getFormData(){
      const { name = '', idNo = '', pid = '', sex = '', healthId = '', questionStatus = '' } = this;

      let { questionRel = [], questionFaker = [] } = this;
      console.log("questionRel.length", questionRel.length)
      if(questionFaker.length > 0){
        questionFaker = questionFaker.join(",")
      } else {
        questionFaker = 'null';
      }
      console.log("getFormData", questionFaker);
      console.log("getFormDataquestionStatus", questionStatus);
      return {
        name,
        idNo,
        pid,
        patHisNo: pid,
        sex,
        healthId,
        questionStatus,
        questionRel: questionFaker,
      };
    }

    validator(id){
        console.log('开始调用validator', id)
        const validate = {
          patientName: {
            regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
            errTip: '请输入2-8位合法姓名',
          },
          idNo: {
            regexp: (() => {
              const regexp = validator.idCard;
              if(typeof regexp === 'function'){
                return (val) => regexp(val.idNo);
              } else {
                return /^\S+$/;
              }
            })(),
            errTip: '请输入18位身份证',
          },
          // patientAddress: {
          //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          //   errTip: '请输入有效的住址',
          // },
          patientAddress: {
            regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
            errTip: '请输入有效的住址',
          },
          patientMobile: {
            regexp: /^1\d{10}$/,
            errTip: '请输入正确的手机号',
          },
          pid: {
            regexp: /^\d{3,8}/,
            errTip: '请输入正确pid号'
          }
        };


        const value = this.getFormData();

        let hasErr = false;
        for(let o in value){
          const obj = validate[o];
          if(obj && obj.regexp){
            let thisErr = false;
            if(typeof obj.regexp === 'function'){
              const retObj = obj.regexp(value);
              console.log('retObj', retObj)
              if(!retObj.ret){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            } else {
              if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            }
            if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
              this.errorElement[obj.errTarget || o] = true;
              this.toptip = obj.errTip || '';
              const errTimer = setTimeout(() => {
                this.toptip = '';
                this.$apply();
                clearTimeout(errTimer);
              }, 2000);
              break;
            }
          }
        }
        return hasErr;
      }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(url) {
        let { qryType = 2 } = this;
        // 跳转带参数 绑定本人或他人
        const jumpUrl = `${url}?qryType=${this.cardList.length > 0 ? 2 : 1}`
        wepy.navigateTo({ url: jumpUrl });
      },
      //切换有无问题的部分;
      clickTab(e) {
        const { current = '0' } = e.currentTarget.dataset;
        this.currentTab = current;
        this.questionStatus = current;
      },
      // 点击选中box或不选中box
      selectBoxs(idx){
        console.log("idx", idx);
        const { diseaseList = [] } = this;
        diseaseList[idx].isChecked = !diseaseList[idx].isChecked;
        this.diseaseList = diseaseList;
        this.$apply();
      },

    };

    async formSubmit(e){
      let value = this.getFormData();
      console.log('formSubmit', value)
      this.hasErr = this.validator();
      if(this.hasErr){
        return false;
      }
      const questionStatus = this.questionStatus;
      const { questionRel = '' } = this.getDiseasse()
      if (questionStatus == 1 && (!questionRel || questionRel === 'null')) {
        wepy.showModal({
          title: '提示',
          content: '请选择后再提交',
          showCancel: false,
        });
        return;
      }
      let param = param = {
        pid: value.pid || '',
        sex: value.sex || '',
        healthId: value.healthId || '',
        questionStatus: questionStatus || '',
        questionRel: questionRel,
      };
      const { code, data = {}, msg } = await (this.type === 'update' ? Api.updateHealthRecord : Api.addHealthRecord)(param);
      // const { idTypes = [], isNewCard, patCards = [], patientTypes = [], relationTypes = [] } = this.hisConfig
      if (code == 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    }
  }
</script>
