@import "../../../../resources/style/mixins";

page{
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.binduser-btn_line{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}


.bindcard-list{
  background: #fff;
  .bindcard-listitem{
    padding: 30rpx;
    display: flex;
    position: relative;

    &.bindcard-listitem_none{
      display: none;
    }

    &:before{
      content: ' ';
      position: absolute;
      left: 30rpx;
      right: 0;
      top: 0;
      border-top: 2rpx solid @hc-color-border;
    }
    &:first-child:before{
      display: none;
    }

    .listitem-head{
      width: 220rpx;
      display: flex;
      align-items: center;
      .textBreak();

      .list-title{
        flex: 1;
        font-size: 30rpx;
        color: @hc-color-title;
        padding-right: 12rpx;
        position: relative;
        line-height: 1;

        &.require {
          position: relative;
          &:after {
            content: '*';
            color: #F76361;
            font-size: 32rpx;
          }
        }

        &.list-title_select:before{
          content: ' ';
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          border-bottom: 10rpx solid @hc-color-title;
          border-right: 10rpx solid @hc-color-title;
          border-top: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body{
      flex: 1;
      padding-left: 30rpx;
      position: relative;
      .textBreak();
      .m-verifycode-btn {
        position: absolute;
        top: 0;
        right: 10rpx;
        color: #3ECEB6;
        height: 1.4rem;
        line-height: 1.4rem;
        &:active {
          color: #666;
        }
      }
    }
  }

  .listitem_accest{
    color: red;
  }

  .listitem_accest .listitem-body:before{
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.m-content{
  font-size: 30rpx;
  color: @hc-color-title;
}

.m-mt-20{
  margin-top: 20rpx;
}

.o-error{
  color: #ff613b;
}

.o-disabled{
  background: #ddd;
}

.afterscan-cardviewbox{
  background: #fff;
  padding: 20rpx 0 44rpx;
}
.afterscan-cardview{

  margin: auto;
  width: 390rpx;
  border-radius: 10rpx;
  border: 2rpx solid @hc-color-primary;
  overflow: hidden;

  .cardview-imgbox{
    width: 370rpx;
    height: 214rpx;
    overflow: hidden;
    margin: 9rpx auto 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .cardview-img{
      max-width: 100%;
      max-height: 100%;
    }
  }

  .afterscan-reupload{
    background: @hc-color-primary;
    color: #fff;
    text-align: center;
    height: 52rpx;
    line-height: 52rpx;
    font-size: 26rpx;
  }

}

.afterscan-opertip{
  display: flex;
  .opertip-msg{
    flex: 1;
    color: #ffa14e;
    padding: 24rpx 0 36rpx 30rpx;
    font-size: 28rpx;
  }
  .opertip-imgbox{
    display: flex;
    align-items: flex-end;
    .opertip-img{
      width: 80rpx;
      height: 112rpx;
      margin: 0 26rpx;
    }

  }
}

.afterscan-operbtnbox{
  margin: 42rpx 40rpx;
}

.binduser-radio{
  margin-left: 110rpx;
  &:first-child{
    margin-left: 0;
  }
}
.binduser-radio_object{
  transform-origin: 0 30%;
  transform: scale(.7);
}
.binduser-radio_text{
  font-size: 30rpx;
}

.listitem-footer {
  // background-color: @hc-color-primary;
  color: @hc-color-primary;
  .verify-code {
    line-height: 1.4rem;
    height: 1.4rem;
  }
}
.listitem-footer.left-time {
  background-color: #fff;
  color: #8590A6;
  border: 1px #8590A6 solid;
  width: 3em;
  text-align: center;
}