<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class List extends wepy.page {
    config = {
      navigationBarTitleText:'健康科普',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.type = options.type;
      this.typeId = options.typeId;
      if(this.type == 'all'){
        wx.setNavigationBarTitle({
          title: options.title
        });
        this.getType();
      }
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      emptyConfig: {
        show: true,
      },
      typeList: [],
      artic: {},
      activeTypeIdx: 0,
      type:'',
      typeId: '',
      singleId:'',
      iconUrlMap: {
        // 生殖护理小百科(短视频)
        258: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/jzgl.png',
        259: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/byhj.png',
        260: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/zlhj.png',
        261: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/qrqjhj.png',
        262: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/ptyzhj.png',
        263: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/hyhj.png',
        264: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/dphj.png',
        265: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/ysyyd.png',
        266: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/jkxl.png',
        267: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/gfqj.png',
        268: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/nxsz.png',
        269: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/rgsj.png',
        270: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/jkzd.png',
        271: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/zyyy.png',
        // 影像科小百科
        272: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/zygg.png',
        273: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/yqbc.png',
        274: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/fbqbbc.png',
        275: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/fkbc.png',
      }
    };

    async getType() {
      const { code, data = [] } = await Api.getTypeList({pid:1 });
      if (code == 0 && data.length > 0) {
        ~ console.log('this.typeId=', this.typeId);
        const list = data.filter(item=> { return item.typeId == this.typeId });
        console.log('list=', list);
        if (list && list[0] && list[0].subTypeList && list[0].subTypeList.length > 0) {
          this.artic = list[0];
          this.emptyConfig.show = false;
        } else {
          this.artic = {};
          this.emptyConfig.show = true;
        }
        this.$apply();
      }
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(item) {
        let url = `/pages/dynamic/list/index?typeId=${item.typeId}&title=${item.typeName || item.title}`;
        if (item.subTypeList && item.subTypeList.length > 0) {
          url = `/pages/dynamic/secondindex/index?type=all&typeId=${item.typeId}&title=${item.typeName || item.title}`;
        }
        wepy.navigateTo({ url });
      },
    };

    events = {
    };

  }
</script>
