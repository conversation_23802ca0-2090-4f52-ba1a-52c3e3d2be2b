<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy";
import { REQUEST_QUERY } from "@/config/constant";
import empty from "@/components/empty/index";
import watermark from "./com/watermark";
import * as Utils from "@/utils/utils";
import WxsUtils from "../../../wxs/utils.wxs";
import * as Api from "./api";
export default class InhospDaily extends wepy.page {
  config = {
    navigationBarTitleText: "住院费用清单",
    navigationBarBackgroundColor: '#fff',
  };
  wxs = {
    WxsUtils: WxsUtils
  };
  components = {
    empty,
    watermark
  };
  data = {
    tabs: [
      { id: 1, text: "日清单", num: 0 },
      { id: 2, text: "汇总清单", num: 0 }
    ],
    // 当前选项
    activeIndex: 0,
    // 绑定的住院人列表
    inhospitalList: [],
    // 住院人信息
    inpatientInfo: {},
    // picker日期选择器最大值
    pickerEndDate: "",
    // 开始日期
    beginDate: [],
    // 日清单
    dayList: [],
    // 汇总清单
    totalList: []
  };
  computed = {
    total: () => {
      let totalDay = 0,
        totalAll = 0;
      for (let i in this.dayList) {
        const item = this.dayList[i];
        totalDay += parseInt(item.itemTotalFee);
      }
      for (let i in this.totalList) {
        const item = this.totalList[i];
        totalAll += parseInt(item.itemTotalFee);
      }
      return {
        totalDay,
        totalAll
      };
    }
  };
  onLoad(options) {
    this.options = options;
    this.pickerEndDate = Utils.getFormatDate(-1, "-");
    this.beginDate = options.inDate || Utils.getFormatDate(-1, "-");
    this.getDayList();
    this.getTotalList();
  }
  onShow() {}
  // 获取日清单
  async getDayList() {
    const {
      inhospitalNo = "",
      patHisNo = "",
      admissionNum = ""
    } = this.options;
    const beginDate = this.beginDate;
    const { code, data = {}, msg } = await Api.getInHospitalDetail({
      patHisNo,
      admissionNum,
      grId: inhospitalNo,
      beginDate
    });
    if (code !== 0) {
      return;
    }
    this.dayList = data.items || [];
    this.$apply();
  }
  // 获取总清单
  async getTotalList() {
    const {
      inhospitalNo = "",
      patHisNo = "",
      admissionNum = ""
    } = this.options;
    const { code, data = {}, msg } = await Api.getInHospitalDetail({
      patHisNo,
      admissionNum,
      grId: inhospitalNo,
      beginDate: ""
    });
    if (code !== 0) {
      return;
    }
    this.totalList = data.items || [];
    this.$apply();
  }
  methods = {
    checkThis(index) {
      this.activeIndex = index;
    },
    beginDateChange(e) {
      this.beginDate = e.detail.value || "";
      this.getDayList();
    }
  };
}
</script>
