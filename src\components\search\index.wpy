<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Empty from "@/components/empty/index";
import * as Utils from "@/utils/utils";
import * as Api from "./api";

export default class Search extends wepy.component {
  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    // 搜索状态
    searchFocus: false,
    // 搜索结果
    deptList: [],
    doctorList: [],
    // 搜索内容
    searchValue: "",
    emptyConfig: {}
  };

  components = {
    empty: Empty
  };

  props = {
    searchFocus: {
      type: Boolean,
      default: false,
      twoWay: true
    },
    isOnline: {
      type: Boolean,
      default: false,
      twoWay: true
    },
    areaCode: {
      type: String,
      default: false
    }
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    /**
     * 开启搜索状态
     * @returns {string}
     */
    bindSearchFocus() {
      this.$emit("search-toggle-focus", true);
      this.searchFocus = true;
      return "";
    },
    /**
     * 关闭搜索状态
     * @returns {string}
     */
    bindSearchBlur() {
      this.$emit("search-toggle-focus", false);
      this.searchFocus = false;
      // 取消搜索，清空数据
      this.deptList = [];
      this.doctorList = [];
      this.searchValue = "";
      return "";
    },
    /**
     * 输入搜索内容
     * @param e
     * @returns {string}
     */
    bindSearchInput(e) {
      let { value } = e.detail;
      this.searchValue = value;

      if (!value) {
        this.deptList = [];
        this.doctorList = [];
        return "";
      }

      // 获取搜索结果
      clearTimeout(this.searchTimer || "");

      this.searchTimer = setTimeout(() => {
        this.search(value);
      }, 200);

      return value;
    },
    /**
     * 跳转到医生详情页
     * @param item
     */
    bindToDocInfo(item) {
      const { deptId, doctorId } = item;
      const query = Utils.jsonToQueryString({
        doctorId,
        deptId,
        areaCode: this.areaCode
      });
      wepy.navigateTo({
        url: `/pages/register/docinfo/index?${query}`
      });
    },
    /**
     * 跳转到医生列表页
     * @param item
     */
    bindToDocList(item) {
      const { deptId, deptName } = item;
      const query = Utils.jsonToQueryString({
        deptName,
        deptId,
        areaCode: this.areaCode
      });
      wepy.navigateTo({
        url: `/pages/register/doclist/index?${query}`
      });
    },
    /**
     * 查看更多
     */
    bindMore() {
      const word = this.searchValue;
      const query = Utils.jsonToQueryString({ word });
      wepy.navigateTo({
        url: `/pages/register/search/index?${query}`
      });
    }
  };

  async search(word) {
    const { code, data = {}, msg } = await Api.search({
      inputData: word,
      transParam: JSON.stringify({ areaCode: this.areaCode })
    });
    if (this.searchValue !== word) {
      // 搜索结果与当前输入框不等，数据不录入
      return;
    }
    const { deptList = [], doctorList = [] } = data;
    this.deptList = deptList;
    this.doctorList = doctorList;

    this.$apply();
  }
}
</script>
