<view class="p-page">
  <view class="top-msg">如需查询2023年1月1日至2023年10月10日的记录，请联系0731-84805365咨询。</view>
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{WxsUtils.convertListStatus(item.status)}}"
          @tap="bindGoDetail({{item}})"
        >
          <!-- <view class="item-icon">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
          </view>
          <view class="item-main">
            <view class="main-tit">
              <text>{{item.statusName}}</text>
              <text class="unit-label" wx:if="{{item.refundStatus == 1 || item.refundStatus == 2 || item.refundStatus == 3}}">有退款</text>
            </view>
            <view class="main-txt">{{item.patientName}}</view>
            <view class="main-txt">{{item.deptName}}/{{item.doctorName}}</view>
          </view>
          <view class="item-extra">
            <view class="extra-tit">￥{{WxsUtils.formatMoney(item.totalRealFee,100)}}</view>
            <view class="extra-tit">就诊时间</view>
            <view class="extra-txt">{{item.visitDate + ' ' + item.visitBeginTime + '-' + item.visitEndTime}}</view>
          </view> -->

          <view class="item-status">
            <view class="item-icon" wx:if="{{WxsUtils.convertListStatus(item.status) !== 'lock'}}">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
            </view>
            <view class="item-status-icon" wx:else>
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-lock.png"></image>
            </view>
            <text>{{item.statusName}}</text>
          </view>
          <view class="item-extra">就诊人：{{item.patientName}}</view>
          <view class="item-extra">医生：{{item.deptName}} {{item.doctorName}}</view>
          <view class="item-extra">就诊时间：{{item.visitDate + ' ' + item.visitBeginTime + '-' + item.visitEndTime}}</view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>