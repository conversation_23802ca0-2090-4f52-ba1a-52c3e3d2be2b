const path = require("path");
const LessPluginAutoPrefix = require("less-plugin-autoprefix");
var prod = process.env.NODE_ENV === "production";
var apiEnv = process.env.API_ENV || "";

module.exports = {
  // target: 'dist-huangliang',
  wpyExt: ".wpy",
  eslint: false,
  // cliLogs: !prod,
  build: {},
  resolve: {
    alias: {
      "@": path.join(__dirname, "src"),
      "hc-private-src": path.join(__dirname, "src"),
    },
    aliasFields: ["wepy"],
    modules: ["node_modules"],
  },
  compilers: {
    less: {
      compress: prod,
      plugins: [
        new LessPluginAutoPrefix({
          browsers: ["Android >= 2.3", "Chrome > 20", "iOS >= 6"],
        }),
      ],
    },
    /*sass: {
      outputStyle: 'compressed'
    },*/
    babel: {
      sourceMap: false,
      presets: ["env"],
      plugins: [
        "transform-class-properties",
        "transform-decorators-legacy",
        "transform-object-rest-spread",
        "transform-export-extensions",
      ],
    },
  },
  plugins: {
    // 将 css 文件里的图片转换为 base64
    img2base64: {},
    replace: {
      filter: /\.(wxml|wpy|js|wxss)$/,
      config: [
        {
          find: /REPLACE\_IMG\_DOMAIN/g,
          // replace: "https://wechatdev.jiahuiyiyuan.com/merchant",// 测试域名
          replace: "https://wechat.jiahuiyiyuan.com/merchant" // 正式域名
        },
        {
          find: /REPLACE\_API\_ENV/g,
          replace: apiEnv,
        },
        {
          find: /REPLACE\_STATIC\_DOMAIN/g,
          // replace: "https://wechatdev.jiahuiyiyuan.com/merchant/his-miniapp/icon/",
          replace: "https://wechat.jiahuiyiyuan.com/merchant/his-miniapp/icon/",
        },
        {
          find: /REPLACE\_EHOSPITAL\_DOMAIN/g,
          // replace: "https://wechatdev.jiahuiyiyuan.com/merchant/ehospital",
          replace: "https://wechat.jiahuiyiyuan.com/merchant/ehospital",
        },
        {
          find: /NODE\_ENV/g,
          replace: process.env.NODE_ENV,
        },
      ],
    },
  },
  appConfig: {
    noPromiseAPI: ["createSelectorQuery"],
  },
};

if (prod) {
  // 压缩sass
  // module.exports.compilers['sass'] = {outputStyle: 'compressed'}

  // 压缩js
  module.exports.plugins = {
    uglifyjs: {
      filter: /\.js$/,
      config: {},
    },
    replace: {
      filter: /\.(wxml|wpy|js|wxss)$/,
      config: [
        {
          find: /REPLACE\_IMG\_DOMAIN/g,
          // replace: `https://wechatdev.jiahuiyiyuan.com/merchant`,
          replace: `https://wechat.jiahuiyiyuan.com/merchant`,
        },
        {
          find: /REPLACE\_API\_ENV/g,
          replace: apiEnv,
        },
        {
          find: /REPLACE\_STATIC\_DOMAIN/g,
          // replace: "https://wechatdev.jiahuiyiyuan.com/merchant/his-miniapp/icon",
          replace: "https://wechat.jiahuiyiyuan.com/merchant/his-miniapp/icon",
        },
        {
          find: /REPLACE\_EHOSPITAL\_DOMAIN/g,
          // replace: "https://wechatdev.jiahuiyiyuan.com/merchant/ehospital",
          replace: "https://wechat.jiahuiyiyuan.com/merchant/ehospital",
        },
        {
          find: /NODE\_ENV/g,
          replace: process.env.NODE_ENV,
        },
      ],
    },
    // imagemin: {
    //   filter: /\.(jpg|png|jpeg)$/,
    //   config: {
    //     jpg: {
    //       quality: 80
    //     },
    //     png: {
    //       quality: 80
    //     }
    //   }
    // },
    img2base64: {},
  };
}
