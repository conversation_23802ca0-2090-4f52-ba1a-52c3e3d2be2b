<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Utils from "@/utils/utils";
import Search from "@/components/search/index";
import DeptList from "@/components/deptlist/index";
import BottomLogo from '@/components/bottomlogo/index';
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "科室列表"
  };

  components = {
    search: Search,
    "dept-list": DeptList,
    "bottom-logo": BottomLogo
  };

  async onLoad() {
    const { deptType = "1", areaCode = '' } = this.$wxpage.options;
    this.deptType = deptType;
    this.areaCode = areaCode;
    if(deptType == 0){
      this.searchHiden = true;
      await this.getEhisAllMainDept();
    } else {
      await this.getAllMainDept();
    }
    // await this.deptListFull();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    areaCode: '',
    searchHiden: false, // 隐藏搜索
    searchFocus: false,
    // 科室类别，0为互联网科室，1为所有科室
    deptType: "1",
    // 科室列表
    deptList: {},
    // 收藏列表
    favoriteList: [],
    // 历史挂号记录
    hisDoctorList: [],
    // 选中tab
    activeIdx: 0,
    // 显示历史记录
    showHistory: true,
    // 一级科室
    deptListCustom: [
      // { deptId: 1, deptName: "初诊挂号", hasChild: '0' },
      // { deptId: 2, deptName: "复诊挂号", hasChild: '0' },
      // { deptId: 6, deptName: "VIP门诊", hasChild: '0' },
      // { deptId: 3, deptName: "互联网门诊", hasChild: '0' },
      // { deptId: 4, deptName: "长沙市健康民生项目", hasChild: '0' },
      // { deptId: 5, deptName: "免费筛查号", hasChild: '0' }
    ],
    // 院区列表
    areaList: [
      {
        areaName: '全部',
        areaCode: '',
      },
      {
        areaName: '麓谷总院',
        areaCode: 2,
      },
      {
        areaName: '开福分院',
        areaCode: 1,
      }
    ],
    showModal: false,
    toInfoQuery: '',
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    bindTapScan() {
      console.log(1);
    },
    bindTapNotice() {
      console.log(2);
    },
    changeTab(item) {
      const { areaCode = '' } = item;
      this.areaCode = areaCode;
      wepy.showLoading({
        title: '加载中',
        mask: true,
      });
      setTimeout(()=>{
        wepy.hideLoading();
      }, 200);
    },
    cancelModal() {
      this.showModal = false;
      this.toInfoQuery = '';
      this.$apply();
    },
    sureModal() {
      this.showModal = false;
      this.$apply();
      wepy.navigateTo({
        url: `/pages/register/doclist/index?${this.toInfoQuery}`
      });
    },
  };

  events = {
    "deptlist-tap-doctor": function(item) {
      const { deptId, doctorId } = item || {};
      wepy.navigateTo({
        url: `/pages/register/docinfo/index?${Utils.jsonToQueryString({
          deptId,
          doctorId,
          areaCode: this.areaCode || '',
        })}`
      });
    },
    "deptlist-tap-dept": function(item) {
      const { deptId, deptName, hasChild = '', address = '', tel = '', skill = '' } = item || {};
      if(hasChild == '0'){
        this.deptListFull(deptId);
        this.$apply();
        return;
      }
      const { deptType = "" } = this;

      const query = Utils.jsonToQueryString({
        deptName,
        deptId,
        areaCode: this.areaCode || ''
      });
      if (deptName == '遗传医师门诊' || deptId == 3161) {
        this.showModal = true;
        this.toInfoQuery = query;
        this.$apply();
        return;
      }
      wepy.navigateTo({
        url: `/pages/register/doclist/index?${query}`
      });

      // if (address == '遗传') {
      //   wepy.navigateTo({
      //     url: `/pages/register/extra/index?${Utils.jsonToQueryString({
      //       deptId,
      //       deptName,
      //       deptType,
      //       note: tel,
      //       skill,
      //       areaCode: this.areaCode || '',
      //     })}`
      //   })
      // } else {
      //   console.log(111);
      //   wepy.navigateTo({
      //     url: `/pages/register/doclist/index?${Utils.jsonToQueryString({
      //       deptId: '1174',
      //       deptName: '龚斐教授团队门诊',
      //       deptType,
      //       skill: '龚斐教授团队门诊就诊前由团队医生整理病例资料',
      //       areaCode: this.areaCode || '',
      //     })}`
      //   });
      // }
    }
  };

  // 获取现场门诊一级科室
  async getAllMainDept() {
    const { code, data = [], msg } = await Api.getAllMainDept();
    if (code != 0) {
      return;
    }
    const { deptList } = data;
    this.deptListCustom = deptList;
    this.deptList = data;
    this.$apply();
  };

  // 获取互联网医院一级科室
  async getEhisAllMainDept() {
    const { code, data = [], msg } = await Api.getEhisAllMainDept();
    if (code != 0) {
      return;
    }
    this.deptListCustom = data;
    this.$apply();
  };

  async deptListFull( deptId = '') {
    let list = {};
    list.deptList = this.deptListCustom.filter(item => {
      // if (this.deptType == "0") {
      //   return item.deptId == "3";
      // } else if (this.deptType == "1") {
      //   return item.deptId != "3";
      // } else {
      //   return item;
      // }
      return item;
    });
    if(list.deptList.length == 0){
      return;
    }
    const extFields = deptId || list.deptList[0].deptId;
    // const { code, data = {}, msg } = await Api.deptListFull({ extFields });
    // if (code != 0) {
    //   return;
    // }
    // let { deptList = {}, favoriteList = [], hisDoctorList = [] } = data;
    // if (favoriteList.length > 0 || hisDoctorList.length > 0) {
    //   this.activeIdx = -1;
    // } else {
    //   this.activeIdx = 0;
    // }
    // list.levelDept = parseInt(deptList.levelDept) + 1;
    const index = list.deptList.findIndex(item => item.deptId == extFields ) || 0;
    if(deptId){
      this.activeIdx = index;
    }
    list.deptList[index].deptList = deptList.deptList;
    this.deptList = list;
    // this.favoriteList = favoriteList;
    // this.hisDoctorList = hisDoctorList;
    this.$apply();
  }
}
</script>
