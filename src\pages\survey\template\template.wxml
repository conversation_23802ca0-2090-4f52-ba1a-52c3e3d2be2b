<!-- 各个问题类型模板 -->
<!-- 单选题 -->
<template name="individual">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{individual.titleNum}}、{{individual.questionsTitle}}（单选）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{individual.required == 1}}"></image>
    </view>
    <radio-group class="radio-group" data-title-id="{{individual.titleId}}" data-questions-type="{{individual.questionsType}}" bindchange="bindValueChange" >
      <view class="radio-group-item" wx:for="{{individual.optionList}}" wx:for-index="optionsIdx" wx:key="key">
        <view>
          <radio checked="{{item.checked}}" value="{{item.optionNum}}" color="#3ECEB6" disabled="{{readonly == 1}}" />
          <label>{{item.optionContent}}</label>
        </view>
        <!-- 选项后接文本框 -->
        <block wx:if="{{item.optionType == '1' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-input">
              <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{individual.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true" disabled="{{readonly == 1}}" class="second-textarea"></textarea>
            </view>
          </view>
        </block>
        <!-- 选项后接复选框 -->
        <block wx:if="{{item.optionType == '2' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-checkboxes">
              <view class="second-checkbox-item" wx:for="{{item.secondOptionList}}" wx:for-item="secOpt" wx:key="secIdx" wx:for-index="secIdx">
                <checkbox disabled="{{readonly == 1}}" value="{{secOpt}}" checked="{{item.secondCheckedMap[secOpt]}}" color="#3ECEB6" bindtap="secondCheckboxChange" data-title-id="{{individual.titleId}}" data-parent-option="{{item.optionNum}}" data-sec-option="{{secOpt}}" />
                <text>{{secOpt}}</text>
              </view>
              <view style="clear:both;"></view>
            </view>
          </view>
        </block>
        <!-- 选项后接多值填空 -->
        <block wx:if="{{item.optionType == '3' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-fill-blank">
              <view class="fill-blank-content">
                <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                  <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                  <text wx:else class="fill-blank-underline">{{part.value || '____'}}</text>
                </block>
              </view>
            </view>
          </view>
        </block>
        <view wx:if="{{item.haveRemarkFrame == 1 && item.checked}}" class="radio-group-ext-panel">
          <block wx:for="{{item.extInputList}}" wx:for-item="ext" wx:for-index="extIdx" wx:key="key">
            <view class="radio-group-ext-input-item">
              <label>{{ext.label}}</label>
              <input maxlength="-1" placeholder="请输入" placeholder-style="color:#bbb;" data-title-id="{{individual.titleId}}" data-idx="{{extIdx}}" value="{{ext.value}}" data-optionnum="{{item.optionNum}}" bindinput="bindExtAnswerRemarkChange" bindblur="bindExtAnswerRemarkChange" />
            </view>
          </block>
        </view>
        <view>
          <image mode="widthFix" src="{{item.imageUrl}}" wx:if="{{item.imageUrl}}"></image>
        </view>
      </view>
    </radio-group>
  </view>
</template>
<!-- 多选题 -->
<template name="multiple">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{multiple.titleNum}}、{{multiple.questionsTitle}}(多选，最多{{multiple.maxAnswer}}项)
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{multiple.required == 1}}"></image>
    </view>
    <view>
      <view class="checkbox" wx:for="{{multiple.optionList}}" wx:key="key">
        <view class="checkbox-item">
          <checkbox disabled="{{readonly == 1}}" value="{{item.optionNum}}" checked="{{item.checked}}" color="#3ECEB6" bindtap="checkboxChange" data-cur-option="{{item.optionNum}}" data-option-group="{{item.optionGroup||''}}" data-title-id="{{multiple.titleId}}" />
          {{item.optionContent}}
        </view>
        <view>
          <image mode="widthFix" src="{{item.imageUrl}}" wx:if="{{item.imageUrl}}"></image>
        </view>
        <!-- 选项后接文本框 -->
        <block wx:if="{{item.optionType == '1' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-input">
              <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{multiple.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true" disabled="{{readonly == 1}}" class="second-textarea"></textarea>
            </view>
          </view>
        </block>
        <!-- 选项后接复选框 -->
        <block wx:if="{{item.optionType == '2' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-checkboxes">
              <view class="second-checkbox-item" wx:for="{{item.secondOptionList}}" wx:for-item="secOpt" wx:key="secIdx" wx:for-index="secIdx">
                <checkbox disabled="{{readonly == 1}}" value="{{secOpt}}" checked="{{item.secondCheckedMap[secOpt]}}" color="#3ECEB6" bindtap="secondCheckboxChange" data-title-id="{{multiple.titleId}}" data-parent-option="{{item.optionNum}}" data-sec-option="{{secOpt}}" />
                <text>{{secOpt}}</text>
              </view>
              <view style="clear:both;"></view>
            </view>
          </view>
        </block>
        <!-- 选项后接多值填空 -->
        <block wx:if="{{item.optionType == '3' && item.checked}}">
          <view class="second-option-wrap">
            <view class="second-option-dot"></view>
            <view class="second-option-fill-blank">
              <view class="fill-blank-content">
                <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                  <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                  <text wx:else class="fill-blank-underline">{{part.value || '____'}}</text>
                </block>
              </view>
            </view>
          </view>
        </block>
        <view wx:if="{{item.haveRemarkFrame == 1 && item.checked}}">
          <input maxlength="20" placeholder="{{item.frameDefaultText}}" placeholder-style="color:#bbb;" data-title-id="{{multiple.titleId}}" value="{{item.answerRemark}}" data-optionnum="{{item.optionNum}}" bindinput="bindAnswerRemarkChange" bindblur="bindAnswerRemarkChange" />
        </view>
      </view>
    </view>

    <!-- 附件显示 -->
    <view wx:if="{{multiple.fileFlg === '1' && multiple.uploadedFiles && multiple.uploadedFiles.length > 0}}" class="file-display-section">
      <view class="file-display-title">附件：</view>
      <view class="file-display-grid">
        <view wx:for="{{multiple.uploadedFiles}}" wx:for-item="file" wx:key="url" class="file-display-item" bindtap="viewFile" data-url="{{file.url}}">
          <!-- 图片文件显示缩略图 -->
          <view wx:if="{{file.type === 'image'}}" class="file-thumbnail image-thumbnail">
            <image src="{{file.url}}" mode="aspectFill" class="thumbnail-image" />
            <view class="file-type-badge">图片</view>
          </view>
          <!-- PDF文件显示图标 -->
          <view wx:else class="file-thumbnail pdf-thumbnail">
            <view class="pdf-icon-large">PDF</view>
            <view class="file-type-badge">PDF</view>
          </view>
          <text class="file-name-grid">{{file.fileName}}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<!-- 文本框多行填空 -->
<template name="multipleBlank">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{multipleBlank.titleNum}}、{{multipleBlank.questionsTitle}}（填空）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{multipleBlank.required == 1}}"></image>
    </view>
    <textarea disabled="{{readonly == 1}}" class="multiple-area" placeholder="请在此输入" placeholder-style="color:#bbb;" maxlength="-1" auto-height data-title-id="{{multipleBlank.titleId}}" data-questions-type="{{multipleBlank.questionsType}}" value="{{multipleBlank.answerContent}}" bindinput="bindValueChange" bindblur="bindValueChange" />

    <!-- 附件显示 -->
    <view wx:if="{{multipleBlank.fileFlg === '1' && multipleBlank.uploadedFiles && multipleBlank.uploadedFiles.length > 0}}" class="file-display-section">
      <view class="file-display-title">附件：</view>
      <view class="file-display-grid">
        <view wx:for="{{multipleBlank.uploadedFiles}}" wx:for-item="file" wx:key="url" class="file-display-item" bindtap="viewFile" data-url="{{file.url}}">
          <!-- 图片文件显示缩略图 -->
          <view wx:if="{{file.type === 'image'}}" class="file-thumbnail image-thumbnail">
            <image src="{{file.url}}" mode="aspectFill" class="thumbnail-image" />
            <view class="file-type-badge">图片</view>
          </view>
          <!-- PDF文件显示图标 -->
          <view wx:else class="file-thumbnail pdf-thumbnail">
            <view class="pdf-icon-large">PDF</view>
            <view class="file-type-badge">PDF</view>
          </view>
          <text class="file-name-grid">{{file.fileName}}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<!-- 星星打分 -->
<template name="starScore">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{starScore.titleNum}}、{{starScore.questionsTitle}}（打分）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{starScore.required == 1}}"></image>
    </view>
    <view class="list-items" style="text-align: center; display: flex; align-item: center; padding: 60rpx 0 85rpx;">
      <image wx:for="{{starScore.optionList}}" style="width: 58rpx; height: 54rpx; margin-right: 18rpx" src="/resources/images/star{{(index < score[starScore.titleId] && score[starScore.titleId] >= 0) ? '-active' : ''}}.png" alt="" data-title-id="{{starScore.titleId}}" bindtap="bindValueChange" data-questions-type="{{starScore.questionsType}}" data-value="{{item.optionNum}}" data-num="{{index+1}}" wx:key="key" />
      <view class="start-lev-txt" style="flex:1; text-align: right; line-height: 54rpx;{{(!starScore.questionScore || starScore.questionScore) < 0 ? 'color: #888;' : ''}}">
        {{score[starScore.titleId] > 0 ? starScore.optionList[score[starScore.titleId] - 1].optionContent : '点击星星评分'}}
      </view>
    </view>

    <!-- 附件显示 -->
    <view wx:if="{{starScore.fileFlg === '1' && starScore.uploadedFiles && starScore.uploadedFiles.length > 0}}" class="file-display-section">
      <view class="file-display-title">附件：</view>
      <view class="file-display-grid">
        <view wx:for="{{starScore.uploadedFiles}}" wx:for-item="file" wx:key="url" class="file-display-item" bindtap="viewFile" data-url="{{file.url}}">
          <!-- 图片文件显示缩略图 -->
          <view wx:if="{{file.type === 'image'}}" class="file-thumbnail image-thumbnail">
            <image src="{{file.url}}" mode="aspectFill" class="thumbnail-image" />
            <view class="file-type-badge">图片</view>
          </view>
          <!-- PDF文件显示图标 -->
          <view wx:else class="file-thumbnail pdf-thumbnail">
            <view class="pdf-icon-large">PDF</view>
            <view class="file-type-badge">PDF</view>
          </view>
          <text class="file-name-grid">{{file.fileName}}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<!-- 单行填空 -->
<template name="singleBlank">
  <view class="template-panle single" id="title-{{index}}">
    <view>
      {{singleBlank.titleNum}}、{{singleBlank.questionsTitle}}（填空）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{singleBlank.required == 1}}"></image>
    </view>
    <input disabled="{{readonly == 1}}" class="single-area" placeholder="请在此输入" placeholder-style="color:#bbb;" maxlength="20" auto-height data-title-id="{{singleBlank.titleId}}" data-questions-type="{{singleBlank.questionsType}}" value="{{singleBlank.answerContent}}" bindinput="bindValueChange" bindblur="bindValueChange" />
  </view>
</template>
<!-- 手机号 -->
<template name="mobileArea">
  <view class="template-panle single" id="title-{{index}}">
    <view>
      {{mobileArea.titleNum}}、{{mobileArea.questionsTitle}}（手机号）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{mobileArea.required == 1}}"></image>
    </view>
    <input disabled="{{readonly == 1}}" class="single-area" type="number" placeholder="请输入手机号" placeholder-style="color:#bbb;" maxlength="11" auto-height data-title-id="{{mobileArea.titleId}}" data-questions-type="{{mobileArea.questionsType}}" value="{{mobileArea.answerContent}}" bindinput="bindValueChange" bindblur="bindValueChange" />
  </view>
</template>
<!-- 姓名 -->
<template name="patName">
  <view class="template-panle single" id="title-{{index}}">
    <view>
      {{patName.titleNum}}、{{patName.questionsTitle}}（姓名）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{patName.required == 1}}"></image>
    </view>
    <input disabled="{{readonly == 1}}" class="single-area" placeholder="请输入姓名" placeholder-style="color:#bbb;" maxlength="30" auto-height data-title-id="{{patName.titleId}}" data-questions-type="{{patName.questionsType}}" value="{{patName.answerContent}}" bindinput="bindValueChange" bindblur="bindValueChange" />
  </view>
</template>
<!-- 身份证 -->
<template name="idNumber">
  <view class="template-panle single" id="title-{{index}}">
    <view>
      {{individual.titleNum}}、{{idNumber.questionsTitle}}（身份证）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{idNumber.required == 1}}"></image>
    </view>
    <input disabled="{{readonly == 1}}" class="single-area" type="idcard" placeholder="请输入身份证号" placeholder-style="color:#bbb;" maxlength="18" auto-height data-title-id="{{idNumber.titleId}}" data-questions-type="{{idNumber.questionsType}}" value="{{idNumber.answerContent}}" bindinput="bindValueChange" bindblur="bindValueChange" />
  </view>
</template>
<!-- 下拉单选 -->
<template name="selectRadio">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{selectRadio.titleNum}}、{{selectRadio.questionsTitle}}
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{selectRadio.required == 1}}"></image>
    </view>
    <view class="listitem-head" data-title-id="{{selectRadio.titleId}}" data-question-type="{{selectRadio.questionsType}}" bindtap="showOptions">
      <view class="list-title list-title_select">
        {{selectRadio.isHide?'请选择':selectRadio.showValue}}
      </view>
    </view>
    <view class="anction-box" wx:if="{{selectRadio.showAnction}}">
      <view class="anction-mask"></view>
      <view class="anction-content">
        <view class="top">
          <view class="content-item" wx:for="{{selectRadio.optionList}}">
            <view>{{item.optionContent}}</view>
            <view>
              <image mode="widthFix" src="{{item.imageUrl}}" wx:if="{{item.imageUrl}}"></image>
            </view>
          </view>
        </view>
        <view class="cancel" bindtap="cancelAnction" data-title-id="{{selectRadio.titleId}}">
          取消
        </view>
      </view>
    </view>
  </view>
</template>
<!-- 段落说明 -->
<template name="explanation">
  <view class="template-panle" id="title-{{index}}">
    <view class="bold">{{explanation.questionsTitle}}</view>
  </view>
</template>
<!-- 签名 -->
<template name="sign">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{sign.titleNum}}、{{sign.questionsTitle}}
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{sign.required == 1}}"></image>
    </view>
    <textarea readOnly class="multiple-area" placeholder="请在此输入（最多200字）" placeholder-style="color:#bbb;" maxlength="200" auto-height data-title-id="{{sign.titleId}}" data-questions-type="{{sign.questionsType}}" bindinput="bindValueChange" bindblur="bindValueChange" value="{{sign.answerContent}}" />
  </view>
</template>
<!-- 辖区 -->
<template name="regionArea">
  <view class="template-panle single address-area" id="title-{{index}}">
    <view>
      {{regionArea.titleNum}}、{{regionArea.questionsTitle}}（辖区）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{regionArea.required == 1}}"></image>
    </view>
    <block wx:if="{{urlAddress}}">
      <view class="picker" style="color:'#bbb'">{{urlAddress}}</view>
    </block>
    <block wx:else>
      <picker disabled="{{readonly == 1}}" mode="region" bindchange="bindRegionChange" data-title-id="{{regionArea.titleId}}" data-questions-type="{{regionArea.questionsType}}" value="{{regionArea.region || []}}" custom-item="{{customItem}}" class="single-area">
        <view class="picker" style="color:{{regionArea.region && regionArea.region.length > 0 && readonly == 1 ?'':'#bbb'}}">
          <block wx:if="{{!regionArea.region && regionArea.answerContent}}">
            {{regionArea.answerContent}}
          </block>
          <block wx:elif="{{regionArea.region && regionArea.region.length > 0}}">
            {{regionArea.region[0]}}{{regionArea.region[1]}}{{regionArea.region[2]}}
          </block>
          <block wx:else>请选择</block>
        </view>
      </picker>
    </block>
  </view>
</template>
<!-- 地址 -->
<template name="addressArea">
  <view class="template-panle single address-area" id="title-{{index}}">
    <view>
      {{addressArea.titleNum}}、{{addressArea.questionsTitle}}（地址）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{addressArea.required == 1}}"></image>
    </view>
    <picker disabled="{{readonly == 1}}" mode="region" bindchange="bindRegionChange" data-title-id="{{addressArea.titleId}}" value="{{addressArea.region || []}}" custom-item="{{customItem}}" class="single-area">
      <view class="picker" style="color:{{addressArea.region && addressArea.region.length > 0  && readonly == 1 || addressArea.answerContent ?'':'#bbb'}}">
        <block wx:if="{{!addressArea.region && addressArea.answerContent}}">
          {{addressArea.answerContent}}
        </block>
        <block wx:elif="{{addressArea.region && addressArea.region.length > 0}}">
          {{addressArea.region[0]}}{{addressArea.region[1]}}{{addressArea.region[2]}}
        </block>
        <block wx:else>请选择</block>
      </view>
    </picker>
    <input type="address" wx:if="{{addressArea.region && addressArea.region.length > 0}}" class="address-input" placeholder="请输入详细地址，具体到街道小区" placeholder-style="color:#bbb;" data-title-id="{{addressArea.titleId}}" data-questions-type="{{addressArea.questionsType}}" data-questions-region="{{addressArea.region}}" bindinput="bindValueChange" bindblur="bindValueChange" />
  </view>
</template>
<!-- 日期选择 -->
<template name="dateSelect">
  <view class="template-panle" id="title-{{index}}">
    <view>
      {{dateSelect.titleNum}}、{{dateSelect.questionsTitle}}（日期选择）
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{dateSelect.required == 1}}"></image>
    </view>
    <picker mode="date" value="{{date}}" start="2015-09-01" bindchange="bindValueChange" data-title-id="{{dateSelect.titleId}}" data-questions-type="{{dateSelect.questionsType}}" value="{{dateSelect.answerContent}}" disabled="{{readonly == 1}}">
      <view class="picker date-select">{{dateSelect.answerContent || '请选择日期'}}</view>
    </picker>
  </view>
</template>
<!-- 新冠风险地区显示 -->
<template name="riskArea">
  <view class="template-panle single" id="title-{{index}}">
    <navigator url="/pages/survey/riskArea/index">
      {{riskArea.titleNum}}、
      <view class="link-item">{{riskArea.questionsTitle}}</view>
    </navigator>
  </view>
</template>

<!-- 填空题（多空填值） -->
<template name="multipleValueBlank">
  <view class="template-panle question-item-fillblank" id="title-{{index}}">
    <text class="question-title question-title-fillblank">
      {{multipleValueBlank.titleNum}}、
    </text>
    <view class="fill-blank-title">
      <!-- 遍历填空项 -->
      <block wx:for="{{multipleValueBlank.blankValues}}" wx:key="index">
        <view class="fill-blank-row">
          <text class="fill-blank-text">{{index+1}}. </text>
          <input 
            class="fill-blank-input" 
            value="{{item}}" 
            disabled="{{readonly == 1}}" 
            placeholder="请输入" 
            placeholder-style="color:#bbb;" 
            maxlength="-1" 
            data-title-id="{{multipleValueBlank.titleId}}" 
            data-idx="{{index}}" 
            bindinput="bindMultipleValueBlankChange" 
            bindblur="bindMultipleValueBlankChange"
          />
        </view>
      </block>
    </view>
  </view>
</template>

<!-- 填空题（带占位符） -->
<template name="placeholderBlank">
  <view class="template-panle" id="title-{{index}}">
    <view class="placeholder-blank-title-row">
      <text>{{placeholderBlank.titleNum}}、</text>
      <rich-text nodes="{{placeholderBlank.formattedContent}}" class="placeholder-blank-text"></rich-text>
      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{placeholderBlank.required == 1}}"></image>
    </view>
    
    <block wx:if="{{!readonly}}">
      <view class="placeholder-blank-inputs">
        <view class="placeholder-blank-input-item" wx:for="{{placeholderBlank.blankValues}}" wx:key="index">
          <text class="placeholder-blank-label">{{index+1}}:</text>
          <input 
            class="placeholder-blank-input" 
            value="{{item}}" 
            disabled="{{readonly == 1}}" 
            placeholder="请输入" 
            placeholder-style="color:#bbb;" 
            maxlength="-1" 
            data-title-id="{{placeholderBlank.titleId}}" 
            data-idx="{{index}}" 
            bindinput="bindPlaceholderBlankChange" 
            bindblur="bindPlaceholderBlankChange"
          />
          <text>（填空）</text>
        </view>
      </view>
    </block>
  </view>
</template>