<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';

  import { TYPE_MAP, CODE_TYPE, TIME_MAP } from '@/config/constant';
  import DetailStatus from '@/components/detailstatus/index';
  import * as Utils from '@/utils/utils';

  import BasicDetail from './com/basicDetail';
  import * as Api from './api';

  const NORMAL_MAP = ['S', 'F', 'L', 'C'];
  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '候补详情',
    };

    data = {
      // 页面参数
      options: {},
      // 订单详情
      detailData: {},
      // 顶部状态配置
      statusConfig: {},
      // 订单状态是否异常，用来确定是否需要重发
      isAbnormal: false,
      // 缴费信息是否展开,默认S状态收起
      payIsExpand: true,
      // 条形码格式
      codeType: '',
      // 剩余时间大于0
      leftTimeFlag: false,
      // 剩余支付时间
      leftTime: '00:00',
    };

    components = {
      'detail-status': DetailStatus,
      'basic-detail': BasicDetail,
    };

    props = {};

    onLoad(options) {
      this.options = options;
      this.codeType = CODE_TYPE;
      const { waitId = '', patHisNo = '' } = options;
      this.orderDetail({ waitId, patHisNo });
    }

    onUnload() {
      this.leftTimer && clearTimeout(this.leftTimer);
    }

    events = {
      'set-navigationbar-color': (param) => {
        wepy.setNavigationBarColor(param);
      }
    }

    methods = {
      /**
       * 重发订单状态查询
       */
      bindRetryOrder(){
        this.retryOrder();
      },
      /**
       * 取消订单
       */
      bindCancelOrder(){
        this.cancelOrder();
      },
     
    };

    async orderDetail(item = {}) {
      const { waitId = '', patHisNo = '' } = item;
      const { code, data = {}, msg } = await Api.orderDetail({ waitId, patHisNo });
      if (code !== 0) {
        return;
      }
      this.detailData = data;
      this.statusConfig = this.getStatus() || {};
      if (data.effectiveFlag === '0') {
        this.payIsExpand = false;
      } else {
        this.payIsExpand = true;
      }
     
      this.$apply();
    }

    /**
     * 获取订单描述文案
     */
    getStatus() {
      const { bizType, effectiveFlag } = this.detailData;
      let stsObj = {};

      // 需要转成map
      if (effectiveFlag == '0') {
        stsObj = {
          statusName: '候补提交成功',
          text: `您已成功申请了此号源的候补排队，如果排队锁号成功，将会有相关消息推送给您，请留意医院公众号的通知，并在收到通知后15分钟内进行取号支付，否则将取消候补。
          \n\n\n 就诊时间请以锁号通知里的时间为准。若未收到锁号通知，则表示您候补未成功。 `,
        };
      } else if (effectiveFlag == '1') {
        stsObj = {
          statusName: '取消候补成功',
          text: '您已成功取消候补排队，若有需要可重新申请候补。',
        };
      } else {
        stsObj = {
          statusName: '候补异常',
          text: `操作超时，请咨询医院窗口。`,
        };
      }

      return {
        ...stsObj,
        status: effectiveFlag == '0' ? 'S' : "C",
        hasRefund: false,
      };
    }

    /**
     * 重发订单状态查询
     */
    async retryOrder() {
      const { orderId = '', bizType = '' } = this.detailData;
      const type = bizType == 2 ? 'DBGH' : 'YYGH';
      const { code, data = {}, msg } = await Api.manualNotify({
        orderId,
        bizType: TYPE_MAP[type] || 'default',
      });
      if (code !== 0) {
        return;
      }
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&time=15&from=detail`
      });
    }

    async cancelOrder() {
      const showModalRes = await wepy.showModal({
        title: '取消候补提示',
        content: '取消后您将失去候补号源，是否确认取消？',
        cancelText: '取消',
        confirmText: '确认',
        cancelColor: '#989898',
        confirmColor: '#3ECDB5',
      });
      if (showModalRes.cancel) {
        return false;
      }
      const { waitId = '' } = this.detailData;
      const { code, data = {}, msg } = await Api.cancelOrder({ waitId });
      if (code !== 0) {
        return;
      }

      wepy.showToast({
        title: '取消成功',
        icon: 'success',
      });

      this.leftTimer && clearTimeout(this.leftTimer);

      const { patHisNo = '' } = this.options;
      this.orderDetail({ waitId, patHisNo });
      wepy.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }

    /**
     * 获取支付页展示信息
     */
    getBizContent(item = {}) {
      const time = Utils.getTimeSlot(item);

      return [
        { key: '费用类型', value: item.registerTypeName },
        { key: '就诊科室', value: item.deptName },
        { key: '医生名称', value: item.doctorName },
        { key: '就诊日期', value: `${item.scheduleDate} ${item.visitWeekName}` },
        { key: '就诊时段', value: time },
        { key: '就诊人', value: item.patientName },
        { key: '就诊卡号', value: item.patCardNo },
      ];
    }

    getLeftTime(time = 0) {
      if (time <= 0) {
        this.leftTimer && clearTimeout(this.leftTimer);
        this.leftTimeFlag = false;
        this.leftTime = '00:00';
        this.$apply();
        return;
      }

      const minute = (`00${Math.floor(time / 60)}`).substr(-2);
      const second = (`00${Math.floor(time % 60)}`).substr(-2);

      this.leftTime = `${minute}:${second}`;
      this.leftTimeFlag = true;
      this.leftTimer = setTimeout(()=> {
        this.getLeftTime(--time);
      }, 1000);
      this.$apply();
    };
  }
</script>
