<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import NavTab from "@/components/navtab/index";

  const InitState = {
    consultList: [],
  }

  export default class UserList extends wepy.page {
    config = {
      navigationBarTitleText: '电话咨询',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      "nav-tab": NavTab,
    };

    onLoad(options) {
    }

    onShow(){
      this.getConsultList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 咨询对应的 icon
      consultIconMap: {
        1: 'szzx.png',
        2: 'yczx.png',
        3: 'ywzx.png',
      },
      // 咨询列表
      consultList: [],
      // tab类型
      navTabType: "counsel",
    };

    async getConsultList(){
      const { data = {}, code } = await Api.getConsultList();
      if(code == 0){
        this.consultList = data;
        this.$apply();
      }
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      makePhoneCall(item) {
        const { tel } = item;
        if(tel){
          wepy.makePhoneCall({ phoneNumber: tel });
        }
      },
    };

    events = {
    };

  }
</script>
