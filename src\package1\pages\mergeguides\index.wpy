<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Utils from "@/utils/utils";
import empty from "@/components/empty/index";
export default class mergeGuides extends wepy.page {
  config = {
    navigationBarTitleText: "指引单",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    empty
  };
  data = {
    options: {},
    // 导诊服务列表
    guideList: [
      {
        title: '门诊指引单',
        url: `https://ssyy.zxxyyy.cn/#/guide/main?pid=111111`,
      },
      {
        title: '进周前指引单',
        url: `https://ssyy.zxxyyy.cn/#/guide/beforecycle`,
      },
      {
        title: '周期中指引单',
        url: `https://ssyy.zxxyyy.cn/#/guide/cycling`,
      }
    ]
  };
  onLoad(options) {
    this.options = options || {};
  }
 
  events = {
  };
 // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    navigateTo(e) {
      const { url } = e.currentTarget.dataset;
      if (!url) return;
      wepy.navigateTo({
        url: `/pages/webview/index?pid=${this.options.patHisNo}&weburl=${url}`
      });
    }
  };
}
</script>



