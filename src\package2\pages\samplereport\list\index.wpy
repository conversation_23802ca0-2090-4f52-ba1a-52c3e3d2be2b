<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import Empty from "@/components/empty/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import { DOMAIN, REQUEST_QUERY } from '@/config/constant';
import md5 from "md5";
import * as Utils from "@/utils/utils";

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '报告查询',
	}

  components = {
    empty: Empty,
  };

  wxs = {
    WxsUtils: WxsUtils,
  };

  data = {
    list: [],
  }

  onShow(options) {}
  onLoad(options) {
    this.getList(options)
  }

  async getList(param) {
    const { code, data = {} } = await Api.getList(param);
      if (data.resultCode === '0') {
        this.list = data.list;
        this.$apply();
      } else {
        wx.showToast({
          title: data.resultMessage,
          icon: "none"
        });
      }
  };
  methods = {
    goBack() {
      wepy.navigateBack();
    },
    async navToBeiriDeatil(e) {
      const { id, name } = e.currentTarget.dataset;
      wx.showLoading();
      //读取本地文件
      wx.downloadFile({
        // 示例 url，并非真实存在
        url: `${DOMAIN}/api/customize/getbeiripdf?_route=h${REQUEST_QUERY.platformId}&id=${id}&pregnantname=${name}&msgKey=${md5(id+'2023beiripdf')}`,
        success: function (res) {
          const filePath = res.tempFilePath;
          console.log(res, '====142')
          wx.openDocument({
            filePath: filePath,
            showMenu: true,
            success: function (res) {
              wx.hideLoading();
            }
          })
        }
      })
    }
  }
}
</script>