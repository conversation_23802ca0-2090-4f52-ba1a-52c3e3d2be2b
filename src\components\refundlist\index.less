@import "../../resources/style/mixins";

.wgt-refundlist {
  background-color: #fff;
  padding: 30rpx;
}
.wgt-refundlist-tit {
  font-size: 34rpx;
  color: @hc-color-title;
  font-weight: bold;
}
.wgt-refundlist-select{
  display: flex;
  align-items: center;
  font-size: 26rpx;
  padding-top: 30rpx;
  padding-bottom: 20rpx;
}
.wgt-refundlist-select-label{
  color: @hc-color-title;
}
.wgt-refundlist-select-value{
  color: @hc-color-text;
}
.wgt-refundlist-select-icon{
  font-size: 15rpx;
  color: @hc-color-primary;
  margin-left: 10rpx;
}
.wgt-refundlist-box {
  position: relative;
  padding-top: 30rpx;
  padding-left: 30rpx;
  display: flex;
  flex-direction: column-reverse;
}
.wgt-refundlist-item {
  position: relative;
  font-size: 30rpx;
  color: @hc-color-title;
  min-height: 72rpx;
  padding-bottom: 40rpx;
  z-index: 2;
}
.wgt-refundlist-item-icon {
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: @hc-color-border;
  border-radius: 50%;
}
.wgt-refundlist-item-line {
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: -2rpx;
  transform: translateY(30rpx);
  width: 4rpx;
  background-color: @hc-color-border;
}
.wgt-refundlist-item-title {
  padding-left: 60rpx;
}
.wgt-refundlist-item {
  &.active {
    z-index: 1;
    .wgt-refundlist-item-icon {
      background-color: @hc-color-primary;
    }
    .wgt-refundlist-item-title{
      color: @hc-color-primary;
    }
    &~.wgt-refundlist-item{
      .wgt-refundlist-item-icon, .wgt-refundlist-item-line {
        background-color: @hc-color-primary;
      }
      
    }
  }
  &:first-child {
    min-height: 50rpx;
    padding-bottom: 0;
    .wgt-refundlist-item-line {
      display: none;
    }
  }
}