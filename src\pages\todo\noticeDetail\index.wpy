<style lang="less" src="./index.less"></style>
<template lang="wxml">
  <view class="notice-detail" wx:if="{{notice}}">
    <view class="title">{{notice.title}}</view>
    <view class="date">{{notice.createTime}}</view>
    <view class="content">{{notice.detail}}</view>
    <view class="btn-area" wx:if="{{hasReceipt}}">
      <button class="btn {{isReceipt? 'isReceipt' : ''}}" loading="{{isSeeing}}" @tap="seeNotice">{{isReceipt ? '我已知晓' : '已知晓'}}</button>
    </view>
  </view>
  <empty :config.sync="emptyConfig">
    <block slot="text">未查询到任何消息</block>
  </empty>
</template>
<script>
import wepy from "wepy";
import empty from "@/components/empty/index";
import { REQUEST_QUERY } from "@/config/constant";
import * as Api from "./api";
export default class NoticeDetail extends wepy.page {
  config = {
    navigationBarTitleText: "消息通知"
  };
  components = { empty };
  data = {
    emptyConfig: {
      show: false
    },
    notice: null, // 通知详情
    hasReceipt: true,
    isReceipt: false,
    isSeeing: false
  };
  onLoad(options) {
    const { todoId = "" } = options;
    this.getTodoList(todoId);
  }
  async getTodoList(id) {
    const { code, data = {} } = await Api.getTodoList();
    if (code != 0) {
      return;
    }
    const { recordList = [] } = data;
    const notice = recordList.find(item => {
      item.extFields = JSON.parse(item.extFields);
      return item.id == id;
    });
    this.emptyConfig.show = !notice;
    const { extFields = {} } = notice;
    this.hasReceipt = extFields.receiptFlag == 1;
    this.isReceipt = notice.readFlag == 1;
    this.notice = notice || null;
    if(notice.readFlag != 1 ){
      this.updateReadFlag();
    }
    this.$apply();
  }
  async updateMsgReceipt() {
    const { hisId = "" } = REQUEST_QUERY;
    const {
      extFields: { msgId = "", openId = "" }
    } = this.notice;
    this.isSeeing = true;
    const { code, data = {} } = await Api.updateMsgReceipt({
      hisId,
      msgId,
      openId
    });
    if (code != 0) {
      return;
    }
    this.isSeeing = false;
    this.isReceipt = true;
    this.$apply();
  }
  async updateReadFlag() {
    if (this.isReceipt) {
      return;
    }
    const { id = "" } = this.notice;
    const {
      hisId = "",
      platformId = "",
      platformSource = ""
    } = REQUEST_QUERY;
    const param = {
      hisId,
      platformId,
      platformSource,
      dataFlag: '10',
      id: id
    };
    const { code, data } = await Api.updateReadFlag(param);
    if (code != 0) {
      return;
    }
  }
  methods = {
    seeNotice(){
      this.updateMsgReceipt();
    }
  };
}
</script>


