@import "../../../../resources/style/mixins";

page{
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.m-cells {
  background-color: #FFF;
  padding: 0 40rpx;
  &:not(:first-child) {
    margin-top: 20rpx;
  }
  .m-cell {
    position: relative;
    color: @hc-color-title;
    font-size: 34rpx;
    line-height: 50rpx;
    padding: 20rpx 0;
    // &:not(:first-child) {
    //   border-top: 2rpx solid #F5F5F5;
    // }
    border-bottom: 2rpx solid #F5F5F5;
    &.active:after {
      display: block;
      border-color: @hc-color-primary;
      position: absolute;
      top: 25rpx;
      right: 10rpx;
      z-index: 999;
      width: 14rpx;
      height: 28rpx;
      border-style: solid;
      border-width: 0 6rpx 6rpx 0;
      content: " ";
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
  }
}

.m-boxs{
  display: flex;
  flex-flow: wrap;
  padding-bottom: 20rpx;
  .u-box {
    height: 60rpx;
    line-height: 60rpx;

    background-color: #EAFAF7;
    color: @hc-color-primary;
    margin: 30rpx 10rpx;
    padding: 0 30rpx;
    border-radius: 12px;
    &.active {
      background-color: #3ECEB6;
      color: #fff;
    }
  }
}

.displayNone {
  display: none;
}
.displayBlock {
  display: block;
}
// .active {
//   border: @hc-color-primary;
//   background-color: @hc-color-primary;
// }



.binduser-btn_line{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}

.afterscan-operbtnbox{
  margin: 42rpx 40rpx;
}