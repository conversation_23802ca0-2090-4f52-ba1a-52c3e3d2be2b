<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Empty from "@/components/empty/index";
import * as Api from "./api";
const STATUS_MAP = {
  0: { icon: "dsh", label: "待审核" },
  1: { icon: "fail", label: "已驳回" },
  2: { icon: "djs", label: "审核通过待结算" },
  3: { icon: "yjs", label: "已结算" },
  4: { icon: "fail", label: "已取消" }
};
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "出院结算申请记录",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    empty: Empty
  };
  data = {
    applyList: [],
    patCardNo: ""
  };
  onLoad(options) {
    this.patCardNo = options.patCardNo || "";
    this.patHisNo = options.patHisNo || "";
    this.pid = options.pid || "";
    this.grid = options.grid || "";
  }
  onShow() {
    this.getList();
  }

  methods = {
    goDetail(item) {
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/detail/index?id=${
          item.id
        }&patCardNo=${this.patCardNo}&patHisNo=${this.patHisNo}&pid=${
          this.pid
        }&grid=${this.grid}`
      });
    }
  };
  getList = async () => {
    const { code, data } = await Api.getList();
    if (code === 0) {
      this.applyList = data.map(i => {
        return {
          ...i,
          ...STATUS_MAP[i.status]
        };
      });
      this.$apply();
    }
  };
}
</script>
