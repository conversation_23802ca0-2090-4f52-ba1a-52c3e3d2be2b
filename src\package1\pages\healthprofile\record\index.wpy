<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import TopTip from '@/components/toptip/index';
  import Folding from "@/components/folding/index";
  import * as Api from './api';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '就诊记录',
    };

    components = {
      toptip: TopTip,
      folding: Folding
    };
    
    onLoad(options) {
      this.pid = options.pid;
    }

    onShow() {
      this.queryRecordList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      rangeList: [
        {key: this.getFormatTime(new Date().setMonth(new Date().getMonth()-3)), value: '三个月'},
        {key: this.getFormatTime(new Date().setMonth(new Date().getMonth()-6)), value: '六个月'},
        {key: this.getFormatTime(new Date().setFullYear(new Date().getFullYear()-1)), value: '一年'},
        {key: this.getFormatTime(new Date().setFullYear(new Date().getFullYear()-2)), value: '两年'},
      ],
      endTime: this.getFormatTime(new Date()),
      rangeIndex: 0,
      recordList: []
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      onRangeChange(e) {
        this.rangeIndex = e.detail.value;
        this.queryRecordList();
      },
      bindExpand(index){
        this.recordList[index].isExpand = !this.recordList[index].isExpand;
      }
    };

    getFormatTime(time) {
      const date = new Date(time);
      let fmt = 'yyyy-MM-dd hh:mm:ss';
      var o = {
        "M+": date.getMonth() + 1,                 //月份
        "d+": date.getDate(),                    //日
        "h+": date.getHours(),                   //小时
        "m+": date.getMinutes(),                 //分
        "s+": date.getSeconds(),                 //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds()             //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      }
      for (let k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    }

    async queryRecordList() {
      const param = {
        // startTime: this.rangeList[this.rangeIndex].key,
        // endTime: this.endTime,
        pid: this.pid,
        type: 1 //1就诊记录(医院内可见) 3仅添加者可见 4医联体可见 5全部可见
      }
      let {code, data = []} = await Api.queryRecordList(param);
      if (code !== 0) return;
      data.forEach(item => item.isExpand = true);
      this.recordList = data;
      this.$apply();
      console.log(this.recordList);
    }

  }
</script>
