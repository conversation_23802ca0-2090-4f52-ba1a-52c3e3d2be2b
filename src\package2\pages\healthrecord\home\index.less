@import "../../../../resources/style/mixins";

.p-page{
  padding-bottom: 24rpx;
  .tips{
    padding: 32rpx 24rpx;
    font-size: 24rpx;
    background: #FFFAF1;
    color: #BE8014;
    text-align: center;
  }
  .page-content{
    padding: 24rpx;
  }
  .content-group{
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    margin-bottom: 16rpx;
    border-radius: 8rpx;
    background: #FFF;
    box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
    .arron {
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid #c7c7cc;
      border-bottom: 5rpx solid #c7c7cc;
      transform: translate(-8rpx) rotate(-45deg);
    }
  }
  .group-left{
    flex: 1;
    width: 91%;
    margin-right: 32rpx;
    .title{
      margin-bottom: 16rpx;
      color: rgba(0, 0, 0, 0.90);
      font-size: 28rpx;
      font-weight: bold;
    }
    .text{
      width: 100%;
      color:#3F969D;
      font-size: 30rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      &.on{
        color: rgba(0, 0, 0, 0.70);
      }
    }
  }
}
.empty-content{
	width: 100%;
	height: 100vh;
	text-align: center;
	line-height: 100vh;
	font-weight: bold;
}