@import "../../../resources/style/mixins";

.material-container {
  padding: 10px;

  .material-container-top {
    padding: 24px 10px 10px 10px;

    .material-name {
      font-size: 24px;
      font-weight: 700;
    }

    .material-number {
      font-size: 14px;
      color: #808080
    }
  }

  .info-container {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 16px;

    .info-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .info-title {
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      font-size: 14px;
    }

    .info-tips {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.4);
      font-weight: 400;
    }

    .info-lable {
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
    }

  }

  .info-photo {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 16px;
    margin-top: 20px;

    .photo-title {
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      font-size: 14px;
      margin-bottom: 20px;
    }

    .photo-des {
      font-size: 15px;
    }

    .xh {
      color: red;
    }

    .m-upload-list {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;

      // padding: 10rpx 0 10rpx;
      .m-upload-item {
        position: relative;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin: 0 32rpx 32rpx 0rpx;
        width: 145rpx;
        height: 145rpx;
        background-color: #f5f5f5;
        overflow: hidden;
        text-align: center;
        border-radius: 24rpx;

        .m-upload-image {
          max-width: 100%;
          max-height: 100%;
        }

        .m-upload-tips-image {
          position: absolute;
          right: 6rpx;
          top: 6rpx;
          width: 26rpx;
          height: 26rpx;
          background-size: 100%;

          &.m-upload-sucess {
            background-position: top right;
            background-repeat: no-repeat;
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACHUlEQVRIS72WPUgjQRTH/2+DnGhycCgERQW9FKkOLiIWmtXuFMUyhY2dhRYWx1V+FBrtLUxhIVhosXBwHKennSRaibEN4gcq5BC95pLzrjA7Mks2bOLsjht0p33z3m/ee/+ZNwTZ0jRfNOTvBaASEGZAA3ch4DcDMgCSqdP8PmKxglMosjP2ZPYDvvvcFAObIKDJKQgDfhEoUagLLB+Ee3OivUJQ9HhnCExfJaBZlrDVzoAsSBlPfRzYqvR7AlKPfkwzRZknBsUNxNzLCDrp+lyyc3jR6l8G4hCQEq8G8MSH6TNWWAlklAv692ozqQTxzABlxCyjAeKNV+7/ZNz2RJY575le9zbMBWKA1PTPGYAtyBxl9rHge+QLD/h6d2nZSrPJyGCcoGk+NeS/hkTCMshkcxixYDvWsidYvzkrbefST53mWyma3u4jYE8WyMluQrSbC6xk+R0uXwzo56BZAuarBckgPC4D5khNb28AGBVpnknoz4EUQ2zyjHYI+GSNOd32AS219fhydmg0V7RcQHhGu0JQV6ARSx0RnP/L4bMA5gZSLN2usHTc2B1oRFwAcwuxls5WDJWwsWDIkLCduuxaaohBJm8T9rfwgHc1b1xDiqXrNy5sNOS/dpo5HLbQEcG32yskBPfESZylC/vcJ6iWfPjPHIeoDc98grx8VPlRPBkTZs6eDD4r7NVHuQnz5HNiwjz5bpXp9IU+kI8KPzAUBWAp/QAAAABJRU5ErkJggg==");
          }


          &.m-upload-queue {
            background-position: top right;
            background-repeat: no-repeat;
            background-image: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/close.png");
            // background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAB8UlEQVRIS7WWMUtdQRCFv4OSzsaAJEXAJmBpIRGJxAjpAkLsg9gYEMQfoEYi5geIIGgjQdIaCAQsBCUoorwiZcAmYJEgaGMnhpEJ8/S+57vv3g2+Le/e3W/3zJmZFQXDzNqAQeAF0AM8jCVnwE/gO7An6W+zrZQ3aWYdwDQwCTwGroBfwGms6QK6gXbgN7ACLEm6aLRnQ5CZvQbWgEfAN2Ad2K7fJA7zChgHfM0fYEKSr6kZd0BmNgMsAD+Ad5IqRfL6vJn1AatAL/Be0sfsuhpQQBaBT3GyyzKQ6j9m9iCUGANms7AbUMj1FdhwKSRZI4iZTfl3Scs5876nS/0WGKnK+A8UWruDXOMBSbk3MbOdAA03MZLf7CBi3OOxrYJmgQ9Af1FMyoAyMTsE5iUtKvLkBKhIGimKSVlQwDwUbpInDhoCdoFRSV/uGfQG2AReOmjO7Qh05iVbFp54I0/6c08XB30Gnkl6WnSbkKPQDHUHOwaOHLQFdEh6ngDypPSEbjQ2s9Y3s33g4n9AnkejTQ6VC0qSrsyt86RLMkMKKArBjRmS7J0IqrG3N7bSCZsIuk3YsGzpElQWFG3jtgSlFtUyoGgXd4tqwLxDFraJIpCZ5beJTOPy7traxlcHa20rz8Ba/zjJwFr/3KorIffygLwGdCorROT1aU8AAAAASUVORK5CYII=");
          }
        }
      }

      .m-upload-icon {
        box-sizing: border-box;
        border: 1px dashed @hc-color-border;
        font-size: 80rpx;
        font-weight: 100;
        line-height: 135rpx;
        text-align: center;
        color: @hc-color-info;
      }
    }
  }

  .button {
    margin-top: 20px;
    background: #3ECEB6;
    border-radius: 5px;
    color: #fff;
  }

}

.ouContent {
  display: flex;
  align-items: center;
  font-size: 15px !important;

  .xh {
    color: red;
    margin-right: 5px;
  }
}

.ouContent-textarea{
  margin: 10px auto 0;
  padding: 10px;
  width: 96%;
  border: 2rpx solid rgba(214, 210, 210, 0.6);
}