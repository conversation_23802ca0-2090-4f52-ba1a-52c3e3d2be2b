<style lang="less" src="./app.less"></style>

<script>
import "promise-polyfill";
import wepy from "wepy";
import "wepy-async-function";
import * as Api from "./api";
import ConfigMixin from "@/pages/configMixin";

export default class extends wepy.app {
  mixins = [ConfigMixin];
  config = {
    pages: [
      // 首页
      "pages/home/<USER>",
      "pages/operation/index", // 手术预约
      // 绑卡
      "pages/bindcard/index/index",
      "pages/bindcard/scancard/index",
      "pages/bindcard/queryuserinfo/index",
      "pages/bindcard/queryothertype/index", // 其它证件类型
      "pages/bindcard/adduser/index",
      "pages/bindcard/binddoctor/index",

      // 授权
      "pages/auth/developing/index",
      "pages/auth/getuserinfo/index",

      // 医院动态
      "pages/dynamic/index/index",
      "pages/dynamic/list/index",
      "pages/dynamic/info/index",
      "pages/dynamic/secondindex/index",

      //取号
      // 'pages/takeno/recordlist/index',
      // 'pages/takeno/index/index',
      // 'pages/takeno/recorddetail/index',

      //充值
      // 'pages/recharge/recordlist/index',
      // 'pages/recharge/amount/index',
      // 'pages/recharge/recorddetail/index',

      // 住院清单
      "pages/inhosp/index/index",
      "pages/inhosp/daily/index",
      "pages/inhosp/amount/index",
      "pages/inhosp/binduser/index",
      "pages/inhosp/recordlist/index",
      "pages/inhosp/recorddetail/index",

      //排队
      // 'pages/queue/queue/index',

      //报告
      "pages/report/reportlist/index",
      "pages/report/examine/index",
      "pages/report/analysis/index",

      // 个人中心
      "pages/usercenter/home/<USER>",
      "pages/usercenter/userlist/index",
      "pages/usercenter/userinfo/index",
      "pages/usercenter/collect/index",
      "pages/usercenter/queryrelation/index",
      "pages/usercenter/newphone/index",
      // 门诊缴费
      "pages/treat/untreatlist/index",
      "pages/treat/untreatdetail/index",
      "pages/treat/recordlist/index",
      "pages/treat/recorddetail/index",

      // 快速缴费
      "pages/paymerge/index",

      // 待办
      "pages/todo/index",
      "pages/todo/noticeDetail/index",

      // 挂号
      "pages/register/deptlist/index",
      "pages/register/search/index",
      "pages/register/doclist/index",
      "pages/register/docinfo/index",
      "pages/register/recordlist/index",
      "pages/register/recorddetail/index",
      "pages/register/extra/index",
      "pages/register/arealist/index",
      "pages/register/alternatedetail/index",
      "pages/register/alternaterecord/index",

      // 满意度调查
      "pages/survey/surveylist/index",
      "pages/survey/surveydetail/index",

      // 样本问卷工具
      "pages/survey/surveytools/index",
      "pages/survey/surveytools/questionnaire/index",
      "pages/survey/surveytools/surveylist/index",

      // 知情告知书
      "pages/notificationletter/list/index",

      // 新冠风险地区显示
      "pages/survey/riskArea/index",

      // 支付处理页
      "pages/waiting/waiting/index",
      "pages/waiting/abnormal/index",

      // 收银台
      "pages/pay/index",

      // 院外报告
      // 'pages/uploadreport/reportlist/index',
      // 'pages/uploadreport/upload/index',

      // 免费咨询
      "pages/consult/choosetype/index",
      "pages/consult/chat/index",
      "pages/consult/chat1/index",

      // 视频问诊
      "pages/consult/video/index",

      //h5 页面展示web
      "pages/webview/index",

      // 送药
      // 'pages/sendmedicine/home/<USER>',
      // 'pages/sendmedicine/edit/index',
      // 'pages/sendmedicine/recordlist/index',
      // 'pages/sendmedicine/recorddetail/index',
      // 'pages/sendmedicine/confirm/index',

      //公安链接
      "pages/security/extra/index",
      "pages/security/home/<USER>",

      // 跳板页面
      "pages/jump/index",

      // 好孕优享
      "pages/priority/extra/index",
      "pages/priority/home/<USER>",

      //胚胎查询
      "pages/embryo/index",

      //冻精查询
      // 'pages/forzensemen/renew/index',
      // 'pages/forzensemen/result/index',
      // 'pages/forzensemen/recordlist/index',
      // 'pages/forzensemen/recorddetail/index',

      //爱心助孕
      "pages/pregnancy/projectlist/index",
      "pages/pregnancy/agreement/index",
      "pages/pregnancy/material/index",
      "pages/pregnancy/recordlist/index",
      "pages/pregnancy/recorddetail/index",

      //自助开单
      "pages/servicebill/index",
      "pages/servicebill/servicebilllist/index",
      "pages/servicebill/servicebilldetail/index",

      //意见反馈
      "pages/feedback/feedbacklist/index",
      "pages/feedback/feedbackdetail/index",
      "pages/feedback/feedbackwrite/index",

      //龚斐团队咨询
      "pages/consultservice/packagelist/index",
      "pages/consultservice/packagedetail/index",
      "pages/consultservice/recorddetail/index",
      "pages/consultservice/recordlist/index",
      "pages/consultservice/vipchatRecordlist/index",

      // 导诊服务
      "pages/guideservice/index",

      //微网站
      "pages/microsite/home/<USER>",

      // 直播
      "pages/live/home/<USER>",
      "pages/hesuansurvey/index",
    ],
    subPackages: [
      {
        root: "package1",
        pages: [
          //微网站
          "pages/microsite/home/<USER>",
          "pages/microsite/map/index",
          "pages/microsite/floorlayout/index",
          "pages/microsite/deptlayout/index",
          "pages/microsite/deptinfo/index",
          "pages/microsite/deptlist/index",
          "pages/microsite/doctorlist/index",
          "pages/microsite/doctorinfo/index",
          "pages/microsite/hisinfo/index",
          "pages/microsite/news/index",
          "pages/microsite/news/news",
          "pages/microsite/news/artical/news/index",

          "pages/live/home/<USER>",
          "pages/live/concern/index",
          "pages/live/waiting/index",
          "pages/live/detail/index",
          "pages/live/end/index",
          "pages/live/play/index",
          "pages/live/video/index",

          //B超预约 web
          "pages/bchao/index",

          //交通住宿攻略
          "pages/traffic/index",

          // 问诊
          "pages/ask/home/<USER>",
          // 导诊查询
          "pages/guide/index/index",
          // 我的推荐
          "pages/recommend/index/index",
          "pages/recommend/add/index",

          // 视频列表
          "pages/videorecord/recordlist/index",
          "pages/videorecord/recorddetail/index",

          // 指引单
          "pages/mergeguides/index",

          // 导诊服务
          "pages/guideservice/index",

          // 电话咨询
          "pages/phonecounsel/index",

          // 快速缴费
          "pages/paymerge/index",

          // 病历查询
          "pages/medicalrecord/recordlist/index",
          "pages/medicalrecord/recorddetail/index",

          // 用药指导单
          "pages/guidelines/index/index",
          // 健康档案
          "pages/healthprofile/profile/index",
          "pages/healthprofile/baseinfo/index",
          "pages/healthprofile/marriage/index",
          "pages/healthprofile/history/index",
          "pages/healthprofile/description/index",
          "pages/healthprofile/record/index",
          "pages/healthprofile/problem/index",
          "pages/healthprofile/menstrual/index",
          //最美医生
          "pages/prettiestDoctor/index",

          // 生殖中心缴费清单
          "pages/reproductcenter/index/index",
          "pages/reproductcenter/daily/index",
          "pages/reproductcenter/amount/index",
          "pages/reproductcenter/recordlist/index",
          "pages/reproductcenter/recorddetail/index",

          // 完善信息
          "pages/addfamilymember/index",
          // 添加家庭成员
          "pages/completeinfo/index",
          // vip中心
          "pages/vipcenter/index",

          // 出院结算记录
          "pages/hospitalbilling/pendinglist/index",
          "pages/hospitalbilling/settlementdetail/index",
          "pages/hospitalbilling/notice/index",
          "pages/hospitalbilling/apply/index",
          "pages/hospitalbilling/detail/index",
          "pages/hospitalbilling/list/index",
          "pages/pidinfo/index",
        ],
        // independent: true,
      },
      {
        root: "package2",
        pages: [
          //扫码开单
          "pages/scancode/home/<USER>",
          "pages/scancode/binddoctor/index",
          "pages/scancode/recordlist/index",
          "pages/scancode/recorddetail/index",
          "pages/scancode/perfectinformation/index",
          "pages/scancode/perfectinformationdetail/index",
          "pages/scancode/productinfo/index",

          //病例复印
          "pages/medical/medicallist/index",
          "pages/medical/medicalinfo/index",
          "pages/medical/agreement/index",
          "pages/medical/recordlist/index",
          "pages/medical/recorddetail/index",

          //健康档案
          "pages/healthrecord/home/<USER>",
          "pages/healthrecord/editbox/index",

          //报告查询
          "pages/samplereport/query/index",
          "pages/samplereport/list/index",
          // 电子发票
          "pages/dzfp/index",
          //扫码查看知情同意书
          "pages/consentform/index",
          //会员卡
          "pages/membercard/index",
          "pages/membercard/bind/index",
        ],
        // independent: true,
      },
    ],
    window: {
      backgroundColor: "#ddd",
      backgroundTextStyle: "light",
      navigationBarBackgroundColor: "#fff",
      navigationBarTitleText: "家辉遗传",
      navigationBarTextStyle: "black",
    },
    plugins: {
      wxparserPlugin: {
        version: "0.4.0",
        provider: "wx9d4d4ffa781ff3ac",
      },
      // myPlugin: {
      // 	version: '1.0.7',
      // 	provider: 'wx5bc2ac602a747594',
      // },
    },
    permission: {
      "scope.userLocation": {
        desc: "你的位置信息将用于小程序位置接口的效果展示",
      },
    },
    // navigateToMiniProgramAppIdList: [
    // 	'wxa89db5e57ee2a65e', //智能客服
    // 	'wx3fa4464915791992', //商保理赔
    // 	'wxafb9e3096f9810c3', //预约体检
    // 	'wx6292cb8430e4b9de', //停车缴费
    // 	'wx25ad95d5a37d5c9c', //预约检查
    // 	'wx4fe7970bd94d058d', //产程查询
    // ],
  };

  globalData = {
    // 用户基本信息
    userInfo: {},
    // 门诊就诊卡数据
    outpatient: {},
    // 住院卡数据
    inpatient: {},
    isClock: false,
    time: 60,
    isYuanMengFreShen: false,
    doctorId: "", // 绑卡关联的医生id
  };

  clock() {
    setInterval(() => {
      const { globalData = {} } = this;
      if (globalData.isClock) {
        globalData.time -= 1;
        return globalData.time;
      }
    }, 1000);
  }

  changeStatus() {
    const { globalData = {} } = this;
    globalData.isClock = !globalData.isClock;
  }

  constructor() {
    super();
    this.use("requestfix");
    this.use("promisify");
  }

  async getOutpatient(item = {}) {
    const { globalData = {} } = this;
    const reqData = { noAuthOn999: true };
    const {
      code,
      data = {},
      msg,
    } = item.noauth == 1
      ? await Api.getOutpatient(reqData)
      : await Api.getPatient({
          noAuthOn999: item.noauth === "2",
        });
    if (code !== 0) {
      // 没有获取到就诊卡列表
      return {
        code: -1,
        data: {},
      };
    }

    let { cardList = [] } = data;
    cardList.forEach((item) => {
      if (item.relationType == 1) {
        // data.activePatient = item;
        data.loginPatient = item;
      }
      if (item.isDefault == 1) {
        data.activePatient = item;
      }
    });
    if (!data.activePatient && cardList.length > 0) {
      data.activePatient = cardList[0];
    }
    globalData.outpatient = data;
    return {
      code: 0,
      data: globalData.outpatient,
    };
  }

  async setDefaultOutpatient(item = {}) {
    const { patientId = "" } = item;
    const { code } = await Api.setDefaultOutpatient({ patientId });
    if (code !== 0) {
      // 没有获取到就诊卡列表
      return {
        code: -1,
        data: {},
      };
    }
    return await this.getOutpatient();
  }

  // 比对版本
  compareVersion(v1, v2) {
    v1 = v1.split(".");
    v2 = v2.split(".");
    const len = Math.max(v1.length, v2.length);
    while (v1.length < len) {
      v1.push("0");
    }
    while (v2.length < len) {
      v2.push("0");
    }

    for (let i = 0; i < len; i++) {
      const num1 = parseInt(v1[i]);
      const num2 = parseInt(v2[i]);
      if (num1 > num2) {
        return 1;
      } else if (num1 < num2) {
        return -1;
      }
    }
    return 0;
  }
  onLaunch(option) {
    this.globalData.appLaunchOption = option;
    // this.clearAuthToken();
    const SDKVersion = wx.getSystemInfoSync().SDKVersion;
    console.log("基础库版本", SDKVersion);
    // 基础库版本是否大于2.8.3
    if (this.compareVersion(SDKVersion, "2.8.3") >= 0) {
      this.globalData.isSDKVersion = true;
    } else {
      this.globalData.isSDKVersion = false;
    }
    // 微信版本是否大于7.0.0
    const version = wx.getSystemInfoSync().version;
    if (this.compareVersion(version, "7.0.0") >= 0) {
      this.globalData.isVersion = true;
    } else {
      this.globalData.isVersion = false;
    }
    // this.errorVersion();

    // 强制版本更新
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(function (res) {
      console.log("onCheckForUpdate=", res);
      // 请求完新版本信息的回调
      console.log(res.hasUpdate);
    });
    updateManager.onUpdateReady(function (res) {
      console.log("onUpdateReady=", res);
      wx.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success: function (res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        },
      });
    });
    updateManager.onUpdateFailed(function (res) {
      console.log("onUpdateFailed=", res);
      // 新版本下载失败
      wx.showModal({
        title: "提示",
        content: "新版本下载失败，请重新进入",
      });
    });
  }

  errorVersion() {
    const apiEnv = "REPLACE_API_ENV";
    const nodeEnv = "NODE_ENV";
    // if (apiEnv === 's' || apiEnv === 'u') {
    //   const sysInfo = wx.getSystemInfoSync();
    //   if (sysInfo.platform !== 'devtools') {
    //     wepy.showModal({
    //       title: '警告',
    //       content: '当前版本小程序对应环境为开发或测试，仅供内部测试使用，禁止发布上线！！！',
    //       showCancel: false,
    //     });
    //   }
    // }
  }
}
</script>
