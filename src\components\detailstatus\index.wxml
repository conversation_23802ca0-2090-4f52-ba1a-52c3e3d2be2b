<view class="wgt-detailstatus wgt-detailstatus-{{statusClassName}}">
  <view class="wgt-detailstatus-bd">
    <view class="wgt-detailstatus-bd-icon">
      <block wx:if="{{statusClassName}}">
        <view class="icon" wx:if="{{statusClassName !== 'lock'}}">
          <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{statusIcon}}.png"></image>
        </view>
        <view wx:else>
          <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-lock.png"></image>
        </view>
      </block>
    </view>
    <view class="wgt-detailstatus-bd-tit">
      <slot name="title"></slot>
    </view>
    <block wx:if="{{config.hasRefund}}">
      <view class="wgt-detailstatus-bd-label">有退款</view>
    </block>
    <view class="wgt-detailstatus-bd-timer" wx:if="{{config.status === 'L'}}">{{leftTime}}</view>
  </view>
  <view class="wgt-detailstatus-ft">
    <slot name="text"></slot>
    <view wx:if="{{config.hasRefund}}">退款金额已原路返回您的支付账户，请注意查收。</view>
  </view>
</view>