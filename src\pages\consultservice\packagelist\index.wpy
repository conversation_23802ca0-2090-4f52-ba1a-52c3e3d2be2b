<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";
import Empty from "@/components/empty/index";
import { PRIMARY_COLOR } from "@/config/constant";

export default class PackageList extends wepy.page {
  config = {
    navigationBarTitleText: "龚斐教授团队全病程管理",
    navigationBarBackgroundColor: '#fff'
  };
  components = {
    empty: Empty
  };

  data = {
    emptyConfig: {
      show: false
    },
    patientInfo: {},
    tips: {},
    packageList: [
      {
        id: "1",
        projectId: "130396",
        listName: "基础管理套餐",
        listPrice: "880元 （60天）",
        profileKey: "getAlertNoteProfile_payConsult_oneyear",
        totalFee: 1
      },
      {
        id: "2",
        projectId: "130397",
        listName: "半年管理套餐",
        listPrice: "2180元 （180天）",
        profileKey: "getAlertNoteProfile_payConsult_halfyear",
        totalFee: 1
      }
    ],
    isPayArr: [], //是否有付费咨询
    isPayLength: 0,
    vipIntroduceLink: ""
  };
  async getNoteProfileLink(param) {
    const { code, data = {}, msg } = await Api.getNoteProfile(param);
    if (code == 0) {
      this.vipIntroduceLink = data.profileValue;
      this.$apply();
    }
  }
  onLoad() {
    this.getNoteProfileLink({
      profileKey: "getAlertNoteProfile_GongFeiTeamServiceNotice",
      hisId: 242
    });
  }
  onShow() {
    this.getPatientsList();
  }

  methods = {
    onClickDetail(params) {
      console.log(params, "params");
      const queryString = JSON.stringify({
        title: params.listName,
        projectId: params.projectId,
        pid: this.patientInfo.patHisNo,
        patientName: this.patientInfo.patientName,
        patCardNo: this.patientInfo.patCardNo,
        patientId: this.patientInfo.patientId,
        profileKey: params.profileKey,
        totalFee: params.totalFee
      });
      wepy.navigateTo({
        url: `/pages/consultservice/packagedetail/index?queryString=${queryString}`
      });
    },
    onClickVipChat(item) {
      wepy.navigateTo({
        url: `/pages/consult/chat/index?groupId=${item.id}&type=${
          item.chatType
        }&vipStartDate=${item.vipStartDate.slice(0, 10)}&vipEndDate=${
          item.vipEndDate
        }&vipCode=${item.vipCode}`
      });
    },
    getRecordList() {
      wepy.navigateTo({
        url: `/pages/consultservice/recordlist/index`
      });
    },
    vipIntroduce() {
      wepy.navigateTo({
        url: `/pages/webview/index?weburl=${this.vipIntroduceLink}`
      });
    }
  };

  queryPatChat = async params => {
    console.log("params111111111", params);
    const param = {
      chatType: "5",
      patHisNo: params.patHisNo
    };
    const { code, data = {} } = await Api.queryPatChat(param);
    //data为空返回的字符串数组
    if (code === 0 && Array.isArray(data)) {
      this.isPayArr = data;
      this.isPayLength = data.length;
      this.$apply();
    }
  };

  getPatientsList = async () => {
    const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
    const { cardList = [] } = data;
    const patientInfo = cardList.length ? cardList[0] : {};
    this.patientInfo = patientInfo;
    this.$apply();
    if (patientInfo.patCardNo) {
      this.queryPatChat(patientInfo);
      return;
    }
    wx.showModal({
      title: "提示",
      content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
      showCancel: true,
      confirmText: "立即绑定",
      confirmColor: PRIMARY_COLOR,
      success: res => {
        console.log(res, "res");
        if (res.confirm) {
          wx.navigateTo({
            url: "/pages/bindcard/queryuserinfo/index?qryType=1"
          });
          return;
        }
        wx.navigateBack();
      }
    });
  };
}
</script>
