<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <folding :isExpand.sync="isExpand">
      <block slot="title">缴费信息</block>
      <block slot="content">
        <view class="list">
          <view class="list-item">
            <view class="item-label">姓名</view>
            <view class="item-value">{{detailData.patientName}}</view>
          </view>
          <view class="list-item">
            <view class="item-label">证件号码</view>
            <view class="item-value">{{detailData.idNumber}}</view>
          </view>
          <!-- <view class="list-item">
            <view class="item-label">开单医生</view>
            <view class="item-value">{{products.docname}}</view>
          </view>
          <view class="list-item">
            <view class="item-label">开单医院</view>
            <view class="item-value">{{detailData.hisName}}</view>
          </view> -->
          <view class="list-item">
            <view class="item-label">支付方式</view>
            <view class="item-value">{{detailData.payMethod}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.totalRealFee}}">
            <view class="item-label">订单金额</view>
            <view class="item-value">
              <view class="unit-price">￥{{WxsUtils.formatMoney(detailData.totalRealFee, 100)}}</view>
            </view>
          </view>
          <view class="list-item" wx:if="{{detailData.payedTime}}">
            <view class="item-label">订单时间</view>
            <view class="item-value">{{detailData.payedTime}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.agtOrdNum && payCertificate.length === 0}}">
            <view class="item-label">支付流水号</view>
            <view class="item-value">{{detailData.agtOrdNum}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.id}}">
            <view class="item-label">平台订单号</view>
            <view class="item-value">{{detailData.id}}</view>
          </view>
          <view class="" wx:if="{{payCertificate.length > 0}}">
            <view class="item-label">门诊缴费凭证</view>
            <view class="">
              <image
                class="value-icon"
                wx:for="{{payCertificate}}"
                wx:key="index" 
                src="{{item}}"
                data-url="{{item}}"
                @tap="viewImage"
              />
            </view>
          </view>
        </view>
      </block>
    </folding>
  </view>
</template>

<script>
  import wepy from 'wepy';
  import Folding from '@/components/folding/index';
  import WxsUtils from '../../../../../wxs/utils.wxs';
  import * as Utils from '@/utils/utils';

  export default class PayDetail extends wepy.component {
    data = {};

    components = {
      'folding': Folding,
    };

    wxs = {
      WxsUtils: WxsUtils,
    };

    props = {
      detailData: {
        type: Object,
        default: {},
      },
      products: {
        type: Object,
        default: {},
      },
      isExpand: {
        type: Boolean,
        default: true,
        twoWay: true,
      },
      payCertificate: {
        type: Array,
        default: [],
      }
    };

    onLoad(options) {
      console.log(this.isExpand, this.payCertificate, '=======100')
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      viewImage(e) {
        const { path, url } = e.currentTarget.dataset;
        wepy.previewImage({
          urls: this.payCertificate || [],
          current: url || '',
        });
      },
    };
  }
</script>
