<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import Empty from "@/components/empty/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import { PRIMARY_COLOR } from '@/config/constant';

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '健康档案',
	}

  components = {
    empty: Empty,
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  data = {
    basicInfo: {},
    familyInfo: {},
    familyHistory: {},
    visitRecord: {},
    auxiliaryExamination: {},
    diseaseDescription: {},
    outHisReports: {},
    patientName: '',
    pathLen: null,
    patientInfo: ''
  }

  onShow(options) {
    if (this.patientInfo.patCardNo) {
      this.getBasicInfo();
      this.getFamilyInfo();
    }
    
  }
  onLoad(options) {
    this.getPatientsList(options);
    this.patientName = options.patientName;
  }

  getPatientsList = async (options) => {
    const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
    const { cardList = [] } = data;
    this.cardList = cardList;
    const patientInfo = cardList.length ? cardList[0] : {};
    this.patientInfo = patientInfo;
    this.$apply();
    if (patientInfo.patCardNo) {
      this.getBasicInfo();
      this.getFamilyInfo();
      return;
    }
    wx.showModal({
      title: "提示",
      content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
      showCancel: true,
      confirmText: "立即绑定",
      confirmColor: PRIMARY_COLOR,
      success: res => {
        if (res.confirm) {
          wepy.navigateTo({
            url: "/pages/bindcard/queryuserinfo/index?qryType=1"
          });
          return;
        }
        wx.navigateBack();
      }
    });
  };

  async getBasicInfo() {
    const { code, data = {} } = await Api.getBasicInfo();
    if(code === 0){
      this.basicInfo = data;
      this.$apply();
    }
  };

  async getFamilyInfo() {
    const { code, data = {} } = await Api.getFamilyInfo();
    if(code === 0){
      this.familyInfo = data;
      this.familyHistory = data.familyHistory ? JSON.parse(data.familyHistory) : {};
      this.visitRecord = data.visitRecord ? JSON.parse(data.visitRecord) : {};
      this.auxiliaryExamination = data.auxiliaryExamination ? JSON.parse(data.auxiliaryExamination) : {};
      this.diseaseDescription = data.diseaseDescription ? JSON.parse(data.diseaseDescription) : {};
      this.outHisReports = data.outHisReports ? JSON.parse(data.outHisReports) : {};
      if(this.outHisReports.reports){
        const { reports } = this.outHisReports;
        const reportsArr = JSON.parse(reports)
        reportsArr.forEach(item => {
          item.reportsPath = item.reportsPath.split(';').map(v => v.replace(/\"/g, ""))
        })
        this.pathLen = reportsArr.reduce((res, {reportsPath}) => res + reportsPath.length, 0);
      }
      this.$apply();
    }
  };


  methods = {
    goMsgPage(e) {
      const { type } = e.currentTarget.dataset;
      let familyTypeValue = false;
      if(type === '2'){
        familyTypeValue = this.familyHistory.info ? true : false;
      }else if(type === '3'){
        familyTypeValue = this.visitRecord.info ? true : false;
      }else if(type === '4'){
        familyTypeValue = this.auxiliaryExamination.info ? true : false;
      }else if(type === '5'){
        familyTypeValue = this.diseaseDescription.info ? true : false;
      }
      wepy.navigateTo({
				url: `/package2/pages/healthrecord/editbox/index?type=${type}&basicInfo=${JSON.stringify(this.basicInfo)}&familyInfo=${JSON.stringify(this.familyInfo)}&patientName=${this.patientName}&familyTypeValue=${familyTypeValue}`,
			})
    }
  }
}
</script>