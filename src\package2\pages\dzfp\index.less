@import "../../../resources/style/mixins";

.m-list {
  padding: 24rpx;
  overflow: hidden;
  .list-item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0px 1rpx 2rpx 0px rgba(0, 0, 0, 0.08);
    .item-icon {
      background-color: rgba(63, 150, 157, 1);
    }
    &.list-item-abnormal {
      .item-icon {
        background-color: rgba(231, 170, 53, 1);
      }
    }
  }
  .item-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    overflow: hidden;
    border-radius: 50%;
    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-status {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }
  .item-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .main-tit {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
  }
  .item-extra {
    // margin-top: 24rpx;
  }
  .extra-tit {
    margin-top: 8rpx;
    color: @hc-color-text;
    .fee {
      font-weight: 600;
      color: @hc-color-assist;
    }
  }
  .action-btn{
    padding: 16rpx 24px;
    text-align: center;
    font-size: 28rpx;
    font-weight: bold;
    color: #fff;
    border-radius: 16rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  }
}
