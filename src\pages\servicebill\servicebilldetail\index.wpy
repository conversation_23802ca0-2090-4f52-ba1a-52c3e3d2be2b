<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

import { TYPE_MAP, CODE_TYPE } from "@/config/constant";
import DetailStatus from "@/components/detailstatus/index";
import RefundList from "@/components/refundlist/index";
import WxsUtils from "../../../wxs/utils.wxs";
import Moment from "moment";
import * as Utils from "@/utils/utils";

import * as Api from "./api";

const NORMAL_MAP = ["S", "F"];
export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "自助开单详情"
  };

  data = {
    // 订单详情
    detailData: {},
    // 顶部状态配置
    statusConfig: {},
    // 退款列表
    refundList: [],
    // 订单状态是否异常，用来确定是否需要重发
    isAbnormal: false,

    list: [],
    address: {},
    patientInfo: {},
    //快递信息
    mailNo: ""
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  components = {
    "detail-status": DetailStatus,
    "refund-list": RefundList
  };

  onLoad(options) {
    this.codeType = CODE_TYPE;
    this.orderDetail(options);
  }

  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };

  methods = {};

  async orderDetail(item = {}) {
    let extFieldsViews = {};
    const { orderId = "" } = item;
    const { code, data = {}, msg } = await Api.orderDetail({ orderId });
    if (code !== 0) {
      return;
    }
    const { status = "" } = data;
    if (data.extFieldsViews) {
      extFieldsViews = JSON.parse(data.extFieldsViews);
    }
    const newData = {
      ...data,
      pid: extFieldsViews.pid,
      xmmc: extFieldsViews.xmmc,
      bzxx: extFieldsViews.bzxx
    };
    this.detailData = newData;
    this.statusConfig = this.getStatus() || {};
    if (NORMAL_MAP.indexOf(status) === -1) {
      this.isAbnormal = true;
    }
    this.$apply();
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    const { status, refundStatus } = this.detailData;
    let stsObj = {};

    // 需要转成map
    if (status == "S") {
      stsObj = {
        statusName: "缴费成功",
        text:
          "自助开单并缴费成功，请及时到对应科室执行项目。如需打印发票，请到收费窗口执行"
      };
    } else if (status == "F") {
      stsObj = {
        statusName: "缴费失败",
        text:
          "您的自助开单缴费提交失败，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。"
      };
    } else if (status == "P") {
      stsObj = {
        statusName: "付款完成，调用医院支付接口中",
        text: ""
      };
    } else if (status == "H" || status == "Z") {
      stsObj = {
        statusName: "自助开单缴费异常",
        text: `操作超时，请咨询医院窗口为您处理。`
      };
    } else if (status == undefined) {
      stsObj = {
        statusName: "",
        text: ""
      };
    } else {
      stsObj = {
        statusName: "自助开单缴费异常",
        text: `操作超时，请咨询医院窗口为您处理。`
      };
    }

    return {
      ...stsObj,
      status,
      hasRefund: refundStatus == 1 || refundStatus == 2
    };
  }
}
</script>
