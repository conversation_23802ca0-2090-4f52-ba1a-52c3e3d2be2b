<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class HisInfo extends wepy.page {
    config = {
      navigationBarTitleText: '医院介绍',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.getHisInfo();
    }

    data = {
      emptyConfig: {
        show: true,
      },
      hisImgHeight: 0,
      hisInfo: {},
    };

    methods = {
      setImgSize(e){
        const { width, height } = e.detail;
        const basScale = 750 / width;
        const imgHeight = height * basScale;
        this.hisImgHeight = imgHeight;
      }
    };
    /**
     * 获取医院信息
     */
    async getHisInfo(){
      const { data = {}, code } = await Api.getHisInfo();
      if(code == 0){
        this.hisInfo = data;
        this.emptyConfig.show = false;
        this.$apply();
      }
    }
  }
</script>
