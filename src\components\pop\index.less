@import "../../resources/style/mixins";

.wgt-pop-box{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
.wgt-pop{
  position: relative;
  width: 90%;
  max-width: 700rpx;
  z-index: 1;
  margin: 100rpx auto 0 auto;
  padding-top: 130rpx;
}
.wgt-pop-close{
  position: absolute;
  top: 0;
  right: 30rpx;
  width: 60rpx;
}
.wgt-pop-close-icon{
  width: 60rpx;
  height: 60rpx;
  
  image{
    width: 100%;
    height: 100%;
    vertical-align: top;
  }
}
.wgt-pop-close-line{
  width: 4rpx;
  height: 70rpx;
  background-color: #fff;
  margin-left: 50%;
  transform: translateX(-50%);
}
.wgt-pop-content{
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.wgt-pop-mask{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.65);
}