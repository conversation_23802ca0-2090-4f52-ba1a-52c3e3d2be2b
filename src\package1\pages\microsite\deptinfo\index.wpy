<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class HisInfo extends wepy.page {
    config = {
      navigationBarTitleText: '医院信息',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.getDepInfo(options);
      
    }

    data = {
      emptyConfig: {
        show: true,
      },
      deptInfo: {},
    };

    methods = {
      makePhoneCall(e) {
        const { phone } = e.target.dataset;
        if(phone){
          wepy.makePhoneCall({ phoneNumber: phone });
        }
      },
    };
    /**
     * 获取医院信息
     */
    async getDepInfo(param){
      const { data = {}, code } = await Api.getDepInfo(param);
      if(code == 0){
        this.deptInfo = data;
        wx.setNavigationBarTitle({
          title: data.deptName
        });
        this.emptyConfig.show = false;
        this.$apply();
      }
    }
  }
</script>
