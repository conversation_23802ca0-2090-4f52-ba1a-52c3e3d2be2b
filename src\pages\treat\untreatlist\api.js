
import { post } from '@/utils/request';
import { REQUEST_QUERY } from "@/config/constant";

export const getList = (param) => post(`/api/oppay/waitpaylist`, param);

export const createOrder = (param) => post(`/api/oppay/createoporder`, param);

export const registerPayOrder = (param) => post(`/api/oppay/registerpayorder`, param);



export const orderProject = (param) => post(`/api/customize/orderProject?_route=h${REQUEST_QUERY.platformId}`, param);