<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from "wepy";
import NavBar from "@/components/navbar/index";
import NavTab from "@/components/navtab/index";
import { getUserInfo } from "@/utils/utils";
import BottomLogo from "@/components/bottomlogo/index";
import * as Api from "./api";
import { DOMAIN } from "@/config/constant";

export default class UserCenter extends wepy.page {
  config = {
    navigationBarTitleText: "个人中心",
    navigationStyle: "custom",
    usingComponents: {
      wxparser: "plugin://wxparserPlugin/wxparser",
    },
  };

  components = {
    "nav-bar": NavBar,
    "nav-tab": NavTab,
    "bottom-logo": BottomLogo,
  };

  onLoad(options) {
    this.getWxInfo();
  }

  onShow() {
    this.getCardList();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    hasLoad: false,
    userInfo: {},
    cardNumber: 0,
    defaultUser: {},
    // tab类型
    navTabType: "usercenter",
    isAuth: false,
    // 在线就诊记录菜单
    menuFunctionList: [
      {
        name: "挂号记录",
        icon: "record-icon.png",
        url: "/pages/register/recordlist/index",
      },
      {
        name: "门诊缴费记录",
        icon: "pay-icon.png",
        url: "/pages/treat/recordlist/index",
      },
      {
        name: "扫码缴费记录",
        icon: "scancode-home.png",
        url: "/package2/pages/scancode/recordlist/index",
      },
      {
        name: "电子发票",
        icon: "satisfaction.png",
        url: "/package2/pages/dzfp/index",
      },
    ],
    // 链接菜单
    linkList: [
      {
        name: "我的收藏",
        url: "/pages/usercenter/collect/index",
        icon: "home-collect.png",
      },
      {
        name: "问卷调查记录",
        url: "/pages/survey/surveylist/index",
        icon: "user-record.png",
      },
      {
        name: "我的会员卡",
        url: "/package2/pages/membercard/index",
        icon: "member-card.png",
      },
      {
        name: "用户服务协议",
        url: "yhfwxx.pdf",
        icon: "record-privacy.png",
      },
      {
        name: "隐私政策",
        url: "xcxysxy.pdf",
        icon: "record-survey.png",
      },
      // {
      //   name: '扫码开单',
      //   url: '/package2/pages/scancode/home/<USER>'
      // },
    ],
    // 切换导航条
    navFlag: "",
    color: "#fff",
  };

  async getWxInfo() {
    const { userInfo = {} } = await getUserInfo();
    this.isAuth = JSON.stringify(userInfo) != "{}";
    this.userInfo = userInfo;
    this.getCardList();
    this.$apply();
  }
  async getCardList() {
    const { data } = await Api.getCardList({ noAuthOn999: true });
    const { cardList = [] } = data;
    this.cardNumber = cardList.length;
    this.defaultUser = {};
    for (let i = 0; i < cardList.length; i++) {
      // 只显示本人信息
      if (cardList[i].relationType == 1) {
        this.defaultUser = cardList[i];
        this.hasLoad = true;
        break;
      }
    }
    this.$apply();
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    navigateTo(e) {
      const { url, type } = e.currentTarget.dataset;
      if (url.includes("pdf")) {
        wx.downloadFile({
          // 示例 url，并非真实存在
          url: `${DOMAIN}/${url}`,
          success: function (res) {
            const filePath = res.tempFilePath;
            wx.openDocument({
              filePath: filePath,
              success: function (res) {
                wx.hideLoading();
              },
            });
          },
        });
      } else {
        wepy.navigateTo({
          url: `${url}?pid=${this.defaultUser.patHisNo}&grid=${this.defaultUser.patCardNo}&patientId=${this.defaultUser.patientId}&type=${type}`,
        });
      }
    },
    // 页面滚动修切换航条颜色
    bindScroll(e) {
      if (e.detail.scrollTop > 50) {
        this.navFlag = "#fff";
        this.color = "#000";
      } else {
        this.navFlag = "transparent";
        this.color = "#fff";
      }
      this.$apply();
    },
  };

  events = {};
}
</script>
