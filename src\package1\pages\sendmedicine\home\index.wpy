<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import { getFormatDate } from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class ReportList extends wepy.page {
    config = {
      navigationBarTitleText: '药品寄送',
    };

    components = {
      'outpatient': Outpatient,
      'empty': Empty,
    };

    onShow() {
      const { patientId = '' } = this.$wxpage.options;
      this.$broadcast('outpatient-get-patient', { patientId });
    }

    data = {
      patientConfig: {
        infoShow: false,
        show: false,
        initUser: {},
      },
      emptyConfig: {
        show: false,
      },
      patient: {},
      pid: '',
      grid: '',
      changeShow: false,
      extra: {
        isShow: true,
        content: "1.我院采用到付的方式进行药品邮寄。\n2.药品在邮寄和收货过程中，出现的药品淋湿、破损、短缺、变质、包裹丢失等不由邮寄方承担。 \n3.考虑药品效应，我院目前仅邮寄常温储存药品，暂不邮寄需冷藏保存的药品。 \n4.请病友严格遵医嘱交待用药，如患者未按使用方法使用由患者本人承担"
      },
      list:[],
      addressList:[],
    };

    methods = {
      changeExtra() {
        this.extra.isShow = false;
        this.$apply();
      },
    };

    events = {
      'outpatient-change-user': function (item = {}) {
        if(item){
          this.patientConfig.infoShow = true;
          this.changeUser(item);
        }
      }
    };

    async changeUser(item = {}) {
      this.getList(item);
      this.getAddressList();
    }

    /**
     * 获取待缴费列表
     */
    async getList(item = {}, dateTypeValue) {
      const { patientId = '', patCardNo: grid = '', patHisNo: pid = '', } = item;
      const param = { pid: '416959' };
      this.emptyConfig.show = true;
      const { code = '', data = '' } = await Api.getList(param);
      if ( code == 0 ) {
        const { medicalSendPro = [] } = data;
        if (medicalSendPro.length > 0) {
          this.emptyConfig.show = false;
        }
        this.list = data && data.medicalSendPro || [];
        this.$apply();
      }
    }

    /**
     * 获取地址列表
     */
    async getAddressList(item = {}, dateTypeValue) {
      const { code = '', data = '' } = await Api.getAddressList();
      if ( code == 0 ) {
        this.addressList = data;
      }
    }
  }
</script>
