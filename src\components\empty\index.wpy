<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

export default class Empty extends wepy.component {
  props = {
    config: {
      type: Object,
      default: {
        show: true,
        action: false
      },
      twoWay: false,
      multipleSlots: true
    }
  };

  data = {};

  events = {};

  methods = {
    navToDeatil(e) {
      wepy.redirectTo({ url: "/pages/bindcard/index/index" });
    }
  };
}
</script>
