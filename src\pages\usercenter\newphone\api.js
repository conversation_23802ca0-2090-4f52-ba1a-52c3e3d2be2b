import { post } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

/**
 * 获取验证码 

 */
export const getValidate = (param) => post(`/api/customize/sendMsgAndValidate?_route=h${REQUEST_QUERY.platformId}`, param);

export const sendMsgAndValidate = (param) => post(`/api/customize/sendMsg?_route=h${REQUEST_QUERY.platformId}&phone=${param.phone}&msgKey=${param.msgKey}`);

export const checkMsgAndValidate = (param) => post(`/api/customize/checkMsg?_route=h${REQUEST_QUERY.platformId}&phone=${param.phone}&msgKey=${param.msgKey}&code=${param.code}`);

export const bindPatient = (param) => post('/api/user/bindpatients', param);

 