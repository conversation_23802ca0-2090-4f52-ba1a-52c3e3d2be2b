<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
// import { ADULT_SURVEY_ID, CHILD_SURVEY_ID } from '../../../config/constant';

export default class SurveyTools extends wepy.page {
  config = {
    navigationBarTitleText: '选择产品',
  };

  components = {
  };

  data = {
    currentStep: 1, // 当前步骤 (1:选择产品, 2:填写基础信息, 3:报告出具)
    selectedType: '', // 选中的问卷类型：normal-普通版，child-儿童版
    patientInfo: {}, // 患者信息
    patientId: '',
    patHisNo: '',
    patCardNo: '',
    id: '', // 问卷ID
    sampleCode: '', // 样本编码
    timestamp: Date.now(), // 时间戳，用于条形码图片URL
    surveyId: '', // 问卷ID，默认为成人版
    adultSurveyId: '82', // 存储成人问卷ID的默认值
    childSurveyId: '82', // 存储儿童问卷ID的默认值
    isConfigLoaded: false, // 配置是否已加载
    hisId: '242', // 默认医院ID
    platformId: '242', // 默认平台ID
    titleList: [], // 问卷题目列表
    startExpandIdx: 0, // 展开的起始索引
    isSpecialDis: false, // 是否特殊疾病
    readonly: 0, // 是否只读
    currentEditingFillBlank: null, // 记录当前正在编辑的填空项
    formData: {
      name: '',
      idType: '',
      idTypeId: '1', // 初始化证件类型ID
      idNumber: '',
      age: '',
      gender: '', // 性别ID
      genderId: '', // 性别ID，男:1，女:2
      genderName: '', // 性别显示名称
      phone: '',
      height: '',
      weight: '',
      occupation: '',
      occupationName: '', // 职业名称
      education: '',
      educationName: '', // 文化程度名称
      // 儿童版监护人信息
      jhrName: '',
      jhrIdType: '身份证',
      jhrIdTypeId: '1', // 初始化监护人证件类型ID
      jhrIdNumber: '',
      jhrPhone: ''
    },
    // 表单验证相关
    focusField: '', // 当前聚焦的字段
    errors: {}, // 表单错误信息
    idTypeOptions: [
      { id: '1', name: '身份证' },
      { id: '2', name: '其他' }
    ],
    genderOptions: ['男', '女'],
    genderMap: {
      '男': '1',
      '女': '2'
    },
    occupationOptions: [
      { id: '1', name: '市场/销售/商务' },
      { id: '2', name: '采购' },
      { id: '3', name: '行政' },
      { id: '4', name: '人力' },
      { id: '5', name: '产品' }
    ],
    educationOptions: [
      { id: '1', name: '初中及以下' },
      { id: '2', name: '高中' },
      { id: '3', name: '大学' },
      { id: '4', name: '研究生' }
    ],
    maritalStatusOptions: [
      { id: '1', name: '未婚' },
      { id: '2', name: '已婚' },
      { id: '3', name: '离异' },
      { id: '4', name: '丧偶' },
      { id: '5', name: '其他' }
    ],
    surveyQuestions: [], // 问卷问题数据
    surveyAnswers: {}, // 问卷答案
    specialReports: {}, // 特殊题目ID对应的文件列表
    signStatus: '0', // 签署状态：0-未签署，1-已签署
    signPdfUrl: '', // 签署的PDF文件URL
    signCheckTimer: null, // 检查签署状态的定时器
    orderDetail: null, // 订单详情
    signFileList: [], // 存储知情同意书文件列表
    reportStatus: '0', // 报告状态
    sampleTime: '', // 样本时间
    receiveTime: '', // 接收时间
    reportTime: '', // 报告时间
    originalBrightness: null, // 存储原始屏幕亮度
    showConsentModal: false,
    showBasicInfoModal: false,
    basicInfoTableRows: [],
    questionType: '', // 保存问卷类型
    questionUserId: '', // 问卷用户ID
    questionnaireStatus: '0' // 问卷状态：0-需要填写，1-查看详情
  };

  onLoad(options) {
    // 先加载配置
    this.loadConfig().then(() => {
      // 获取参数
      const { patientId = '', patHisNo = '', patCardNo = '', type = '', step = '', id = '', surveyId = '', orderId = '', questionType = '' } = options;
    this.patientId = patientId;
    this.patHisNo = patHisNo;
    this.patCardNo = patCardNo;
    this.id = id; // 保存订单ID
    this.questionType = questionType; // 保存问卷类型
    
    // 初始化证件类型为身份证
    this.formData.idType = '身份证';
    this.formData.idTypeId = '1';
    this.formData.jhrIdType = '身份证';
    this.formData.jhrIdTypeId = '1';
    
    // 如果传入了orderId参数，优先加载订单详情
    if (orderId) {

      // 调用loadOrderDetail方法加载订单详情并根据状态跳转
      this.loadOrderDetail(orderId);
      return; // 提前返回，不执行后续代码
    }
    
    // 如果传入了问卷ID则直接使用
    if (surveyId) {
      this.surveyId = surveyId;

    }
          // 如果传入了问卷类型，根据类型选择对应的问卷ID
      else if (questionType) {
        this.surveyId = questionType === '2' ? this.childSurveyId : this.adultSurveyId;

      }
      // 如果传入了type参数，根据type选择对应的问卷ID
      else if (type) {
        this.surveyId = type === 'child' ? this.childSurveyId : this.adultSurveyId;
        this.questionType = type === 'child' ? '2' : '1'; // 设置问卷类型

      }
      // 默认使用成人版问卷ID
      else {
        this.surveyId = this.adultSurveyId;
        this.questionType = '1'; // 默认成人版问卷类型

      }
    
    // 如果传入了type参数，说明是直接选择了问卷类型
    if (type) {
      this.selectedType = type;
      this.$apply();
    }
    
    // 如果传入了step参数，设置当前步骤
    if (step) {
      const stepNum = parseInt(step);
      if (!isNaN(stepNum) && stepNum >= 1 && stepNum <= 3) {
        this.currentStep = stepNum;
        // 更新导航栏标题
        let title = '选择产品';
        switch (stepNum) {
          case 2: title = '填写基础信息'; break;
          case 3: title = '报告出具'; break;
        }
        wepy.setNavigationBarTitle({ title });

        // 如果是步骤3，加载订单详情
        if (stepNum === 3 && this.id) {
          this.methods.loadOrderDetailForReport.call(this);
        }

        this.$apply();
      }
    }
    
          // 如果没有传入患者信息，尝试从全局获取
      if (!patientId && !patHisNo) {
        try {
          this.getPatientInfo();
        } catch (error) {
          console.error('获取患者信息失败', error);
        }
      }
    }).catch(error => {
      console.error('加载配置失败', error);
      wepy.showToast({
        title: '加载配置失败',
        icon: 'none',
        duration: 2000
      });
    });
  }

  onShow() {
    // 确保职业和文化程度的显示名称正确设置
    if (this.formData.occupation) {
      this.formData.occupationName = this.formData.occupation;
    }
    
    if (this.formData.education) {
      this.formData.educationName = this.formData.education;
    }
    
    // 如果当前在步骤四且有订单ID，重新启动定时器检查签署状态
    // 这确保从知情同意书小程序返回时能继续查询签署状态
    if (this.currentStep === 4 && this.id) {
      console.log('从其他小程序返回到步骤四，重新启动定时器检查签署状态');
      this.methods.startCheckSignStatus.call(this);
    }
    
    // 如果在报告出具页面（步骤3），重新加载订单详情以刷新状态
    if (this.currentStep === 3 && this.id) {
      console.log('在报告出具页面检测到页面显示，重新加载订单详情');
      this.methods.loadOrderDetailForReport.call(this);
    }
    
    this.$apply();
  }

  /**
   * 页面隐藏时清除定时器
   */
  onHide() {
    // 清除定时器
    this.clearSignCheckTimer();
    // 恢复原始亮度
    this.restoreOriginalBrightness();
  }

  /**
   * 页面卸载时清除定时器
   */
  onUnload() {
    // 清除定时器
    this.clearSignCheckTimer();
    // 恢复原始亮度
    this.restoreOriginalBrightness();
  }
  
  /**
   * 清除签署状态检查定时器
   */
  clearSignCheckTimer() {
    console.log('清除签署状态检查定时器');
    if (this.signCheckTimer) {
      clearInterval(this.signCheckTimer);
      this.signCheckTimer = null;
    }
  }

  // 获取患者信息
  async getPatientInfo() {
    try {
      // 检查是否能够访问全局数据
      if (!this.$parent || !this.$parent.$parent) {
        console.log('无法访问全局数据，$parent或$parent.$parent为undefined');
        return;
      }
      
      // 从全局数据获取患者信息
      const globalData = this.$parent.$parent.globalData || {};
      const outpatient = globalData.outpatient || {};
      const activePatient = outpatient.activePatient || {};
      
      if (activePatient.patientId) {
        this.patientId = activePatient.patientId;
        this.patHisNo = activePatient.patHisNo;
        this.patCardNo = activePatient.patCardNo;
        
        // 填充基础信息表单
        this.formData.name = activePatient.name || '';
        this.formData.genderName = activePatient.sex || '';
        // 设置性别ID
        if (activePatient.sex === '男') {
          this.formData.gender = '1';
          this.formData.genderId = '1';
          this.formData.genderName = '男';
        } else if (activePatient.sex === '女') {
          this.formData.gender = '2';
          this.formData.genderId = '2';
          this.formData.genderName = '女';
        }
        this.formData.idNumber = activePatient.idCard || '';
        this.formData.idType = '身份证';
        this.formData.idTypeId = '1'; // 默认身份证
        this.formData.phone = activePatient.phone || '';
        if (activePatient.age) {
          this.formData.age = String(activePatient.age);
        }
        
        // 设置职业和文化程度的显示名称
        if (this.formData.occupation) {
          this.formData.occupationName = this.formData.occupation;
        }
        
        if (this.formData.education) {
          this.formData.educationName = this.formData.education;
        }
      }
      this.$apply();
    } catch (error) {
      console.error('获取患者信息失败', error);
    }
  }

  /**
   * 验证表单
   */
  validateForm() {
    // 重置错误信息
    this.errors = {};
    let hasError = false;
    
    // 验证必填字段
    if (!this.formData.name) {
      this.errors.name = '请输入姓名';
      hasError = true;
    } else if (this.formData.name.length < 2) {
      this.errors.name = '姓名至少需要2个字符';
      hasError = true;
    }
    
    // 儿童版不需要验证idType，只验证监护人的证件类型
    if (this.selectedType !== 'child') {
      if (!this.formData.idType) {
        this.errors.idType = '请选择证件类型';
        hasError = true;
      }
        
      if (!this.formData.idNumber) {
        this.errors.idNumber = '请输入证件号码';
        hasError = true;
      } else if (this.formData.idType === '身份证' && !this.validateIdCard(this.formData.idNumber)) {
        this.errors.idNumber = '请输入有效的身份证号码';
        hasError = true;
      }
    }
    
    if (!this.formData.age) {
      this.errors.age = '请输入年龄';
      hasError = true;
    } else if (isNaN(this.formData.age) || parseInt(this.formData.age) <= 0 || parseInt(this.formData.age) > 120) {
      this.errors.age = '请输入有效的年龄(1-120)';
      hasError = true;
    }
    
    if (!this.formData.genderId) {
      this.errors.gender = '请选择性别';
      hasError = true;
    }
    
    if (!this.formData.phone && this.selectedType !== 'child') {
      this.errors.phone = '请输入联系电话';
      hasError = true;
    } else if (this.formData.phone && !/^1[3-9]\d{9}$/.test(this.formData.phone) && this.selectedType !== 'child') {
      this.errors.phone = '请输入有效的手机号码';
      hasError = true;
    }
    
    // 验证可选字段 - 只有当用户填写了这些字段时才验证格式
    if (this.formData.height && this.formData.height.trim() !== '') {
      if (isNaN(this.formData.height) || parseFloat(this.formData.height) <= 0 || parseFloat(this.formData.height) > 250) {
        this.errors.height = '请输入有效的身高(0-250cm)';
        hasError = true;
      }
    }

    if (this.formData.weight && this.formData.weight.trim() !== '') {
      if (isNaN(this.formData.weight) || parseFloat(this.formData.weight) <= 0 || parseFloat(this.formData.weight) > 300) {
        this.errors.weight = '请输入有效的体重(0-300kg)';
        hasError = true;
      }
    }

    // 如果是儿童版，验证监护人信息
    if (this.selectedType === 'child') {
      if (!this.formData.jhrName) {
        this.errors.jhrName = '请输入监护人姓名';
        hasError = true;
      }
      
      if (!this.formData.jhrIdType) {
        this.errors.jhrIdType = '请选择监护人证件类型';
        hasError = true;
      }
      
      if (!this.formData.jhrIdNumber) {
        this.errors.jhrIdNumber = '请输入监护人证件号码';
        hasError = true;
      } else if (this.formData.jhrIdType === '身份证' && !this.validateIdCard(this.formData.jhrIdNumber)) {
        this.errors.jhrIdNumber = '请输入正确的身份证号码';
        hasError = true;
      }
      
      if (!this.formData.jhrPhone) {
        this.errors.jhrPhone = '请输入监护人联系电话';
        hasError = true;
      } else if (!/^1[3-9]\d{9}$/.test(this.formData.jhrPhone)) {
        this.errors.jhrPhone = '请输入正确的手机号码';
        hasError = true;
      }
    }

    this.$apply();

    // 返回验证结果：没有错误则返回true
    return !hasError;
  }

  methods = {

    /**
     * 选择问卷类型
     */
    selectSurveyType(e) {
      const { type } = e.currentTarget.dataset;
      this.selectedType = type;
      // 根据选择的问卷类型更新surveyId
      this.surveyId = type === 'child' ? this.childSurveyId : this.adultSurveyId;

      this.$apply();
    },

    /**
     * 选择问卷类型并直接进入步骤2
     */
    goToStep2(e) {
      const { type } = e.currentTarget.dataset;
      // 设置选择的问卷类型
      this.selectedType = type;
      // 根据选择的问卷类型更新surveyId
      this.surveyId = type === 'child' ? this.childSurveyId : this.adultSurveyId;

      
      // 更新当前步骤到第2步，显示基础信息填写界面
      this.currentStep = 2;
      // 更新导航栏标题
      wepy.setNavigationBarTitle({
        title: '填写基础信息'
      });
      this.$apply();
    },

    /**
     * 下一步
     */
    nextStep() {
      if (!this.selectedType) {
        wepy.showToast({
          title: '请选择产品类型',
          icon: 'none'
        });
        return;
      }

      // 更新当前步骤到第2步，显示基础信息填写界面
      this.currentStep = 2;
      // 更新导航栏标题
      wepy.setNavigationBarTitle({
        title: '填写基础信息'
      });
      this.$apply();
    },
    
    /**
     * 选择证件类型
     */
    selectIdType() {
      const that = this;
      wepy.showActionSheet({
        itemList: this.idTypeOptions.map(item => item.name)
      }).then(res => {
          const index = res.tapIndex;
          const selectedOption = that.idTypeOptions[index];
          that.formData.idType = selectedOption.name; // 显示名称
          that.formData.idTypeId = selectedOption.id; // 保存ID值用于提交
            // 清除错误提示
          if (that.errors.idType) {
            that.errors.idType = '';
          }
          that.$apply();
      }).catch(err => {
        console.log('选择证件类型取消', err);
      });
    },

    /**
     * 选择性别
     */
    selectGender() {
      const that = this;
      wepy.showActionSheet({
        itemList: this.genderOptions
      }).then(res => {
          const index = res.tapIndex;
        const genderName = that.genderOptions[index];
        const genderId = that.genderMap[genderName]; // 获取对应的性别ID
        that.formData.gender = genderId; // 保存ID值用于提交
        that.formData.genderId = genderId; // 设置对应的性别ID
        that.formData.genderName = genderName; // 设置性别显示名称
          // 清除错误提示
          if (that.errors.gender) {
            that.errors.gender = '';
          }
          that.$apply();
      }).catch(err => {
        console.log('选择性别取消', err);
      });
    },

    /**
     * 选择监护人证件类型
     */
    selectJhrIdType() {
      const that = this;
      wepy.showActionSheet({
        itemList: this.idTypeOptions.map(item => item.name)
      }).then(res => {
        const index = res.tapIndex;
        const selectedOption = that.idTypeOptions[index];
        that.formData.jhrIdType = selectedOption.name; // 显示名称
        that.formData.jhrIdTypeId = selectedOption.id; // 保存ID值用于提交
        // 清除错误提示
        if (that.errors.jhrIdType) {
          that.errors.jhrIdType = '';
        }
        that.$apply();
      }).catch(err => {
        console.log('选择监护人证件类型取消', err);
      });
    },

    /**
     * 选择职业
     */
    selectOccupation() {
      const that = this;
      wepy.showActionSheet({
        itemList: this.occupationOptions.map(item => item.name)
      }).then(res => {
          const index = res.tapIndex;
        if (index !== undefined) {
          const selectedOption = that.occupationOptions[index];
          that.formData.occupation = selectedOption.name; // 保存名称用于提交
          that.formData.occupationName = selectedOption.name; // 保存名称用于显示
  
          // 清除错误提示
          if (that.errors.occupation) {
            that.errors.occupation = '';
          }
          that.$apply();
        }
      }).catch(err => {
        console.log('选择职业取消', err);
      });
    },

    /**
     * 选择文化程度
     */
    selectEducation() {
      const that = this;
      wepy.showActionSheet({
        itemList: this.educationOptions.map(item => item.name)
      }).then(res => {
          const index = res.tapIndex;
        if (index !== undefined) {
          const selectedOption = that.educationOptions[index];
          that.formData.education = selectedOption.name; // 保存名称用于提交
          that.formData.educationName = selectedOption.name; // 保存名称用于显示
  
          // 清除错误提示
          if (that.errors.education) {
            that.errors.education = '';
          }
          that.$apply();
        }
      }).catch(err => {
        console.log('选择文化程度取消', err);
      });
    },

    /**
     * 输入框内容变化
     */
    inputChange(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;
      this.formData[field] = value;
      
      // 清除对应字段的错误提示
      if (this.errors[field]) {
        this.errors[field] = '';
      }
      
      // 当输入身份证号且证件类型是身份证时，自动计算年龄
      if (field === 'idNumber' && this.formData.idTypeId === '1' && value.length >= 15) {
        try {
          // 使用自定义方法计算年龄
          const age = this.calculateAgeFromIdCard(value);
          if (age > 0) {
            this.formData.age = String(age);

            
            // 自动设置性别
            const gender = this.getSexFromIdCard(value);
            if (gender) {
              this.formData.genderName = gender;
              this.formData.gender = this.genderMap[gender];
              this.formData.genderId = this.genderMap[gender];

            }
          }
        } catch (error) {
          console.error('计算年龄出错:', error);
        }
      }
      
      // 监护人身份证号处理
      if (field === 'jhrIdNumber' && this.formData.jhrIdTypeId === '1' && value.length >= 15) {
        try {
          // 使用自定义方法计算监护人年龄
          const age = this.calculateAgeFromIdCard(value);
          console.log('监护人年龄:', age);
        } catch (error) {
          console.error('计算监护人年龄出错:', error);
        }
      }
    },
    
    /**
     * 单选问卷答案变化
     */
    radioChange(e) {
      const { questionId } = e.currentTarget.dataset;
      this.surveyAnswers[questionId] = e.detail.value;
      this.$apply();
    },

    /**
     * 多选问卷答案变化
     */
    checkboxChange(e) {
      const { questionId } = e.currentTarget.dataset;
      console.log('[checkboxChange] Question ID:', questionId, 'Event Detail Value:', e.detail.value);
      console.log('[checkboxChange] Before update surveyAnswers[' + questionId + ']:', this.surveyAnswers[questionId]);
      // 直接用所有选中的值覆盖
      this.surveyAnswers[questionId] = e.detail.value;
      console.log('[checkboxChange] After update surveyAnswers[' + questionId + ']:', this.surveyAnswers[questionId]);
      this.$apply();
    },

    /**
     * 单选题选项变化
     */
    bindValueChange(e) {
      const { titleId, questionsType } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      console.log('单选题选项变化:', titleId, value);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        // 重置所有选项的选中状态
        this.titleList[titleIndex].optionList.forEach(option => {
          const wasChecked = option.checked;
          // 确保使用字符串比较，因为optionNum和value可能是字符串类型
          option.checked = String(option.optionNum) === String(value);
          
          console.log(`选项 ${option.optionNum} 选中状态: ${option.checked}, 比较: ${String(option.optionNum)} === ${String(value)}`);
          
          // 如果选项状态发生变化，处理二级选项
          if (option.checked !== wasChecked) {
            console.log('选项状态变化:', option.optionNum, '从', wasChecked, '变为', option.checked);
            
            // 如果是新选中的选项，处理二级选项
            if (option.checked) {
              console.log('选项被选中:', option.optionNum, 'optionType:', option.optionType, 'secondOptionContent:', option.secondOptionContent);
              
              // 如果有secondOptionContent字段且optionType为2，处理成数组
              if (option.optionType === '2' && option.secondOptionContent) {
                console.log('处理二级选项数组:', option.secondOptionContent);
                // 确保secondOptionArray是一个有效的数组
                if (option.secondOptionContent.indexOf(',') !== -1) {
                  option.secondOptionArray = option.secondOptionContent.split(',');
                } else {
                  option.secondOptionArray = [option.secondOptionContent];
                }

                // 确保二级选项数组已正确初始化
                if (!option.secondOptionArray || option.secondOptionArray.length === 0) {
                  option.secondOptionArray = ['无选项'];
                }

                // 确保selectedSecondOptions是一个空数组，这样所有二级选项默认都不选中
                option.selectedSecondOptions = [];
                console.log('初始化二级选项数组:', option.secondOptionArray, '选中状态:', option.selectedSecondOptions);
              }

              // 如果有secondOptionContent字段且optionType为3，处理成多值填空
              if (option.optionType === '3' && option.secondOptionContent) {
                console.log('处理二级多值填空:', option.secondOptionContent);
                option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
                console.log('初始化二级多值填空:', option.secondContentParts);
              }
            }
          }
        });
        
        // 确保至少有一个选项被选中
        const hasCheckedOption = this.titleList[titleIndex].optionList.some(option => option.checked);
        if (!hasCheckedOption && this.titleList[titleIndex].optionList.length > 0) {
          console.log('没有选中的选项，强制选中值为', value);
          const optionToCheck = this.titleList[titleIndex].optionList.find(option => String(option.optionNum) === String(value));
          if (optionToCheck) {
            optionToCheck.checked = true;
            console.log('强制选中选项:', optionToCheck.optionNum);
          }
        }
        
        console.log('更新后的选项状态:', this.titleList[titleIndex].optionList.map(o => ({
          optionNum: o.optionNum,
          checked: o.checked,
          optionType: o.optionType,
          secondOptionContent: o.secondOptionContent
        })));
        
      this.$apply();
      }
    },
    
    /**
     * 多选题选项变化
     */
    checkboxChange(e) {
      const { titleId, curOption, optionGroup } = e.currentTarget.dataset;
      
      console.log('多选题选项变化:', titleId, curOption, optionGroup);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        // 找到对应的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(curOption));
        if (optionIndex !== -1) {
          // 切换选中状态
          const wasChecked = this.titleList[titleIndex].optionList[optionIndex].checked;
          this.titleList[titleIndex].optionList[optionIndex].checked = !wasChecked;
          
          console.log(`多选项 ${curOption} 选中状态从 ${wasChecked} 变为 ${!wasChecked}`);
          
          // 如果是新选中的选项，处理二级选项
          if (!wasChecked) {
            const option = this.titleList[titleIndex].optionList[optionIndex];
            console.log('选项被选中:', option.optionNum, 'optionType:', option.optionType, 'secondOptionContent:', option.secondOptionContent);
            
            // 如果有secondOptionContent字段且optionType为2，处理成数组
            if (option.optionType === '2' && option.secondOptionContent) {
              console.log('处理二级选项数组:', option.secondOptionContent);
              // 确保secondOptionArray是一个有效的数组
              if (option.secondOptionContent.indexOf(',') !== -1) {
                option.secondOptionArray = option.secondOptionContent.split(',');
              } else {
                option.secondOptionArray = [option.secondOptionContent];
              }

              // 确保二级选项数组已正确初始化
              if (!option.secondOptionArray || option.secondOptionArray.length === 0) {
                option.secondOptionArray = ['无选项'];
              }

              // 确保selectedSecondOptions是一个空数组，这样所有二级选项默认都不选中
              option.selectedSecondOptions = [];
              console.log('初始化二级选项数组:', option.secondOptionArray, '选中状态:', option.selectedSecondOptions);
            }

            // 如果有secondOptionContent字段且optionType为3，处理成多值填空
            if (option.optionType === '3' && option.secondOptionContent) {
              console.log('处理二级多值填空:', option.secondOptionContent);
              option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
              console.log('初始化二级多值填空:', option.secondContentParts);
            }
          }
          
          // 如果有选项组，处理互斥逻辑
          if (optionGroup) {
            // 如果当前选项被选中，取消同组其他选项的选中状态
            if (this.titleList[titleIndex].optionList[optionIndex].checked) {
              this.titleList[titleIndex].optionList.forEach((item, idx) => {
                if (idx !== optionIndex && item.optionGroup === optionGroup) {
                  item.checked = false;
                }
              });
            }
          }
          
          this.$apply();
        } else {
          console.warn(`未找到选项 ${curOption} 在题目 ${titleId} 中`);
        }
      } else {
        console.warn(`未找到题目 ${titleId}`);
      }
    },
    
    /**
     * 二级输入框内容变化
     */
    bindSecondAnswerChange(e) {
      const { titleId, optionnum } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      console.log('二级输入框内容变化:', titleId, optionnum, value);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        console.log(`找到题目 ${titleId}，索引: ${titleIndex}`);
        
        // 找到对应的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => item.optionNum === optionnum);
        if (optionIndex !== -1) {
          console.log(`找到选项 ${optionnum}，索引: ${optionIndex}`);
          
          // 更新二级输入框内容
          this.titleList[titleIndex].optionList[optionIndex].secondAnswerContent = value;
          console.log(`已更新二级输入框内容: "${value}"`);
          
          // 确保应用更新
          this.$apply();
        } else {
          console.warn(`未找到选项 ${optionnum} 在题目 ${titleId} 中`);
        }
      } else {
        console.warn(`未找到题目 ${titleId}`);
      }
    },
    
    /**
     * 备注框内容变化
     */
    bindAnswerRemarkChange(e) {
      const { titleId, optionnum } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      console.log('备注框内容变化:', titleId, optionnum, value);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        // 找到对应的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => item.optionNum === optionnum);
        if (optionIndex !== -1) {
          // 更新备注框内容
          this.titleList[titleIndex].optionList[optionIndex].answerRemark = value;
          this.$apply();
          }
      }
    },
    
    /**
     * 扩展输入框内容变化
     */
    bindExtAnswerRemarkChange(e) {
      const { titleId, optionnum, idx } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      console.log('扩展输入框内容变化:', titleId, optionnum, idx, value);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        // 找到对应的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => item.optionNum === optionnum);
        if (optionIndex !== -1 && this.titleList[titleIndex].optionList[optionIndex].extInputList) {
          // 更新扩展输入框内容
          this.titleList[titleIndex].optionList[optionIndex].extInputList[idx].value = value;
          this.$apply();
          }
      }
    },

    /**
     * 二级多值填空输入框获取焦点
     */
    bindSecondFillBlankFocus(e) {
      try {
        const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset;
        const value = e.detail.value || '';

        console.log(`二级多值填空获得焦点: 题目ID=${titleId}, 选项=${optionNum}, 索引=${index}, 唯一ID=${uniqueId}, 值="${value}"`);
      } catch (error) {
        console.error('bindSecondFillBlankFocus 错误:', error);
      }
    },

    /**
     * 二级多值填空输入框内容变化
     */
    bindSecondFillBlankChange(e) {
      try {
        const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset;
        const value = e.detail.value;

        console.log(`二级多值填空内容变化: 题目ID=${titleId}, 选项=${optionNum}, 索引=${index}, 唯一ID=${uniqueId}, 值="${value}"`);

        // 找到对应的题目和选项
        const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
        if (titleIndex !== -1) {
          const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum));
          if (optionIndex !== -1) {
            const option = this.titleList[titleIndex].optionList[optionIndex];

            // 如果有唯一ID，优先使用唯一ID查找
            if (uniqueId && option.secondContentParts) {
              const partIndex = option.secondContentParts.findIndex(part => part.uniqueId === uniqueId);
              if (partIndex !== -1) {
                option.secondContentParts[partIndex].value = value;

                // 同时更新答案数组
                if (!option.secondAnswerValues) {
                  option.secondAnswerValues = new Array(option.secondInputCount || 0).fill('');
                }

                // 确保数组有足够长度
                const targetIndex = option.secondContentParts[partIndex].index;
                while (option.secondAnswerValues.length <= targetIndex) {
                  option.secondAnswerValues.push('');
                }

                option.secondAnswerValues[targetIndex] = value || '';

                console.log(`通过唯一ID=${uniqueId}更新二级多值填空: 题目ID=${titleId}, 选项=${optionNum}, 部分索引=${partIndex}, 目标索引=${targetIndex}, 值="${value}"`);

                // 强制应用更新
                this.$apply();
                return;
              }
            }
          }
        }
      } catch (error) {
        console.error('bindSecondFillBlankChange 错误:', error);
      }
    },

    /**
     * 二级选项复选框变化
     */
    bindSecondOptionChange(e) {
      const { titleId, optionNum } = e.currentTarget.dataset;
      const values = e.detail.value;
      
      console.log('二级选项复选框变化:', titleId, optionNum, values);
      
      // 找到对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1) {
        console.log(`找到题目 ${titleId}，索引: ${titleIndex}`);
        
        // 找到对应的选项
        const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum));
        if (optionIndex !== -1) {
          console.log(`找到选项 ${optionNum}，索引: ${optionIndex}`);
          
          // 保存选中的二级选项值
          this.titleList[titleIndex].optionList[optionIndex].selectedSecondOptions = values || [];
          console.log(`已保存选中的二级选项: ${JSON.stringify(values || [])}`);
          
          // 确保应用更新
      this.$apply();
        } else {
          console.warn(`未找到选项 ${optionNum} 在题目 ${titleId} 中`);
        }
      } else {
        console.warn(`未找到题目 ${titleId}`);
      }
    },
    

    
    /**
     * 处理填空题输入框获取焦点
     */
    bindFillBlankFocus(e) {
      try {
        // 保存当前正在编辑的输入框信息，防止自动清空
        const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset;
        const value = e.detail.value || '';
        
        console.log(`填空题获得焦点: 题目ID=${titleId}, 选项=${optionNum}, 索引=${index}, 唯一ID=${uniqueId}, 值="${value}"`);
        
        // 记录当前正在编辑的信息
        this.currentEditingFillBlank = {
          titleId,
          optionNum,
          index: Number(index),
          uniqueId,
          value
        };
        
        // 设置输入框样式为聚焦状态
        //e.currentTarget.style.borderBottomColor = '#2BA99A';
        
        // 在获取焦点时，打印当前题目的所有填空项信息，用于调试
        const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
        if (titleIndex !== -1) {
          const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum));
          if (optionIndex !== -1) {
            const option = this.titleList[titleIndex].optionList[optionIndex];
            const inputParts = option.contentParts.filter(part => part.isInput);
            
            console.log(`题目${titleId}选项${optionNum}的所有填空项:`, 
              inputParts.map(part => ({
                索引: part.index,
                唯一ID: part.uniqueId,
                值: part.value
              }))
            );
          }
        }
      } catch (error) {
        console.error('bindFillBlankFocus 错误:', error);
      }
    },
    
    /**
     * 处理填空题输入框失去焦点
     */
    bindFillBlankBlur(e) {
      try {
        const { titleId, optionNum, index, uniqueId } = e.currentTarget.dataset;
        const value = e.detail.value;
        
        console.log(`填空题失去焦点: 题目ID=${titleId}, 选项=${optionNum}, 索引=${index}, 唯一ID=${uniqueId}, 值="${value}"`);
        
        // 恢复输入框样式为普通状态
        // e.currentTarget.style.borderBottomColor = '#3ECEB6';
        
        // 直接在这里处理输入变化，而不是调用bindFillBlankChange
        try {
          // 使用精确的索引确保更新到正确的输入框
          const currentIndex = Number(index);
          
          // 找到对应的题目和选项
          const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
          if (titleIndex !== -1) {
            const optionIndex = this.titleList[titleIndex].optionList.findIndex(item => String(item.optionNum) === String(optionNum));
            if (optionIndex !== -1) {
              // 更新输入框的值
              const option = this.titleList[titleIndex].optionList[optionIndex];
              
              // 如果有唯一ID，优先使用唯一ID查找
              if (uniqueId) {
                const partIndex = option.contentParts.findIndex(part => part.uniqueId === uniqueId);
                if (partIndex !== -1) {
                  option.contentParts[partIndex].value = value;
                  
                  // 同时更新答案数组
                  if (!option.answerValues) {
                    option.answerValues = new Array(option.inputCount || 0).fill('');
                  }
                  
                  // 确保数组有足够长度
                  const targetIndex = option.contentParts[partIndex].index;
                  while (option.answerValues.length <= targetIndex) {
                    option.answerValues.push('');
                  }
                  
                  option.answerValues[targetIndex] = value || '';
                  
                  console.log(`通过唯一ID=${uniqueId}更新: 题目ID=${titleId}, 选项=${optionNum}, 部分索引=${partIndex}, 目标索引=${targetIndex}, 值="${value}"`);
                  
                  // 强制应用更新
                  this.$apply();
                  
                  // 清除正在编辑的状态
                  if (this.currentEditingFillBlank && 
                      this.currentEditingFillBlank.titleId === titleId && 
                      this.currentEditingFillBlank.optionNum === optionNum && 
                      (this.currentEditingFillBlank.index === Number(index) || this.currentEditingFillBlank.uniqueId === uniqueId)) {
                    this.currentEditingFillBlank = null;
                  }
                  
                  return;
                }
              }
              
              // 使用索引查找（备用方案）
              let inputIdx = 0;
              let foundIndex = -1;
              
              for (let i = 0; i < option.contentParts.length; i++) {
                if (option.contentParts[i].isInput) {
                  if (inputIdx === currentIndex) {
                    option.contentParts[i].value = value;
                    foundIndex = i;
                    break;
                  }
                  inputIdx++;
                }
              }
              
              if (foundIndex !== -1) {
                // 调试日志，记录具体设置的值
                console.log(`通过索引更新: 题目ID=${titleId}, 选项=${optionNum}, 索引=${currentIndex}, 内容部分索引=${foundIndex}, 值="${value}"`);
              
                // 同时更新答案数组
                if (!option.answerValues) {
                  option.answerValues = new Array(option.inputCount || 0).fill('');
                }
                
                // 确保数组有足够长度
                while (option.answerValues.length <= currentIndex) {
                  option.answerValues.push('');
                }
                
                option.answerValues[currentIndex] = value || ''; // 确保即使是空值也会被保存
              }
              
              // 强制应用更新
              this.$apply();
            }
          }
        } catch (innerError) {
          console.error('处理填空题输入变化错误:', innerError);
        }
        
        // 清除正在编辑的状态
        if (this.currentEditingFillBlank && 
            this.currentEditingFillBlank.titleId === titleId && 
            this.currentEditingFillBlank.optionNum === optionNum && 
            (this.currentEditingFillBlank.index === Number(index) || this.currentEditingFillBlank.uniqueId === uniqueId)) {
          this.currentEditingFillBlank = null;
        }
      } catch (error) {
        console.error('bindFillBlankBlur 错误:', error);
      }
    },
    
    /**
     * 提交基础信息，进入下一步
     */
    async submitBasicInfo() {
      if (!this.validateForm()) {
        this.showToast('请完善必填信息', 'none');
        return;
      }
      
      try {
        wepy.showLoading({
          title: '提交中...',
          mask: true
        });
        
        // 准备提交的数据
        const submitData = {
          userId: this.patientId, // 使用patientId作为userId
          hisId: '242', // 医院ID，固定为242
          questionType: this.selectedType === 'child' ? '2' : '1' // 问卷类型，成人版:1，儿童版:2
        };

        // 添加共同的必填字段
        submitData.sjzName = this.formData.name; // 姓名
        submitData.sjzAge = this.formData.age; // 年龄
        submitData.sjzSex = this.formData.genderId; // 性别ID，男:1，女:2
        
        // 添加共同的选填字段（如果有值）
        if (this.formData.height) submitData.sjzHeight = this.formData.height;
        if (this.formData.weight) submitData.sjzWeight = this.formData.weight;
        if (this.formData.nation) submitData.sjzNation = this.formData.nation;
        
        // 根据问卷类型添加不同的字段
        if (this.selectedType === 'child') {
          // 儿童版添加监护人信息（必填）
          submitData.jhrName = this.formData.jhrName; // 监护人姓名
          submitData.jhrIdType = this.formData.jhrIdTypeId; // 监护人证件类型ID
          submitData.jhrIdNum = this.formData.jhrIdNumber; // 监护人证件号码，改为jhrIdNum
          submitData.sjzPhone = this.formData.jhrPhone; // 监护人电话，传递到sjzPhone字段
          
          // 儿童版选填字段
          if (this.formData.height) submitData.sjzHeight = this.formData.height;
          if (this.formData.weight) submitData.sjzWeight = this.formData.weight;
        } else {
          // 成人版添加成人特有的字段
          submitData.sjzIdType = this.formData.idTypeId; // 证件类型ID，身份证:1，其他:2
          submitData.sjzIdNum = this.formData.idNumber; // 证件号码
          submitData.sjzPhone = this.formData.phone; // 电话
          
          // 成人版选填字段
          if (this.formData.occupation) submitData.occupation = this.formData.occupation;
          if (this.formData.education) submitData.education = this.formData.education;
        }
        

        
        // 调用API保存基础信息数据
        const res = await Api.saveSampleInfo(submitData);
        
        wepy.hideLoading();
        
        if (res && res.code === 0) {
          // 保存返回的ID，用于后续步骤
          if (res.data && res.data.id) {
            this.id = res.data.id;
          }
          
          // 确保职业和文化程度的显示名称正确设置
          if (this.formData.occupation) {
            this.formData.occupationName = this.formData.occupation;
          }
          
          if (this.formData.education) {
            this.formData.educationName = this.formData.education;
          }
          
          // 提示成功
          this.showToast('基础信息保存成功', 'success');
          
          // 跳转到报告出具页面（第3步）
          setTimeout(() => {
            this.currentStep = 3;
            // 更新导航栏标题
            wepy.setNavigationBarTitle({
              title: '报告出具'
            });
            this.$apply();
          }, 1000);
        } else {
          this.showToast(res.msg || '保存失败，请重试', 'none');
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('提交基础信息失败:', error);
        this.showToast('提交失败，请重试', 'none');
      }
    },

    /**
     * 保存基础信息按钮点击
     */
    async saveBasicInfo() {
      if (!this.validateForm()) {
        this.showToast('请完善必填信息', 'none');
        return;
      }

      try {
        wepy.showLoading({
          title: '保存中...',
          mask: true
        });

        // 准备提交的数据
        const submitData = {
          userId: this.patientId,
          hisId: '242',
          questionType: this.selectedType === 'child' ? '2' : '1'
        };

        // 添加共同的必填字段
        submitData.sjzName = this.formData.name;
        submitData.sjzAge = this.formData.age;
        submitData.sjzSex = this.formData.genderId;

        // 儿童版和成人版的不同处理
        if (this.selectedType === 'child') {
          // 儿童版字段
          submitData.jhrName = this.formData.jhrName;
          submitData.jhrIdType = this.formData.jhrIdTypeId; // 使用ID值 1或2
          submitData.jhrIdNum = this.formData.jhrIdNumber; // 改为jhrIdNum
          submitData.sjzPhone = this.formData.jhrPhone; // 监护人电话传递到sjzPhone字段

          // 儿童版选填字段
          if (this.formData.height) submitData.sjzHeight = this.formData.height;
          if (this.formData.weight) submitData.sjzWeight = this.formData.weight;
        } else {
          // 成人版字段
          submitData.sjzIdType = this.formData.idType;
          submitData.sjzIdNum = this.formData.idNumber;
          submitData.sjzPhone = this.formData.phone;

          // 成人版选填字段
          if (this.formData.occupation) submitData.occupation = this.formData.occupation;
          if (this.formData.education) submitData.education = this.formData.education;
        }

        console.log('保存基础信息数据:', submitData);

        // 调用API保存基础信息数据
        const res = await Api.saveSampleInfo(submitData);

        wepy.hideLoading();

        if (res && res.code === 0) {
          // 保存返回的ID，用于后续步骤
          if (res.data && res.data.id) {
            this.id = res.data.id;
          }

          // 提示成功
          this.showToast('基础信息保存成功', 'success');

          // 跳转到报告出具页面（第3步）
          setTimeout(() => {
            this.currentStep = 3;
            wepy.setNavigationBarTitle({
              title: '报告出具'
            });
            // 加载订单详情以显示基础信息
            if (this.id) {
              this.methods.loadOrderDetailForReport.call(this);
            }
            this.$apply();
          }, 1000);
        } else {
          this.showToast(res.msg || '保存失败，请重试', 'none');
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('保存基础信息失败:', error);
        this.showToast('保存失败，请重试', 'none');
      }
    },

    /**
     * 处理问卷详情点击
     */
    handleQuestionnaireClick() {
      // 确定要使用的问卷ID
      const surveyId = this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId;
      
      // 获取问卷类型参数
      const questionType = this.questionType || (this.selectedType === 'child' ? '2' : '1');
      
      // 根据问卷状态决定模式和操作
      if (this.questionnaireStatus === '1') {
        // 问卷已填写，跳转到surveydetail页面查看
        console.log('问卷已填写，跳转到surveydetail页面查看');
        
        // 构建URL参数
        let url = `/pages/survey/surveydetail/index?id=${surveyId}&readOnly=true&record=1`;
        
        // 添加orderId参数
        if (this.id) {
          url += `&orderId=${this.id}`;
        }
        
        // 添加问卷用户ID参数(优先使用questionnaireUrl，其次使用questionUserId)
        if (this.orderDetail && this.orderDetail.questionnaireUrl) {
          url += `&questionUserId=${this.orderDetail.questionnaireUrl}`;
        } else if (this.orderDetail && this.orderDetail.questionUserId) {
          url += `&questionUserId=${this.orderDetail.questionUserId}`;
        }
        
        console.log('跳转到问卷详情页面，URL:', url);
        
        wepy.navigateTo({
          url: url,
          success: () => {
            console.log('跳转到问卷详情页面成功');
          },
          fail: (error) => {
            console.error('跳转到问卷详情页面失败:', error);
            wepy.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          },
          complete: () => {
            // 页面返回时重新加载订单详情以刷新状态
            setTimeout(() => {
              if (this.currentStep === 3 && this.id) {
                console.log('从问卷详情页面返回，重新加载订单详情');
                this.methods.loadOrderDetailForReport.call(this);
              }
            }, 500);
          }
        });
      } else {
        // 问卷未填写，进入编辑模式
        console.log('问卷未填写，进入编辑模式');
        wepy.navigateTo({
          url: `/pages/survey/surveytools/questionnaire/index?id=${this.id}&surveyId=${surveyId}&questionType=${questionType}&mode=edit`,
          success: () => {
            console.log('跳转到问卷编辑页面成功');
          },
          fail: (error) => {
            console.error('跳转到问卷编辑页面失败:', error);
            wepy.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          },
          complete: () => {
            // 页面返回时重新加载订单详情以刷新状态
            setTimeout(() => {
              if (this.currentStep === 3 && this.id) {
                console.log('从问卷编辑页面返回，重新加载订单详情');
                this.methods.loadOrderDetailForReport.call(this);
              }
            }, 500);
          }
        });
      }
    },

    /**
     * 处理知情同意书点击
     */
    handleConsentClick() {
      // 检查知情同意书签署状态
      if (this.signStatus === '1') {
        // 已签署，打开知情同意书弹窗查看
        this.openConsentModal();
      } else {
        // 未签署，跳转到签署小程序
        console.log('知情同意书未签署，跳转到签署小程序');
        this.navigateToConsentMiniProgramDirectly();
      }
    },

    /**
     * 为报告出具页面加载订单详情
     */
    async loadOrderDetailForReport() {
      if (!this.id) return;

      try {
        wepy.showLoading({ title: '加载中...', mask: true });
        const res = await Api.getHealthOrderById({ id: this.id });
        wepy.hideLoading();

        if (res && res.code === 0 && res.data) {
          const orderData = res.data;

          // 更新订单详情
          this.orderDetail = orderData;
          
          // 从API响应中获取问卷状态和签署状态
          this.questionnaireStatus = orderData.questionnaireStatus || '0'; // 获取问卷状态
          this.signStatus = orderData.signStatus || '0'; // 获取签署状态
          
          console.log('订单详情数据:', orderData);
          console.log('问卷状态:', this.questionnaireStatus, '签署状态:', this.signStatus);

          // 设置基础信息显示所需的数据
          this.sjzName = orderData.sjzName || '';
          this.sjzPhone = orderData.sjzPhone || '';
          this.sampleCode = orderData.sampleNumber || orderData.id || '';
          
          // 条形码内容
          if (orderData.reportStatus === '0') {
            this.sampleCode = orderData.id;
            // 报告状态为0时，提高屏幕亮度以便扫码
            this.setMaxScreenBrightness();
          } else {
            this.sampleCode = orderData.sampleNumber || '';
            // 报告状态不为0时，恢复原始亮度
            this.restoreOriginalBrightness();
          }
          
          // 进度条状态
          this.reportStatus = orderData.reportStatus || '0';
          this.sampleTime = orderData.sampleTime || '';
          this.receiveTime = orderData.receiveTime || '';
          this.reportTime = orderData.reportTime || '';
          
          // 知情同意书文件
          if (orderData.signFiles) {
            try {
              this.signFileList = typeof orderData.signFiles === 'string' 
                ? JSON.parse(orderData.signFiles) 
                : orderData.signFiles;
              
              // 格式化日期
              this.signFileList.forEach(file => {
                if (file.signDate) {
                  try {
                    const date = new Date(file.signDate);
                    file.formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
                  } catch (e) {
                    console.error('格式化日期失败:', e);
                    file.formattedDate = file.signDate;
                  }
                }
              });
            } catch (e) {
              console.error('解析签署文件列表失败:', e);
              this.signFileList = [];
            }
          } else if (orderData.signFileList && orderData.signFileList.length > 0) {
            // 兼容旧的signFileList格式
            this.signFileList = orderData.signFileList;
            
            // 如果有signPdfUrl，也记录下来
            if (orderData.signPdfUrl) {
              this.signPdfUrl = orderData.signPdfUrl;
            }
          } else {
            this.signFileList = [];
            // 如果有signPdfUrl，也记录下来
            if (orderData.signPdfUrl) {
              this.signPdfUrl = orderData.signPdfUrl;
            }
          }
          
          // 设置时间戳，用于条形码图片URL
          this.timestamp = Date.now();
          
          // 准备基础信息表格数据
          this.methods.prepareBasicInfoTableData.call(this, orderData);
          
          this.$apply();
        } else {
          wepy.showToast({
            title: res && res.msg ? res.msg : '获取订单详情失败',
            icon: 'none'
          });
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('获取订单详情失败:', error);
        wepy.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    },

    /**
     * 提交问卷调查
     */
    submitSurvey() {
      // 直接调用submit方法，避免重复代码
      this.submit();
    },
    
    /**
     * 准备基础信息表格数据
     * @param {Object} orderData - 订单数据
     */
    prepareBasicInfoTableData(orderData) {
      if (!orderData) return;
      
      try {
        // 准备基础信息表格行数据
        this.basicInfoTableRows = [
          { label: '姓名', value: orderData.sjzName || '-' },
          { label: '性别', value: orderData.sjzSex || '-' },
          { label: '年龄', value: orderData.sjzAge || '-' },
          { label: '证件号码', value: orderData.sjzIdNum || '-' },
          { label: '联系电话', value: orderData.sjzPhone || '-' }
        ];
        
        // 如果有身高和体重，也添加到表格中
        if (orderData.sjzHeight) {
          this.basicInfoTableRows.push({ label: '身高', value: orderData.sjzHeight + ' cm' });
        }
        
        if (orderData.sjzWeight) {
          this.basicInfoTableRows.push({ label: '体重', value: orderData.sjzWeight + ' kg' });
        }
        
        // 如果是儿童版，添加监护人信息
        if (orderData.questionType === '2' || this.selectedType === 'child') {
          if (orderData.jhrName) {
            this.basicInfoTableRows.push({ label: '监护人姓名', value: orderData.jhrName });
          }
          
          if (orderData.jhrIdNum) {
            this.basicInfoTableRows.push({ label: '监护人证件号', value: orderData.jhrIdNum });
          }
          
          if (orderData.jhrPhone) {
            this.basicInfoTableRows.push({ label: '监护人电话', value: orderData.jhrPhone });
          }
        }
        
        console.log('基础信息表格数据已准备:', this.basicInfoTableRows);
      } catch (error) {
        console.error('准备基础信息表格数据失败:', error);
      }
    },
    
    /**
     * 提交按钮点击事件（问卷模板中的提交按钮）
     */
    async submit() {
      try {
        // 验证问卷是否填写完整
        if (!this.validateSurvey()) {
          return;
        }
        console.log('this', this);
        
        // 直接在这里收集问卷答案
        console.log('开始收集问卷答案...');
        const surveyData = [];
        
        try {
          // 遍历题目列表收集答案
          this.titleList.forEach((question) => {
            const { titleId, questionsType, optionList = [], required = '0' } = question;
            
            // 不同题型的答案收集处理
            switch (questionsType) {
              case '0': // 单选题
                // 找到选中的选项
                const selectedOption = optionList.find(option => option.checked);
                if (selectedOption) {
                  console.log(`收集单选题答案: 题目ID=${titleId}, 选项=${selectedOption.optionNum}`);
                  
                  // 创建基本答案对象
                  const answerItem = {
                    titleId,
                    optionNum: selectedOption.optionNum,
                    type: '0'
                  };
                  
                  // 处理二级选项内容
                  if (selectedOption.optionType === '1' && selectedOption.secondAnswerContent) {
                    // 二级文本框
                    answerItem.secondAnswerContent = selectedOption.secondAnswerContent;
                    console.log(`单选题二级文本框内容: ${selectedOption.secondAnswerContent}`);
                  } else if (selectedOption.optionType === '2' && selectedOption.selectedSecondOptions && selectedOption.selectedSecondOptions.length > 0) {
                    // 二级复选框，值用英文逗号隔开保存
                    answerItem.secondAnswerContent = selectedOption.selectedSecondOptions.join(',');
                    console.log(`单选题二级复选框内容: ${answerItem.secondAnswerContent}`);
                  } else if (selectedOption.optionType === '3' && selectedOption.secondAnswerValues && selectedOption.secondAnswerValues.length > 0) {
                    // 二级多值填空，检查是否有任意一个输入框有值
                    const hasValue = selectedOption.secondAnswerValues.some(val => val && val.trim() !== '');
                    if (hasValue) {
                      // 值用英文逗号隔开保存，与二级复选框格式一致
                      answerItem.secondAnswerContent = selectedOption.secondAnswerValues.join(',');
                      console.log(`单选题二级多值填空内容: ${answerItem.secondAnswerContent}`);
                    }
                  } else if (selectedOption.secondAnswerContent) {
                    // 兼容原有的secondAnswerContent字段
                    answerItem.secondAnswerContent = selectedOption.secondAnswerContent;
                    console.log(`单选题二级内容: ${selectedOption.secondAnswerContent}`);
                  }
                  
                  // 收集文件路径 - 先检查选项级别的文件
                  let filePaths = [];
                  
                  // 检查选项级别的uploadedFiles
                  if (selectedOption.uploadedFiles && selectedOption.uploadedFiles.length > 0) {
                    filePaths = filePaths.concat(selectedOption.uploadedFiles.map(file => file.url));
                    console.log(`单选题选项文件路径: ${filePaths.join(',')}`);
                  }
                  
                  // 检查题目级别的uploadedFiles
                  if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                    filePaths = filePaths.concat(question.uploadedFiles.map(file => file.url));
                    console.log(`单选题题目文件路径: ${filePaths.join(',')}`);
                  }
                  
                  // 如果有文件路径，添加filePath字段
                  if (filePaths.length > 0) {
                    answerItem.filePath = filePaths.join(',');
                    console.log(`单选题最终文件路径: ${answerItem.filePath}`);
                  }
                  
                  // 添加到答案列表
                  surveyData.push(answerItem);
                } else if (required === '1' || required === 1) {
                  // 必填题但没有选中任何选项，添加空答案
                  console.log(`必填单选题未选择: 题目ID=${titleId}`);
                  
                  // 创建基本答案对象
                  const answerItem = {
                    titleId,
                    optionNum: '',
                    type: '0'
                  };
                  
                  // 即使没有选中选项，也检查题目级别的uploadedFiles
                  if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                    answerItem.filePath = question.uploadedFiles.map(file => file.url).join(',');
                    console.log(`单选题题目文件路径(未选择选项): ${answerItem.filePath}`);
                  }
                  
                  surveyData.push(answerItem);
                }
                break;
                
              case '1': // 多选题
                // 找到所有选中的选项
                const selectedOptions = optionList.filter(option => option.checked);
                
                if (selectedOptions.length > 0) {
                  // 对每个选中的选项单独创建答案项
                  selectedOptions.forEach(option => {
                    console.log(`收集多选题答案: 题目ID=${titleId}, 选项=${option.optionNum}`);
                    
                    // 创建基本答案对象
                    const answerItem = {
                      titleId,
                      optionNum: option.optionNum,
                      type: '1'
                    };
                    
                    // 处理二级选项内容
                    if (option.optionType === '1' && option.secondAnswerContent) {
                      // 二级文本框
                      answerItem.secondAnswerContent = option.secondAnswerContent;
                      console.log(`多选题二级文本框内容: ${option.secondAnswerContent}`);
                    } else if (option.optionType === '2' && option.selectedSecondOptions && option.selectedSecondOptions.length > 0) {
                      // 二级复选框，值用英文逗号隔开保存
                      answerItem.secondAnswerContent = option.selectedSecondOptions.join(',');
                      console.log(`多选题二级复选框内容: ${answerItem.secondAnswerContent}`);
                    } else if (option.optionType === '3' && option.secondAnswerValues && option.secondAnswerValues.length > 0) {
                      // 二级多值填空，检查是否有任意一个输入框有值
                      const hasValue = option.secondAnswerValues.some(val => val && val.trim() !== '');
                      if (hasValue) {
                        // 值用英文逗号隔开保存，与二级复选框格式一致
                        answerItem.secondAnswerContent = option.secondAnswerValues.join(',');
                        console.log(`多选题二级多值填空内容: ${answerItem.secondAnswerContent}`);
                      }
                    } else if (option.secondAnswerContent) {
                      // 兼容原有的secondAnswerContent字段
                      answerItem.secondAnswerContent = option.secondAnswerContent;
                      console.log(`多选题二级内容: ${option.secondAnswerContent}`);
                    }
                    
                    // 收集文件路径 - 先检查选项级别的文件
                    let filePaths = [];
                    
                    // 检查选项级别的uploadedFiles
                    if (option.uploadedFiles && option.uploadedFiles.length > 0) {
                      filePaths = filePaths.concat(option.uploadedFiles.map(file => file.url));
                      console.log(`多选题选项文件路径: ${filePaths.join(',')}`);
                    }
                    
                    // 检查题目级别的uploadedFiles
                    if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                      filePaths = filePaths.concat(question.uploadedFiles.map(file => file.url));
                      console.log(`多选题题目文件路径: ${filePaths.join(',')}`);
                    }
                    
                    // 如果有文件路径，添加filePath字段
                    if (filePaths.length > 0) {
                      answerItem.filePath = filePaths.join(',');
                      console.log(`多选题最终文件路径: ${answerItem.filePath}`);
                    }
                    
                    // 添加到答案列表
                    surveyData.push(answerItem);
                  });
                } else if (required === '1' || required === 1) {
                  // 必填题但没有选中任何选项，添加空答案
                  console.log(`必填多选题未选择: 题目ID=${titleId}`);
                  
                  // 创建基本答案对象
                  const answerItem = {
                    titleId,
                    optionNum: '',
                    type: '1'
                  };
                  
                  // 即使没有选中选项，也检查题目级别的uploadedFiles
                  if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                    answerItem.filePath = question.uploadedFiles.map(file => file.url).join(',');
                    console.log(`多选题题目文件路径(未选择选项): ${answerItem.filePath}`);
                  }
                  
                  surveyData.push(answerItem);
                }
                break;
                
              case '18': // 填空题
                // 处理填空题
                optionList.forEach((option) => {
                  // 如果有答案数组
                  if (option.answerValues && option.answerValues.length > 0) {
                    // 检查是否有任意一个输入框有值，或者题目是必填的
                    const hasValue = option.answerValues.some(val => val && val.trim() !== '');
                    
                    // 对于必填题，即使没有填写也要提交空值
                    if (hasValue || required === '1' || required === 1) {
                      // 将填空答案合并为一个字符串，使用 | 分隔
                      const answerContent = option.answerValues.join(',');
                      
                      // 创建基本答案对象
                      const answerItem = {
                        titleId,
                        optionNum: option.optionNum,
                        answerContent,
                        type: '18'
                      };
                      
                      // 收集文件路径 - 先检查选项级别的文件
                      let filePaths = [];
                      
                      // 检查选项级别的uploadedFiles
                      if (option.uploadedFiles && option.uploadedFiles.length > 0) {
                        filePaths = filePaths.concat(option.uploadedFiles.map(file => file.url));
                        console.log(`填空题选项文件路径: ${filePaths.join(',')}`);
                      }
                      
                      // 检查题目级别的uploadedFiles
                      if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                        filePaths = filePaths.concat(question.uploadedFiles.map(file => file.url));
                        console.log(`填空题题目文件路径: ${filePaths.join(',')}`);
                      }
                      
                      // 如果有文件路径，添加filePath字段
                      if (filePaths.length > 0) {
                        answerItem.filePath = filePaths.join(',');
                        console.log(`填空题最终文件路径: ${answerItem.filePath}`);
                      }
                      
                      // 添加到答案列表
                      surveyData.push(answerItem);
                      
                      console.log(`收集填空题答案: 题目ID=${titleId}, 选项=${option.optionNum}, 值=[${option.answerValues.join(', ')}]`);
                    }
                  } else if (required === '1' || required === 1) {
                    // 必填题但没有填写任何内容，添加空答案
                    console.log(`必填填空题未填写: 题目ID=${titleId}, 选项=${option.optionNum}`);
                    
                    // 创建基本答案对象
                    const answerItem = {
                      titleId,
                      optionNum: option.optionNum,
                      answerContent: '',
                      type: '18'
                    };
                    
                    // 即使没有填写内容，也检查题目级别的uploadedFiles
                    let filePaths = [];
                    
                    // 检查选项级别的uploadedFiles
                    if (option.uploadedFiles && option.uploadedFiles.length > 0) {
                      filePaths = filePaths.concat(option.uploadedFiles.map(file => file.url));
                    }
                    
                    // 检查题目级别的uploadedFiles
                    if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                      filePaths = filePaths.concat(question.uploadedFiles.map(file => file.url));
                    }
                    
                    // 如果有文件路径，添加filePath字段
                    if (filePaths.length > 0) {
                      answerItem.filePath = filePaths.join(',');
                      console.log(`填空题题目文件路径(未填写): ${answerItem.filePath}`);
                    }
                    
                    surveyData.push(answerItem);
                  }
                });
                break;
                
              default:
                // 其他题型处理
                console.log(`未处理的题型: ${questionsType}, 题目ID=${titleId}`);
                break;
            }
          });
      } catch (error) {
          console.error('收集问卷答案出错:', error);
        }
        
        console.log('收集到的问卷答案:', surveyData);
        
        // 直接在这里保存问卷答案，不再调用单独的saveSurveyAnswers方法
        try {
          // 显示加载提示
          wepy.showLoading({
            title: '提交中...',
            mask: true
          });
          
          // 将收集到的答案格式化为API要求的格式
          const formattedAnswers = surveyData.map(item => {
            // 基本结构
            const formattedItem = {
              titleId: parseInt(item.titleId) || 0,
              optionNum: item.optionNum
            };
            
            // 填空题，添加answerContent字段
            if (item.type === '18' && item.answerContent) {
              formattedItem.answerContent = item.answerContent;
      }
            
            // 如果有二级选项内容，添加secondAnswerContent字段
            if (item.secondAnswerContent) {
              formattedItem.secondAnswerContent = item.secondAnswerContent;
            }
            
            // 如果有文件路径，添加filePath字段
            if (item.filePath) {
              formattedItem.filePath = item.filePath;
            }
            
            return formattedItem;
          });
          
          // 准备请求参数
          const params = {
            JsonToAnswer: JSON.stringify(formattedAnswers), // 将JSON对象转为字符串
            healthSampleOrderId: this.id || '', // 使用this.id作为healthSampleOrderId，这个ID应该是步骤二提交基础信息后返回的
            examId: this.surveyId || (this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId) // 问卷ID，根据问卷类型选择
          };
          
          console.log('提交问卷参数:', params);
          
          // 调用保存问卷API
          const res = await Api.saveQuestion(params);
          
          // 隐藏加载提示
          wepy.hideLoading();
          
          console.log('提交问卷接口返回:', res);
          
          if (res && res.code === 0) {
            // 提交成功，显示提示
            wepy.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 1500
            });
            
            // 延迟后跳转到步骤4（签署知情同意书）
            setTimeout(() => {
              this.currentStep = 4;
              // 更新导航栏标题
              wepy.setNavigationBarTitle({
                title: '签署知情同意书'
              });
              this.$apply();
              
              // 重置页面滚动位置到顶部
              wx.pageScrollTo({
                scrollTop: 0,
                duration: 300
              });
              
              // 自动调用签署知情同意书API
              console.log('提交问卷成功后自动调用签署知情同意书API，样本ID:', this.id);
              
              // 延迟一小段时间后调用签署API，确保UI已经更新
              setTimeout(async () => {
                try {
                  // 显示加载提示
                  wepy.showLoading({
                    title: '处理中...',
                    mask: true
                  });
                  
                  // 调用签署知情同意书API
                  let signRes;
                  
                  if (this.id) {
                    // 先调用getHealthOrderById检查是否已经有appId和pagePath
                    try {
                      const orderRes = await Api.getHealthOrderById({ id: this.id });
                      console.log('getHealthOrderById接口返回:', orderRes);
                      
                      // 如果接口调用成功并且返回了appId和pagePath，直接使用
                      if (orderRes && orderRes.code === 0 && orderRes.data) {
                        if (orderRes.data.appId && orderRes.data.pagePath) {
                          console.log('已获取到appId和pagePath，无需调用signSampleFile');
                          signRes = orderRes;
                        } else {
                          // 没有appId或pagePath，调用signSampleFile接口
                          console.log('未获取到appId或pagePath，调用signSampleFile接口');
                          signRes = await Api.signSampleFile({ id: this.id });
                        }
                      } else {
                        // 接口调用失败，使用signSampleFile作为备选
                        console.log('getHealthOrderById调用失败，使用signSampleFile作为备选');
                        signRes = await Api.signSampleFile({ id: this.id });
                      }
                    } catch (orderError) {
                      console.error('调用getHealthOrderById接口失败:', orderError);
                      console.log('尝试调用signSampleFile接口');
                      signRes = await Api.signSampleFile({ id: this.id });
                    }
                  } else {
                    // 如果没有id，尝试通过getProfileByKey获取配置
                    console.log('id值为空，调用getProfileByKey接口获取配置信息');
                    
                    // 准备请求参数
                    const params = {
                      hisId: 242,
                      platformId: 242,
                      key: this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId
                    };
                    
                    console.log('请求getProfileByKey接口参数:', params);
                    
                    // 调用getProfileByKey接口
                    const profileRes = await Api.getProfileByKey(params);
                    console.log('getProfileByKey接口返回:', profileRes);
                    
                    // 如果接口调用成功，使用返回的数据
                    if (profileRes && profileRes.code === 0) {
                      signRes = profileRes;
                    } else {
                      wepy.hideLoading();
                      this.showToast(profileRes && profileRes.msg ? profileRes.msg : '获取配置信息失败', 'none');
          return;
        }
                  }
                  
                  // 隐藏加载提示
                  wepy.hideLoading();
                  
                  console.log('签署知情同意书API返回:', signRes);
                  
                  if (signRes && signRes.code === 0) {
                    // API调用成功，显示返回数据
                    console.log('签署知情同意书成功，返回数据:', signRes.data);
                    
                    // 如果有签名URL，可以显示
                    if (signRes.data && signRes.data.signatureUrl) {
                      wepy.showToast({
                        title: '签署成功',
                        icon: 'success',
                        duration: 1500
                      });
                      
                      // 延迟后跳转到步骤5
                      setTimeout(() => {
                        this.currentStep = 5;
                        // 更新导航栏标题
                        wepy.setNavigationBarTitle({
                          title: '报告出具'
                        });
                        
                        // 如果返回了样本编码，保存它
                        if (signRes.data.sampleCode) {
                          this.sampleCode = signRes.data.sampleCode;
                        }
                        
                        this.$apply();
                        
                        // 重置页面滚动位置到顶部
                        wx.pageScrollTo({
                          scrollTop: 0,
                          duration: 300
                        });
                      }, 1500);
                      
                      return;
                    }
                    
                    // 从接口响应中获取appId和pagePath
                    const appId = signRes.data && signRes.data.appId ? signRes.data.appId : 'wx8caca9b0d69657b1'; // 默认使用接口返回的appId
                    const pagePath = signRes.data && signRes.data.pagePath ? signRes.data.pagePath : 'pages/patientSign/sickPersonSign'; // 默认使用接口返回的pagePath
                    
                    console.log('准备跳转到小程序，appId:', appId, 'pagePath:', pagePath);
                    
                    // 准备额外数据
                    const extraData = {
                      patientId: this.patientId,
                      patHisNo: this.patHisNo,
                      patCardNo: this.patCardNo,
                      id: this.id
                    };
                    
                    // 如果API返回了其他需要传递的数据，也添加到extraData中
                    if (signRes.data) {
                      if (signRes.data.orderCode) extraData.orderCode = signRes.data.orderCode;
                      if (signRes.data.fileCode) extraData.fileCode = signRes.data.fileCode;
                    }
                    
                    console.log('跳转携带的额外数据:', extraData);
                    
                    // 使用半屏小程序组件跳转到指定的小程序和页面
                    wepy.openEmbeddedMiniProgram({
                      appId: appId,
                      path: pagePath,
                      extraData: extraData,
                      // 半屏打开的高度
                      height: 600, // 以rpx为单位，可根据实际情况调整
                      // 是否开启全屏可拖拽，默认 false
                      draggable: true,
                      // 使弹窗内容可滚动（不支持手势跟随），默认 false
                      scrollable: true,
                      // 点击透明蒙层是否关闭，默认 true
                      maskClosable: true,
                      success: (result) => {
                        console.log('半屏跳转成功', result);
                        
                        // 跳转成功后，可以显示提示或进行其他操作
                        wepy.showToast({
                          title: '已打开签署页面',
                          icon: 'success',
                          duration: 1500
                        });
                      },
                      fail: (err) => {
                        console.error('半屏跳转失败', err);
                        wepy.showToast({
                          title: '打开签署页面失败，请重试',
                          icon: 'none',
                          duration: 2000
                        });
                      }
                    });
                  } else {
                    // API调用失败，显示错误信息
                    this.showToast(signRes && signRes.msg ? signRes.msg : '签署失败，请重试', 'none');
                  }
                } catch (error) {
                  wepy.hideLoading();
                  console.error('签署知情同意书失败:', error);
                  this.showToast('签署失败，请重试', 'none');
                }
              }, 500); // 延迟500毫秒后执行签署操作
            }, 1500);
          } else {
            // 提交失败，显示错误信息
            wepy.showToast({
              title: res && res.msg ? res.msg : '提交失败，请重试',
              icon: 'none',
              duration: 2000
            });
          }
        } catch (apiError) {
          wepy.hideLoading();
          console.error('提交问卷API调用失败:', apiError);
          wepy.showToast({
            title: '提交失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('提交问卷失败:', error);
        this.showToast('提交失败，请重试', 'none');
      }
    },
    
    /**
     * 跳过问卷，直接到签署知情同意书
     */
    async skipToMedical() {
      // 确保所有加载提示都已隐藏
      try {
        wepy.hideLoading();
      } catch (e) {
        console.log('隐藏加载提示出错，可能已经隐藏', e);
      }
      
      try {
        // 显示加载提示
        wepy.showLoading({
          title: '处理中...',
          mask: true
        });
        
        // 检查是否已经有id，如果有则表示已经保存过基础信息
        if (!this.id) {
          console.log('跳过问卷前保存基础信息');
          
          // 验证表单
          if (!this.validateForm()) {
            wepy.hideLoading();
            this.showToast('请完善必填信息', 'none');
            return;
          }
          
          // 准备提交的数据
          const submitData = {
            userId: this.patientId, // 使用patientId作为userId
            hisId: '242', // 医院ID，固定为242
            questionType: this.selectedType === 'child' ? '2' : '1' // 问卷类型，成人版:1，儿童版:2
          };

          // 添加共同的必填字段
          submitData.sjzName = this.formData.name; // 姓名
          submitData.sjzAge = this.formData.age; // 年龄
          submitData.sjzSex = this.formData.genderId; // 性别ID，男:1，女:2
          
          // 添加共同的选填字段（如果有值）
          if (this.formData.height) submitData.sjzHeight = this.formData.height;
          if (this.formData.weight) submitData.sjzWeight = this.formData.weight;
          if (this.formData.nation) submitData.sjzNation = this.formData.nation;
          
          // 根据问卷类型添加不同的字段
          if (this.selectedType === 'child') {
            // 儿童版添加监护人信息（必填）
            submitData.jhrName = this.formData.jhrName; // 监护人姓名
            submitData.jhrIdType = this.formData.jhrIdTypeId; // 监护人证件类型ID
            submitData.jhrIdNum = this.formData.jhrIdNumber; // 监护人证件号码
            submitData.sjzPhone = this.formData.jhrPhone; // 监护人电话，传递到sjzPhone字段
            // 成人版添加成人特有的字段
            submitData.sjzIdType = this.formData.idTypeId; // 证件类型ID，身份证:1，其他:2
            submitData.sjzIdNum = this.formData.idNumber; // 证件号码
            // 成人版选填字段
            if (this.formData.occupation) submitData.occupation = this.formData.occupation;
            if (this.formData.education) submitData.education = this.formData.education;
          } else {
            // 成人版添加成人特有的字段
            submitData.sjzIdType = this.formData.idTypeId; // 证件类型ID，身份证:1，其他:2
            submitData.sjzIdNum = this.formData.idNumber; // 证件号码
            submitData.sjzPhone = this.formData.phone; // 电话
            
            // 成人版选填字段
            if (this.formData.occupation) submitData.occupation = this.formData.occupation;
            if (this.formData.education) submitData.education = this.formData.education;
          }
          
          console.log('跳过问卷提交基础信息数据:', submitData);
          
          // 调用API保存基础信息数据
          const res = await Api.saveSampleInfo(submitData);
          
          if (res && res.code === 0) {
            // 保存返回的ID，用于后续步骤
            if (res.data && res.data.id) {
              this.id = res.data.id;
            }
          } else {
            wepy.hideLoading();
            this.showToast(res.msg || '保存失败，请重试', 'none');
            return;
          }
        }
        
        // 更新当前步骤到第4步，显示签署知情同意书界面
        this.currentStep = 4;
        // 更新导航栏标题
        wepy.setNavigationBarTitle({
          title: '签署知情同意书'
        });
        this.$apply();
        
        // 隐藏加载提示
        wepy.hideLoading();
        
        // 短暂延迟后自动调用跳转到知情同意书小程序的方法
        setTimeout(() => {
          console.log('自动调用跳转到知情同意书小程序方法');
          // 调用跳转方法
          if (this.methods && this.methods.navigateToConsentMiniProgram) {
            this.methods.navigateToConsentMiniProgram.call(this);
          } else {
            this.navigateToConsentMiniProgram();
          }
        }, 500);
      } catch (error) {
        wepy.hideLoading();
        console.error('跳过问卷失败:', error);
        this.showToast('操作失败，请重试', 'none');
      }
    },



    /**
     * 选择文件
     */
    chooseFile(e) {
      const { titleId } = e.currentTarget.dataset;
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      let uploadedCount = 0;
      if (titleIndex !== -1 && this.titleList[titleIndex].uploadedFiles) {
        uploadedCount = this.titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length;
      }
      wx.showActionSheet({
        itemList: ['拍摄', '从相册选择', '上传PDF文件'],
        success: (res) => {
          const tapIndex = res.tapIndex;
          if (tapIndex === 0 || tapIndex === 1) {
            wx.chooseImage({
              count: 9 - uploadedCount,
              sizeType: ['original', 'compressed'],
              sourceType: tapIndex === 0 ? ['camera'] : ['album'],
              success: (res) => {
                const tempFilePaths = res.tempFilePaths;
                if (!tempFilePaths || tempFilePaths.length === 0) return;
                const uploadNext = (idx) => {
                  if (idx >= tempFilePaths.length) {
                    this.updateAllImageCount();
                    this.$apply();
                    wx.showToast({ title: '上传成功', icon: 'success' });
                    return;
                  }
                  wx.showLoading({ title: '上传中...', mask: true });
                  Api.uploadFile(tempFilePaths[idx], 'image')
                    .then(res => {
                      wx.hideLoading();
                      if (res && res.code === 0 && res.data && res.data.url) {
                        if (titleId === 'gene_report' || titleId === 'physical_report') {
                          if (!this.specialReports) this.specialReports = {};
                          if (!this.specialReports[titleId]) this.specialReports[titleId] = [];
                          this.specialReports[titleId].push({ url: res.data.url, type: 'image', fileName: '图片' + (this.specialReports[titleId].length + 1) });
                        } else if (titleIndex !== -1) {
                          if (!this.titleList[titleIndex].uploadedFiles) this.titleList[titleIndex].uploadedFiles = [];
                          this.titleList[titleIndex].uploadedFiles.push({ url: res.data.url, type: 'image', fileName: '图片' + (this.titleList[titleIndex].uploadedFiles.length + 1) });
                        }
                      }
                      this.updateAllImageCount();
                      uploadNext(idx + 1);
                    })
                    .catch(() => {
                      wx.hideLoading();
                      wx.showToast({ title: '上传失败，请重试', icon: 'none' });
                      this.updateAllImageCount();
                      uploadNext(idx + 1);
                    });
                };
                uploadNext(0);
              },
              fail: (error) => {
                console.error('选择图片失败:', error);
              }
            });
          } else if (tapIndex === 2) {
            // 上传PDF文件，兼容性判断
            if (wx.chooseMessageFile) {
              wx.chooseMessageFile({
                count: 1,
                type: 'file',
                extension: ['pdf'],
                success: (res) => {
                  const tempFiles = res.tempFiles;
                  if (tempFiles && tempFiles.length > 0) {
                    const file = tempFiles[0];
                    const fileSizeInMB = file.size / (1024 * 1024);
                    if (fileSizeInMB > 20) {
                      wx.showToast({ title: '文件大小不能超过20MB', icon: 'none' });
                      return;
                    }
                    wx.showLoading({ title: '上传中...', mask: true });
                    Api.uploadFile(file.path, 'pdf')
                      .then(res => {
                        wx.hideLoading();
                        if (res && res.code === 0 && res.data && res.data.url) {
                          if (titleId === 'gene_report' || titleId === 'physical_report') {
                            if (!this.specialReports) this.specialReports = {};
                            if (!this.specialReports[titleId]) this.specialReports[titleId] = [];
                            this.specialReports[titleId].push({ url: res.data.url, type: 'pdf', fileName: file.name || 'PDF文件' + (this.specialReports[titleId].length + 1) });
                          } else if (titleIndex !== -1) {
                            if (!this.titleList[titleIndex].uploadedFiles) this.titleList[titleIndex].uploadedFiles = [];
                            this.titleList[titleIndex].uploadedFiles.push({ url: res.data.url, type: 'pdf', fileName: file.name || 'PDF文件' + (this.titleList[titleIndex].uploadedFiles.length + 1) });
                          }
                          this.$apply();
                          wx.showToast({ title: '上传成功', icon: 'success' });
                        } else {
                          wx.showToast({ title: '上传失败，请重试', icon: 'none' });
                        }
                      })
                      .catch(() => {
                        wx.hideLoading();
                        wx.showToast({ title: '上传失败，请重试', icon: 'none' });
                      });
                  }
                },
                fail: (error) => {
                  console.error('选择PDF文件失败:', error);
                  wx.showToast({
                    title: '当前微信版本不支持或用户取消',
                    icon: 'none'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '请升级微信版本以支持PDF上传',
                icon: 'none'
              });
            }
          }
        },
        fail: (error) => {
          console.error('显示操作菜单失败:', error);
        }
      });
    },
    

    
    /**
     * 删除文件
     */
    deleteFile(e) {
      const { titleId, url } = e.currentTarget.dataset;
      

      
      // 处理特殊题目ID
      if (titleId === 'gene_report' || titleId === 'physical_report') {
        if (this.specialReports && this.specialReports[titleId]) {
          // 从上传文件列表中移除
          this.specialReports[titleId] = this.specialReports[titleId].filter(file => file.url !== url);
          this.$apply();
        }
        return;
      }
      
      // 常规题目ID，查找对应的题目
      const titleIndex = this.titleList.findIndex(item => item.titleId === titleId);
      if (titleIndex !== -1 && this.titleList[titleIndex].uploadedFiles) {
        // 从上传文件列表中移除
        this.titleList[titleIndex].uploadedFiles = this.titleList[titleIndex].uploadedFiles.filter(file => file.url !== url);
        this.updateAllImageCount();
        this.$apply();
      }
    },
    
    /**
     * 查看文件
     */
    viewFile(e) {
      const { url } = e.currentTarget.dataset;
      

      
      // 判断文件类型
      if (url.toLowerCase().endsWith('.pdf')) {
        // PDF文件，使用新页面打开
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 下载文件
        wx.downloadFile({
          url: url,
          success: function(res) {
            wx.hideLoading();
            // 打开文档
            wx.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: function() {
                console.log('打开PDF成功');
              },
              fail: function(err) {
                console.error('打开PDF失败', err);
                wx.showToast({
                  title: '打开文件失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: function(err) {
            wx.hideLoading();
            console.error('下载PDF失败', err);
            wx.showToast({
              title: '下载文件失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 图片文件，使用预览组件
        wx.previewImage({
          urls: [url],
          current: url,
          fail: (err) => {
            console.error('预览图片失败', err);
            wx.showToast({
              title: '预览失败',
              icon: 'none'
            });
          }
        });
      }
    },

    /**
     * 跳转到知情同意书小程序
     */
    async navigateToConsentMiniProgram() {
      console.log('跳转到知情同意书小程序');
      
      try {
        // 确保先隐藏可能存在的加载提示
        try {
          wepy.hideLoading();
        } catch (e) {
          console.log('隐藏加载提示出错，可能已经隐藏', e);
        }
        
        // 显示加载提示
        wepy.showLoading({
          title: '处理中...',
          mask: true
        });
        
        // 在跳转前先清除定时器，避免在后台继续运行
        this.clearSignCheckTimer();
        
        let res;
        
        // 检查是否有id值
        if (!this.id) {
          console.log('id值为空，调用getProfileByKey接口获取配置信息');
          
          // 准备请求参数
          const params = {
            hisId: 242, // 使用常量中的医院ID
            platformId: 242, // 使用常量中的平台ID
            key: this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId // 根据问卷类型选择问卷ID
          };
          
          console.log('请求getProfileByKey接口参数:', params);
          
          // 调用getProfileByKey接口
          try {
            const profileRes = await Api.getProfileByKey(params);
            console.log('getProfileByKey接口返回:', profileRes);
            
            // 如果接口调用成功，使用返回的数据
            if (profileRes && profileRes.code === 0) {
              res = profileRes; // 使用profileRes作为后续处理的数据源
            } else {
              // 接口调用失败，显示错误信息
              wepy.hideLoading();
              wepy.showToast({
                title: profileRes && profileRes.msg ? profileRes.msg : '获取配置信息失败',
                icon: 'none',
                duration: 2000
              });
              return;
            }
          } catch (profileError) {
            console.error('调用getProfileByKey接口失败:', profileError);
            wepy.hideLoading();
            wepy.showToast({
              title: '获取配置信息失败，请重试',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          // 有id值，先调用getHealthOrderById检查是否已经有appId和pagePath
          console.log('先调用getHealthOrderById检查是否已经有签署参数，ID:', this.id);
          try {
            const orderRes = await Api.getHealthOrderById({ id: this.id });
            console.log('getHealthOrderById接口返回:', orderRes);
            
            // 如果接口调用成功并且返回了appId和pagePath，直接使用
            if (orderRes && orderRes.code === 0 && orderRes.data) {
              if (orderRes.data.appId && orderRes.data.pagePath) {
                console.log('已获取到appId和pagePath，无需调用signSampleFile');
                res = orderRes;
              } else {
                // 没有appId或pagePath，调用signSampleFile接口
                console.log('未获取到appId或pagePath，调用signSampleFile接口');
                res = await Api.signSampleFile({ id: this.id });
              }
            } else {
              // 接口调用失败，使用signSampleFile作为备选
              console.log('getHealthOrderById调用失败，使用signSampleFile作为备选');
              res = await Api.signSampleFile({ id: this.id });
            }
          } catch (orderError) {
            console.error('调用getHealthOrderById接口失败:', orderError);
            console.log('尝试调用signSampleFile接口');
            res = await Api.signSampleFile({ id: this.id });
          }
        }
        
        // 隐藏加载提示
        wepy.hideLoading();
        
        console.log('接口返回:', res);
        
        if (res && res.code === 0) {
          // API调用成功，显示返回数据
          console.log('接口调用成功，返回数据:', res.data);
          
          // 如果有签名URL，可以显示
          if (res.data && res.data.signatureUrl) {
            wepy.showToast({
              title: '签署成功',
              icon: 'success',
              duration: 1500
            });
            
            // 延迟后跳转到步骤5
            setTimeout(() => {
              this.currentStep = 5;
              // 更新导航栏标题
              wepy.setNavigationBarTitle({
                title: '报告出具'
              });
              
              // 如果返回了样本编码，保存它
              if (res.data.sampleCode) {
                this.sampleCode = res.data.sampleCode;
              }
              
              // 清除定时器，因为已经跳转到步骤5
              this.clearSignCheckTimer();
              
              this.$apply();
            }, 1500);
            
            return;
          }
          
          // 从接口响应中获取appId和pagePath
          const appId = res.data && res.data.appId ? res.data.appId : 'wx8caca9b0d69657b1'; // 默认使用接口返回的appId
          const pagePath = res.data && res.data.pagePath ? res.data.pagePath : 'pages/patientSign/sickPersonSign'; // 默认使用接口返回的pagePath
          
          console.log('准备跳转到小程序，appId:', appId, 'pagePath:', pagePath);
          
          // 准备额外数据
          const extraData = {
              patientId: this.patientId,
              patHisNo: this.patHisNo,
              patCardNo: this.patCardNo,
            id: this.id
          };
          
          // 如果API返回了其他需要传递的数据，也添加到extraData中
          if (res.data) {
            if (res.data.orderCode) extraData.orderCode = res.data.orderCode;
            if (res.data.fileCode) extraData.fileCode = res.data.fileCode;
          }
          
          console.log('跳转携带的额外数据:', extraData);
          
          // 使用半屏小程序组件跳转到指定的小程序和页面
          wepy.openEmbeddedMiniProgram({
            appId: appId,
            path: pagePath,
            extraData: extraData,
            // 半屏打开的高度
            height: 600, // 以rpx为单位，可根据实际情况调整
            // 是否开启全屏可拖拽，默认 false
            draggable: true,
            // 使弹窗内容可滚动（不支持手势跟随），默认 false
            scrollable: true,
            // 点击透明蒙层是否关闭，默认 true
            maskClosable: true,
            success: (result) => {
              console.log('半屏跳转成功', result);
              
              // 跳转成功后，可以显示提示或进行其他操作
              wepy.showToast({
                title: '已打开签署页面',
                icon: 'success',
                duration: 1500
              });
            },
            fail: (err) => {
              console.error('半屏跳转失败', err);
              wepy.showToast({
                title: '打开签署页面失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          // API调用失败，显示错误信息
          this.showToast(res && res.msg ? res.msg : '签署失败，请重试', 'none');
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('跳转到知情同意书失败:', error);
        this.showToast('操作失败，请重试', 'none');
      }
    },
    
    /**
     * 开始定时检查签署状态
     */
    startCheckSignStatus() {
      // 确保清除之前可能存在的定时器
      this.clearSignCheckTimer();
      
      // 如果没有订单ID，则不启动定时器
      if (!this.id) {
        console.log('没有订单ID，不启动定时器');
        return;
      }
      
      console.log('开始定时检查签署状态，ID:', this.id);
      
      // 先立即检查一次当前状态
      this.methods.checkSignStatus.call(this);
      
      // 设置定时器，每1.5秒检查一次
      this.signCheckTimer = setInterval(() => {
        // 如果当前不在步骤4，停止定时器
        if (this.currentStep !== 4) {
          console.log('不在步骤4，停止定时检查');
          this.clearSignCheckTimer();
          return;
        }
        
        // 如果没有订单ID，停止定时器
        if (!this.id) {
          console.log('没有订单ID，停止定时检查');
          this.clearSignCheckTimer();
          return;
        }
        
        this.methods.checkSignStatus.call(this);
      }, 1500); // 每1.5秒执行一次，稍微降低频率减轻服务器压力
    },
    
    /**
     * 检查签署状态
     */
    async checkSignStatus() {
      try {
        if (!this.id) {
          // 不再显示警告
          return;
        }
        
        console.log('正在检查签署状态，ID:', this.id);
        
        // 调用getHealthOrderById接口获取最新状态 - 不显示加载提示
        const res = await Api.getHealthOrderById({ id: this.id }, false);
        console.log('检查签署状态返回:', res);
        
        if (res && res.code === 0 && res.data) {
          // 保存订单详情
          this.orderDetail = res.data;
          
          // 更新签署状态
          this.signStatus = res.data.signStatus || '0';
          this.signPdfUrl = res.data.signPdfUrl || '';
          
          console.log('签署状态:', this.signStatus, '签署PDF URL:', this.signPdfUrl);
          
          // 如果签署状态为1，表示已经签署
          if (this.signStatus === '1') {
            console.log('检测到签署成功');
            
            // 清除定时器
            this.clearSignCheckTimer();
            
            // 显示签署成功提示，但只显示一次
            wx.showToast({
              title: '签署成功',
              icon: 'success',
              duration: 1500
            });
            
            // 更新页面显示
            this.$apply();
            
            // 2秒后自动跳转到报告页面（步骤5）
            setTimeout(() => {
              // 使用methods中的方法或直接调用类方法
              if (this.methods && this.methods.goToReportPage) {
                this.methods.goToReportPage.call(this);
              } else {
                this.goToReportPage();
              }
            }, 2000);
          }
          
          this.$apply();
        }
      } catch (error) {
        console.error('检查签署状态出错:', error);
        // 发生错误不中断定时查询，只记录错误
        // 不需要显示错误提示，避免干扰用户体验
      }
    },
    

    
    /**
     * 切换步骤
     */
    // async goToStep(step) {
     
    // },
    
    /**
     * 前进到下一步
     */
    goToNextStep() {
      if (this.currentStep < 5) {
        let step = this.currentStep + 1;
        
        try {
          // 检查步骤是否有效
          if (step < 1 || step > 5) {
            console.warn('步骤无效:', step);
            return;
          }

          // 如果是从步骤1到步骤2，需要验证是否选择了产品类型
          if (this.currentStep === 1 && step === 2) {
            if (!this.selectedType) {
              wepy.showToast({
                title: '请选择产品类型',
                icon: 'none'
              });
              return;
            }
          }
          
          // 如果离开步骤4，清除定时器
          if (this.currentStep === 4 && step !== 4) {
            this.clearSignCheckTimer();
          }
          
          // 更新当前步骤
          this.currentStep = step;
          
          // 更新导航栏标题
          let title = '选择产品';
          switch (step) {
            case 2: title = '填写基础信息'; break;
            case 3: title = '填写病史信息'; break;
            case 4: title = '签署知情同意书'; break;
            case 5: title = '报告出具'; break;
          }
          wepy.setNavigationBarTitle({ title });
          
          // 如果进入步骤3，获取问卷详情
          if (step === 3) {
            console.log('切换到步骤3，加载问卷详情');
            this.getSurveyDetail(this.surveyId).then(() => {
              this.$apply();
            }).catch(error => {
              console.error('加载问卷详情失败:', error);
            });
          }
          
          // 如果进入步骤4，开始定时检查签署状态
          if (step === 4 && this.id) {
            console.log('切换到步骤4，开始定时检查签署状态，ID:', this.id);
            this.methods.startCheckSignStatus.call(this);
          }
          
          this.$apply();
          
          // 重置页面滚动位置到顶部
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 300
          });
        } catch (error) {
          console.error('切换步骤出错:', error);
        }
      }
    },
    
    /**
     * 查看签署的PDF文件
     */
    viewSignedPdf() {
      // 检查是否有知情同意书文件
      if (this.signFileList && this.signFileList.length > 0 && this.signFileList[0].signPdfUrl) {
        // 使用第一个文件的URL
        const pdfUrl = this.signFileList[0].signPdfUrl;
        console.log('查看签署的PDF文件(从signFileList):', pdfUrl);
        
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 下载文件
        wx.downloadFile({
          url: pdfUrl,
          success: function(res) {
            wx.hideLoading();
            // 打开文档
            wx.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: function() {
                console.log('打开PDF成功');
              },
              fail: function(err) {
                console.error('打开PDF失败', err);
                wx.showToast({
                  title: '打开文件失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: function(err) {
            wx.hideLoading();
            console.error('下载PDF失败', err);
            wx.showToast({
              title: '下载文件失败',
              icon: 'none'
            });
          }
        });
        
      } else if (this.signPdfUrl) {
        // 兼容旧代码，使用signPdfUrl
        console.log('查看签署的PDF文件(从signPdfUrl):', this.signPdfUrl);
        
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 下载文件
        wx.downloadFile({
          url: this.signPdfUrl,
          success: function(res) {
            wx.hideLoading();
            // 打开文档
            wx.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: function() {
                console.log('打开PDF成功');
              },
              fail: function(err) {
                console.error('打开PDF失败', err);
                wx.showToast({
                  title: '打开文件失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: function(err) {
            wx.hideLoading();
            console.error('下载PDF失败', err);
            wx.showToast({
              title: '下载文件失败',
              icon: 'none'
            });
          }
        });
          } else {
      wepy.showToast({
          title: '文件不存在',
          icon: 'none'
        });
        return;
      }
    },


    
    /**
     * 跳转到步骤五（测试方法）
     */
    async goToStep5() {
      console.log('跳转到步骤五（测试）');
      // 步骤五前先请求订单详情
      try {
        const orderId = this.id;
        if (orderId) {
          wepy.showLoading({ title: '加载中...', mask: true });
          const res = await Api.getHealthOrderById({ id: orderId });
          console.log('订单详情:', res);
          wepy.hideLoading();
          if (res && res.code === 0 && res.data) {
            this.orderDetail = res.data;
            // 条形码内容
            if (res.data.reportStatus === '0') {
              this.sampleCode = res.data.id;
              // 报告状态为0时，提高屏幕亮度以便扫码
              this.setMaxScreenBrightness();
            } else {
              this.sampleCode = res.data.sampleNumber || '';
              // 报告状态不为0时，恢复原始亮度
              this.restoreOriginalBrightness();
            }
            // 姓名、手机号
            this.sjzName = res.data.sjzName || '';
            this.sjzPhone = res.data.sjzPhone || '';
            // 进度条状态
            this.reportStatus = res.data.reportStatus;
            this.sampleTime = res.data.sample_time || '';
            this.receiveTime = res.data.receiveTime || '';
            this.reportTime = res.data.reportTime || '';
          } else {
            this.sampleCode = 'SAMPLE' + Date.now();
            // 默认提高屏幕亮度以便扫码
            this.setMaxScreenBrightness();
          }
        } else {
          this.sampleCode = 'SAMPLE' + Date.now();
          // 默认提高屏幕亮度以便扫码
          this.setMaxScreenBrightness();
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('获取订单详情失败:', error);
        this.sampleCode = 'SAMPLE' + Date.now();
        // 默认提高屏幕亮度以便扫码
        this.setMaxScreenBrightness();
      }
      
      this.currentStep = 5;
      wepy.setNavigationBarTitle({ title: '报告出具' });
      this.clearSignCheckTimer();
      this.timestamp = Date.now();
      this.$apply();
      wepy.pageScrollTo({ scrollTop: 0, duration: 300 });
      wepy.showToast({ title: '已跳转到步骤五', icon: 'success', duration: 1500 });
    },

    /**
     * 步骤五页面查看报告
     */
    viewReport() {
      if (this.reportStatus !== '3') {
        wepy.showToast({ title: '报告尚未出具', icon: 'none' });
        return;
      }
      // TODO: 跳转或打开PDF，路径后续补充
      wepy.showToast({ title: '查看报告PDF功能待实现', icon: 'none' });
    },
    

    
    /**
     * 获取问卷用户ID
     * @param {String} surveyId - 问卷ID
     * @returns {Promise<String|null>} 问卷用户ID或null
     */
    async getQuestionUserId(surveyId) {
      // 从 orderDetail 中获取 questionnaireUrl 作为 questionUserId
      if (this.orderDetail && this.orderDetail.questionnaireUrl) {
        this.questionUserId = this.orderDetail.questionnaireUrl;
        console.log('从 questionnaireUrl 获取问卷用户ID:', this.questionUserId);
        
        this.$apply();
        return this.questionUserId;
      }
      
      console.warn('未找到 questionnaireUrl');
      return null;
    },
    
    /**
     * 获取问卷答案列表
     * @param {String} surveyId - 问卷ID
     * @returns {Promise<Object|null>} 问卷答案列表或null
     */
    async getAnswerList(surveyId) {
      try {
        const res = await Api.getSampleSurveyList({ id: surveyId });
        return res;
      } catch (error) {
        console.error('获取问卷答案列表失败:', error);
        return null;
      }
    },
    
    scroll() {
      // 这里可以添加滚动逻辑
    },
    
    /**
     * 跳转到问卷详情页面
     * @param {String} surveyId - 问卷ID
     * @param {String} questionUserId - 问卷用户ID，可选
     */
    goToSurveyDetail(surveyId, questionUserId) {
      // 构建URL，添加必要参数
      let url = `/pages/survey/surveydetail/index?id=${surveyId}&readOnly=true`;
      
      // 添加record=1参数，表示需要回显问卷记录
      url += '&record=1';
      
      // 添加questionUserId参数（如果有）
      if (questionUserId) {
        url += `&questionUserId=${questionUserId}`;
      }
      
      // 添加问卷类型参数
      if (this.questionType) {
        url += `&questionType=${this.questionType}`;
      }
      
      console.log('跳转到问卷详情页面，URL:', url);
      
      // 导航到问卷详情页面
      wepy.navigateTo({
        url: url
      });
    },
    
    /**
     * 跳转到报告页面（步骤5）
     */
    async goToReportPage() {
      try {
        // 显示加载提示
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 重新加载订单详情，确保数据最新
        if (this.id) {
          await this.loadOrderDetail(this.id);
        }
        
        // 更新当前步骤
        this.currentStep = 5;
        
        // 更新导航栏标题
        wepy.setNavigationBarTitle({
          title: '报告出具'
        });
        
        // 隐藏加载提示
        wx.hideLoading();
        
        this.$apply();
        
        // 重置页面滚动位置到顶部
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
      } catch (error) {
        // 隐藏加载提示
        wx.hideLoading();
        
        console.error('跳转到报告页面失败:', error);
        
        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
        
        // 仍然尝试切换到步骤5
        this.currentStep = 5;
        wepy.setNavigationBarTitle({
          title: '报告出具'
        });
        this.$apply();
      }
    },
    
    /**
     * 查看样本详情
     * @param {Object} e - 事件对象
     */
    viewSampleDetail(e) {
      const id = e.currentTarget.dataset.id;
      if (!id) {
        wx.showToast({
          title: '订单ID不能为空',
          icon: 'none'
        });
        return;
      }
      
      console.log('查看样本详情，ID:', id);
      
      // 加载订单详情并根据状态跳转
      this.loadOrderDetail(id);
    },
  };

  /**
   * 验证身份证号
   */
  validateIdCard(idCard) {
    // 简单验证，15位或18位
    if (/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)) {
      return true;
    }
    return false;
  }


  
  /**
   * 获取问卷详情
   * @param {String} id - 问卷ID
   */
  async getSurveyDetail(id) {
    try {
      // 检查API是否存在
      if (!Api || !Api.getSurveyDetail) {
        console.error('API或getSurveyDetail方法不存在');
        return;
      }
      
      // 默认使用ID为77的问卷
     
      
      wepy.showLoading({
        title: '加载中...',
        mask: true
      });
      
      // 保存问卷ID用于提交
      if (id) {
        this.surveyId = id;
      } else if (!this.surveyId) {
        // 如果没有设置问卷ID，则根据selectedType选择对应的问卷ID
        this.surveyId = this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId;
      }
      
      // 确保传递正确的id参数
      const res = await Api.getSurveyDetail({ id: this.surveyId });
      
      wepy.hideLoading();
      
      console.log('问卷详情接口返回:', res);
      
      if (res && res.code === 0 && res.data) {
        // 处理问卷数据
        this.titleList = res.data.titleList || [];
        this.startExpandIdx = res.data.startExpandIdx || 0;
        this.isSpecialDis = res.data.isSpecialDis || false;
        
        // 如果返回了问卷ID，保存它
        if (res.data.id) {
          this.surveyId = res.data.id;
        }
        
        // 初始化问卷答案数据
        this.initSurveyAnswers();
        
        // 假设获取到题目后：
        this.titleList.forEach(q => {
          q.imageCount = (q.uploadedFiles || []).filter(f => f.type === 'image').length;
        });
        
      this.$apply();
      } else {
        wepy.showToast({
          title: res.msg || '获取问卷详情失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      wepy.hideLoading();
      console.error('获取问卷详情失败', error);
      wepy.showToast({
        title: '获取问卷详情失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }
  
  /**
   * 初始化问卷答案数据
   */
  initSurveyAnswers() {
    console.log('初始化问卷答案数据...');
    
    // 遍历问卷题目，初始化答案数据结构
    if (this.titleList && this.titleList.length > 0) {
      this.titleList.forEach((question, qIndex) => {
        console.log(`初始化题目 ${question.titleId}: ${question.questionsTitle}`);
        
        // 处理填空题（多空填值）
        if (question.questionsType === '18') {
          try {
            if (question.optionList && question.optionList.length > 0) {
              question.optionList.forEach((option, optIndex) => {
                // 解析optionContent，将{val}替换为输入框
                const content = option.optionContent || '';
                const parts = [];
                let lastIndex = 0;
                let inputCount = 0;
                
                console.log(`处理填空题选项: ${option.optionNum}, 内容: ${content}`);
                
                // 使用正则表达式查找所有{val}占位符
                const regex = /\{val\}/g;
                let match;
                
                try {
                  while ((match = regex.exec(content)) !== null) {
                    // 添加占位符前的文本
                    if (match.index > lastIndex) {
                      parts.push({
                        isInput: false,
                        text: content.substring(lastIndex, match.index)
                      });
                    }
                    
                    // 确保索引是数字
                    const thisIndex = inputCount;
                    // 创建唯一ID，组合题目ID、选项编号和索引
                    const thisUniqueId = `${question.titleId}_${option.optionNum}_${thisIndex}`;
                    
                    // 添加输入框
                    parts.push({
                      isInput: true,
                      value: '',
                      index: thisIndex,
                      uniqueId: thisUniqueId
                    });
                    
                    console.log(`创建填空框 #${inputCount}: 索引=${thisIndex}, 唯一ID=${thisUniqueId}`);
                    
                    inputCount++;
                    lastIndex = match.index + match[0].length;
                  }
                  
                  // 添加最后一段文本
                  if (lastIndex < content.length) {
                    parts.push({
                      isInput: false,
                      text: content.substring(lastIndex)
                    });
                  }
                } catch (regexError) {
                  console.error('正则表达式解析错误:', regexError);
                  // 如果解析失败，就将整个内容作为文本显示
                  parts.push({
                    isInput: false,
                    text: content
                  });
                  inputCount = 0;
                }
                
                // 保存解析后的内容部分
                option.contentParts = parts;
                
                // 初始化答案数组，确保数组长度与输入框数量一致
                option.answerValues = new Array(inputCount).fill('');
                
                // 记录输入框总数
                option.inputCount = inputCount;
                
                // 打印填空题的所有输入框信息
                const inputParts = parts.filter(part => part.isInput);
                console.log(`填空题 ${question.titleId} 选项 ${option.optionNum} 初始化完成:`, {
                  输入框数量: inputCount,
                  输入框: inputParts.map(part => ({
                    索引: part.index,
                    唯一ID: part.uniqueId
                  }))
                });
              });
            }
          } catch (error) {
            console.error('初始化填空题失败:', error);
          }
        }
        
        if (question.optionList && question.optionList.length > 0) {
          question.optionList.forEach((option, oIndex) => {
            console.log(`初始化选项 ${option.optionNum}: ${option.optionContent}, 选项类型: ${option.optionType}`);
            
            // 初始化二级输入框内容
            if (option.optionType === '1') {
              option.secondAnswerContent = '';
              console.log(`初始化二级输入框内容为空`);
            }
            
            // 初始化二级选项数据
            if (option.optionType === '2') {
              // 确保selectedSecondOptions是一个空数组，这样所有二级选项默认都不选中
              option.selectedSecondOptions = [];

              // 如果有secondOptionContent字段，处理成数组
              if (option.secondOptionContent) {
                console.log(`处理二级选项内容: ${option.secondOptionContent}`);

                // 确保secondOptionArray是一个有效的数组
                if (option.secondOptionContent.indexOf(',') !== -1) {
                  option.secondOptionArray = option.secondOptionContent.split(',');
                } else {
                  option.secondOptionArray = [option.secondOptionContent];
                }

                // 确保二级选项数组已正确初始化
                if (!option.secondOptionArray || option.secondOptionArray.length === 0) {
                  option.secondOptionArray = ['无选项'];
                }

                console.log(`二级选项数组: ${JSON.stringify(option.secondOptionArray)}`);
              } else {
                console.log('选项没有secondOptionContent字段');
                // 确保即使没有secondOptionContent，也初始化secondOptionArray为空数组
                option.secondOptionArray = [];
              }
            }

            // 初始化二级多值填空数据
            if (option.optionType === '3') {
              // 如果有secondOptionContent字段，处理成多值填空
              if (option.secondOptionContent) {
                console.log(`处理二级多值填空内容: ${option.secondOptionContent}`);
                option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
                option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length;
                option.secondAnswerValues = new Array(option.secondInputCount).fill('');
                console.log(`二级多值填空解析结果: ${JSON.stringify(option.secondContentParts)}`);
              } else {
                console.log('选项没有secondOptionContent字段');
                option.secondContentParts = [];
                option.secondInputCount = 0;
                option.secondAnswerValues = [];
              }
            }
            
            // 初始化备注框内容
            if (option.haveRemarkFrame === 1) {
              option.answerRemark = '';
              console.log(`初始化备注框内容为空`);
              
              // 初始化扩展输入列表
              if (!option.extInputList) {
                option.extInputList = [{ label: '备注', value: '' }];
                console.log(`初始化扩展输入列表`);
              }
            }
            
            // 初始化上传文件列表
            if (question.fileFlg === '1' && !question.uploadedFiles) {
              question.uploadedFiles = [];
              console.log(`初始化上传文件列表`);
            }
          });
        }
      });
    }
    
    console.log('问卷答案数据初始化完成');
    this.$apply();
  }
  
  /**
   * 显示Toast提示
   */
  showToast(title, icon = 'none') {
    wepy.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  }

  /**
   * 验证问卷是否填写完整
   */
  validateSurvey() {
    // 检查必填题是否已填写
    let isValid = true;
    let firstErrorTitleId = '';
    let firstErrorTitleNum = '';
    let errorMessage = '请完成所有必填题目';
    
    console.log('开始验证问卷...');
    
    if (this.titleList && this.titleList.length > 0) {
      for (let i = 0; i < this.titleList.length; i++) {
        const question = this.titleList[i];
        
        console.log(`检查题目 ${question.titleId}: ${question.questionsTitle}, 题号: ${question.titleNum}, 是否必填: ${question.required === 1 || question.required === '1' ? '是' : '否'}, required值: ${question.required}`);
        
        // 检查必填题 - 兼容字符串"1"和数字1
        if (question.required === 1 || question.required === '1') {
          let hasAnswer = false;
          let hasSecondaryError = false;
          let secondaryErrorType = '';
          
          // 填空题（多空填值）验证
          if (question.questionsType === '18') {
          if (question.optionList && question.optionList.length > 0) {
              const option = question.optionList[0]; // 通常填空题只有一个选项
              
              if (option && option.answerValues) {
                // 检查是否所有输入框都已填写
                hasAnswer = option.answerValues.every(value => value && value.trim() !== '');
                
                if (!hasAnswer) {
                  console.log(`填空题 ${question.titleId} 未完全填写`);
                }
              } else {
                hasAnswer = false;
                console.log(`填空题 ${question.titleId} 没有答案数据`);
              }
            }
          }
          // 检查是否有选中的选项
          else if (question.optionList && question.optionList.length > 0) {
            // 先检查是否有选中的一级选项
            const selectedOptions = question.optionList.filter(option => option.checked);
            hasAnswer = selectedOptions.length > 0;
            
            console.log(`题目 ${question.titleId} 选中选项数量: ${selectedOptions.length}`);
            
            // 如果有选中的选项，检查是否需要填写二级内容
            if (hasAnswer) {
              for (const option of selectedOptions) {
                  // 检查二级文本框
                if (option.optionType === '1' && !option.secondAnswerContent && option.secondOptionContent) {
                      hasSecondaryError = true;
                      secondaryErrorType = 'text';
                  console.log(`选项 ${option.optionNum} 的二级文本框未填写`);
                      break;
                    }
                  
                // 检查二级选项
                if (option.optionType === '2' && (!option.selectedSecondOptions || option.selectedSecondOptions.length === 0) && option.secondOptionArray && option.secondOptionArray.length > 0) {
                      hasSecondaryError = true;
                      secondaryErrorType = 'checkbox';
                  console.log(`选项 ${option.optionNum} 的二级选项未选择`);
                      break;
                }

                // 检查二级多值填空
                if (option.optionType === '3' && option.secondContentParts && option.secondContentParts.length > 0) {
                  const inputCount = option.secondContentParts.filter(part => part.isInput).length;
                  if (!option.secondAnswerValues || option.secondAnswerValues.length < inputCount) {
                    wepy.showToast({
                      title: `第${question.titleNum}题第1个空未填写`,
                      icon: 'none',
                      duration: 2000
                    });
                    return false;
                  }
                  // 检查每个输入框都必须有值
                  for (let i = 0; i < inputCount; i++) {
                    if (!option.secondAnswerValues[i] || option.secondAnswerValues[i].trim() === '') {
                      wepy.showToast({
                        title: `第${question.titleNum}题第${i+1}个空未填写`,
                        icon: 'none',
                        duration: 2000
                      });
                      return false;
                    }
                  }
                }
              }
            }
          }
          
          if (!hasAnswer || hasSecondaryError) {
            isValid = false;
            
            // 记录第一个错误的题目ID和题号
            if (!firstErrorTitleId) {
            firstErrorTitleId = question.titleId;
              firstErrorTitleNum = question.titleNum;
            
              if (hasSecondaryError) {
              if (secondaryErrorType === 'text') {
                  errorMessage = `请填写题号 ${firstErrorTitleNum} 的输入框`;
              } else if (secondaryErrorType === 'checkbox') {
                  errorMessage = `请选择题号 ${firstErrorTitleNum} 的选项`;
              }
            } else {
                errorMessage = `请完成必填题 ${firstErrorTitleNum}`;
            }
            }
          }
        }
      }
    }
    
    if (!isValid) {
      // 提示用户第一个未完成的必填项
      this.showToast(errorMessage, 'none');
      
      // 滚动到该题目位置
      if (firstErrorTitleId) {
          wx.pageScrollTo({
          selector: `#title-${firstErrorTitleId}`,
          duration: 300,
          offsetTop: -20 // 适当的偏移量
      });
    }
      }
      
    return isValid;
  }
  
  /**
   * 加载订单详情并根据状态跳转
   * @param {String} orderId - 订单ID
   */
  async loadOrderDetail(orderId) {
    if (!orderId) {
      this.showToast('订单ID不能为空', 'none');
      return;
    }

    try {
      // 显示加载提示
      wepy.showLoading({
        title: '加载中...',
        mask: true
      });
              
      // 调用API获取订单详情
      const res = await Api.getHealthOrderById({ id: orderId });
      
      // 隐藏加载提示
      wepy.hideLoading();
      
      console.log('订单详情接口返回:', res);
      
      if (res && res.code === 0 && res.data) {
        const orderData = res.data;
        
        // 保存订单ID和其他必要信息
        this.id = orderData.id;
        this.orderDetail = orderData;
        // 如果有用户ID，保存它
        if (orderData.userId) {
          this.patientId = orderData.userId;
        }
        
        // 保存问卷用户ID，用于问卷详情回显
        if (orderData.questionUserId) {
          this.questionUserId = orderData.questionUserId;
          console.log('保存问卷用户ID:', this.questionUserId);
        } else {
          // 如果没有questionUserId，尝试获取
          this.methods.getQuestionUserId.call(this, this.id);
        }
                
        // 如果有医院ID，保存它
        if (orderData.hisId) {
          this.hisId = orderData.hisId;
        }
        
        // 根据状态判断跳转到哪个步骤
        const infoStatus = orderData.infoStatus + '';  // 转为字符串
        const questionnaireStatus = orderData.questionnaireStatus + '';  // 转为字符串
        const signStatus = orderData.signStatus + '';  // 转为字符串
        
        console.log('订单状态:', {
          infoStatus,
          questionnaireStatus,
          signStatus
        });
        
        // 根据状态设置当前步骤 - 根据新的逻辑，不再依赖infoStatus
        if (signStatus === '1') {
          // 知情同意书已签署，跳转到步骤5报告出具页面
          this.currentStep = 5;
          wepy.setNavigationBarTitle({
            title: '报告出具'
          });
          
          // 保存知情同意书文件列表
          this.signFileList = orderData.signFileList || [];
          
          // 如果有signFileList且不为空，从第一个文件获取PDF URL
          if (this.signFileList && this.signFileList.length > 0) {
            this.signPdfUrl = this.signFileList[0].signPdfUrl || '';
          } else {
            this.signPdfUrl = orderData.signPdfUrl || '';
          }
          
          // 设置条形码内容
          // 无论报告状态如何，都设置条形码内容，确保显示
          this.sampleCode = orderData.sampleNumber || orderData.id || '';
          console.log('设置条形码内容:', this.sampleCode);
          
          // 根据报告状态调整屏幕亮度
          if (orderData.reportStatus === '0') {
            // 报告状态为0时，提高屏幕亮度以便扫码
            this.setMaxScreenBrightness();
          } else {
            // 报告状态不为0时，恢复原始亮度
            this.restoreOriginalBrightness();
          }
          
          // 设置姓名、手机号
          this.sjzName = orderData.sjzName || '';
          this.sjzPhone = orderData.sjzPhone || '';
          
          // 设置进度条状态
          this.reportStatus = orderData.reportStatus || '0';
          this.sampleTime = orderData.sample_time || '';
          this.receiveTime = orderData.receiveTime || '';
          this.reportTime = orderData.reportTime || '';
          
          // 打印状态信息，便于调试
          console.log('进度状态信息:', {
            reportStatus: this.reportStatus,
            sampleTime: this.sampleTime,
            receiveTime: this.receiveTime,
            reportTime: this.reportTime
          });
          
          // 更新时间戳（用于条形码图片URL）
          this.timestamp = Date.now();
        } else if (signStatus === '0' && questionnaireStatus === '0') {
          // 知情同意书未签署，问卷未填写，跳转到步骤3填写病史信息页面
          this.currentStep = 3;
          wepy.setNavigationBarTitle({
            title: '填写病史信息'
          });
          
          // 加载问卷模板
          if (orderData.questionnaireUrl) {
            // 如果返回了问卷URL，可以直接加载
            this.surveyUrl = orderData.questionnaireUrl;
          } else {
            // 否则根据问卷类型加载默认问卷
            this.getSurveyDetail();
          }
        } else if (signStatus === '0' && questionnaireStatus === '1') {
          // 知情同意书未签署，问卷已填写，跳转到步骤4签署知情同意书页面
          this.currentStep = 4;
          wepy.setNavigationBarTitle({
            title: '签署知情同意书'
          });
          
          // 保存签署状态和ID
          this.signStatus = signStatus;
          
          // 保存知情同意书文件列表
          this.signFileList = orderData.signFileList || [];
          
          // 如果有signFileList且不为空，从第一个文件获取PDF URL
          if (this.signFileList && this.signFileList.length > 0) {
            this.signPdfUrl = this.signFileList[0].signPdfUrl || '';
          } else {
            this.signPdfUrl = orderData.signPdfUrl || '';
          }
          
          // 直接在这里实现定时检查逻辑，不使用setTimeout和methods中的方法
          console.log('从订单详情跳转到步骤4，直接开始定时检查签署状态，订单ID:', orderId);
          
          // 清除可能存在的旧定时器
          this.clearSignCheckTimer();
          
          // 立即执行一次检查
          this.checkOrderSignStatus(orderId);
          
          // 设置新的定时器
          this.signCheckTimer = setInterval(() => {
            // 如果当前不在步骤4，停止定时器
            if (this.currentStep !== 4) {
              console.log('不在步骤4，停止定时检查');
              this.clearSignCheckTimer();
              return;
            }
            
            this.checkOrderSignStatus(orderId);
          }, 1000);
          
          // 应用数据更改，确保UI已更新
          this.$apply();
          
          // 延迟一小段时间后自动跳转到知情同意书小程序
          setTimeout(() => {
            console.log('从订单详情进入步骤4，自动跳转到知情同意书小程序');
            // 使用直接方法，而不是通过methods调用
            this.navigateToConsentMiniProgramDirectly();
          }, 500); // 延迟500毫秒后执行跳转操作，确保UI更新完成
        } else {
          // 其他状态，默认跳转到步骤1
          this.currentStep = 1;
          wepy.setNavigationBarTitle({
            title: '选择产品'
          });
        }
        
        // 应用更改
        this.$apply();
        
        // 重置页面滚动位置到顶部
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
        
        return true;
    } else {
        this.showToast(res && res.msg ? res.msg : '获取订单详情失败', 'none');
        return false;
      }
    } catch (error) {
      wepy.hideLoading();
      console.error('加载订单详情失败:', error);
      this.showToast('加载失败，请重试', 'none');
      return false;
    }
  }
  
  /**
   * 直接检查签署状态（用于非methods作用域中调用）
   * @param {String} [orderId] - 可选的订单ID，如果提供则使用这个ID，否则使用this.id
   */
  async checkSignStatusDirectly(orderId) {
    try {
      // 使用传入的orderId或this.id
      const id = orderId || this.id;
      
      if (!id) {
        console.warn('没有有效的ID，无法检查签署状态');
        return;
      }
      
      console.log('直接检查签署状态，ID:', id);
      
      // 调用getHealthOrderById接口获取最新状态
      const res = await Api.getHealthOrderById({ id });
      console.log('检查签署状态返回:', res);
      
      if (res && res.code === 0 && res.data) {
        // 保存订单详情
        this.orderDetail = res.data;
        
        // 更新签署状态
        this.signStatus = res.data.signStatus || '0';
        this.signPdfUrl = res.data.signPdfUrl || '';
        
        console.log('签署状态:', this.signStatus, '签署PDF URL:', this.signPdfUrl);
        
        // 如果签署状态为1，表示已经签署
        if (this.signStatus === '1') {
          console.log('检测到签署成功');
          
          // 清除定时器
          if (this.signCheckTimer) {
            clearInterval(this.signCheckTimer);
            this.signCheckTimer = null;
          }
          
          // 显示签署成功提示
          wx.showToast({
            title: '签署成功',
            icon: 'success',
            duration: 1500
          });
        }
        
        this.$apply();
      }
    } catch (error) {
      console.error('检查签署状态出错:', error);
    }
  }

  /**
   * 检查指定订单ID的签署状态（专用于loadOrderDetail方法）
   * @param {String} orderId - 订单ID
   */
  async checkOrderSignStatus(orderId) {
    try {
      if (!orderId) {
        console.warn('订单ID为空，无法检查签署状态');
        return;
      }
      
      console.log('检查订单签署状态，订单ID:', orderId);
      
      // 调用getHealthOrderById接口获取最新状态 - 不显示加载提示
      const res = await Api.getHealthOrderById({ id: orderId }, false);
      console.log('检查签署状态返回:', res);
      
      if (res && res.code === 0 && res.data) {
        // 保存订单详情
        this.orderDetail = res.data;
        
        // 更新签署状态
        this.signStatus = res.data.signStatus || '0';
        this.signPdfUrl = res.data.signPdfUrl || '';
        
        console.log('签署状态:', this.signStatus, '签署PDF URL:', this.signPdfUrl);
        
        // 如果签署状态为1，表示已经签署
        if (this.signStatus === '1') {
          console.log('检测到签署成功');
          
          // 清除定时器
          if (this.signCheckTimer) {
            clearInterval(this.signCheckTimer);
            this.signCheckTimer = null;
          }
          
          // 显示签署成功提示
          wx.showToast({
            title: '签署成功',
            icon: 'success',
            duration: 1500
          });
        }
        
        // 刷新UI
        this.$apply();
      }
    } catch (error) {
      console.error('检查订单签署状态出错:', error, '订单ID:', orderId);
    }
  }

  /**
   * 直接跳转到知情同意书小程序（在组件内部使用，而不是从methods中调用）
   */
  async navigateToConsentMiniProgramDirectly() {
    console.log('直接跳转到知情同意书小程序');
    try {
      // 确保先隐藏可能存在的加载提示
      try {
        wepy.hideLoading();
      } catch (e) {
        console.log('隐藏加载提示出错，可能已经隐藏', e);
      }
      
      // 显示加载提示
      wepy.showLoading({
        title: '处理中...',
        mask: true
      });
      
      // 在跳转前先清除定时器，避免在后台继续运行
      this.clearSignCheckTimer();
      
      let res;
      
      // 检查是否有id值
      if (!this.id) {
        console.log('id值为空，调用getProfileByKey接口获取配置信息');
        
        // 准备请求参数
        const params = {
          hisId: 242, // 使用常量中的医院ID
          platformId: 242, // 使用常量中的平台ID
          key: this.selectedType === 'child' ? this.childSurveyId : this.adultSurveyId // 根据问卷类型选择问卷ID
        };
        
        console.log('请求getProfileByKey接口参数:', params);
        
        // 调用getProfileByKey接口
        try {
          const profileRes = await Api.getProfileByKey(params);
          console.log('getProfileByKey接口返回:', profileRes);
          
          // 如果接口调用成功，使用返回的数据
          if (profileRes && profileRes.code === 0) {
            res = profileRes; // 使用profileRes作为后续处理的数据源
          } else {
            // 接口调用失败，显示错误信息
            wepy.hideLoading();
            wepy.showToast({
              title: profileRes && profileRes.msg ? profileRes.msg : '获取配置信息失败',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } catch (profileError) {
          console.error('调用getProfileByKey接口失败:', profileError);
          wepy.hideLoading();
          wepy.showToast({
            title: '获取配置信息失败，请重试',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      } else {
        // 有id值，先调用getHealthOrderById检查是否已经有appId和pagePath
        console.log('直接方法: 先调用getHealthOrderById检查是否已经有签署参数，ID:', this.id);
        try {
          const orderRes = await Api.getHealthOrderById({ id: this.id });
          console.log('getHealthOrderById接口返回:', orderRes);
          
          // 如果接口调用成功并且返回了appId和pagePath，直接使用
          if (orderRes && orderRes.code === 0 && orderRes.data) {
            if (orderRes.data.appId && orderRes.data.pagePath) {
              console.log('已获取到appId和pagePath，无需调用signSampleFile');
              res = orderRes;
            } else {
              // 没有appId或pagePath，调用signSampleFile接口
              console.log('未获取到appId或pagePath，调用signSampleFile接口');
              res = await Api.signSampleFile({ id: this.id });
            }
          } else {
            // 接口调用失败，使用signSampleFile作为备选
            console.log('getHealthOrderById调用失败，使用signSampleFile作为备选');
            res = await Api.signSampleFile({ id: this.id });
          }
        } catch (orderError) {
          console.error('调用getHealthOrderById接口失败:', orderError);
          console.log('尝试调用signSampleFile接口');
          res = await Api.signSampleFile({ id: this.id });
        }
      }
      
      // 隐藏加载提示
      wepy.hideLoading();
      
      console.log('接口返回:', res);
      
      if (res && res.code === 0) {
        // API调用成功，显示返回数据
        console.log('接口调用成功，返回数据:', res.data);
        
        // 如果有签名URL，可以显示
        if (res.data && res.data.signatureUrl) {
          wepy.showToast({
            title: '签署成功',
            icon: 'success',
            duration: 1500
          });
          
          // 延迟后跳转到步骤5
          setTimeout(() => {
            this.currentStep = 5;
            // 更新导航栏标题
            wepy.setNavigationBarTitle({
              title: '报告出具'
            });
            
            // 如果返回了样本编码，保存它
            if (res.data.sampleCode) {
              this.sampleCode = res.data.sampleCode;
            }
            
            // 清除定时器，因为已经跳转到步骤5
            this.clearSignCheckTimer();
            
            this.$apply();
          }, 1500);
          
          return;
        }
        
        // 从接口响应中获取appId和pagePath
        const appId = res.data && res.data.appId ? res.data.appId : 'wx8caca9b0d69657b1'; // 默认使用接口返回的appId
        const pagePath = res.data && res.data.pagePath ? res.data.pagePath : 'pages/patientSign/sickPersonSign'; // 默认使用接口返回的pagePath
        
        console.log('准备跳转到小程序，appId:', appId, 'pagePath:', pagePath);
        
        // 准备额外数据
        const extraData = {
            patientId: this.patientId,
            patHisNo: this.patHisNo,
            patCardNo: this.patCardNo,
          id: this.id
        };
        
        // 如果API返回了其他需要传递的数据，也添加到extraData中
        if (res.data) {
          if (res.data.orderCode) extraData.orderCode = res.data.orderCode;
          if (res.data.fileCode) extraData.fileCode = res.data.fileCode;
        }
        
        console.log('跳转携带的额外数据:', extraData);
        
        // 使用半屏小程序组件跳转到指定的小程序和页面
        wepy.openEmbeddedMiniProgram({
          appId: appId,
          path: pagePath,
          extraData: extraData,
          // 半屏打开的高度
          height: 600, // 以rpx为单位，可根据实际情况调整
          // 是否开启全屏可拖拽，默认 false
          draggable: true,
          // 使弹窗内容可滚动（不支持手势跟随），默认 false
          scrollable: true,
          // 点击透明蒙层是否关闭，默认 true
          maskClosable: true,
          success: (result) => {
            console.log('半屏跳转成功', result);
            
            // 跳转成功后，可以显示提示或进行其他操作
            wepy.showToast({
              title: '已打开签署页面',
              icon: 'success',
              duration: 1500
            });
          },
          fail: (err) => {
            console.error('半屏跳转失败', err);
            wepy.showToast({
              title: '打开签署页面失败，请重试',
              icon: 'none',
              duration: 2000
            });
          }
        });
      } else {
        // API调用失败，显示错误信息
        this.showToast(res && res.msg ? res.msg : '签署失败，请重试', 'none');
      }
    } catch (error) {
      wepy.hideLoading();
      console.error('直接跳转到知情同意书失败:', error);
      this.showToast('操作失败，请重试', 'none');
    }
  }

  /**
   * 设置屏幕亮度为最大
   */
  async setMaxScreenBrightness() {
    try {
      // 获取当前亮度并保存
      const res = await wepy.getScreenBrightness();
      this.originalBrightness = res.value;
      console.log('保存原始屏幕亮度:', this.originalBrightness);
      
      // 设置亮度为最大
      await wepy.setScreenBrightness({
        value: 1.0 // 亮度值范围 0-1，1为最亮
      });
      console.log('设置屏幕亮度为最大');
    } catch (error) {
      console.error('设置屏幕亮度出错:', error);
    }
  }

  /**
   * 恢复原始屏幕亮度
   */
  async restoreOriginalBrightness() {
    try {
      // 如果有保存的原始亮度，则恢复
      if (this.originalBrightness !== null) {
        await wepy.setScreenBrightness({
          value: this.originalBrightness
        });
        console.log('恢复原始屏幕亮度:', this.originalBrightness);
        this.originalBrightness = null;
      }
    } catch (error) {
      console.error('恢复屏幕亮度出错:', error);
    }
  }

  // 这里不需要重复定义viewReport函数
  
  // 查看基础信息
  viewBasicInfo() {
    console.log('查看基础信息');
    // 弹出模态框展示基本信息
    wepy.showModal({
      title: '基础信息',
      content: `姓名: ${this.sjzName || '-'}\n` +
               `手机号: ${this.sjzPhone || '-'}\n` +
               `样本编号: ${this.sampleCode || '-'}\n` +
               `订单ID: ${this.id || '-'}`,
      showCancel: false,
      confirmText: '确定'
    });
  }
  
  // 查看问卷详情
  viewSurveyDetail() {
    console.log('查看问卷详情');
    if (!this.orderDetail || !this.orderDetail.id) {
      wepy.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }
    
    // 检查问卷状态
    const questionnaireStatus = this.orderDetail.questionnaireStatus;
    if (questionnaireStatus === '0') {
      wepy.showToast({
        title: '未填写问卷信息',
        icon: 'none'
      });
      return;
    }
    
    // 根据问卷类型选择对应的问卷ID
    let surveyId = this.orderDetail.id;
    
    // 如果有问卷类型，则根据类型选择对应的问卷ID
    if (this.orderDetail.questionType) {
      if (this.orderDetail.questionType === '1') {
        // 成人版问卷
        surveyId = this.adultSurveyId;
      } else if (this.orderDetail.questionType === '2') {
        // 儿童版问卷
        surveyId = this.childSurveyId;
      }
    }
    
    // 获取questionUserId，优先使用questionnaireUrl
    const questionUserId = this.questionUserId ||
                          (this.orderDetail && this.orderDetail.questionnaireUrl) ||
                          (this.orderDetail && this.orderDetail.questionUserId);

    // 如果没有questionUserId，尝试获取
    if (!questionUserId) {
      // 使用methods中的方法
      this.methods.getQuestionUserId.call(this, this.id).then(fetchedQuestionUserId => {
        this.methods.goToSurveyDetail.call(this, surveyId, fetchedQuestionUserId);
      }).catch(error => {
        console.error('获取问卷用户ID失败:', error);
        // 即使获取失败也尝试跳转
        this.methods.goToSurveyDetail.call(this, surveyId);
      });
    } else {
      // 已有questionUserId，直接跳转
      this.methods.goToSurveyDetail.call(this, surveyId, questionUserId);
    }
  }
  
  // 查看知情同意书
  viewConsentForm() {
    console.log('查看知情同意书');
    
    if (this.signFileList && this.signFileList.length > 0) {
      // 有多份知情同意书，弹出选择列表
      let items = this.signFileList.map(file => {
        return {
          name: file.fileName || '知情同意书',
          value: file.id
        };
      });
      
      wepy.showActionSheet({
        itemList: items.map(item => item.name),
        success: (res) => {
          if (res.tapIndex >= 0) {
            // 选择了某个知情同意书
            let selectedFile = this.signFileList[res.tapIndex];
            this.downloadAndOpenPdf(selectedFile.signPdfUrl);
          }
        }
      });
    } else if (this.signPdfUrl) {
      // 只有一份知情同意书
      this.downloadAndOpenPdf(this.signPdfUrl);
    } else {
      wepy.showToast({
        title: '未找到知情同意书',
        icon: 'none'
      });
    }
  }
  
  // 下载并打开PDF
  downloadAndOpenPdf(url) {
    if (!url) {
      wepy.showToast({
        title: '文件链接无效',
        icon: 'none'
      });
      return;
    }
    
    wepy.showLoading({ title: '加载中...', mask: true });
    
    wepy.downloadFile({
      url: url,
      success: function(res) {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: function() {
              console.log('打开文档成功');
            },
            fail: function(error) {
              console.error('打开文档失败:', error);
              wepy.showToast({
                title: '打开文档失败',
                icon: 'none'
              });
            }
          });
        } else {
          wepy.showToast({
            title: '下载文件失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        wx.hideLoading();
        console.error('下载文件失败:', error);
        wepy.showToast({
          title: '下载文件失败',
          icon: 'none'
        });
      }
    });
  }

  // 打开知情同意书弹窗
  openConsentModal() {
    // 在打开弹窗前，处理时间戳转换为日期字符串
    if (this.signFileList && this.signFileList.length > 0) {
      this.signFileList.forEach(file => {
        if (file.signDate) {
          // 判断是否为时间戳格式（数字或数字字符串）
          if (/^\d+$/.test(file.signDate)) {
            const timestamp = parseInt(file.signDate);
            const date = new Date(timestamp);
            // 格式化为 YYYY-MM-DD HH:MM:SS
            file.formattedDate = this.formatDate(date);
          }
        }
      });
    }
    this.showConsentModal = true;
    this.$apply();
  }
  
  // 格式化日期为 YYYY-MM-DD HH:MM:SS
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  // 关闭知情同意书弹窗
  closeConsentModal() {
    this.showConsentModal = false;
    this.$apply();
  }
  // 弹窗中点击查看PDF
  viewConsentPdf(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) {
      wepy.showToast({ title: '文件链接无效', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '加载中...', mask: true });
    wx.downloadFile({
      url: url,
      success: function(res) {
        wx.hideLoading();
        wx.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: function() {
            console.log('打开PDF成功');
          },
          fail: function(err) {
            console.error('打开PDF失败', err);
            wx.showToast({ title: '打开文件失败', icon: 'none' });
          }
        });
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('下载PDF失败', err);
        wx.showToast({ title: '下载文件失败', icon: 'none' });
      }
    });
  }

  // 打开基础信息弹窗
  openBasicInfoModal() {
    const d = this.orderDetail || {};
    const genderText = d.sjzSex === '1' ? '男' : (d.sjzSex === '2' ? '女' : '');
    // const questionTypeText = d.questionType === '1' ? '成人版' : (d.questionType === '2' ? '儿童版' : '');
    const fields = [
      { label: '姓名', value: d.sjzName },
      { label: '证件类型', value: d.sjzIdType },
      { label: '证件号码', value: d.sjzIdNum },
      { label: '年龄', value: d.sjzAge },
      { label: '性别', value: genderText },
      { label: '电话', value: d.sjzPhone },
      { label: '身高', value: d.sjzHeight },
      { label: '体重', value: d.sjzWeight },
      { label: '民族', value: d.sjzNation },
      { label: '职业', value: d.occupation },
      { label: '文化程度', value: d.education },
      // { label: '问卷类型', value: questionTypeText },
      { label: '监护人姓名', value: d.jhrName },
      { label: '监护人证件类型', value: d.jhrIdType },
      { label: '监护人证件号码', value: d.jhrIdNum },
    ];
    // 只展示有值的字段
    const rows = fields.filter(f => f.value !== undefined && f.value !== null && f.value !== '' && f.value !== '-');
    this.basicInfoTableRows = rows;
    this.showBasicInfoModal = true;
    this.$apply();
  }
  // 关闭基础信息弹窗
  closeBasicInfoModal() {
    this.showBasicInfoModal = false;
    this.$apply();
  }

  /**
   * 字段获取焦点
   */
  onFieldFocus(e) {
    const { field } = e.currentTarget.dataset;
    this.focusField = field;
    this.$apply();
  }
  
  /**
   * 验证单个字段
   */
  validateField(e) {
    const { field } = e.currentTarget.dataset;
    const value = this.formData[field];
    
    // 重置焦点状态
    this.focusField = '';
    
    // 清除当前字段的错误
    if (this.errors[field]) {
      this.errors[field] = '';
    }
    
    // 根据字段类型验证
    switch (field) {
      case 'name':
        if (!value) {
          this.errors.name = '请输入姓名';
        } else if (value.length < 2) {
          this.errors.name = '姓名至少需要2个字符';
        }
        break;
        
      case 'idType':
        if (!value && this.selectedType !== 'child') {
          this.errors.idType = '请选择证件类型';
        }
        break;
        
      case 'idNumber':
        if (!value && this.selectedType !== 'child') {
          this.errors.idNumber = '请输入证件号码';
        } else if (value && this.formData.idType === '身份证' && !this.validateIdCard(value) && this.selectedType !== 'child') {
          this.errors.idNumber = '请输入有效的身份证号码';
        }
        break;
        
      case 'age':
        if (!value) {
          this.errors.age = '请输入年龄';
        } else if (isNaN(value) || parseInt(value) <= 0 || parseInt(value) > 120) {
          this.errors.age = '请输入有效的年龄(1-120)';
        }
        break;
        
      case 'gender':
      case 'genderId':
      case 'genderName':
        if (!this.formData.genderId) {
          this.errors.gender = '请选择性别';
        }
        break;
        
      case 'phone':
        if (!value && this.selectedType !== 'child') {
          this.errors.phone = '请输入联系电话';
        } else if (value && !/^1[3-9]\d{9}$/.test(value) && this.selectedType !== 'child') {
          this.errors.phone = '请输入有效的手机号码';
        }
        break;
        
      case 'height':
        if (value && value.trim() !== '') {
          if (isNaN(value) || parseFloat(value) <= 0 || parseFloat(value) > 250) {
            this.errors.height = '请输入有效的身高(0-250cm)';
          }
        }
        break;
        
      case 'weight':
        if (value && value.trim() !== '') {
          if (isNaN(value) || parseFloat(value) <= 0 || parseFloat(value) > 300) {
            this.errors.weight = '请输入有效的体重(0-300kg)';
          }
        }
        break;
        
      // 儿童版监护人信息验证
      case 'jhrName':
        if (!value && this.selectedType === 'child') {
          this.errors.jhrName = '请输入监护人姓名';
        }
        break;
        
      case 'jhrIdType':
        if (!value && this.selectedType === 'child') {
          this.errors.jhrIdType = '请选择监护人证件类型';
        }
        break;
        
      case 'jhrIdNumber':
        if (!value && this.selectedType === 'child') {
          this.errors.jhrIdNumber = '请输入监护人证件号码';
        } else if (value && this.formData.jhrIdType === '身份证' && !this.validateIdCard(value) && this.selectedType === 'child') {
          this.errors.jhrIdNumber = '请输入正确的身份证号码';
        }
        break;
        
      case 'jhrPhone':
        if (!value && this.selectedType === 'child') {
          this.errors.jhrPhone = '请输入监护人联系电话';
        } else if (value && !/^1[3-9]\d{9}$/.test(value) && this.selectedType === 'child') {
          this.errors.jhrPhone = '请输入正确的手机号码';
        }
        break;
    }
    
    this.$apply();
  }

  // 自定义计算年龄方法，更精确
  calculateAgeFromIdCard(idNumber) {
    if (!idNumber || (idNumber.length !== 15 && idNumber.length !== 18)) {
      return 0;
    }
    
    try {
      // 提取身份证中的出生日期
      let birthYear, birthMonth, birthDay;
      
      if (idNumber.length === 18) {
        birthYear = parseInt(idNumber.substring(6, 10));
        birthMonth = parseInt(idNumber.substring(10, 12));
        birthDay = parseInt(idNumber.substring(12, 14));
      } else {
        // 15位身份证
        birthYear = parseInt('19' + idNumber.substring(6, 8));
        birthMonth = parseInt(idNumber.substring(8, 10));
        birthDay = parseInt(idNumber.substring(10, 12));
      }
      
      // 获取当前日期
      const today = new Date();
      const currentYear = today.getFullYear();
      const currentMonth = today.getMonth() + 1; // 月份从0开始
      const currentDay = today.getDate();
      
      // 计算年龄
      let age = currentYear - birthYear;
      
      // 如果当前月份小于出生月份，或当前月份等于出生月份但当前日期小于出生日期，则年龄减1
      if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
        age--;
      }
      
      return age;
    } catch (error) {
      console.error('计算年龄出错:', error);
      return 0;
    }
  }

  // 从身份证获取性别，1男2女
  getSexFromIdCard(idNumber) {
    if (!idNumber || (idNumber.length !== 15 && idNumber.length !== 18)) {
      return '';
    }
    
    try {
      // 性别位，18位身份证第17位，15位身份证第15位
      const sexBit = idNumber.length === 18 ? idNumber.charAt(16) : idNumber.charAt(14);
      const sexValue = parseInt(sexBit) % 2;
      return sexValue === 1 ? '男' : '女';
    } catch (error) {
      console.error('获取性别出错:', error);
      return '';
    }
  }

  /**
   * 加载问卷配置信息
   * 从接口获取成人版和儿童版问卷ID
   */
  async loadConfig() {
    try {
      // 显示加载提示
      wepy.showLoading({
        title: '加载配置...',
        mask: true
      });
      
      // 准备请求参数
      const params = {
        hisId: this.hisId,
        platformId: this.platformId
      };
      
      // 获取成人版问卷ID
      const adultRes = await Api.getProfileByKey({
        ...params,
        key: 'health_sample_que_adult_id'
      });
      
      // 获取儿童版问卷ID
      const childRes = await Api.getProfileByKey({
        ...params,
        key: 'health_sample_que_child_id'
      });
      
      // 隐藏加载提示
      wepy.hideLoading();
      
      // 处理返回结果
      if (adultRes && adultRes.code === 0 && adultRes.data && adultRes.data.profileValue) {
        this.adultSurveyId = adultRes.data.profileValue;
        console.log('成人问卷ID:', this.adultSurveyId);
      } else {
        console.error('获取成人问卷ID失败:', adultRes);
        // 使用默认值
        this.adultSurveyId = '82';
        console.log('使用默认成人问卷ID:', this.adultSurveyId);
      }
      
      if (childRes && childRes.code === 0 && childRes.data && childRes.data.profileValue) {
        this.childSurveyId = childRes.data.profileValue;
        console.log('儿童问卷ID:', this.childSurveyId);
      } else {
        console.error('获取儿童问卷ID失败:', childRes);
        // 使用默认值
        this.childSurveyId = '82';
        console.log('使用默认儿童问卷ID:', this.childSurveyId);
      }
      
      // 标记配置已加载
      this.isConfigLoaded = true;
      this.$apply();
      
      return Promise.resolve();
    } catch (error) {
      console.error('加载配置失败:', error);
      // 使用默认值
      this.adultSurveyId = '82';
      this.childSurveyId = '82';
      console.log('使用默认问卷ID:', this.adultSurveyId, this.childSurveyId);
      
      // 标记配置已加载
      this.isConfigLoaded = true;
      this.$apply();
      
      // 隐藏加载提示
      wepy.hideLoading();
      
      return Promise.reject(error);
    }
  }

  // 工具函数：更新所有题目的 imageCount 字段
  updateAllImageCount() {
    if (!this.titleList) return;
    this.titleList.forEach(q => {
      q.imageCount = (q.uploadedFiles || []).filter(f => f.type === 'image').length;
    });
  }

  /**
   * 解析二级多值填空内容
   * 将包含{val}占位符的字符串解析为文本和输入框的组合
   */
  parseSecondFillBlankContent(content) {
    if (!content) return [];

    const parts = [];
    let currentIndex = 0;
    let inputIndex = 0;

    // 使用正则表达式查找所有{val}占位符
    const regex = /\{val\}/g;
    let match;
    let lastIndex = 0;

    while ((match = regex.exec(content)) !== null) {
      // 添加占位符前的文本部分
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index);
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          });
        }
      }

      // 添加输入框部分
      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      });

      inputIndex++;
      currentIndex++;
      lastIndex = regex.lastIndex;
    }

    // 添加最后剩余的文本部分
    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex);
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        });
      }
    }

    return parts;
  }
}
</script>