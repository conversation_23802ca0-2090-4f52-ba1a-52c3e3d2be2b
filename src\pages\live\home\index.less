@import "../../../resources/style/mixins";
page {
  background: #f9fbff;
}
.page {
  height: 100%;
  background: @hc-color-primary;
  .header {
    padding: 30rpx 0 35rpx;
  }
  .content {
    width: 100%;
    background: #ffffff;
    position: relative;
    border-top-left-radius: 29rpx;
    border-top-right-radius: 29rpx;
    min-height: 200rpx;
    .divider {
      height: 12rpx;
      width: 100%;
      background: #f3f5f9;
    }
    .banner-box {
      position: relative;
      padding: 30rpx 0;
      display: flex;
      justify-content: center;
      .banner {
        height: 212rpx;
        width: 690rpx;
        margin: 0 auto;
        border-radius: 17rpx;
      }
    }
    .loadmore-tip {
      text-align: center;
      color: @hc-color-text;
      padding-bottom: 20px;
      background: #f3f5f9;
      font-size: 28rpx;
    }
  }
}
