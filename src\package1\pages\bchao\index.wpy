<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { PRIMARY_COLOR } from '@/config/constant';


  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '',
    };

    data = {
      weburl: '',
      patHisNo: ''
    } 

    onLoad(options){
      const { patHisNo = '' } = options;
      this.patHisNo = patHisNo;
      this.$apply();
    }

    onShow(){
      // this.getPatient(this.patHisNo);
      this.getPatientsList();
    }
    
    async getPatientsList() {
      const { code, data } = await Api.getPatientsList({ isLogin: '1' });
      const { cardList = [] } = data;
      const card = cardList && cardList.length > 0 && cardList[0];
      this.getPatient(card.patHisNo);
    }

    async getPatient(patHisNo) {
      if (patHisNo) {
        this.weburl = `https://bcyy.zxxyyy.cn/pacsRegister/#/myOrder?pid=${patHisNo}`;
        this.$apply();
      } else {
        const showModalRes = await wepy.showModal({
          title: '提示',
          content: '您还尚未绑定任何就诊人，绑定后可继续操作。',
          showCancel: true,
          confirmText: '立即绑定',
          confirmColor: PRIMARY_COLOR,
        });
        if (showModalRes.confirm) {
          wepy.navigateTo({
            url: '/pages/bindcard/queryuserinfo/index?qryType=1',
          });
        } else {
          wepy.navigateBack();
        }
        return;
      }
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      weburl: '',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
