import { post, uploadFile } from '@/utils/request';

/**
 * 获取推流地址
 */
export const pushStream = (param) => post('/api/liveStream/pushStream', param);

/**
 * 获取拉流地址
 */
export const pullStream = (param) => post('/api/liveStream/pullStream', param, false, false);

/**
 * 对话开始
 */
export const startLive = (param) => post('/api/liveStream/startLive', param);

/**
 * 结束视频问诊
 */
export const endLive = (param) => post('/api/liveStream/endLive', param);

/**
 * 获取聊天列表
 */
export const getChat = (param) => post('/api/chat/queryChatInfo', param, false, false);

/**
 * 发送消息
 */
export const sendMsg = (param) => post('/api/chat/sendMessage', param,false);

/**
 * 获取视频状态
 */
export const getStatus = (param) => post('/api/liveStream/getStatus', param, false, false);

/**
 * 获取签名
 */
export const sign = (param) => post('/api/liveStream/sign', param, false, false);

/**
 * 获取医生详情
 */
export const getDocDetail = (param) => post('/api/ehis/health/api/doctor/doctor', param);

/**
 * 收藏医生
 */
export const addCollect = (param) => post('/api/ehis/user/favorite/addmyfavorite', param, false);

/**
 * 取消收藏医生
 */
export const cancelCollect = (param) => post('/api/ehis/user/favorite/cancelmyfavorite', param, false);

/**
 * 批量上传图片到服务器
 */
export const uploadImages = (imgs, param) => uploadFile(imgs, param);