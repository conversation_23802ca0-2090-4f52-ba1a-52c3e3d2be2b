import { REQUEST_QUERY } from '@/config/constant';
import { post } from '@/utils/request';

export const getPatientsList = (param) => post('/api/user/patientslist', param, false, false);
export const getProductList = (param) => post(`/api/customize/getProductList?_route=h${REQUEST_QUERY.hisId}`, param);
export const saveOrder = (param) => post('/api/ext/saveorder', param);
export const registerPayOrder = (param) => post("/api/ext/extpayorder", param);

export const add = (param) => post('/api/relationpatient/add', param, false, false);
export const getContract = (param) => post('/api/contract/get-contract-by-institution', param, false, false);