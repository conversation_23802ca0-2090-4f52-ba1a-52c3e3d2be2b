<style lang="less"></style>
<template lang="wxml">

  <view class="m-list">
  <view class="list-tit">就诊信息</view>
  <view class="list">
    <!-- 公共字段 -->
    <view class="list-item" wx:if="{{detailData.patientName}}">
      <view class="item-label">就诊人</view>
      <view class="item-value">{{detailData.patientName}}</view>
    </view>
    <view class="list-item" wx:if="{{detailData.deptName}}">
      <view class="item-label">就诊科室</view>
      <view class="item-value">{{detailData.deptName}}</view>
    </view>
    
    <view class="list-item" wx:if="{{detailData.doctorName}}">
      <view class="item-label">接诊医生</view>
      <view class="item-value">{{detailData.doctorName}}</view>
    </view>


    <!-- 缴费异常保留字段 -->
    <block wx:if="{{dataFrom === 'normal'}}">
      <view class="list-item" wx:if="{{detailData.queCode}}">
        <view class="item-label">排队号</view>
        <view class="item-value">{{detailData.queCode}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.visitPosition}}">
        <view class="item-label">科室位置</view>
        <view class="item-value">{{detailData.visitPosition}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.doctorTitle}}">
        <view class="item-label">医生职称</view>
        <view class="item-value">{{detailData.doctorTitle}}</view>
      </view>
    </block>

    <!-- 通用字段 -->
    <view class="list-item" wx:if="{{detailData.hisName}}">
      <view class="item-label">医院名称</view>
      <view class="item-value">{{detailData.hisName}}</view>
    </view>
  </view>
</view>
      

  <block wx:if="{{detailData.prescriptionType || detailData.guideInfo}}">
    <view class="m-list">
      <view class="list-tit">拿药地址</view>
      <view class="list">
        <block wx:if="{{detailData.prescriptionType}}">
          <view class="list-item">
            <view class="item-label">处方类型</view>
            <view class="item-value">{{detailData.prescriptionType}}</view>
          </view>
        </block>
        <block wx:if="{{detailData.guideInfo}}">
          <view class="list-item">
            <view class="item-label">药房名称</view>
            <view class="item-value">{{detailData.guideInfo}}</view>
          </view>
        </block>
      </view>
    </view>
  </block>
</template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';

  export default class BasicDetail extends wepy.component {
    data = {};

    components = {};

    props = {
      detailData: {
        type: Object,
        default: {},
      }
    };

    onLoad(options) {
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {};
  }
</script>
