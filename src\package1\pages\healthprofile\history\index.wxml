<form bindsubmit="formSubmit" report-submit='true'>
  <block wx:for="{{historyDisease}}" wx:for-index="idx" wx:key="idx">
    <view class="m-historylist">
      <view>{{ item.dictValue }}</view>
      <view class="switch-type">
        <view class="btn {{item.isChecked ? 'active' : ''}}" @tap='selectedIsTrue({{idx}})'>
          有
        </view>
        <view class="btn {{item.isChecked ? '' : 'active'}}" @tap='selectedIsFalse({{idx}})'>
          无
        </view>
      </view>
    </view>
  </block>
  <view class="section showSection">
    <textarea class="showText" value="{{anamnesisRecord}}" placeholder="若存在其他疾病，请填写在此处，限80字内。" name="textarea" />
  </view>
  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">保存</button>
  </view>
</form>
<!-- <toptip :toptip.sync="toptip" /> -->