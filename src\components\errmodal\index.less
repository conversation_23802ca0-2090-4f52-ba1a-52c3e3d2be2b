@import "../../resources/style/mixins";

.wgt-errmodal-ct {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
}

.wgt-errmodal {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.wgt-errmodal-bd {
  padding: 60rpx 0;
}

.wgt-errmodal-icon {
  text-align: center;
  
  image {
    width: 50%;
    vertical-align: top;
  }
}

.wgt-errmodal-title, .wgt-errmodal-text {
  padding: 0 30rpx;
  text-align: center;
}

.wgt-errmodal-title {
  color: @hc-color-warn;
  font-size: 34rpx;
  margin-top: 40rpx;
}

.wgt-errmodal-text {
  margin-top: 20rpx;
  color: @hc-color-text;
  font-size: 30rpx;
  max-height: 6em;
  overflow: hidden;
}

.wgt-errmodal-text-item{
  &~.wgt-errmodal-text-item{
    margin-top: 10rpx;
  }
}

.wgt-errmodal-ft {
  display: flex;
  border-top: 2rpx solid @hc-color-border;
}

.wgt-errmodal-btn {
  text-align: center;
  line-height: 96rpx;
  padding: 0 30rpx;
  white-space: nowrap;
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  flex: 1;
  font-size: 34rpx;
  & ~ .wgt-errmodal-btn {
    border-left: 2rpx solid @hc-color-border;
  }
  &:active{
    background-color: #ddd;
  }
}

.wgt-errmodal-mark {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.65);
}