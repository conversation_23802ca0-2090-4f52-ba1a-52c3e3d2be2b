<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import { getFormatDate } from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import Empty from '@/components/empty/index';
  import * as Api from './api';
  import Moment from 'moment';

  export default class ReportList extends wepy.page {
    config = {
      navigationBarTitleText: '病历列表',
    };

    components = {
      'outpatient': Outpatient,
      'empty': Empty,
    };

    onShow() {
      const { patientId = '' } = this.$wxpage.options;
      this.$broadcast('outpatient-get-patient', { patientId });
    }

    data = {
      patientConfig: {
        infoShow: true,
        show: false,
        initUser: {},
      },
      emptyConfig: {
        show: true,
      },
      patient: {},
      repList: [],
      minDate: Moment(new Date().getTime() - 90 * 24 * 60 * 60 * 1000).format('YYYY-MM'),
      maxDate: Moment(new Date().getTime()).format('YYYY-MM'),
      selectedDate: Moment(new Date().getTime()).format('YYYY-MM'),
    };

    methods = {
      navToDeatil(e) {
        const { id = '' } = e.currentTarget.dataset;
        wepy.navigateTo({ url: `/package1/pages/medicalrecord/recorddetail/index?patientId=${this.patientId}&visitRecordId=${id}` });
      },
    };

    events = {
      'outpatient-change-user': function (item = {}) {
        this.changeUser(item);
      }
    };

    async changeUser(item = {}) {
      // FIXME: 切换就诊人后，之前的选中信息没有初始化
      const { patientId = '' } = item;
      this.patientId = patientId;
      this.$apply();
      this.getList(item);
    }

    /**
     * 获取病历列表
     */
    async getList(item = {}) {
      this.emptyConfig.show = true;
      this.repList = [];
      const { code, data = {} } = await Api.getList({ patientId: this.patientId, startDate: this.selectedDate});
      if(code == 0){
        this.repList = data.repList || [];
        this.minDate = Moment(data.queryStartDt).format('YYYY-MM');
        this.maxDate = Moment(data.queryEndDt).format('YYYY-MM');
        console.log(this.minDate);
        console.log(this.maxDate);
        if(this.repList.length > 0){
          this.emptyConfig.show = false;
        }
        this.$apply();
      }
    }
     changeDate(e) {
      const { value = '' } = e.detail;
      if (value == this.selectedDate) {
        return;
      }
      this.selectedDate = value;
      this.getList();
    }
  }
</script>
