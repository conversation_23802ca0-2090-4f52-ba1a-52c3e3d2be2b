@import "../../../../resources/style/mixins.less";

page {
  background-color: #f6f7f9;
}

.guide-index {
  .date-check {
    margin-bottom: 20rpx;
  }
  .table-tips {
    padding: 10rpx 20rpx;
    color: @hc-color-text;
    font-size: 24rpx;
  }
  .table-box {
    width: 750rpx;
    height: auto;
    overflow-y: hidden;
  }
  .guide-table {
    width: 1500rpx;
    background-color: @hc-color-white;
    .g-th,
    .g-tr {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-flow: row nowrap;

      .g-td {
        flex: 1;
        display: inline-block;
        padding: 10rpx 30rpx;
        text-align: center;
      }
      .address {
        flex: 3;
      }
      .tips {
        flex: 3;
      }
    }
    .g-tr {
      color: @hc-color-text;
    }
    .g-th {
      color: @hc-color-white;
      background-color: @hc-color-primary;
    }
  }
}
