<view class="p-page">
  <view class="page-content">
    <view class="content-title">样本信息</view>
    <view class="content-list list-code {{perfectInfoDetail.id ? 'disabled' : ''}}">
      <view class="list-title requird">样本编号</view>
      <input @input="inputNumber" disabled="{{perfectInfoDetail.id}}" data-id="sampleNumber" class="list-info" type="text" value="{{sampleNumber}}" placeholder="请输入或扫码样本编号" />
      <view class="camera-box" @tap="scanCode" wx:if="{{!perfectInfoDetail.id}}">
        <image class="info-camera" src="/resources/images/camera.png" />
      </view>
      
    </view>
    <view class="content-list">
      <view class="list-title">样本快递编号</view>
      <input @input="inputNumber" data-id="expressnumber" class="list-info" type="text" value="{{expressnumber}}" placeholder="请输入样本快递编号" />
    </view>
  </view>
  <view class="page-content">
    <view class="content-title">受检者信息</view>
    <view class="content-area">
      <view class="name">{{patientName}}</view>
      <view class="area-name">身份证号：{{idNumber}}</view>
      <view class="area-msg">手机号：{{mobile}}</view>
    </view>
    <view class="content-title">病史（请根据实际情况填写）</view>
    <view class="content-textarea">
      <textarea class="list-info" @input="inputNumber" data-id="field1" placeholder="请输入病史" value="{{field1}}" maxlength="10000" />
    </view>
    <view class="content-title">对异常参数打勾</view>
    <view class="check-content">
      <view class="check-title">检测结果超过正常值的有：</view>
      <view class="check-list">
        <checkbox-group class="check-list-group" bindchange="checkboxChange" data-id="field2">
          <label class="weui-cell weui-check__label" wx:for="{{moreArray}}" wx:key="index">
            <view class="weui-cell__hd">
              <checkbox value="{{item.value}}" checked="{{item.checked}}"/>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </label>
        </checkbox-group>
      </view>
      <view class="check-content">
        <view class="check-title">检测结果低于正常值的有：</view>
        <view class="check-list">
          <checkbox-group class="check-list-group" bindchange="checkboxChange" data-id="field3">
            <label class="weui-cell weui-check__label" wx:for="{{lessArray}}" wx:key="index">
              <view class="weui-cell__hd">
                <checkbox value="{{item.value}}" checked="{{item.checked}}"/>
              </view>
              <view class="weui-cell__bd">{{item.name}}</view>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>
    <view class="content-title">知情同意书照片</view>
    <view class="content-list">
      <view class="content">
        <view class="content-view">
          <image  wx:if="{{tempFilePaths.length > 0}}" wx:for="{{tempFilePaths}}" wx:key="index" wx:for-item="item" class="img-item" src="{{item}}" bindlongpress="longTapImg" data-url="{{item}}" @tap="viewImage" data-path="{{item}}"  />
          <image
            wx:if="{{tempFilePaths.length < 9}}"
            src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/upload-icon.png"
            class="img-item"
            @tap="updateImg"
          />
        </view>
      </view>
      
      <view wx:if="{{tempFilePaths.length === 0}}">最多可上传9张</view>
    </view>
  </view>
  <view class="btn-box">
    <view class="btn confirm" @tap="confirm">确认</view>
    <view class="btn cancel" @tap="cancle">取消</view>
  </view>
</view>