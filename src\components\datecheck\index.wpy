<template>
  <view class="date-check">
    <block wx:for="{{dateList}}" wx:key="{{index}}">
      <view class="date-item {{checkIndex == index ? 'active' : '' }}" @tap="checThis({{index}})">
        {{item.text}}
      </view>
    </block>
  </view>
</template>
<script>
import wepy from 'wepy';

export default class dateCheck extends wepy.component {
  props = {
    dateList: {
      type: Array,
      default: [],
      twoWay: true,
    },
    checkIndex: {
      type: Number,
      default: 0,
      twoWay: true,
    }
  };
  data = {};
  onLoad(){}
  methods = {
    checThis(index){
      this.checkIndex = index;
      this.$apply();
      this.$emit('date-change',index);
    }
  }
}
</script>
<style lang="less" scoped>
@import '../../resources/style/mixins';
.date-check{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row nowrap;
  padding: 32rpx;
  box-sizing: border-box;
  // background-color: #ffffff;
  .date-item{
    flex: 1;
    text-align: center;
    color: @hc-color-text;
    font-size: 24rpx;
    height: 68rpx;
    line-height: 68rpx;
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 16rpx;
    // border-bottom: 2rpx solid @hc-color-border;
    // border-bottom: 4rpx solid transparent;
    &.active{
      // border-color: @hc-color-primary;
      // color: @hc-color-primary;
      color: @hc-color-white;
      background:  @hc-color-primary;
    }
  }
  .date-item + .date-item{
    margin-left: 32rpx;
  }
}
</style>