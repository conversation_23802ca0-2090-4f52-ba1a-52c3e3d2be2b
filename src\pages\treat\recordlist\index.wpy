<style lang="less" src="./index.less">

</style>

<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';
  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '门诊缴费记录',
      navigationBarBackgroundColor: '#fff',
    };
    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      activeTab: 'success', // 当前激活tab
      successList: [], // 缴费成功数据（来自orderListPay）
      abnormalList: [], // 缴费异常数据（来自orderList）
      patCardNo: '', // 患者卡号（用于orderListPay接口）
      patientName: '' // 患者姓名（用于orderListPay接口）
    };
    wxs = {
      WxsUtils: WxsUtils
    };
    components = {
      'empty': Empty
    };
    props = {};
    onShow() {
      // 根据当前激活的tab加载数据
      if (this.activeTab === 'success') {
        this.getOrderListPay();
      } else {
        this.getOrderList();
      }
    }
    computed = {
      // 新增计算属性
      currentList() {
        return this.activeTab === 'success' ? this.successList : this.abnormalList;
      }
    };
    onLoad() {
      // 初始化加载异常数据（保证首次加载时有基础患者信息）
      this.activeTab = 'success';
      this.getOrderList(); // 异常数据
      this.getOrderListPay(); // 成功数据
    }
    methods = {
      switchTab(tab) {
        if (this.activeTab === tab) return;
        this.activeTab = tab;
        this.$apply();
        // 延迟加载数据
        setTimeout(() => {
          if (tab === 'success' && this.successList.length === 0) {
            this.getOrderListPay();
          }
          if (tab === 'abnormal' && this.abnormalList.length === 0) {
            this.getOrderList();
          }
        }, 20);
      },
      bindGoDetail(item) {
        // 缴费异常保持原有逻辑
        if (this.activeTab === 'abnormal') {
          wepy.navigateTo({
            url: `/pages/treat/recorddetail/index?orderId=${item.orderId}`
          });
          return;
        }
        // 仅缴费成功携带额外参数
        const params = {
          orderId: item.orderId,
          cardNo: this.patCardNo,
          patName: this.patientName,
          parterId: 'A00000001',
          passwd: Utils.encryptPasswd(this.patCardNo, 'A00000001', 'd27a3810b6e3caba7c1305c0bc7614fe')
        };
        wepy.navigateTo({
          url: `/pages/treat/recorddetail/index?${Object.entries(params)
            .map(([k, v]) => `${k}=${encodeURIComponent(v)}`).join('&')}`
        });
      }
    };
    async getOrderList() {
      try {
        const {
          code,
          data = {}
        } = await Api.orderList();
        if (code !== 0) {
          this.abnormalList = [];
          this.$apply();
          return;
        }
        const outpatientList = data.outpatientList || [];
        this.abnormalList = outpatientList
          .filter(item => item.status !== 'S')
          .map(item => ({
            ...item,
            totalRealFee: (item.totalRealFee || item.totalFee || 0) / 100,
          }));
        if (outpatientList.length > 0) {
          const firstPatient = outpatientList[0];
          this.patCardNo = firstPatient.patCardNo || '';
          this.patientName = firstPatient.patientName || '';
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.$apply();
      }
    }
    /**
     * 获取缴费成功订单列表
     * @async
     * @function getOrderListPay
     * @description 获取患者缴费成功的订单数据，需确保已加载患者信息(cardNo和patientName)
     * @returns {Promise<void>} 无直接返回值，成功时将更新组件的successList数据
     * @throws {Error} 捕获并打印缴费数据加载失败的错误信息
     */
    async getOrderListPay() {
      try {
        // 确保有患者信息（首次加载可能需先调用getOrderList）
        if (!this.patCardNo || !this.patientName) {
          await this.getOrderList();
        }
        const param = {
          cardNo: this.patCardNo,
          patName: this.patientName,
          parterId: 'A00000001',
          passwd: Utils.encryptPasswd(this.patCardNo, 'A00000001', 'd27a3810b6e3caba7c1305c0bc7614fe')
        };
        console.log('param', param);
        const {
          code,
          data = {}
        } = await Api.orderListPay(param);
        console.log('data', data);
        if (code === 0) {
          this.successList = (data.resultList || []).map(item => ({
            orderId: item.costHeadID.toString(), // 唯一标识
            patientName: item.patName, // 直接使用接口字段
            payedTime: item.costDate, // 直接使用costDate
            totalRealFee: item.totalFee, // 保持元单位
            status: 'S',
            statusName: '缴费成功',
            refundStatus: 0
          }));
        }
      } catch (e) {
        console.error('缴费成功数据加载失败:', e);
      } finally {
        this.$apply();
      }
    }
  }
</script>
