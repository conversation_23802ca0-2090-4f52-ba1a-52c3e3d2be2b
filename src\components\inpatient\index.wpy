<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  export default class User extends wepy.component {
    props = {
      config: {
        type: Object,
        default: {
          infoShow: false,
          show: false,
        },
        twoWay: true,
      },
      patient: {
        type: Object,
        default: {
          cardList: [],
        },
        twoWay: true,
      }
    };

    onLoad() {
      //      this.getPatient();
    }

    data = {
    };

    events = {
      'inpatient-get-patient': function (item = {}) {
        this.getPatient(item);
      }
    };

    methods = {
      bindShow(){
        this.config.show = true;
      },
      bindClose(){
        this.closeSheet();
      },
      /**
       * 添加就诊人
       */
      bindGoBindUser(){
        this.closeSheet();
        wepy.navigateTo({
          url: '/pages/inpatient/binduser/index',
        });
      },
      bindGoUserList(){
        this.closeSheet();
        wepy.navigateTo({
          url: '/pages/inpatient/userlist/index',
        });
      },
      /**
       * 切换默认就诊人
       * @param item
       */
      bindChangeUser(item = {}){
        this.closeSheet();
        this.changeUser(item);
      }
    };

    async getPatient(item = {}) {
      const { code, data = {}, msg } = await Api.getPatientList();
      if (code !== 0) {
        // 没有获取到就诊卡列表
        return {
          code: -1,
          data: {},
        };
      }

      let { list: cardList = [] } = data;
      for (let i = 0; i < cardList.length; i++) {
        if (item.inpatientId) {
          if (cardList[i].inpatientId == item.inpatientId) {
            data.activePatient = cardList[i];
            break;
          }
        } else if (cardList[i].isDefault) {
          data.activePatient = cardList[i];
          break;
        }
      }
      this.patient = data;
      this.$emit('inpatient-change-user', data.activePatient);
      this.$apply();
    }

    async changeUser(item = {}) {
      const { activePatient = {} } = this.patient || {};
      if (item.inpatientId === activePatient.inpatientId) {
        return;
      }

      const { inpatientId = '' } = item;
      const { code } = await Api.setDefault({ inpatientId });
      if (code !== 0) {
        // 没有获取到就诊卡列表
        return {
          code: -1,
          data: {},
        };
      }
      this.getPatient();
    }

    closeSheet(){
      this.config.show = false;
    }
  }
</script>
