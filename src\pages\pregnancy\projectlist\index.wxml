<view class="project">
  <outpatient :config.sync="outpatientConfig" :patient.sync="outpatient" :emptyNotice.sync="emptyNotice"></outpatient>
	<view class="pregnancy-img" @tap="goToNextPage({{0}})">
    <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/pregnancy.png" />
	</view>
  	<view class="pregnancy-img" @tap="goToNextPage({{1}})">
    <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/pregnancy-v1.png" />
	</view>
  <view class="project-container" wx:if="{{projectList.length > 0}}">
    <view class="project-tips">目前医院提供以下爱心助孕项目，点击查看详情和提交申请</view>
    <view class="project-list" wx:for="{{projectList}}" wx:key="{{index}}">
      <view class="project-item" @tap="onClick({{item}})">{{item.projectName}}</view>
    </view>
  </view>
  <view>
    <empty :config.sync="emptyConfig">
      <text slot="text">暂未查询到可申请的爱心助孕项目</text>
    </empty>
  </view>
  <button class="btn" @tap="queryList">查询历史申请记录</button>
</view>