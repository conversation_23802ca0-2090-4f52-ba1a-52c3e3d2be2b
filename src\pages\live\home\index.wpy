<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveListMixin from '@/mixins/live/listMixin';
import CardList from './comm/cardList';
import Search from './comm/search';
import ScrollBar from './comm/scrollBar';
import Tabs from './comm/tabs';
import * as Api from './api';

const types = {
  0: 'live',
  1: 'video'
};

export default class LiveHome extends wepy.page {
  config = {
    navigationBarTitleText: '医生直播',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'white'
  };

  data = {
    scrollBars: [],
    scrollBarActiveIndex: -1,
    tabsActive: 0,
    pageNum: 1
  };
  // 在mixin中处理了对列表的请求，以及是否加载更多
  mixins = [LiveListMixin];
  components = {
    search: Search,
    tabs: Tabs,
    'scroll-bar': ScrollBar,
    'card-list': CardList
  };

  methods = {
    onListItemTap(info) {
      this.mixinListItemTap(info);
    }
  };

  events = {
    onSearch(val) {
      console.log(val);
      // TODO: 请求对应搜索内容的列表数据
      // this.mixinGetLiveList();
    }
  };

  watch = {
    scrollBarActiveIndex(newVal) {
      this.pageNum = 1;
    },
    tabsActive(newVal) {
      // 如果切换直播和视频后，要求更改选择的类型为全部，可在此处更改
      // this.scrollBarActiveIndex = -1;
      this.pageNum = 1;
    },
    liveParams(newVal, oldVal) {
      console.log('new',newVal);
      // if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        this.mixinGetLiveList(newVal);
      // }
    }
  };


  computed = {
    liveParams() {
      // 计算请求直播列表的入参，同时监听 liveParams 的变动，及时触发请求
      const param = {
        type: types[this.tabsActive],
        pageNum: this.pageNum
      };
      if (this.scrollBarActiveIndex >= 0) {
        param.liveType = this.scrollBars[this.scrollBarActiveIndex];
      }
      return param;
    }
  };

  onLoad() {
    // this.getLiveTypeList();
  }

  onShow() {
    wepy.navigateTo({
      url: `/pages/webview/index?weburl=https://media.ny.haici.com/portal/site/173`
    });
    return;
    this.getLiveTypeList();
    this.mixinGetLiveList(this.liveParams);
  }
  onReachBottom() {
    if (this.hasMoreData) {
      this.pageNum = this.pageNum + 1;
    }
  }
  async getLiveTypeList() {
    const { code, data } = await Api.getLiveType();
    if (code == 0) {
      this.scrollBars = data.map(item => item.type);
      this.$apply();
    }
  }
}
</script>
