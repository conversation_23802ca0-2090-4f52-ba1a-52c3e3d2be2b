<view class="p-page">
  <block wx:if="{{isShowConsult}}">
    <view class="m-list list-all-box">
      <block wx:for="{{chatList}}" wx:key="{{index}}">
        <view class="list-item" @tap="openConsult({{item}}, 'navigateTo')" wx:if="{{item.chatType != 1}}">
            <view class="item-box">
              <view class="item-hd">
                <image wx:if="{{item.chatType == 1}}" mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/p242/single.png' />
                <image wx:if="{{item.chatType == 2}}" mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/p242/team.png' />
              </view>
              <view class="item-bd">
                <view class="bd-info">
                  <view class="info-lt">
                    <view class="lt-title">{{item.chatType == 1 ? '单独咨询' : '家庭组咨询'}}</view>
                  </view>
                </view>
                <view wx:if="{{item.chatType == 2 && patientsVoList.length > 0}}" class="bd-text">
                    <block wx:for="{{patientsVoList}}" wx:for-item="itm" wx:for-index="idx" wx:key="idx">
                      {{itm.name}}
                      <text wx:if="{{idx !== patientsVoList.length - 1}}">、</text>
                    </block>
                </view>
              </view>
              <view wx:if="{{item.id}}" class="info-rt">
                  <view class="bd-extra">{{item.time || item.updateTime || item.createTime}}</view>
                  <view class="bd-extra active" wx:if="{{item.unreadNum > 0}}">有未读消息</view>
                  <view class="bd-extra" wx:else>暂无未读消息</view>
              </view>
              <view wx:else class="info-rt">
                  <view class="bd-extra">点击创建咨询会话</view>
              </view>
            </view>
          </view>
      </block>
      <!-- <view class="list-item" @tap="openVideo">视频问诊</view> -->
    </view>
    <view class="tips" wx:if="{{isShowTeamConsult}}">
        说明：您的家庭成员也已注册关联信息，您可以选择“家庭组咨询”同家庭成员和医生护士一起交流。若成员信息有误，请联系客服处理。
    </view>
  </block>
  <!-- <block wx:for="{{chatList}}">
    <view class="bottom-css" @tap="openConsult({{item}}, 'navigateTo')" wx:if="{{item.chatType == 1}}">点击单独咨询</view>
  </block> -->
  <!--<view class="empty-content">功能待开放，敬请期待～</view>-->
  <empty :config.sync="emptyConfig">
    <block slot="text">未查询到可咨询信息</block>
  </empty>


</view>