<view class="p-page">
  <outpatient :config.sync="patientConfig" :patient.sync="patient"></outpatient>
  <view class="m-date">
    <picker
      mode="date"
      fields="month"
      value="{{selectedDate}}"
      start="{{minDate}}"
      end="{{maxDate}}"
      @change="changeDate"
    >
      <view class="date">{{selectedDate}}<view class="arrow"></view></view>
    </picker>
  </view>
  <block wx:for="{{repList}}" wx:for-index="idx" wx:key="idx">
    <view class="m-record" @tap="navToDeatil" data-id="{{item.visitRecordId}}">
      <view class="record-item">
        <view class="record-title">就诊信息：{{item.deptName}} | {{item.doctorName}}</view>
        <view class="record-text">就诊时间：{{item.visitDateTime}}</view>
      </view>
    </view>
  </block>
  <empty :config.sync="emptyConfig">
    <block slot="text">暂无病历，请切换就诊人</block>
  </empty>
</view>
