<view class="p-page">
  <block wx:if="{{doctorList.length > 0}}">
    <view class="m-list list-all-box">
      <block
        wx:for="{{doctorList}}"
        wx:key="index"
      >
        <view
          class="list-item"
          @tap="toDoctorIntro({{item}})"
        >
          <view class="item-box">
            <view class="item-hd">
              <image
                mode="widthFix"
                src="{{item.doctorImg || (item.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}"
                alt="" />
            </view>
            <view class="item-bd">
              <view class="bd-info">
                <view class="info-lt">
                  <view class="lt-title">{{item.doctorName}} <text class="title">{{item.doctorTitle}}</text></view>
                  <view class="lt-text">擅长:{{item.skill}}</view>
                </view>
              </view>
              <!--<view class="bd-extra">{{item.skill || ''}}</view>-->
            </view>
          </view>
        </view>
      </block>
    </view>
  </block>

  <empty :config.sync="emptyConfig">
    <block slot="text">暂未查询到医生信息</block>
  </empty>
</view>
