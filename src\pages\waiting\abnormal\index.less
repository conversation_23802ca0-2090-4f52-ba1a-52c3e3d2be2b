@import "../../../resources/style/mixins";

page{
  min-height: 100%;
}

.p-page{
}

.m-retry{
  background-color: #fff;
  .retry-img{
    padding-top: 50rpx;
    text-align: center;
    image{
      width: 250rpx;
      vertical-align: top;
    }
  }
  .retry-text{
    margin-top: 20rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    color: @hc-color-warn;
  }
  .retry-btn{
    padding: 30rpx 30rpx 60rpx 30rpx;
  }
  .btn{
    background-color: @hc-color-primary;
    color:#fff;
    line-height: 88rpx;
    height: 88rpx;
    text-align: center;
    border-radius: 4rpx;
  }
}
.m-detail{
  margin-top: 20rpx;
  background-color: #fff;
  font-size: 30rpx;
  .detail-text{
    padding: 30rpx;
    color: @hc-color-title;
    border-bottom: 2rpx solid @hc-color-border;
  }
  .detail-list{
    padding-bottom: 60rpx;
  }
  .list-item{
    display: flex;
    padding: 0 30rpx;
    margin-top: 40rpx;
    color: @hc-color-text;
    overflow: hidden;
  }
  .item-lt{
    
  }
  .item-rt{
    flex: 1;
    white-space: pre-wrap;
    word-break: break-all;
    text-align: right;
    margin-left: 20rpx;
  }
}