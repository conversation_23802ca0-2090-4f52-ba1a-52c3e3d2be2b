<template>
  <view class="water-mark-mask">
    <view class="row" wx:for="{{rows}}" wx:key="{{index}}">
      <view class="col" wx:for="{{cols}}" wx:key="{{index}}" style="color:{{color}}">{{text}}</view>
    </view>
  </view>
</template>
<script>
import wepy from "wepy";
export default class WaterMark extends wepy.component {
  props = {
    text: {
      type: String,
      default: "仅供参考"
    },
    color: {
      type: String,
      default: "rgba(0,0,0,0.1)"
    }
  };
  data = {
    rows: [],
    cols: []
  };
  onLoad() {
    const { windowWidth, windowHeight } = wx.getSystemInfoSync();
    const cols = Math.ceil(windowWidth / (50 * this.text.length));
    const rows = Math.ceil(windowHeight / 200);
    this.rows = rows;
    this.cols = cols;
    this.$apply();
  }
}
</script>
<style lang="less" scoped>
.water-mark-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none; //无视鼠标事件，相当于鼠标事件透传下去一样
  flex: 1;
}

.row {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.col {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  transform: rotate(45deg);
}
</style>
