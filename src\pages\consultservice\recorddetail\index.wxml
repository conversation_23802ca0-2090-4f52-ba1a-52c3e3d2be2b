<view class="p-page">
	<detail-status :config.sync="statusConfig">
		<block slot="title">{{statusConfig.statusName}}</block>
		<block slot="text">
			<view>{{statusConfig.text}}</view>
		</block>
	</detail-status>
	<view class="m-list">
		<view class="list-tit">个人信息</view>
		<view class="list">
			<view class="list-item" wx:if="{{detailData.patientName}}">
				<view class="item-label">姓名</view>
				<view class="item-value">{{detailData.patientName}}</view>
			</view>
			<view class="list-item" wx:if="{{getpid}}">
				<view class="item-label">PID</view>
				<view class="item-value">{{getpid}}</view>
			</view>
		</view>
	</view>
	<view class="m-list">
		<view class="list-tit">套餐信息</view>
		<view class="list">
			<view class="list-item" wx:if="{{projectName}}">
				<view class="item-label">购买套餐</view>
				<view class="item-value">{{projectName}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.totalFee}}">
				<view class="item-label">缴费金额</view>
				<view class="item-value">{{WxsUtils.formatMoney(detailData.totalFee,100)}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.payedTime}}">
				<view class="item-label">缴费时间</view>
				<view class="item-value">{{detailData.payedTime}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.agtOrdNum}}">
				<view class="item-label">服务日期</view>
				<view class="item-value">{{detailData.vipStartDate}}-{{detailData.vipEndDate}}</view>
			</view>
		</view>
	</view>
	<view class="m-list">
		<view class="list-tit">其他信息</view>
		<view class="list">
			<view class="list-item" wx:if="{{detailData.agtOrdNum}}">
				<view class="item-label">支付流水号</view>
				<view class="item-value">{{detailData.agtOrdNum}}</view>
			</view>
		</view>
	</view>
	<view class="m-button">
		<button class="button" @tap="onApply">进入咨询服务</button>
	</view>

</view>

