@import "../../../../resources/style/mixins";

page{
  min-height: 100%;
  padding: 20rpx;
  margin: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.m-card{
  border-radius: 4rpx;
  background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/report/icon-analysisbg.png") no-repeat;
  background-position: 0 100%;
  background-size: 404rpx 481rpx;
  background-color: #fff;
  flex: 1;

  .report-title{
    font-size: 40rpx;
    font-weight: 600;
    padding: 20rpx 20rpx 0 20rpx;
  }

  .report-info{
    position: relative;
    padding: 0 20rpx 47rpx;

     &:after{
      content: ' ';
      position: absolute;
      left: 34rpx;
      right: 34rpx;
      border-top: 1rpx dashed #c2c9c9;
    }

    &:before{
      top: 0;
    }

    &:after{
      bottom: 0;
    }

    .info-block {
      display: flex;
      padding-top: 15rpx;

      .info-item{
        flex: 1;
        display: flex;
        max-width: 50%;

        &.info-item_block{
          max-width: none;
        }

        &:first-child .item-text{
          padding-right: 20rpx;
        }
        
      }

      .item-title{
        width: 5em;
        font-size: 30rpx;
        color: @hc-color-text;
      }

      .item-text{
        .ellipsis();
        font-size: 28rpx;
        flex: 1;
      }

    }
  }
  .report-text{
    font-size: 30rpx;
    color: @hc-color-text;
    padding:18px 20rpx 0 20rpx;
  }
  .report-list{
    padding-top: 20rpx;
    .list-head{
      padding: 20rpx 0;
      // color: @hc-color-text;
      position: relative;
      font-size: 30rpx;
      background: #ddd;

      &:before, &:after{
        content: ' ';
        position: absolute;
        left: 20rpx;
        right: 20rpx;
        bottom: 0;
        border-top: 2rpx solid #e2e9e8;
      }
    }

    .list-item{
      color: @hc-color-title;
    }

    .list-tr{
      display: flex;
    }

    .td-1{
      width: 35%;
      text-align: left;
      font-size: 34rpx;
      padding: 18rpx 15rpx 18rpx 20rpx;
    }
    .td-2{
      width: 20%;
      text-align: center;
      font-size: 34rpx;
      padding: 18rpx 15rpx;
    }
    .td-3{
      width: 20%;
      text-align: center;
      font-size: 34rpx;
      padding: 18rpx 15rpx;
    }
    .td-4{
      width: 25%;
      text-align: right;
      font-size: 34rpx;
      padding: 18rpx 20rpx 18rpx 15rpx;
    }

    .result-1{ //偏高
      position: relative;
      padding-right: 35rpx;
      background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/report/result-height.png") no-repeat;
      background-position: 100% 50%;
      background-size: 24rpx;
    }
    .result-2{ //偏低
      position: relative;
      padding-right: 35rpx;
      background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/report/result-low.png") no-repeat;
      background-position: 100% 50%;
      background-size: 24rpx;
    }
  }
}
.report-item{
  padding: 20rpx 0;

  .item-title{
    color: @hc-color-title;
    font-size: 34rpx;
    font-weight: 400;
  }

  .item-text{
    font-size: 30rpx;
    color: @hc-color-text;
    margin-top: 14rpx;
  }
}
.ad-report{
  margin-top: 20rpx;
  image{
    width: 100%;
  }
}
