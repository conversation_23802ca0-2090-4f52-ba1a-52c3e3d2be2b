<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from "wepy";
import { DOMAIN } from "@/config/constant";

export default class WebView extends wepy.page {
  config = {
    navigationBarTitleText: "查看文件",
  };

  onShow() {
    console.log("显示了查看文件页面");
    if (this.isOpenedDoc) {
      wx.redirectTo({
        url: "/pages/home/<USER>",
      });
    }
  }

  onLoad(options) {
    const { q = "" } = options;
    let url = decodeURIComponent(q);
    const querys = url.substring(url.indexOf("?") + 1).split("&");
    const result = [];
    for (let i = 0; i < querys.length; i++) {
      let temp = querys[i].split("=");
      if (temp.length < 2) {
        result[temp[0]] = "";
      } else {
        result[temp[0]] = temp[1];
      }
    }
    const pdfurl = `${DOMAIN}/${result["name"]}.pdf`;
    this.openFile(pdfurl);
  }

  openFile(pdfurl) {
    this.isOpenedDoc = false;
    let that = this;
    wepy.showLoading({ title: "加载中", mask: true });
    wx.downloadFile({
      url: pdfurl,
      success(res) {
        const filePath = res.tempFilePath;
        wx.openDocument({
          filePath,
          success() {
            that.isOpenedDoc = true;
            wepy.hideLoading();
          },
          fail: (res) => {
            wepy.showToast({
              title: "文件打开失败", //提示的内容,
              icon: "none", //图标
            });
          },
        });
      },
      fail: (res) => {
        wepy.showToast({
          title: "下载文件失败", //提示的内容,
          icon: "none", //图标
        });
      },
    });
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    isOpenedDoc: false, //是否打开了文档
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {};
}
</script>
