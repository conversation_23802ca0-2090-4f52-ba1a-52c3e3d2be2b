@import "../../../resources/style/mixins";

page {
}

.p-page {
  padding-bottom: 180rpx;
  height: calc(100vh - 110rpx);
  overflow-y: auto;
  
}
.nav{
  .capsule-box{
    padding-left: 24rpx;
    font-size: 34rpx;
    text-align: left;
    font-weight: 600;
  }
}

.m-wxinfo {
  margin: 77rpx 20rpx 0 20rpx;
  background-color: #fff;
  border-radius: 4rpx 4rpx 0 0;
  padding: 0 30rpx 0;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;

  .m-wxicon {
    width: 102rpx;
    height: 102rpx;
    border-radius: 50%;
    position: absolute;
    top: -51rpx;
    left: 18rpx;
    z-index: 1;
  }
  .m-nickname {
    padding: 15rpx 0 0 108rpx;
    font-size: 30rpx;
    line-height: 1;
    color: @hc-color-title;
    height: 30rpx;
  }
}

.m-mycard {
  margin: 0 20rpx 1rpx 20rpx;
  color: @hc-color-title;
  font-size: 28rpx;
  line-height: 1;
  position: relative;
  background-color: #fff;
  padding: 51rpx 30rpx 22rpx;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  outline: none;

  &:after {
    content: " ";
    position: absolute;
    right: 32rpx;
    bottom: 24rpx;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translate(-8rpx) rotate(-45deg);
  }
}

.m-nocard {
  margin: 0 20rpx;
  background-color: #fff;
  padding: 96rpx 0 67rpx;
  display: flex;
  justify-content: center;
  .nocard-btn {
    padding: 22rpx 30rpx;
    font-size: 32rpx;
    color: @hc-color-primary;
    box-sizing: border-box;
    border-radius: 10rpx;
    border: 2rpx solid @hc-color-primary;
  }
}

.m-card {
  margin: 1rpx 20rpx 20rpx;
  background-color: #fff;
  border-radius: 0 4rpx 4rpx 0;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);

  .card-info {
    padding: 28rpx 0 46rpx;
  }
  .info-main {
    display: flex;
    align-items: flex-start;
  }
  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .name {
    font-size: 37rpx;
    height: 37rpx;
  }
  .status {
    font-size: 24rpx;
    height: 40rpx;
    line-height: 40rpx;
    color: @hc-color-text;
    margin-left: 15rpx;
    border: 2rpx solid @hc-color-text;
    border-radius: 4rpx;
    padding: 0 7rpx;
  }
  .info-extra {
    margin-top: 20rpx;
    color: @hc-color-text;
    font-size: 28rpx;
  }
}

.m-function {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);

  .function-list {
  }
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20rpx 30rpx;
    border-top: 2rpx solid #f4f9f9;
  }
  .list-item {
    &:first-child {
      .item {
        border-top: none;
      }
    }
  }
  .item-icon {
    text-align: center;
    width: 60rpx;
    line-height: 0;
  }
  .item-main {
    flex: 1;
    margin-left: 24rpx;
  }
  .main-title {
    font-size: 30rpx;
    color: @hc-color-title;
  }
}
.ad-center {
  margin: 0 20rpx 20rpx;
  image {
    width: 100%;
  }
  .circle-box {
    position: relative;
    top: -32rpx;
    padding: 0 48%;
  }
  .min-circle {
    width: 8rpx;
    height: 8rpx;
    background: #fff;
    opacity: 0.5;
    border-radius: 50%;
    float: left;
    margin-left: 6rpx;
  }
  .active {
    opacity: 1;
  }
}

// UI调整版本
.ui-userinfo {
  display: flex;
  padding: 40rpx 32rpx 200rpx;
  align-items: center;
  padding: 180rpx 32rpx 190rpx;
  background: url('REPLACE_IMG_DOMAIN/his-miniapp/images/top-bg.png') no-repeat;
  background-size: cover;
  background-position: center;
  // background: linear-gradient(135deg, #30A1A6 0%, rgba(48, 161, 166, 0.51) 100%);
  
  
  .avatar {
    width: 146rpx;
    height: 146rpx;
    border-radius: 50%;
  }
  .avatar1 {
    width: 146rpx;
    height: 146rpx;
    border-radius: 50%;
  }
  .title-tips {
    margin-left: 32rpx;
    .title-tips-text {
      font-size: 36rpx;
      color: #fff;
      font-weight: 600;
    }
    .title-tips-info {
      font-size: 26rpx;
      color: #fff;
    }
    .trangle {
      margin-left: 16rpx;
      width: 18rpx;
      height: 18rpx;
    }
  }
  .right {
    
    color: #fff;
    margin-left: 40rpx;
    .name{
      color: #FFF;
      font-size: 64rpx;
      font-weight: 600;
    }
    .num{
      position: relative;
      font-weight: 600;
      &:after {
        content: " ";
        position: absolute;
        top: 14rpx;
        width: 17rpx;
        height: 17rpx;
        border-right: 3rpx solid #fff;
        border-bottom: 3rpx solid #fff;
        transform: translate(-8rpx) rotate(-45deg);
      }
    }
    
  }
}
.user-info{
  border-radius: 8rpx;
  background: linear-gradient(180deg, #EBFAFB 0%, #FFF 100%);
  margin: -130rpx 24rpx 16rpx;
  padding: 24rpx;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  .user-icon-empty{
    display: flex;
    padding: 30rpx 0;
    flex-direction: column;
    align-items: center;
    .add-icon{
      width: 56rpx;
      height: 56rpx;
    }
    .user-info-tips{
      margin-top: 16rpx;
      font-size: 26rpx;
      font-weight: 600;
      color: #2F848B;
    }
  }
  
  .card-box{
    display: flex;
    margin-bottom: 24rpx;
    justify-content: space-between;
    align-items: center;
    text{
      color: #2D666F;
      font-size: 24rpx;
      font-weight: 600;
    }
  }
  .user-box{
    padding: 0 8rpx 8rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    image{
      width: 215rpx;
      height: 25rpx;
    }
    .user-name-box{
      display: flex;
      align-items: center;
    }
    .name{
      margin-bottom: 8rpx;
      color: rgba(0, 0, 0, 0.90);
      font-size: 36rpx;
      font-weight: 600;
    }
    .code{
      color: rgba(0, 0, 0, 0.50);
      font-size: 28rpx;
    }
  }
  .user-box-right{
    margin-left: 16rpx;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    background: rgba(45, 102, 111, 0.12);
    color: #2D666F;
    font-size: 12rpx;
    font-weight: 600;
  }
}
.ui-menu-function {
  display: flex;
  flex-direction: column;
  border-radius: 8rpx;
  background: #fff;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  margin: 16rpx 24rpx;
  &.top-function{
    margin-top: -130rpx;
  }
  .title {
    padding: 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
  }
  .menu {
    display: flex;
    flex-wrap: wrap;
    .menu-item {
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.7);
      text-align: center;
      flex-basis: 25%;
      padding-bottom: 42rpx;
      .icon {
        width: 80rpx;
        height: 80rpx;
      }
    }
  }
  .function-icon-box{
    display: flex;
    flex: 1;
    image{
      margin-right: 16rpx;
      width: 40rpx;
      height: 40rpx;
    }
  }
}
.ui-link-list {
  background: #ffffff;
  border-radius: 8rpx;
  margin: 16rpx 24rpx;
  .title{
    padding-bottom: 0;
  }
  .link-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.9);
    padding: 32rpx;
    .arron {
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid #c7c7cc;
      border-bottom: 5rpx solid #c7c7cc;
      transform: translate(-8rpx) rotate(-45deg);
    }
  }
  .link-item + .link-item {
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }
}
