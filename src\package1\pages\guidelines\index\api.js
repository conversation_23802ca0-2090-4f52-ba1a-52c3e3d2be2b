import { REQUEST_QUERY } from '@/config/constant';
import { post } from '@/utils/request';

export const getCardList = (param) => post('/api/user/patientslist', param);

export const getReport = (param) => post(`/api/customize/queryMedicationGuide?_route=h${REQUEST_QUERY.platformId}`, param);

export const getPatientInfo = (param) => post('/api/user/patientinfo', param);

export const queryRelationPatients = (param) => post(`/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`, param);