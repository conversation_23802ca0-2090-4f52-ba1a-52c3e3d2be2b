<view class="p-page">

  <!-- 自定义导航 -->
  <!-- <nav-bar bgColor="transparent" isBack="0" color="#fff">家辉遗传</nav-bar> -->

  <!-- 顶部banner展示 -->
  <view class="m-banner">
    <view class="banner">
      <image 
        class="banner-image" 
        src="{{hisInfo.imgUrl || 'REPLACE_IMG_DOMAIN/his-miniapp/images/hospital-bg.png'}}" 
        mode="widthFix" 
        alt=""
      />
    </view>
  </view>
  <view class="g-main">
    <view class="logo">
      <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/jiahui-logo.png" mode="widthFix" class="logo-img" alt="" />
    </view>
    <view class="g-main-box">
      <view class="m-info">
        <view class="info-name">
          
          <view>
            <view class="m-hisname">{{hisInfo.hisName}}</view>
          </view>
          <!-- <image class="icon-qrcode"
            src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/icon-qrcode.png" mode="widthFix"
            @tap="preview('REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/juhuama.jpg')"
          /> -->
        </view>
        <view class="info-detail">
          <view class="detail-item" @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}">
            <view class="item-text" @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}">
              咨询电话：{{hisInfo.telNo || '暂未录入'}}</view>
              <image class="icon-phone"
                src="REPLACE_IMG_DOMAIN/his-miniapp/images/icon-phone.png" mode="aspectFit"
                @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}"
              />
          </view>
          <!--<view class="detail-item" @tap="navigateTo" data-url="/package1/pages/microsite/map/index?area=1">-->
          <view class="detail-item" @tap="navigateToMap">
            <view class="item-text">
              <text class="item-label"></text>{{hisInfo.address}}</view>
              <image class="icon-address" src="REPLACE_IMG_DOMAIN/his-miniapp/images/icon-address.png" mode="aspectFit" />
          </view>
          
          <!-- <view class="ad-microsite">
            <view class="ad-content">出行卡打车7折起</view>
            <view class="main-btn">去打车</view>
          </view> -->
        </view>
      </view>

      <view class="m-media-box">
        <view class="m-media" @tap="navigateTo" data-url="/package1/pages/microsite/hisinfo/index">
          <view class="media-hd">
            <text class="hd-tit">医院介绍</text>
            <text class="hd-extra">查看详情</text>
          </view>
          <view class="media-bd article">
            <block wx:if="{{hisInfo.introduction}}">
              <rich-text nodes="{{hisInfo.introduction}}" />
            </block>
            <block wx:if="{{!hisInfo.introduction}}">暂无介绍</block>
          </view>
        </view>
      </view>

      <view id="J_list" class="m-list">
        <view class="list-hd">功能清单</view>
        <view class="list-bd">
          <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlist/index?funType=dept">
            <view class="item-icon">
              <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ksjs.png" mode="aspectFit" /> -->
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/department-intro.png" mode="aspectFit" />
            </view>
            <view class="item-tit">科室介绍</view>
          </view>
          <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlist/index?funType=doctor">
            <view class="item-icon">
              <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ysjs.png" mode="aspectFit" /> -->
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/doctor.png" mode="aspectFit" />
            </view>
            <view class="item-tit">医生介绍</view>
          </view>
          <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlayout/index">
            <view class="item-icon">
              <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ksfb.png" mode="aspectFit" /> -->
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/department-arae.png" mode="aspectFit" />
            </view>
            <view class="item-tit">科室分布</view>
          </view>
          <view class="list-item" @tap="navigateTo" data-url="/pages/dynamic/index/index?position=1">
            <view class="item-icon">
              <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-wwz.png" mode="aspectFit" /> -->
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/function-file.png" mode="aspectFit" />
            </view>
            <view class="item-tit">遗传宣教</view>
          </view>
          <view class="list-item" @tap="navigateTo" data-url="/pages/dynamic/index/index?position=2">
            <view class="item-icon">
              <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-wwz.png" mode="aspectFit" /> -->
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/hospital-file.png" mode="aspectFit" />
            </view>
            <view class="item-tit">医院动态</view>
          </view>
        </view>
      </view>

    </view>
  </view>

  <!-- 底部tab菜单导航 -->
  <nav-tab :type="navTabType" />
</view>
