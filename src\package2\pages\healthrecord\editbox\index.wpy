<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import Empty from "@/components/empty/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import { PRIMARY_COLOR } from '@/config/constant';
import { uploadFile } from "@/utils/request";

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '基本信息',
	}

  components = {
    empty: Empty,
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  data = {
    type: '',
    placeholder: '',
    tempFilePaths: [],
    basicMsg: {
      career: '',
      education: '',
      nation: '',
      height: '',
      weight: ''
    },
    familyValue: '',
    familyMsg: {},
    isEdit: false,
    hasBsicMsg: false,
    hasFamilyMsg: false,
    patientName: '',
    date: '',
    familyTypeValue: false,
    nativePath: [],
    arrLen: 0,
    pathDate: '',
    familyMsgDate: '',
    id: '',
    picCount: 0,
    currentPicCount: 0
  };

  onLoad(options) {
    let date = new Date(); //获取当前时间
    let year = date.getFullYear(); //获取当前年份
    let month = date.getMonth() + 1; //获取当前月份
    var hour = date.getHours(); //获取当前小时
    var minute = date.getMinutes(); //获取当前分钟
    var second = date.getSeconds(); //获取当前秒钟
    let day = date.getDate();
    this.date = `${year}-${month > 9 ? month : '0'+month}-${day} ${hour}:${minute}:${second}`;
    this.pathDate = `${year}-${month > 9 ? month : '0'+month}-${day}`;
    this.patientName = options.patientName;
    const { type, familyTypeValue } = options;
    if(options.basicInfo !== '{}'){
      this.isEdit = true;
      this.hasBsicMsg = true;
      this.basicMsg = JSON.parse(options.basicInfo);
    }
    if(options.familyInfo){
      this.familyMsg = JSON.parse(options.familyInfo);
      this.id = this.familyMsg.id;
      if(JSON.stringify(this.familyMsg) !== '{}'){
        this.isEdit = true;
      }
    }
    this.familyTypeValue = familyTypeValue;
    this.hasFamilyMsg = this.familyTypeValue === 'true' ? true : false
    this.type = type;
    if(type === '1'){
      wx.setNavigationBarTitle({
        title: '基本信息'
      });
    }else if(type === '2'){
      this.placeholder = "请详细描述您的家族相关病史（若有）";
      this.familyValue = this.familyMsg && this.familyMsg.familyHistory ? JSON.parse(this.familyMsg.familyHistory).info : '';
      if(this.familyMsg && this.familyMsg.familyHistory){
        const {updateTime, updateName} = JSON.parse(this.familyMsg.familyHistory);
        this.familyMsgDate = `${updateTime} ${updateName}保存`;
      }
     
      wx.setNavigationBarTitle({
        title: '家族病史'
      });
    }else if(type === '3'){
      this.placeholder = "请详细描述您的就诊记录（若有）";
      this.familyValue = this.familyMsg && this.familyMsg.visitRecord ? JSON.parse(this.familyMsg.visitRecord).info : '';
      if(this.familyMsg && this.familyMsg.visitRecord){
        const {updateTime, updateName} = JSON.parse(this.familyMsg.visitRecord);
        this.familyMsgDate = `${updateTime} ${updateName}保存`;
      }
      wx.setNavigationBarTitle({
        title: '就诊记录'
      });
    }else if(type === '4'){
      this.placeholder = "请详细描述您或您的家族成员做过的相关辅助检查情况（若有）";
      this.familyValue = this.familyMsg && this.familyMsg.auxiliaryExamination ? JSON.parse(this.familyMsg.auxiliaryExamination).info : '';
      if(this.familyMsg && this.familyMsg.auxiliaryExamination){
        const {updateTime, updateName} = JSON.parse(this.familyMsg.auxiliaryExamination);
        this.familyMsgDate = `${updateTime} ${updateName}保存`;
      }
      wx.setNavigationBarTitle({
        title: '辅助检查'
      });
    }else if(type === '5'){
      this.placeholder = "请详细描述您目前的病情状况";
      this.familyValue = this.familyMsg && this.familyMsg.diseaseDescription ? JSON.parse(this.familyMsg.diseaseDescription).info : '';
      if(this.familyMsg && this.familyMsg.diseaseDescription){
        const {updateTime, updateName} = JSON.parse(this.familyMsg.diseaseDescription);
        this.familyMsgDate = `${updateTime} ${updateName}保存`;
      }
      wx.setNavigationBarTitle({
        title: '病情描述'
      });
    }else if(type === '6'){
      if(this.familyMsg.outHisReports){
        const outHisReports = JSON.parse(this.familyMsg.outHisReports);
        const reports = JSON.parse(outHisReports.reports);
        
        reports.forEach(item => {
          item.reportsPath = item.reportsPath.split(';').map(v => v.replace(/\"/g, ""))
        })
        this.tempFilePaths = reports;
        this.arrLen = this.tempFilePaths.length;
        this.picCount = this.tempFilePaths.reduce((res, {reportsPath}) => res + reportsPath.length, 0);
        this.currentPicCount = this.tempFilePaths.reduce((res, {reportsPath}) => res + reportsPath.length, 0);
      }
      wx.setNavigationBarTitle({
        title: '外院报告上传'
      });
    }
  }

  async updateBasicInfo() {
    const { code, data } = await Api.updateBasicInfo(this.basicMsg);
    if(code === 0){
      wx.showToast({
        title: "保存成功",
        icon: "success",
        duration: 3000,
        success: () => {
          wepy.navigateBack({
            delta: 1
          });
        }
      });
    }
  };
  async saveBasicInfo() {
    const { code, data } = await Api.saveBasicInfo(this.basicMsg);
    if(code === 0){
      wx.showToast({
        title: "保存成功",
        icon: "success",
        duration: 3000,
        success: () => {
          wepy.navigateBack({
            delta: 1
          });
        }
      });
    }
  };
  
  async updateFamilyInfo(params) {
    const { code, data } = await Api.updateFamilyInfo({...params, id: this.id});
    if(code === 0){
      wx.showToast({
        title: "保存成功",
        icon: "success",
        duration: 3000,
        success: () => {
          wepy.navigateBack({
            delta: 1
          });
        }
      });
    }
  };

  async saveFamilyInfo(params) {
    const { code, data } = await Api.saveFamilyInfo(params);
    if(code === 0){
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 3000,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  };

  validData() {
    if(!this.basicMsg.career){
      wx.showToast({title: '请输入职业',icon: 'none',duration: 2000})
      return;
    }
    if(!this.basicMsg.education){
      wx.showToast({title: '请输入学历',icon: 'none',duration: 2000})
      return;
    }
    if(!this.basicMsg.nation){
      wx.showToast({title: '请输入民族',icon: 'none',duration: 2000})
      return;
    }
    if(!this.basicMsg.height){
      wx.showToast({title: '请输入身高',icon: 'none',duration: 2000})
      return;
    }
    if(!this.basicMsg.weight){
      wx.showToast({title: '请输入体重',icon: 'none',duration: 2000})
      return;
    }
    return true
  }
  async pushImg(v) {
    const { code, data } = await uploadFile(
      "/api/files/uploadpic",
      v
    );
    if (code === 0) {
      this.nativePath.push(data.url);
      if(this.tempFilePaths.length){
        let sameDateArr = this.tempFilePaths[this.tempFilePaths.length - 1].date === this.pathDate ? [this.tempFilePaths[this.tempFilePaths.length - 1]] : []; // 判断今天是否有上传过图片
        if(sameDateArr.length){
          this.tempFilePaths.pop();
          sameDateArr.forEach(item => {
            item.reportsPath = item.reportsPath.concat(this.nativePath);
          })

          this.tempFilePaths = this.tempFilePaths.concat(sameDateArr);
        }else{
          this.tempFilePaths[this.arrLen] = {
            updateTime: this.date,
            date: this.pathDate,
            reportsPath: this.nativePath
          };
        }
      }else{
        this.tempFilePaths[this.arrLen] = {
          updateTime: this.date,
          date: this.pathDate,
          reportsPath: this.nativePath
        };
      }
      this.nativePath = [];
      this.currentPicCount = this.tempFilePaths.reduce((res, {reportsPath}) => res + reportsPath.length, 0);
      console.log(this.currentPicCount, this.picCount, '=====259')
      this.$apply();
    } else {
      wx.showToast({
        title: "图片上传失败",
        icon: "none",
        duration: 1400
      });
    }
  }

  bindItemTap(item = {}, url) {
    const { action = '' } = item;
    console.log('item', item)
    if (!url) {
      wepy.showModal({
        title: '提示',
        content: '请选择有效图片',
        showCancel: false,
      });
      return;
    }
    if (action === 'save') {
      this.saveImage(url);
    } else if (action === 'delete') {
      this.deleteImg(url);
    }
  }

async saveImage(url) {
  // 保存图片
  wepy.showLoading({ title: '保存中', mask: true });
    wx.downloadFile({
      url: url || '',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: (res) => {
            wepy.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 1000,
              success: () => {
              }
            });
          },
          fail: (res) => {
            wepy.hideLoading();
            console.log('res1', res);
            if (res.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
              this.getSetting();
              return;
            }
            wepy.showModal({
              title: '提示',
              content: '保存图片失败',
              showCancel: false,
            });
          }
        })
      },
      fail:function(res){
        console.log('res2', res);
        wepy.hideLoading();
        wepy.showModal({
          title: '提示',
          content: '保存图片失败',
          showCancel: false,
        });
      }
    });
  }
  deleteImg(url) {
    this.tempFilePaths.forEach(item => {
      item.reportsPath = item.reportsPath.filter(v => v !== url);
    })
    this.tempFilePaths = this.tempFilePaths.filter(v => v.reportsPath.length !== 0);
    this.currentPicCount = this.tempFilePaths.reduce((res, {reportsPath}) => res + reportsPath.length, 0);
    this.$apply();
  }

  methods = {
    longTapImg(e) {
      const { url } = e.currentTarget.dataset;
      wx.showActionSheet({
        itemList: ['保存到手机', '删除'],
        success: (res) => {
          const actionList = ['save', 'delete'];
          this.bindItemTap({ action: actionList[res.tapIndex] }, url);
        },
        fail: (res) => {
          console.log(res.errMsg)
        }
      });
    },
    async updateImg() {
      let that = this;
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 9,
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"]
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths;
        wepy.showLoading({ title: "发送中...", mask: true });
        tempFilePaths.forEach(v => {
          this.pushImg(v);
        })
      }
    },
    
    saveMsg() {
      if(this.type === '1'){
        if(!this.validData()) return;
        if(this.hasBsicMsg){
          this.updateBasicInfo();
        }else{
          this.saveBasicInfo();
        }
      }
      if(this.type !== '1' && this.type !== '6'){
        let params = {};
        const param = {
          updateName: this.patientName,
          updateTime: this.date,
          info: this.familyValue
        };
        if(this.type === '2'){
          params = {
            familyHistory : JSON.stringify(param)
          };
        }else if(this.type === '3'){
           params = {
            visitRecord : JSON.stringify(param)
          };
        }else if(this.type === '4'){
          params = {
            auxiliaryExamination : JSON.stringify(param)
          };
        }else if(this.type === '5'){
           params = {
            diseaseDescription : JSON.stringify(param)
          };
        }
        if(this.familyMsg && (this.familyMsg.auxiliaryExamination || this.familyMsg.familyHistory || this.familyMsg.visitRecord || this.familyMsg.auxiliaryExamination || this.familyMsg.outHisReports )){
          this.updateFamilyInfo(params);
        }else{
          this.saveFamilyInfo(params);
        }
      }
    },
    saveImg() {
      const filePath = this.tempFilePaths;
      filePath.forEach(item => {
        item.reportsPath = JSON.stringify(item.reportsPath.join(';'));
      })
      
      const param = {
        updateTime: this.date,
        updateName: this.patientName,
        reports: JSON.stringify(filePath)
      }
      const params = {
        outHisReports: JSON.stringify(param)
      }
      if(this.familyMsg && (this.familyMsg.auxiliaryExamination || this.familyMsg.familyHistory || this.familyMsg.visitRecord || this.familyMsg.auxiliaryExamination || this.familyMsg.outHisReports )){
        this.updateFamilyInfo(params);
      }else{
        this.saveFamilyInfo(params);
      }
    },
    editMsg() {
      this.isEdit = false;
      this.$apply();
    },
    inputValue(e) {
      const { value } = e.detail;
      const { id } = e.currentTarget;
      this.basicMsg[id] = value;
      this.$apply();
    },
    textareaValue(e) {
      const { value } = e.detail;
      this.familyValue = value;
      this.$apply();
    },
    cancel() {
      wepy.navigateBack({
        delta: 1
      });
    },
    
    viewImage(e) {
      const { path, url } = e.currentTarget.dataset;
      wepy.previewImage({
        urls: path.reportsPath || [],
        current: url || '',
      });
    },
  }
}
</script>