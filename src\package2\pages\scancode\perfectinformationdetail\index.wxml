<view class="p-page">
  <view class="page-content">
    <view class="content-title">样本信息</view>
    <view class="content-list list-code">
      <view class="list-title requird">样本编号</view>
      <input disabled class="list-info" type="text" value="{{sampleNumber}}" />
    </view>
    <view class="content-list">
      <view class="list-title">样本快递编号</view>
      <input disabled class="list-info" type="text" value="{{expressnumber}}"/>
    </view>
  </view>
  <view class="page-content">
    <view class="content-title">受检者信息</view>
    <view class="content-list list-code">
      <view class="list-title requird">受检者姓名</view>
      <input disabled class="list-info" type="text" value="{{patientName}}" />
    </view>
     <view class="content-list list-code">
      <view class="list-title requird">受检者身份证号</view>
      <input disabled class="list-info" type="text" value="{{idNumber}}" />
    </view>
     <view class="content-list list-code">
      <view class="list-title requird">受检者手机号</view>
      <input disabled class="list-info" type="text" value="{{mobile}}" />
    </view>
    <view class="content-title bt">病史（请根据实际情况填写）</view>
    <view class="content-textarea">
      <textarea class="list-info" disabled placeholder="请输入病史" value="{{field1}}" />
    </view>
    <view class="content-title bt">对异常参数打勾</view>
    <view class="check-content">
      <view class="check-title">检测结果超过正常值的有：</view>
      <view class="check-list">
        <checkbox-group class="check-list-group">
          <label class="weui-cell weui-check__label" wx:for="{{moreArray}}" wx:key="index">
            <view class="weui-cell__hd">
              <checkbox value="{{item.value}}" disabled checked="{{item.checked}}"/>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </label>
        </checkbox-group>
      </view>
      <view class="check-content">
        <view class="check-title">检测结果低于正常值的有：</view>
        <view class="check-list">
          <checkbox-group class="check-list-group">
            <label class="weui-cell weui-check__label" wx:for="{{lessArray}}" wx:key="index">
              <view class="weui-cell__hd">
                <checkbox value="{{item.value}}" disabled checked="{{item.checked}}"/>
              </view>
              <view class="weui-cell__bd">{{item.name}}</view>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>
    <view class="content-title bt">知情同意书照片</view>
    <view class="content-list">
      <view class="content" wx:if="{{tempFilePaths.length > 0}}">
        <view class="content-view">
          <image wx:for="{{tempFilePaths}}" wx:key="index" wx:for-item="item" class="img-item" src="{{item}}" @tap="viewImage" data-path="{{item}}" data-url="{{item}}" />
        </view>
      </view>
    </view>
  </view>
  <view class="btn-box" wx:if="{{perfectInfoDetail.reportPath}}">
    <view class="btn confirm" @tap="showReport">查看报告</view>
  </view>
</view>