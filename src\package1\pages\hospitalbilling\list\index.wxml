<view class="p-page">
  <view class="settlement">
    <view class="settlement-item" wx:for="{{applyList}}" wx:for-index="index" @tap="goDetail({{item}})">
      <view class="status-title">
        <image src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/{{item.icon}}.png' class="status-icon" />
        <text>{{item.label}}</text>
      </view>
      <view class="name">{{item.patientName}}</view>
      <view class="info">PID：{{item.patHisNo}}</view>
      <view class="info">住院号：{{item.hospitalNo}}</view>
      <view class="info">申请时间：{{item.createTime}}</view>
      <view class="info">
        结算金额：
        <text style="color: #FC7F60">¥{{(item.refundTotalFee * 1 || 0) / 100}}</text>
      </view>
    </view>
    <empty wx:if="{{applyList.length === 0}}">
      <text slot="text">未查询到数据</text>
    </empty>
  </view>
</view>
