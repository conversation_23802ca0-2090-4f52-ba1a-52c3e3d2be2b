<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <folding :isExpand.sync="isExpand">
      <block slot="title">缴费详情</block>
      <block slot="content">
        <view class="list">
          <view class="list-item" wx:if="{{detailData.totalRealFee}}">
            <view class="item-label">交易金额</view>
            <view class="item-value">
              <view class="unit-price">￥{{WxsUtils.formatMoney(detailData.totalRealFee, 100)}}</view>
            </view>
          </view>
          <view class="list-item" wx:if="{{detailData.bizTypeName}}">
            <view class="item-label">费用类型</view>
            <view class="item-value">{{detailData.bizTypeName}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.hisName}}">
            <view class="item-label">医院名称</view>
            <view class="item-value">{{detailData.hisName}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.hisOrderNo}}">
            <view class="item-label">医院单号</view>
            <view class="item-value">{{detailData.hisOrderNo}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.orderId}}">
            <view class="item-label">平台单号</view>
            <view class="item-value">{{detailData.orderId}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.agtOrdNum}}">
            <view class="item-label">支付流水号</view>
            <view class="item-value">{{detailData.agtOrdNum}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.payStatus}}">
            <view class="item-label">支付状态</view>
            <view class="item-value">{{detailData.payStatus == 1 ? '已支付' : '未支付'}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.payedTime}}">
            <view class="item-label">支付时间</view>
            <view class="item-value">{{detailData.payedTime}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.cancelTime}}">
            <view class="item-label">取消时间</view>
            <view class="item-value">{{detailData.cancelTime}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.cancelReason}}">
            <view class="item-label">取消原因</view>
            <view class="item-value">{{detailData.cancelReason}}</view>
          </view>
        </view>
      </block>
    </folding>
  </view>
</template>

<script>
  import wepy from 'wepy';
  import Folding from '@/components/folding/index';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Utils from '@/utils/utils';

  export default class PayDetail extends wepy.component {
    data = {};

    components = {
      'folding': Folding,
    };

    wxs = {
      WxsUtils: WxsUtils,
    };

    props = {
      detailData: {
        type: Object,
        default: {},
      },
      isExpand: {
        type: Boolean,
        default: true,
        twoWay: true,
      },
    };

    onLoad(options) {
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {};
  }
</script>
