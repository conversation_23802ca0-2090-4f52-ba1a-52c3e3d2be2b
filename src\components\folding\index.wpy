<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  export default class Folding extends wepy.component {
    props = {
      isExpand: {
        type: Boolean,
        default: true,
        twoWay: true,
      }
    };

    data = {
    };

    events = {};

    methods = {
      bindExpand(){
        this.isExpand = !this.isExpand;
      }
    };
  }
</script>
