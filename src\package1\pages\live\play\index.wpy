<!--  -->
<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import { loopRequest } from '@/utils/utils';
import LiveDetailMixin from '@/mixins/live/detailMixin';
import DoctorInfoCover from '../comm/doctorInfoCover';
import OnlineNum from './comm/onlineNum';
import ChatMsgList from './comm/chatMsg';

import * as Api from './api';
export default class LiveHome extends wepy.page {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom'
  };
  // 在mixin中处理了对直播详情的请求
  mixins = [LiveDetailMixin];
  data = {
    id: '', // 页面路径的入参
    barrages: [], // 弹幕列表
    plays: 0, // 在线人数
    likes: 0, // 爱心数量
    chatMsgText: '',
    liveUrl: '',
    doctorInfo: {},
    inputFocus: false
  };

  components = {
    'doctor-info': DoctorInfoCover,
    'online-num': OnlineNum,
    'chat-list': ChatMsgList
  };

  methods = {
    onPlayStateChange(e) {
      console.log('live-play code:', e.detail.code, e.detail.message);
      const { code, message } = e.detail;
      switch (code) {
        case 2004:  // 视频播放开始
          wepy.hideLoading();
          this.showLiveToast(message);
          break;
        case -2301:
          this.showLiveErrorModal('网络断连，且经多次重连抢救无效，请退出重试！');
          break;
        case 2104:
          this.showLiveToast('网络不稳定');
          break;
        case 3001:
          this.showLiveErrorModal(`${message}，请退出重试！`);
          break;
        case 3002:
          this.showLiveErrorModal(`${message}，请退出重试！`);
          break;
        case 3003:
          this.showLiveErrorModal(`${message}，请退出重试！`);
          break;
        case 3005:
          this.showLiveErrorModal(`${message}，请退出重试！`);
          break;
        default:
          break;
      }
    },
    onPlayError(e) {
      console.log('live-play error:', e);
      wepy.hideLoading();
      
      wx.showModal({
        title: '播放失败', //提示的标题,
        content: `失败原因：${e.detail.errMsg}`, //提示的内容,
        showCancel: false,
        confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
        confirmColor: '#3CC51F', //确定按钮的文字颜色,
        success: res => {
          if (res.confirm) {
            wepy.navigateBack({
              delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
            });
          }
        }
      });
    },
    onInputMsg(e) {
      console.log(e);
      if (e.detail.value.length > 50) {
        wepy.showToast({
          title: '只能输入50字', //提示的内容,
          icon: 'none', //图标,
          duration: 2000 //延迟时间,
        });
      } else {
        this.chatMsgText = e.detail.value;
      }
    },
    onInputBlur() {
      this.inputFocus = false;
    },
    onInputConfirm() {
      this.reqSendMsg();
    },
    onFocus() {
      this.inputFocus = true;
    },
    onSendMsg() {
      if (!this.chatMsgText) {
        wepy.showToast({
          title: '请输入内容', //提示的内容,
          icon: 'none', //图标,
          duration: 2000 //延迟时间,
        });
        return;
      }
      this.reqSendMsg();
    },
    onQuit() {
      wx.navigateBack({
        delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
      });
    },
    onSendHeart() {
      this.reqAddLike();
    }
  };

  events = {};

  watch = {
    chatMsgText(newVal) {
      if (newVal.length == 50) {
        wepy.showToast({
          title: '够50字啦，不要再写咯', //提示的内容,
          icon: 'none', //图标,
          duration: 2000 //延迟时间,
        });
      }
    }
  };

  computed = {};

  onLoad(options) {
    const { id, url } = options;
    this.id = id;
    this.setKeepScreenOn();
    this.reqEnterLive(id);
    this.reqLiveInfo();
    this.liveInfoInterval = loopRequest(() => this.reqLiveInfo());
  }

  onShow() {}
  onUnload() {
    clearInterval(this.liveInfoInterval);
    this.reqQuitLive();
    this.setKeepScreenOn(false);
  }
  onShareAppMessage() {
    return {
      title: `${this.doctorInfo.doctorName}医生的直播`,
      complete: res => {
        console.log(res);
      }
    };
  }
  showLiveErrorModal(content) {
    if (this.modalIsShow) return;
    this.modalIsShow = true;
    wx.showModal({
      title: '播放失败', //提示的标题,
      content, //提示的内容,
      showCancel: false, //是否显示取消按钮,
      confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
      confirmColor: '#3CC51F', //确定按钮的文字颜色,
      success: res => {
        this.modalIsShow = false;
        if (res.confirm) {
          wepy.navigateBack({
            delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
          });
        }
      }
    });
  }
  showLiveToast(title) {
    wepy.showToast({
      title, //提示的内容,
      icon: 'none', //图标,
      duration: 2000, //延迟时间,
    });
  }
  setKeepScreenOn(on = true) {
    wx.setKeepScreenOn({
      keepScreenOn: on //是否保持屏幕常亮,
    });
  }
  async reqEnterLive(liveId) {
    // 进入直播
    const { code, data } = await Api.enterLive({ liveId });
    if (code == 0) {
      this.liveUrl = data.url;
      // this.doctorInfo = data || {};
      wepy.showLoading({
        title: '连接中...', //提示的内容,
        mask: true, //显示透明蒙层，防止触摸穿透,
        success: res => {}
      });
      
      this.$apply();
    }
  }
  async reqQuitLive() {
    await Api.quitLive({ liveId: this.id });
  }
  async reqAddLike() {
    const { code, data } = await Api.addLike({ liveId: this.id });
    if (code == 0) {
      wepy.showToast({
        title: '点赞成功', //提示的内容,
        icon: 'success', //图标,
        duration: 2000 //延迟时间,
      });
    }
  }
  // 实时获取直播的信息，比如弹幕、在线人数
  async reqLiveInfo() {
    const { code, data } = await Api.getLiveRealTimeInfo({ id: this.id });
    if (code == 0 && data) {
      this.barrages = Array.isArray(data.barrages)
        ? data.barrages
            .slice(0, data.barrages.length > 30 ? 30 : data.barrages.length)
            .reverse()
        : [];
      // 产品说弹幕只展示七条
      this.plays = data.plays;
      this.likes = data.likes;
      this.$apply();
      return Promise.resolve();
    } else if (!data) {
      clearInterval(this.liveInfoInterval);
      wx.showModal({
        title: '温馨提示', //提示的标题,
        content: '医生已结束直播，请退出', //提示的内容,
        showCancel: false, //是否显示取消按钮,
        confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
        confirmColor: '#3CC51F', //确定按钮的文字颜色,
        success: res => {
          if (res.confirm) {
            wepy.navigateBack({
              delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
            });
          }
        }
      });
      return Promise.reject();
    }
  }
  async reqSendMsg() {
    wepy.showToast({
      title: '发送中', //提示的内容,
      icon: 'loading', //图标,
      duration: 2000 //延迟时间,
    });
    const { code, data, msg } = await Api.sendMsg({
      liveId: this.id,
      content: this.chatMsgText
    });
    wepy.hideLoading();

    if (code == 0) {
      wepy.showToast({
        title: '发送成功', //提示的内容,
        icon: 'success' //图标,
      });
      this.chatMsgText = '';
      this.$apply();
    } else {
      wepy.showToast({
        title: '发送失败：' + msg, //提示的内容,
        icon: 'none', //图标,
        duration: 2000 //延迟时间,
      });
    }
  }
}
</script>
