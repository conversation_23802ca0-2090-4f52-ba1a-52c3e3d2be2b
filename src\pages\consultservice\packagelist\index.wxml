<view class="package-content">
  <view class="package-content-top">
    <view class="package-name">{{patientInfo.patientName}}</view>
    <view class="package-number">PID: {{patientInfo.patHisNo}}</view>
  </view>
  <view class="package-detail">
    <view class="package-detail-tips" wx:if="{{isPayLength === 0}}">目前医院提供龚斐教授团队全病程管理服务以下两个服务套餐，可点击查看套餐服务详情，请根据自己的需求进行下单购买。</view>
     <view class="package-detail-tips" wx:if="{{isPayLength > 0}}">您已购买龚斐教授团队咨询服务套餐，可点击直接进行VIP咨询</view>
    <view class="package-introuce-btn"  @tap="vipIntroduce">龚斐团队咨询服务介绍></view>
  </view>
  <view wx:if="{{isPayLength === 0}}">
    <view class="package-listcontain" wx:for="{{packageList}}" wx:key="index">
      <view class="package-list mange{{item.id}}" @tap="onClickDetail({{item}})">
        <view class="package-listleft">
          <view class="package-list-name">{{item.listName}}</view>
          <view class="package-list-price">{{item.listPrice}}</view>
        </view>
        <view class="package-listright"></view>
      </view>
    </view>
  </view>
  <view wx:if="{{isPayLength > 0}}">
    <view class="isPay-listcontain" wx:for="{{isPayArr}}" wx:key="index">
      <view class="isPaylist" @tap="onClickVipChat({{item}})">
        <view class="isPay-name">龚斐团队VIP咨询</view>
        <view class="isPay-right">
          <view class="isPay-time">{{item.updateTime}}</view>
          <view class="isPay-read">消息已读</view>
        </view>
      </view>
    </view>
  </view>
  
  <view class="package-record"  @tap="getRecordList">查询购买记录</view>
  <!-- <empty :config.sync="emptyConfig">
    <block slot="text">暂未查寻到相关内容</block>
  </empty> -->
</view>