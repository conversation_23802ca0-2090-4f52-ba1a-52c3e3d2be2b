<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { PRIMARY_COLOR, REQUEST_QUERY } from "@/config/constant";
import * as Api from "./api";

export default class BindCard extends wepy.page {
  config = {
    navigationBarTitleText: "健康档案"
  };

  components = {};

  onShow() {
    this.cardList = [];
    this.chatList = [];
    this.patientsVoList = [];
    this.patHisNo = "";
    this.isShowConsult = false;
    this.patientInfo();
    // this.queryDiseaseList();
    // this.getProfileInfo();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    // 顶部tab，0：生殖，1：遗传
    tabIndex: 0,
    femalInfo: { sex: "F" },
    manInfo: { sex: "M" },
    healthId: "",
    patHisNo: "",
    showHistoryF: [],
    showHistoryM: [],
    description: "", //病情描述，在manInfo里面
    diseaseF: [], //女方疾病
    diseaseM: [], //男方疾病
  };
  //查询病史
  async queryDiseaseHistoryF() {
    console.log("this", this);
    const { code, data } = await Api.queryDiseaseList({
      type: "anamnesis_record "
    });
    let { anamnesisRel = "" } = this.$parent.femalInfo;
    let showHistoryF = [];
    anamnesisRel = anamnesisRel.split(",");
    console.log("anamnesisRel", anamnesisRel);
    console.log("anamnesisRel", anamnesisRel.length);
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < anamnesisRel.length; j++) {
        if (data[i].id == anamnesisRel[j]) {
          showHistoryF.push(data[i].dictValue);
        }
      }
      this.showHistoryF = showHistoryF;
      console.log("this.showHistoryF", showHistoryF);
    }
    this.$apply();
  }
  async queryDiseaseHistoryM() {
    console.log("this", this);
    const { code, data } = await Api.queryDiseaseList({
      type: "anamnesis_record "
    });
    let { anamnesisRel = "" } = this.$parent.manInfo;
    let showHistoryM = [];
    anamnesisRel = anamnesisRel.split(",");
    console.log("anamnesisRel", anamnesisRel);
    console.log("anamnesisRel", anamnesisRel.length);
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < anamnesisRel.length; j++) {
        if (data[i].id == anamnesisRel[j]) {
          showHistoryM.push(data[i].dictValue);
        }
      }
      this.showHistoryM = showHistoryM;
      console.log("this.showHistoryM", showHistoryM);
    }
    this.$apply();
  }

  // 查询女方疾病
  async queryDiseaseListF() {
    console.log("this", this);
    const { code, data } = await Api.queryDiseaseList({
      type: "f_disease_type"
    });
    let { questionRel = "", diseaseF = [] } = this.$parent.femalInfo;
    questionRel = questionRel.split(",");
    console.log("questionRel", questionRel);
    console.log("questionRel", questionRel.length);
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < questionRel.length; j++) {
        if (data[i].id == questionRel[j]) {
          diseaseF.push(data[i].dictValue);
        }
      }
      this.diseaseF = diseaseF;
      console.log("this.diseaseF", diseaseF);
    }
    this.$apply();
  }
  // 查询男方疾病
  async queryDiseaseListM() {
    const { code, data } = await Api.queryDiseaseList({
      type: "m_disease_type"
    });
    let { questionRel = "", diseaseM = [] } = this.$parent.manInfo;
    questionRel = questionRel.split(",");
    console.log("questionRel", questionRel);
    console.log("questionRel", questionRel.length);
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < questionRel.length; j++) {
        if (data[i].id == questionRel[j]) {
          diseaseM.push(data[i].dictValue);
        }
      }
      this.diseaseM = diseaseM;
      console.log("this.diseaseM", diseaseM);
    }
    this.$apply();
  }

  async getProfileInfo(param = {}) {
    let { data, code } = await Api.getProfileInfo(param);
    const { sex } = param;
    if (code === 0 && data) {
      if (data.basicInfo && (data.basicInfo.name || data.basicInfo.idNo)) {
        // 已保存基本信息
        data.isExistBasicInfo = true;
      }
      if (sex === "M") {
        this.manInfo = data;
        this.$parent.manInfo = data;
        console.log("manInfo", this.$parent.manInfo);
        this.description = this.$parent.manInfo.description;
        this.queryDiseaseListM();
        this.queryDiseaseHistoryM();
      } else {
        this.femalInfo = data;
        this.$parent.femalInfo = data;
        console.log("femalInfo", this.$parent.femalInfo);
        this.queryDiseaseListF();
        this.queryDiseaseHistoryF();
      }
    }
    if (code == 0 && !data) {
      if (sex === "M") {
        this.$parent.manInfo = this.manInfo;
      } else {
        this.$parent.femalInfo = this.femalInfo;
      }
    }
    this.$apply();
  }

  async patientInfo() {
    const {
      globalData: {
        outpatient: { cardList = [] }
      }
    } = this.$parent;
    const currentPatient = cardList.find(item => {
      return item.relationType == 1;
    });
    if (!currentPatient) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "请登录后再操作",
        // showCancel: false,
        confirmText: "确定",
        confirmColor: PRIMARY_COLOR
      });
      if (showModalRes.confirm) {
        wepy.navigateTo({
          url: "/pages/bindcard/index/index"
        });
      }else{
        wepy.navigateBack();
      }
      return;
    }
    const { patHisNo = "", patientId = "", patientName = "", idType = '' } = currentPatient;
    const { hisId = "", platformId = "", platformSource = "" } = REQUEST_QUERY;
    const { code, data = {} } = await Api.patientinfo({
      hisId,
      platformSource,
      platformId,
      patientId,
      idFullTransFlag: 1
    });
    if (code != 0) {
      return;
    }
    console.log(patientId, patHisNo);
    this.patHisNo = patHisNo;
    this.patientId = patientId;
    await this.queryRelationPatients({
      patName: patientName,
      idNo: data.patientFullIdNo || "",
      qryType: 2,
      idType
    });
  }
  async queryRelationPatients(param) {
    const { code, data = {} } = await Api.queryRelationPatients(param);
    if(!data.itemList && data.resultCode != 0){
      await wepy.showModal({
        title: '提示',
        content: data.resultMessage || '',
        showCancel: false,
      })
      return;
    }
    const { items = [] } = data.itemList[0];
    const infoKeys = { JT01: "femalInfo", JT02: "manInfo" };
    const sexKeys = { JT01: "F", JT02: "M" };
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      console.log(item);
      this[infoKeys[item.familyMemberCode]] = {
        sex: sexKeys[item.familyMemberCode],
        basicInfo: {
          name: item.patName,
          idNo: item.idNo,
          address: item.address,
          profession: item.jobName,
          nation: item.nationName,
          education: item.educationName
        }
      };
    }
    this.$parent.profilePatientInfo = {
      patHisNo: this.patHisNo,
      patientId: this.patientId
    };
    this.getProfileInfo({ pid: this.patHisNo, sex: "M" });
    this.getProfileInfo({ pid: this.patHisNo, sex: "F" });
    this.$apply();
  }
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    navigateTo(url) {
      let { qryType = 2 } = this;
      // 跳转带参数 绑定本人或他人
      const jumpUrl = `${url}?qryType=${this.cardList.length > 0 ? 2 : 1}`;
      wepy.navigateTo({ url: jumpUrl });
    },
    /**
     * 修改tab状态（挂号、介绍）
     */
    bindChangeTabIndex(index) {
      if (this.tabIndex === index) {
        return;
      }
      this.tabIndex = index;
    },
    jumppage(url = "baseinfo", info = {}) {
      // 跳转页面
      const { sex = "F", isExistBasicInfo, healthId = "" } = info;
      // if (!isExistBasicInfo && url !== "baseinfo") {
      //   // 需要先填写基本信息
      //   wepy.showModal({
      //     title: "提示",
      //     content: "请先填写基本信息后再填写其他健康档案内容",
      //     confirmText: "确认",
      //     showCancel: false,
      //     success(res) {
      //       console.log("res", res);
      //       if (res.confirm) {
      //         this.jumppage("baseinfo", info);
      //       }
      //     }
      //   });
      //   return;
      // }
      wx.navigateTo({
        url: `/pages/healthprofile/${url}/index?sex=${sex}&healthId=${healthId}&pid=${this.patHisNo || ""}&type=${isExistBasicInfo ? "update" : "new"}`
      });
    }
  };
}
</script>
