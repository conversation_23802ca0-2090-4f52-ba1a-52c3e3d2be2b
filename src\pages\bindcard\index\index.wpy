<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import { CHILD_MAX_AGE } from '@/config/constant';
  import * as Api from './api';
  import BottomLogo from '@/components/bottomlogo/index';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '重要提示',
    };

    components = {
      'bottom-logo': BottomLogo,
    };

    onLoad(options){
      const { isScan = 0 } = options;
      this.isScan = isScan;
    }

    onShow() {
      this.cardList = [];

      this.getCardList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      CHILD_MAX_AGE,
      openItem: '', // adult成人，child儿童
      qryType: '2', // 绑定类型1 本人 2他人
      cardList: [],
    };

    async getCardList(){
      const { data = {} } = await Api.getCardList();
      const { cardList = [], leftBindNum = 0 } = data;
      this.cardList = cardList;
      let qryType = 1;
      cardList.forEach((item) => {
        if (item.relationType == 1) {
          qryType = 2;
        }
      });
      this.qryType = qryType;
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(url) {
        let { qryType = 2 } = this;
        // 跳转带参数 绑定本人或他人
        const jumpUrl = `${url}?qryType=${this.qryType}&isScan=${this.isScan}`
        wepy.navigateTo({ url: jumpUrl });
      },
      openItem(itemName){
        if(this.openItem === itemName){
          this.openItem = '';
        } else {
          this.openItem = itemName;
        }
      },
      refuses() {
        // 拒绝
        wepy.redirectTo({ url: '/pages/home/<USER>' });
      }
    };

  }
</script>
