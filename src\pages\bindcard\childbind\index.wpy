<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Message from '@/components/message/index';
  import TopTip from '@/components/toptip/index';
  import { CURSOR_SPACING, CHILD_MAX_AGE } from '@/config/constant';
  import { validator, getAgeByBirthday, getBirthdayByIdCard } from '@/utils/utils';
  import * as Api from './api';

  export default class ChildBind extends wepy.page {
    config = {
      navigationBarTitleText: '绑定就诊卡',
    };

    components = {
      message: Message,
      toptip: TopTip,
    };

    onLoad(option) {
      this.initData(option);
      this.getHisConfig();
      this.setPickerDate();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      CURSOR_SPACING,

      placeholderColor: '',
      errorColor: 'red',
      
      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      hisConfig: {
        relationTypes: [],
        patientTypes: [],
        idTypes: [],
        patCards: [],
      },

      idTypesIdx: 0,
      patCardsIdx: 0,
      hasIdCard: 1,

      birthdayStart: '',
      birthdayEnd: '',

      isNewCard: 0,
      isNoCard: 0,
      patientName: '',
      idType: '1',
      birthday: '',
      idNo: '',
      sex: '',
      parentName: '',
      parentIdType: '',
      parentIdNo: '',
      patientAddress: '',
      patCardNo: '',
      patCardType: '21',
      patientMobile: '',
      relationType: '5',
      patientType: '1',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      changeProp(e){
        const { prop } = e.target.dataset;
        const { value } = e.detail;
        this[prop] = value;
      },
      resetThisError(e){
        const { id } = e.currentTarget;
        this.errorElement[id] = false;
      },
      actionSheetType(e){
        const { prop = '' } = e.target.dataset || {};
        if(prop){
          const listData = this.hisConfig[prop] || [];
          wepy.showActionSheet({
            itemList: listData.map((v) => v.dictValue),
          }).then((res) => {
            const idxKey = `${prop}Idx`;
            this[idxKey] = res.tapIndex;
            this.$apply();
          });
        }
      },
      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        this[id] = value;
      },
      formSubmit(e){
        this.hasErr = this.validator();
        if(this.hasErr){
          return false;
        }
        const { formId = '' } = e.detail;
        Api.saveFormId({ formId });
        this.pushData();
      },
    };

    initData(option){
      const { isnewcard = '0', isNoCard = '0' } = option;
      this.isNewCard = isnewcard;
      this.isNoCard = isNoCard;
    }

    async getHisConfig() {
      const { code, data = {}, msg } = await Api.getHisConfig()
      if(code == 0){
        if(data.idTypes) {
          data.idTypes = data.idTypes.slice(0, 6); //最多6项
          data.idTypes = data.idTypes.map((v) => {
            if(v.dictKey == 1){
              v.regexp = validator.idCard,
              v.errTip = "请输入18位监护人身份证";
              v.inputType = 'idcard';
            }
            return v;
          });
        }
        this.hisConfig = data;
        this.$apply();
      }
    }

    async pushData(){ // 绑卡
      wepy.showLoading({ title: '正在绑卡', mask: true });
      const value = this.getFormData();
      const { code, data, msg } = await Api.bindCard(value);
      if(code == 0){
        wepy.hideLoading();
        wepy.showToast({
          title: this.isNewCard == '1' ? '新建成功' : '绑定成功',
          icon: 'success',
        });
        const timer = setTimeout(() => {
          clearTimeout(timer);
          wepy.navigateBack({ delta: 3 });
        }, 2000);
      }
    }

    validator(id){
      const validate = {
        patientName: {
          regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
          errTip: '请输入2-8位合法姓名',
        },
        idNo: {
          regexp: this.hasIdCard == 1 ? (val) => validator.idCard(val.idNo) : '',
          errTip: '请输入18位儿童身份证',
        },
        sex: {
          regexp: this.hasIdCard == 0 ? /^\S+$/ : '',
          errTip: '请选择儿童性别',
        },
        birthday: {
          regexp: this.hasIdCard == 0 ? 
          (val) => {
            return {
              ret: val.birthday && getAgeByBirthday(val.birthday, '-') <= CHILD_MAX_AGE
            };
          }
          :
          (val) => {
            return {
              ret: getAgeByBirthday(getBirthdayByIdCard(val.idNo, '-'), '-') <= CHILD_MAX_AGE
            };
          },
          errTip: `儿童年龄不超过${CHILD_MAX_AGE}岁，请使用正确的${this.hasIdCard == 0 ? '出生日期' : '身份证号'}`,
          errTarget: this.hasIdCard == 0 ? '' : 'idNo', // 提示错误的元素
        },
        parentName: {
          regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
          errTip: '监护人姓名最少2位',
        },
        parentIdNo: {
          regexp: (() => {
            const regexp = this.hisConfig.idTypes[this.idTypesIdx].regexp;
            if(typeof regexp === 'function'){
              return (val) => regexp(val.parentIdNo);
            } else {
              return /^\S+$/;
            }
          })(),
          errTip: '请输入正确的监护人证件号',
        },
        patientAddress: {
          regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          errTip: '请输入有效的监护人住址',
        },
        patCardNo: {
          regexp: (() => {
            return this.isNewCard == 1 ? '' : /^\S+$/;
          })(),
          errTip: '儿童就诊卡号不能为空',
        },
        patientMobile: {
          regexp: /^1\d{10}$/,
          errTip: '请输入11位监护人手机号',
        },
      };

      

      const value = this.getFormData();

      let hasErr = false;
      for(let o in value){
        const obj = validate[o];
        if(obj && obj.regexp){
          let thisErr = false;
          if(typeof obj.regexp === 'function'){
            const retObj = obj.regexp(value);
            if(!retObj.ret){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          } else {
            if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          }
          if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
            this.errorElement[obj.errTarget || o] = true;
            this.toptip = obj.errTip || '';
            const errTimer = setTimeout(() => {
              this.toptip = '';
              this.$apply();
              clearTimeout(errTimer);
            }, 2000);
            break;
          }
        }
      }

      return hasErr;
    }

    getFormData(){
      const {
        patientName, idNo, patientAddress, patCardNo, patientMobile,
        isNewCard, isNoCard, idTypesIdx, patCardsIdx, hisConfig,
        birthday, sex, parentName, parentIdNo, patCardType,
        relationType, patientType,
      } = this;

      const idType = this.hasIdCard == '0' ? '' : this.idType;
      const parentIdType = this.hisConfig.idTypes[idTypesIdx].dictKey; 

      return {
        patientName,
        idNo,
        idType,
        birthday,
        sex,
        parentName,
        parentIdType,
        parentIdNo,
        patientAddress,
        patCardNo,
        patientMobile,
        relationType,
        patientType,
        patCardType,
        isNewCard,
        isNoCard,
      };
    }

    setPickerDate(){
      const nowDay = new Date();
      const agao14Year = new Date();
      agao14Year.setTime(nowDay.getTime() - 14 * 365 * 24 * 60 * 60 * 1000);
      this.birthdayStart = `${nowDay.getFullYear()}-${nowDay.getMonth()}-${nowDay.getDate()}`;
      this.birthdayEnd = `${agao14Year.getFullYear()}-${agao14Year.getMonth()}-${agao14Year.getDate()}`;
    }

  }
</script>
