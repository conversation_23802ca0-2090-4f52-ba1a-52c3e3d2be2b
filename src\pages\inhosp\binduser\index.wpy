<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import { INPATIENT_HAS_CARD } from '@/config/constant';

  import HasCard from './com/hascard/index';
  import NoCard from './com/nocard/index';
  import * as Api from './api';

  export default class Widget extends wepy.page {
    config = {
      navigationBarTitleText: '添加住院人',
    };

    components = {
      'has-card': HasCard,
      'no-card': NoCard,
    };

    onLoad(options) {
    }

    data = {
      // 住院是否有卡模式
      inpatientHasCard: INPATIENT_HAS_CARD,
    };

    methods = {
    };
  }
</script>

