<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class List extends wepy.page {
    config = {
      navigationBarTitleText:'遗传科普',
      navigationBarBackgroundColor: '#fff',  
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      const { position } = options
      wx.setNavigationBarTitle({
        title: position == 1 ? '遗传宣教' : '医院动态'
      })
      this.position = position
      this.getType();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // tab类型
      navTabType: "dynamic",
      emptyConfig: {
        show: true,
      },
      typeList: [],
      iconUrlMap: {
        251: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/sgzk.png',
        252: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/szkt.png',
        253: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/szhlxbk.png',
        256: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/yczk.png',
        254: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/hydy.png',
        257: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/hjflzc.png',
      },
      artic: {},
      singleId:'',
      position: ''
    };

    onPullDownRefresh(){
      const { currentPage, endPageIndex } = this.artic;
      if (currentPage * 1 < endPageIndex * 1) {
        this.getArticList((currentPage * 1) + 1,this.singleId);
      }
      wx.stopPullDownRefresh();
    }

    async getType() {
      const { code, data = [] } = await Api.getTypeList({pid:1, position: 3 });
      if (code == 0 && data.length > 0) {
        this.typeList = data;
        this.getArticList(1, data[0].typeId);
        this.singleId = data[0].typeId
        this.$apply();
      }
    }

    async getArticList(pageNum = 1,singleId) {
      const { code, data = {} } = await Api.getArticList({typeId:singleId, pageNum: 999 });
      data.recordList = data.recordList.filter(item => item.position.indexOf(this.position) !== -1);
      if (code == 0 && data.recordList && data.recordList.length > 0) {
        this.artic = data;
        // const { recordList = [] } = this.artic;
        // const { recordList: _recordList = [] } = data;
        // this.artic = data;
        // this.artic.recordList = [...recordList, ..._recordList];
        this.emptyConfig.show = false;
      } else {
        this.artic = {};
        this.emptyConfig.show = true;
      }
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      chooseAticle(item){
        this.singleId = item.typeId
        this.getArticList(1, item.typeId);
      },
      navigateToInfo(item) {
        let url = '';
        if(item.articleId){
          url = `/pages/dynamic/info/index?articleId=${item.articleId}&position=${this.position}`;
        }else{
          url = `/pages/dynamic/secondindex/index?typeId=${item.typeId}&title=${item.typeName || item.title}`;
        }
        wepy.navigateTo({ url });
      },
    };

    events = {
    };

  }
</script>
