<view class="m-card">
  <view class="report-title">基础信息</view>
  <view class="report-info">
    <view class="info-block">
      <view class="info-item">
        <view class="item-title">就诊人：</view>
        <view class="item-text">{{baseInfo.patName}}</view>
      </view>
      <view class="info-item info-item_block">
        <view class="item-title">就诊科室：</view>
        <view class="item-text">{{baseInfo.deptName}}</view>
      </view>
    </view>
    <view class="info-block">
      <view class="info-item">
        <view class="item-title">性别：</view>
        <view class="item-text">{{baseInfo.patSex}}</view>
      </view>
      <view class="info-item">
        <view class="item-title">就诊医生：</view>
        <view class="item-text">{{baseInfo.doctorName}}</view>
      </view>  
    </view>
    <view class="info-block">
      <view class="info-item">
        <view class="item-title">年龄：</view>
        <view class="item-text">{{baseInfo.patAge}}</view>
      </view>
      <view class="info-item info-item_block">
        <view class="item-title">就诊时间：</view>
        <view class="item-text">{{baseInfo.visitDateTime}}</view>
      </view>
    </view>
    <view class="info-block">
      <view class="info-item info-item_block">
        <view class="item-title">病历类型</view>
        <view class="item-text">{{baseInfo.recordTypeDesc}}</view>
      </view>
    </view>
  </view>
  <view class="report-title">就诊详情</view>
  <view class="report-info">
    <view wx:if="{{visitDetail.chiefComplaint}}" class="report-item">
      <view class="item-title">主诉</view>
      <view class="item-text">{{visitDetail.chiefComplaint}}</view>
    </view>
    <view wx:if="{{visitDetail.presentHistory}}" class="report-item">
      <view class="item-title">现病史</view>
      <view class="item-text">{{visitDetail.presentHistory}}</view>
    </view>
    <view wx:if="{{visitDetail.previousHistory}}" class="report-item">
      <view class="item-title">既往史</view>
      <view class="item-text">{{visitDetail.previousHistory}}</view>
    </view>
    <view wx:if="{{visitDetail.medicalExam}}" class="report-item">
      <view class="item-title">体格检查</view>
      <view class="item-text">{{visitDetail.medicalExam}}</view>
    </view>
    <view wx:if="{{visitDetail.auxiliaryExam}}" class="report-item">
      <view class="item-title">辅助检查</view>
      <view class="item-text">{{visitDetail.auxiliaryExam}}</view>
    </view>
    <view wx:if="{{visitDetail.diagnosticInfo}}" class="report-item">
      <view class="item-title">诊断信息</view>
      <view class="item-text">{{visitDetail.diagnosticInfo}}</view>
    </view>
    <view wx:if="{{visitDetail.treatmentAdvice}}" class="report-item">
      <view class="item-title">处理意见</view>
      <view class="item-text">{{visitDetail.treatmentAdvice}}</view>
    </view>
  </view>

  <view class="report-title">医嘱信息</view>
  <view wx:if="{{doctorAdvice.extrInfo}}" class="report-text">{{doctorAdvice.extrInfo}}</view>
  <view class="report-list">
    <view class="list-tr list-head">
      <view class="td-1">医嘱名称</view>
      <view class="td-2">数量</view>
      <view class="td-3">单位</view>
      <view class="td-4">缴费状态</view>
    </view>
    <block wx:for="{{doctorAdvice.adviceList}}" wx:for-index="idx" wx:key="idx">
      <view class="list-tr list-item">
        <view class="td-1">{{item.name}}</view>
        <view class="td-2">
          <text class="result-{{item.abnormal}}">{{item.num}}</text>
        </view>
        <view class="td-3">{{item.unit}}</view>
        <view class="td-4">{{item.stateDesc}}</view>
      </view>
    </block>
  </view>
</view>