<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '生殖中心缴费记录',
      navigationBarBackgroundColor: '#fff',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 记录列表
      orderList: []
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'empty': Empty
    };

    props = {};

    onShow(options) {
      this.getOrderList();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到挂号详情页
       * @param item
       */
      bindGoDetail(item){
        const { id = '' } = item;
        wepy.navigateTo({
          url: `/package1/pages/reproductcenter/recorddetail/index?orderId=${id}`,
        });
      },
    };

    convertListStatus (status) {
      const statusMap = {
        'S': 'success',
        'F': 'fail',
        'L': 'lock',
        'C': 'cancel',
        'P': 'abnormal',
        'H': 'abnormal',
        'Z': 'abnormal',
        'E': 'abnormal',
      };
      return statusMap[status] || '';
    };

    formatMoney (moneyString = '0', mark = 100) {
      var moneyNumber = parseFloat(moneyString);
      if (typeof moneyNumber === 'number' && typeof mark === 'number') {
        return parseFloat(moneyNumber / mark).toFixed(2);
      }
      return 0;
    };

    async getOrderList(word) {
      const { code, data = {} } = await Api.orderList({bizType: "reproductive_center"});
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      this.orderList = data.extorderList || [];
      this.orderList.forEach(item => {
        item.statusIcon = this.convertListStatus(item.status);
        item.totalFee = this.formatMoney(item.totalFee, 100);
      });
      this.$apply();
    }
  }
</script>
