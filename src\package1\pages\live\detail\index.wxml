<view class="header">
  <view class="header-title">{{liveDetail.name}}</view>
  <view class="header-des">直播时间：{{liveDetail.liveTime}}</view>
</view>
<view class="content">
  <view class="content-title">主讲人介绍</view>
  <doctor-info :info.sync="doctorInfo" />
  <view class="intro-box">
    <view>
      <view class="intro-title">直播类型</view>
      <view class="intro-des">{{liveDetail.liveType}}</view>
    </view>
  </view>
  <view class="divider"></view>
  <!-- <view class="intro-box">
    <view>
      <view class="intro-title">医生简介</view>
      <view class="intro-des">{{liveDetail.introduction}}</view>
    </view>
  </view> -->
  <view class="divider"></view>
  <view class="intro-box">
    <view>
      <view class="intro-title">本期主题</view>
      <view class="intro-des">{{liveDetail.name}}</view>
    </view>
  </view>
  <view class="divider"></view>
  <view class="intro-box">
    <view>
      <view class="intro-title">详细介绍</view>
      <text class="intro-des">{{liveDetail.introduction}}</text>
    </view>
  </view>
</view>
<view class="bottom-btn" @tap="onSetConcern" wx:if="{{issubscribe == 0}}">开播提醒</view>
<view class="bottom-btn" @tap="onCancelConcern" wx:else>取消提醒</view>