@import "../../../resources/style/mixins";

page {}

.p-page {
  padding: 16px;
}

.m-list {
  overflow: hidden;

  .list-item {
    padding: 40rpx 30rpx;
    align-items: center;
    margin: 20rpx 0;
    background-color: #fff;
    border-radius: 15px;
  }

  .item-icon {
    width: 30rpx;
    height: 30rpx;
    overflow: hidden;
    border-radius: 50%;

    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }

  .item-main {
    display: flex;
    margin-bottom: 30rpx;
    align-items: center;

    .main-tit {
      margin-left: 20rpx;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .main-txt {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 20rpx;
  }

  .extra-tit {
    color: rgba(0, 0, 0, 0.7);
    font-size: 13px;
  }


  .list-item {
    .item-icon {
      background-color: @hc-color-warn;
    }

    &.list-item-success,
    &.list-item-lock {
      .item-icon {
        background-color: @hc-color-primary;
      }

      .extra-tit {
        color: @hc-color-warn;
      }
    }

    &.list-item-fail {
      .item-icon {
        background-color: @hc-color-error;
      }
    }

    &.list-item-cancel {
      .item-icon {
        background-color: @hc-color-text;
      }
    }

    &.list-item-abnormal {
      .item-icon {
        background-color: #FFA500;
      }
    }
  }
}