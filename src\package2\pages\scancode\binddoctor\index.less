.p-page{
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #2F848B;
}
.header{
  flex-shrink: 0;
  padding: 100rpx 24rpx 90rpx;
  font-size: 48rpx;
  font-weight: 600;
  color: #fff;
  .line{
    margin-top: 12rpx;
    width: 40rpx;
    height: 7rpx;
    background: #fff;
  }
}
.content{
  flex: 1;
  padding: 48rpx 40rpx;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
  text-align: center;
  .title{
    font-weight: 600;
    font-size: 36rpx;
    color: #000;
  }
  .text{
    padding: 32rpx 0 88rpx;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.7);
  }
  .btn{
    height: 100rpx;
    line-height: 100rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: rgba(0, 0, 0, 0.7);
    background: rgba(0, 0, 0, 0.04);
    border-radius: 76rpx;
    margin-bottom: 24rpx;
    &.main{
      color: #fff;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    }
  }
}