<view class="p-page">

  <block wx:for="{{cardList}}" wx:for-index="idx" wx:for-item="card" wx:key="idx">
    <navigator class="m-card" url="/pages/usercenter/userinfo/index?patientId={{card.patientId}}&relationType={{card.relationType}}">
      <view class="card-info">
        <view class="info-main">
          <view class="main-name">
            <view class="name">{{card.patientName}}</view>
            <!-- <view class="status" wx:if="{{card.isDefault == 1}}">默认</view> -->
            <view class="status" wx:if="{{card.relationType == 1}}">登录用户</view>
          </view>
        </view>
        <view class="info-extra">就诊卡号：{{card.patCardNo}}</view>
      </view>
    </navigator>
  </block>

  <!-- <navigator url="{{cardList.length > 0 ? '/pages/bindcard/scancard/index' : '/pages/bindcard/index/index?qryType=1'}}" class="m-adduser" wx:if="{{leftBindNum > 0}}">
    <view class="add-title">添加就诊人</view>
    <view class="add-text">还可添加{{leftBindNum}}人</view>
  </navigator> -->

  <view class="afterscan-operbtnbox" wx:if="{{leftBindNum > 0}}">
    <button class="binduser-btn_line" @tap="toAdd">添加家庭成员</button>
  </view>

</view>
