.u-img {
  height: 150px;
  width: 100%;
}

.g-title {
  display: flex;
  justify-content: space-between;
  p:nth-child(1) {
    font-size: 16px;
  }

  p:nth-child(2) {
    color: rgb(94, 195, 241);
  }
}

.u-doctorPic {
  height: 120px;
  width: 100px;
  padding-right: 9px;
}

.g-doctorInfo {
  margin: 8px 0;
  padding: 9px 9px 2px 9px;
  background-color: #fff;
  overflow: hidden;

  .g-check {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;

    .u-checkbox {
      transform: scale(0.7, 0.7);
    }
  }

  .g-vote {
    text-align: center;
    color: #f00;
  }

  .g-ellipsisIntroduce {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 9;
    overflow: hidden;
    color:rgb(132, 130, 127);
  }

  .g-introduce {
    display: -webkit-box;
    color:rgb(132, 130, 127);
  }

  .u-unfold {
    text-align: right;
    color: #3eceb5;
  }

  .u-introTitle {
    position: absolute;
    color:#000;
  }
}

.m-activeInfo {
  background-color: #fff;
  padding: 10px;
}

.u-icon {
  width: 18px;
  height: 18px;
}

.g-textIcon {
  display: flex;
  margin: 5px 0;

  image {
    flex: none;
    padding-right: 3px;
  }

  p {
    color: rgb(153, 153, 153);
  }
}


.u-listTitle {
  background-color: rgb(62, 206, 181);
  text-align: center;
  color: #fff;
  padding: 6px;
  margin-top: 8px;
  font-weight: bold;
}


.u-button {
  background-color: #3eceb5;
  padding: 8px 0;
  text-align: center;
  font-size: 17px;
  color: #fff;
}

.u-unbutton{
  background-color: #84827f;
  padding: 8px 0;
  text-align: center;
  font-size: 17px;
  color: #fff;
}

.g-doctorInfo {
  margin: 8px 0;
  padding: 9px 9px 5px 9px;
  background-color: #fff;
  overflow: hidden;
}