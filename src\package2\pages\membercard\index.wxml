<view class="p-page">
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block wx:for="{{orderList || []}}" wx:key="index">
        <view class="list-item list-item-{{item.status == 1 ? 'success' : 'abnormal'}}">
          <view class="item-main">
            <view class="main-tit">
              <view class="item-icon">
                <image class="item-status" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{item.status == 1 ? 'success' : 'abnormal'}}.png"></image>
              </view>
              <text>{{item.status == 1 ? '有效' : '失效'}}</text>
            </view>
            
          </view>
          <view class="item-extra">
          <view class="main-txt">卡类型：{{item.cardTypeName}}</view>
            <view class="main-txt">卡号：{{item.cardNo}}</view>
            <view class="main-txt">激活时间：{{item.activeTime}}</view>
            <view class="main-txt">有效日期：{{item.validityDate}}</view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">未查询到会员卡数据</block>
    </empty>
  </block>
  <view class="btn-view">
  <button class="action-btn" @tap="toBind">绑定新卡</button>
  </view>
</view>