<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from 'wepy';

export default class Table extends wepy.component {
  props = {
    title: {
      type: String,
      default: '',
    },
    tableHeader: {
      type: Array,
      default: [],
    },
    columnKey: {
      type: Array,
      default: [],
    },
    dataSource: {
      type: Array,
      default: [],
    },
  };

  components = {};

  onLoad(options) {}

  data = {};

  methods = {
    onTap(data) {
      this.$emit('getBillDetail', data);
    },
  };
}
</script>
