<template>
  <view class="comm-doctor-info">
    <image class="info-avatar" wx:if="{{info.doctorImage && info.doctorImage!=null}}"  src="{{info.doctorImage}}" mode="aspectFill" />
    <image class="info-avatar" wx:else src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png" mode="aspectFill" />
    <view class="info-text">
      <!-- <view class="text-title">{{info.doctorName}}{{info.level}}</view> -->
      <view class="text-title">{{info.doctorName}}</view>
      <view class="text-des" wx:if="{{info.deptName && info.deptName!=null}}">{{info.deptName}}</view>
    </view>
    <!-- <view wx:if="{{!isCollect}}" class="btn-small" @tap="onChangeCollect(true)">+收藏</view>
    <view wx:else class="btn-small btn-grey" @tap="onChangeCollect(false)">已收藏</view> -->
  </view>
</template>

<script>
import wepy from 'wepy';
import { post } from '@/utils/request';
export default class DoctorInfo extends wepy.component {
  props = {
    info: {
      type: Object,
      default: {},
    }
  };

  data = {
    isCollect: false,
    hasGetCollectStatus: false,
  };

  components = {};

  methods = {
    onChangeCollect(collect) {
      const param = {
        doctorId: this.info.doctorId,
        deptId: this.info.deptId
      };
      if (collect && collect == 'true') {
        this.collect(param);
      } else {
        this.unCollect(param);
      }
    }
  };

  events = {};

  watch = {
    info(newVal) {
      if (newVal.doctorId && !this.hasGetCollectStatus) {
        this.hasGetCollectStatus = true;
        // this.getCollectStatus();
      }
    }
  };

  computed = {};

  onUnload() {
    this.hasGetCollectStatus = false;
  }

  async getCollectStatus() {
    const param = {
      doctorId: this.info.doctorId,
      deptId: this.info.deptId
    };
    const { code, data } = await post('/api/ehis/health/api/doctor/doctor', param);
    if (code == 0) {
      this.isCollect = data.isFavorite;
      this.$apply();
    }
  }
  async collect(param) {
    const { code, data } = await post('/api/ehis/user/favorite/addmyfavorite', param);
    if (code == 0) {
      this.isCollect = true;
      wepy.showToast({
        title: '收藏成功',
        icon: 'success'
      });
      this.$apply();
    }
  }
  async unCollect(param) {
    const { code, data } = await post('/api/ehis/user/favorite/cancelmyfavorite', param);
    if (code == 0) {
      this.isCollect = false;
      wepy.showToast({
        title: '取消成功',
        icon: 'success'
      });
      this.$apply();
    }
  }
}
</script>

<style lang='less'>
@import '../../../../resources/style/mixins.less';
.comm-doctor-info {
  padding: 28rpx 30rpx;
  background: #fafafa;
  display: flex;
  align-items: center;
  .info-avatar {
    height: 100rpx;
    width: 100rpx;
    margin-right: 20rpx;
    border-radius: 50rpx;
  }
  .info-text {
    flex: 1;
    .text-title {
      font-size: 32rpx;
      font-weight: 600;
      color: @hc-color-title;
      margin-bottom: 5rpx;
    }
    .text-des {
      font-size: 26rpx;
      color: @hc-color-text;
    }
  }
  .btn-small {
    width: 110rpx;
    height: 60rpx;
    background: @hc-color-primary;
    border-radius: 10rpx;
    font-size: 30rpx;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    &.btn-grey {
      background: @hc-color-info;
    }
  }
}
</style>