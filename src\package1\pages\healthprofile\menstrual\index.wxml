<form bindsubmit="formSubmit" report-submit='true'>
  <view class="patInfo-list">
    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">月经周期</text>
      </view>
      <view class="listitem-body">
        <picker mode="multiSelector" bindchange="pickerMultiChange('yjzq')" value="{{yjzqIndx}}" range="{{yjzqList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择月经周期" placeholder-style="{{ yjzq ? '': ''}}"
              value="{{yjzq}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">末次月经时间</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('mcyjsj')" mode="date" start="{{minDate}}" end="{{maxDate}}" value="{{mcyjsj}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择末次月经日期" placeholder-style="{{ mcyjsj ? '': ''}}"
              value="{{mcyjsj}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">月经天数</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('yjts')" value="{{yjtsIndex}}" range="{{yjtsList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择月经天数"
              placeholder-style="{{ yjts ? '': ''}}" value="{{yjts}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">经量情况</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('jlqk')" value="{{jlqkIndex}}" range="{{jlqkList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择经量情况"
              placeholder-style="{{ jlqk ? '': ''}}" value="{{jlqk}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">初潮年龄</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('ccnl')" value="{{ccnlIndex}}" range="{{ccnlList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择初潮年龄"
              placeholder-style="{{ ccnl ? '': ''}}" value="{{ccnl}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

  </view>

  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">保存</button>
  </view>
</form>

<toptip :toptip.sync="toptip" />