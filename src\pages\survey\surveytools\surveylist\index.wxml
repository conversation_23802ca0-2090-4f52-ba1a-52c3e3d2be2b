<view class="p-page">
  <view class="header">
    <view class="page-title">样本列表</view>
     <!--<view class="page-subtitle">您有 {{surveyList.length || 0}} 份未完成的问卷</view>-->
  </view>
      <!-- <navigator url="/pages/survey/surveydetail/index?id=82" class="wgt-btn" >
        <view class="wgt-btn">问卷调查</view>
      </navigator> -->
  <!-- 问卷列表 -->
  <view class="survey-list" wx:if="{{!emptyConfig.show}}">
    <view class="survey-item {{item.statusClass}}" wx:for="{{surveyList}}" wx:key="id" bindtap="continueSurvey" data-id="{{item.id}}">
      <!-- 左上角问卷类型标志 -->
      <view class="type-badge {{item.questionType === '2' ? 'child' : ''}}">
        <text>{{item.questionType === '2' ? '儿童版' : '成人版'}}</text>
      </view>
      
      <!-- 右上角状态标志 -->
      <view class="status-corner {{item.reportStatus == '1' ? 'completed' : 'processing'}}">
        <text>{{item.reportStatus == '1' ? '已完成' : '进行中'}}</text>
      </view>
      
      <!-- 卡片内容: 核心信息 -->
      <view class="item-body">
        <view class="info-row">
          <text class="label">姓名：</text>
          <text class="value name">{{item.sjzName}}</text>
        </view>
        <view class="info-row">
          <text class="label">样本编号：</text>
          <text class="value {{!item.sampleNumber ? 'warning' : ''}}">{{item.sampleNumber || '未采样'}}</text>
        </view>
      </view>
      
      <!-- 卡片底部操作区 -->
      <view class="item-footer">
        <view class="footer-hint">创建时间：{{item.createTime}}</view>
        <!-- 右下角查看按钮 -->
        <view class="view-detail" catchtap="viewDetail" data-id="{{item.id}}">
          <text>查看详情</text>
          <image class="view-icon" src="../../../../static/icon/arrow-right.png" mode="aspectFit" />
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <empty wx:if="{{emptyConfig.show}}" 
    tip="{{emptyConfig.tip}}" 
    btnText="{{emptyConfig.btnText}}" 
    btnShow="{{emptyConfig.btnShow}}" 
    bind:onBtnTap="onEmptyBtnTap">
  </empty>

  <!-- 加载更多提示 -->
  <view class="loading-more" wx:if="{{isLoading}}">
    <view class="loading-icon"></view>
    <text>加载中...</text>
  </view>
  
  <!-- 没有更多数据提示 -->
  <view class="no-more" wx:if="{{!hasMore && surveyList.length > 0 && !isLoading}}">
    <view class="no-more-line"></view>
    <text>没有更多数据了</text>
    <view class="no-more-dot"></view>
  </view>

  <!-- 新增按钮容器 -->
  <view class="add-button-container" wx:if="{{!emptyConfig.show}}">
    <view class="add-button" bindtap="addNewSurvey">
      <image class="add-icon" src="../../../../static/icon/add.png" mode="aspectFit" />
      <text>新增样本采集</text>
    </view>
  </view>
</view> 