<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '',
    };
 
    onLoad(options){
      console.log(decodeURIComponent(options.weburl));
      console.log('options', options);
      const weburl = decodeURIComponent(options.weburl);
      this.weburl = `${weburl}?pid=${options.pid}`;
      console.log('this.weburl', this.weburl);
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      weburl: '',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
