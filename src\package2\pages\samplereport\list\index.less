@import "../../../../resources/style/mixins";

.p-page{
  padding-bottom: 24rpx;
  .top{
    color: #FFF;
    text-align: center;
    font-weight: 600;
    background: linear-gradient(121deg, #30A1A6 9.07%, rgba(48, 161, 166, 0.51) 100%);
    padding: 32rpx 0;
  }
  .feed-write {
    padding: 0 24rpx;
    margin-bottom: 16rpx;
    background-color: #fff;
  }

  .feed-box {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #eee;
    &:last-child{
      border: none;
    }

    &.feed-textarea{
      flex-direction: column;
      align-items: flex-start;
    }

    .title {
      width: 35%;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.90);
      span{
        color: #FF613B;
      }
    }

    .start {
      color: #FC7F60;
    }

    .fedd-input {
      flex: 1;
    }

    .fedd-texarea {
      margin-top: 24rpx;
      width: 100%;
      height: 246rpx;
    }

    .choose-container {
      display: flex;

      .choose-box {
        padding: 24rpx 122rpx 24rpx 122rpx;
        background: #F6F7F9;
        border-radius: 16rpx;
        margin-right: 22rpx;
        // margin-left: 10rpx;
      }

      .ischoose-box {
        padding: 24rpx 122rpx 24rpx 122rpx;
        background: #3ECEB6;
        border-radius: 16rpx;
        margin-right: 22rpx;
        color: #fff;
        // margin-left: 10rpx;
      }
    }

    .feed-img {
      display: flex;
      align-items: center;
      margin-top: 24rpx;
      .m-upload-list {
        display: flex;
        flex-wrap: wrap;

        .m-upload-item {
          position: relative;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          margin-right: 24rpx;
          width: 185rpx;
          height: 185rpx;
          background-color: #f5f5f5;
          overflow: hidden;
          text-align: center;

          .m-upload-image {
            max-width: 100%;
            max-height: 100%;
          }

          .m-upload-tips-image {
            position: absolute;
            right: 6rpx;
            top: 6rpx;
            width: 26rpx;
            height: 26rpx;
            background-size: 100%;

            &.m-upload-sucess {
              background-position: top right;
              background-repeat: no-repeat;
              background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACHUlEQVRIS72WPUgjQRTH/2+DnGhycCgERQW9FKkOLiIWmtXuFMUyhY2dhRYWx1V+FBrtLUxhIVhosXBwHKennSRaibEN4gcq5BC95pLzrjA7Mks2bOLsjht0p33z3m/ee/+ZNwTZ0jRfNOTvBaASEGZAA3ch4DcDMgCSqdP8PmKxglMosjP2ZPYDvvvcFAObIKDJKQgDfhEoUagLLB+Ee3OivUJQ9HhnCExfJaBZlrDVzoAsSBlPfRzYqvR7AlKPfkwzRZknBsUNxNzLCDrp+lyyc3jR6l8G4hCQEq8G8MSH6TNWWAlklAv692ozqQTxzABlxCyjAeKNV+7/ZNz2RJY575le9zbMBWKA1PTPGYAtyBxl9rHge+QLD/h6d2nZSrPJyGCcoGk+NeS/hkTCMshkcxixYDvWsidYvzkrbefST53mWyma3u4jYE8WyMluQrSbC6xk+R0uXwzo56BZAuarBckgPC4D5khNb28AGBVpnknoz4EUQ2zyjHYI+GSNOd32AS219fhydmg0V7RcQHhGu0JQV6ARSx0RnP/L4bMA5gZSLN2usHTc2B1oRFwAcwuxls5WDJWwsWDIkLCduuxaaohBJm8T9rfwgHc1b1xDiqXrNy5sNOS/dpo5HLbQEcG32yskBPfESZylC/vcJ6iWfPjPHIeoDc98grx8VPlRPBkTZs6eDD4r7NVHuQnz5HNiwjz5bpXp9IU+kI8KPzAUBWAp/QAAAABJRU5ErkJggg==");
            }


            &.m-upload-queue {
              background-position: top right;
              background-repeat: no-repeat;
              background-image: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/close.png");
            }
          }
        }

        .m-upload-icon {
          box-sizing: border-box;
          // border: 1px dashed @hc-color-border;
          font-size: 80rpx;
          font-weight: 100;
          line-height: 135rpx;
          text-align: center;
          color: @hc-color-info;
        }
      }

      .img-desc {
        margin-left: 10rpx;

        .img-desc-title {
          font-weight: 600;
          font-size: 16px;
        }

      }
      .img-desc-content {
        color: rgba(0, 0, 0, 0.4);
        margin-top: 10rpx;
        font-size: 28rpx;
      }
    }

    .zhanwei {
      width: 100%;
      height: 100%;
    }
  }
  .btn{
    margin: 64rpx 24rpx 43rpx;
    text-align: center;
    color: #fff;
    font-size: 34rpx;
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 76rpx;
    background: var(--Linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
  }
  .notice{
    border-radius: 8rpx;
    background: #FFFAF1;
    padding: 32rpx 24rpx;
    margin: 0 24rpx;
    color: #BE8014;
    font-size: 24rpx;
    font-weight: 400;
    .title{
      font-weight: 700;
      text-align: center;
      font-size: 34rpx;
      line-height: 48rpx;
    }
    .content{
      >view{
        margin-top: 16rpx;
        >text{
          font-size: 28rpx;
          font-weight: 600;
          color: #FC7F60;
        }
      }
    }
  }
  .bottom{
    margin-top: 24rpx;
    color:rgba(0, 0, 0, 0.4);
    >span{
      margin-left: 24rpx;
    }
    .btn{
      margin-top: 12rpx;
    }
  }
  .m-card {
    margin: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 32rpx;
    position: relative;
    box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  
    // &:after {
    //   content: " ";
    //   position: absolute;
    //   right: 30rpx;
    //   top: 50%;
    //   width: 17rpx;
    //   height: 17rpx;
    //   border-right: 5rpx solid #c7c7cc;
    //   border-bottom: 5rpx solid #c7c7cc;
    //   transform: translate(-8rpx, -50%) rotate(-45deg);
    // }
    .info-main {
      display: flex;
      align-items: flex-start;
      padding-left: 2rpx;
    }
    .main-name {
      display: flex;
      align-items: center;
      flex: 1;
    }
    .name {
      font-weight: 600;
      font-size: 36rpx;
      line-height: 5r4px;
      color: rgba(0, 0, 0, 0.9);
    }
    .status {
      background: rgba(45, 102, 111, 0.10);
      border-radius: 4rpx;
      margin-left: 16rpx;
      font-weight: 600;
      font-size: 20rpx;
      color: #2D666F;
      padding: 4rpx 8rpx;
    }
    .info-extra {
      margin-top: 8rpx;
      color: rgba(0, 0, 0, 0.4);
      font-size: 28rpx;
    }
    .item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
      padding: 16px 0;
      &:last-child{
        border-bottom: 0;
        padding-bottom: 0;
      }
    }
    .arron {
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid rgba(0, 0, 0, 0.4);
      border-bottom: 5rpx solid rgba(0, 0, 0, 0.4);
      transform: translate(-8rpx) rotate(-45deg);
      margin-left: 12rpx;
    }
    .flex-box{
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .text{
      color: rgba(0, 0, 0, 0.4);
    }
  }
}
.empty-content{
	width: 100%;
	height: 100vh;
	text-align: center;
	line-height: 100vh;
	font-weight: bold;
}