<view class="p-page page-inquiry-video">
    <live-pusher class="live-pusher {{ pushMagnify ? 'magnify' : 'shrink' }}" url="{{pushUrl}}" mode="RTC" autopush bindstatechange="pushstatechange" binderror="error1" aspect="3:4" minBitrate="200"  maxBitrate="1200" background-mute="true" audio-quality="high" @tap="liveScale" />
    <live-player class="live-player {{ pushMagnify ? 'shrink' : 'magnify' }}" id="player" object-fit="contain" orientation="{{orientation}}" src="{{playUrl}}" mode="RTC"  autoplay bindstatechange="playstatechange" binderror="error" @tap="liveScale" />
    <cover-view class="middle-layer">
      <!-- style="height:calc(99% - {{showOpts?'240':'100'}}rpx);" -->
      <cover-view class='doc-item'>
        <cover-view class="doc-info">
          <cover-image class="doc-img" src="{{docInfo.image || 'REPLACE_IMG_DOMAIN/ih-miniapp/doc.png'}}" alt="医生头像" />
          <cover-view class="text-box">
            <cover-view class='doc-name'>{{docInfo.name}}</cover-view>
            <cover-view class='doc-des'>{{docInfo.level}}</cover-view>
          </cover-view>
          <!-- <cover-view class="favor-item {{isFavorite ? 'favor-gray' : ''}}" @tap="switchCollect({{isFavorite}})">{{isFavorite ? '已收藏' : '+收藏'}}</cover-view> -->
        </cover-view>
      </cover-view>
      <cover-view class="cover-chat" wx:if="{{hideMsg}}">
        <cover-view class="cover-content" scroll-top="{{scroll}}">
          <cover-view id="chat-box" style="overflow-y: scroll">
            <cover-view wx:for="{{list}}" wx:key="{{index}}">
              <cover-view class="chat-list" wx:if="{{(item.type == 'BIZ' && item.userIsShow == '1') || (item.type == 'SYSTEM' && item.userIsShow == 1)}}">
                <cover-view class="img-box">
                  <cover-image class="chat-img" src="{{userInfo.avatarUrl || 'REPLACE_IMG_DOMAIN/ih-miniapp/doc.png'}}" wx:if="{{item.direction == 'TO_DOCTOR' && item.type == 'BIZ'}}" />
                  <cover-image class="chat-img" src="{{docInfo.image || 'REPLACE_IMG_DOMAIN/ih-miniapp/doc.png'}}" wx:if="{{item.direction == 'TO_USER' && item.type == 'BIZ'}}" />
                  <cover-image class="chat-img" src="REPLACE_IMG_DOMAIN/ih-miniapp/system-tip.png" wx:if="{{item.type == 'SYSTEM' && item.userIsShow == 1}}" />
                </cover-view>
                <cover-view class="chat-item">
                  <cover-view class="tringle"></cover-view>
                  <cover-view class="content" wx:if="{{!item.url && !item.subType && item.type == 'BIZ'}}">{{item.content + '　'}}</cover-view>
                  <cover-view class="content msg" wx:if="{{item.type == 'SYSTEM'}}">{{item.content + '　'}}</cover-view>
                  <cover-view class="content" wx:if="{{!item.url && item.subType == 1 && item.type == 'BIZ' && item.direction == 'TO_USER'}}" @tap="jumpDet('{{item.content}}')">医生开了处方 点击查看{{'　'}}</cover-view>
                  <cover-view class="content" wx:if="{{!item.url && item.subType == 2 && item.type == 'BIZ' && item.direction == 'TO_USER'}}" @tap="jumpDet('{{item.content}}', 2)">医生开了检查 点击查看{{'　'}}</cover-view>
                  <cover-view class="content" wx:if="{{!item.url && item.subType == 3 && item.type == 'BIZ' && item.direction == 'TO_USER'}}" @tap="jumpDet('{{item.content}}', 3)">医生开了检验 点击查看{{'　'}}</cover-view>
                  <cover-view class="content" wx:if="{{!item.url && item.subType == 1 && item.type == 'BIZ' && item.direction == 'TO_DOCTOR'}}" @tap="jumpDet('{{item.content}}')">我给医生发送了处方 点击查看{{'　'}}</cover-view>
                  <cover-view class="content" wx:if="{{!item.url && item.subType == 4 && item.type == 'BIZ' && item.direction == 'TO_DOCTOR'}}" @tap="jumpSign('{{item.content}}')">我给医生发送了体征 点击查看{{'　'}}</cover-view>
                  <cover-view @tap="previewImg" data-preurl="{{item.url}}" wx:if="{{item.url}}">
                    <cover-view wx:if="{{item.direction == 'TO_USER'}}"><cover-image class='image' src='{{item.url}}' /></cover-view>
                    <cover-view wx:if="{{item.direction == 'TO_DOCTOR'}}"><cover-image class='image' src='{{item.url}}' /></cover-view>
                  </cover-view>
                </cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </cover-view>
    </cover-view>
    <cover-view class='modal' wx:if="{{isShow}}">
      <cover-view class='modal-body'>
        <cover-view class='modal-title'><cover-image class="chat-image" src="REPLACE_IMG_DOMAIN/ih-miniapp/chat.png" /></cover-view>
        <cover-view class='modal-content'>确定挂断视频通话？</cover-view>
        <cover-view class='modal-footer'>
          <cover-view class="foot-type" @tap="cancel">取消</cover-view>
          <cover-view class="foot-type" @tap="sure">结束</cover-view>
        </cover-view>
      </cover-view>
    </cover-view>
    
    <cover-image wx:if="{{showOpts}}" @tap="tapShowOpts" class="opts-close" src="REPLACE_IMG_DOMAIN/ih-miniapp/circle-close-white.png"></cover-image>

    <cover-view class="footer" wx:if="{{!showOpts}}">
      <!-- <view class="foot-input">
        <input cursor-spacing="10" value='{{msgText}}' placeholder="点击输入" @input="saveMsg"/>
        <image @tap="sendMsg" class="image" src="REPLACE_IMG_DOMAIN/ih-miniapp/arr.png" />
      </view> -->
      <!-- <view class="foot-eye">
        <image @tap='tapShowMsg' class="image" src="REPLACE_IMG_DOMAIN/ih-miniapp/eye.png" />
      </view> -->
      <!-- <view class="foot-img">
        <image @tap='picture' class="image" src="REPLACE_IMG_DOMAIN/ih-miniapp/pic.png" />
      </view> -->
      <!-- <view class="foot-img">
        <image @tap='tapShowOpts' class="image" src="../../../resources/images/plus-white.png" />
      </view> -->
      <cover-view class="foot-view"></cover-view>
      <cover-view class="foot-view">
        <cover-image class="foot-hangup" @tap="openModal" src="REPLACE_IMG_DOMAIN/ih-miniapp/hang_up2.png" />
      </cover-view>
      <cover-view class="foot-view">
        <cover-view class="foot-rotate">
          <cover-image class="image" @tap="screenRoate" src="REPLACE_IMG_DOMAIN/ih-miniapp/screen-rotate.png" />
        </cover-view>
      </cover-view>
    </cover-view>

    <view class="footer-opts" wx:else>
      <block wx:for="{{bottomItem}}" wx:key="{{index}}">
        <view class="footer-opt-item" wx:if="{{item.show && item.event}}" @tap="bottomTap('{{item.event}}', '{{index}}')">
          <image src='{{item.icon}}' mode="aspectFill"/>
          <text>{{item.name}}</text>
        </view>
        <navigator class="footer-opt-item" wx:if="{{item.show && item.url}}" url="{{item.url}}">
          <image src='{{item.icon}}' mode="aspectFill"/>
          <text>{{item.name}}</text>
        </navigator>
      </block>
    </view>
</view>