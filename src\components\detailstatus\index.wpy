<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from 'wepy';

const STATUS_MAP = {
  S: {
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  L: {
    name: 'lock',
    icon: 'lock',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  F: {
    name: 'fail',
    icon: 'fail',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  P: {
    name: 'abnormal',
    icon: 'abnormal',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  H: {
    name: 'abnormal',
    icon: 'abnormal',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  Z: {
    name: 'abnormal',
    icon: 'abnormal',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  E: {
    name: 'abnormal',
    icon: 'abnormal',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  C: {
    name: 'cancel',
    icon: 'cancel',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  W: {
    name: 'wait',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#000',
      backgroundColor: '#fff',
    },
  },
  0:{
    name: 'wait',
    icon: 'abnormal',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#FFA500',
    },
  },
  1:{
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#3ECDB5',
    },
  },
  2:{
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#3ECDB5',
    },
  },
  4:{
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#3ECDB5',
    },
  },
  3:{
    name: 'fail',
    icon: 'fail',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#F76260',
    },
  },
  5:{
    name: 'fail',
    icon: 'fail',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#F76260',
    },
  },
  7:{
    name: 'fail',
    icon: 'fail',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#F76260',
    },
  },
  6:{
    name: 'cancel',
    icon: 'cancel',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#989898',
    },
  },
  100: {
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#3ECDB5',
    },
  },
  101: {
    name: 'success',
    icon: 'success',
    navigationBarColor: {
      frontColor: '#ffffff',
      backgroundColor: '#3ECDB5',
    },
  },
};

export default class DetailStatus extends wepy.component {
  props = {
    config: {
      type: Object,
      default: {
        status: '',
      },
      twoWay: true,
    },
    leftTime: {
      type: String,
      default: '',
    },
  };

  watch = {
    config(newValue) {
      this.setStatus(newValue);
    },
  };

  data = {
    statusClassName: '',
    statusIcon: '',
  };

  methods = {};

  setStatus(config = {}) {
    const { status = '' } = config;
    const statusObj = STATUS_MAP[status] || {};
    // this.$emit('set-navigationbar-color', statusObj.navigationBarColor); // 根据订单状态设置导航颜色
    this.statusClassName = statusObj.name || '';
    this.statusIcon = statusObj.icon || '';
    this.$apply();
  }
}
</script>
