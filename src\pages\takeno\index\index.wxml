<view class="p-page">
  <!-- <outpatient :config.sync="patientConfig" :patient.sync="patient"></outpatient> -->
  <block wx:for="{{takeList}}" wx:for-index="idx" wx:key="idx">
    <view class="m-card">
      <view class="card-head">
        <view class="card-head-status">
          <view class="card-head-icon">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/wait.png"></image>
          </view>
          <text>等待取号</text>
        </view>
      </view>
      <view class="card-body">
        <view class="takeinfo-item">
          <view class="item-label">医生：{{item.deptName}}｜{{item.doctorName}}</view>
          <!-- <view class="item-info">{{item.deptName}} | {{item.doctorName}}</view> -->
        </view>
        <view class="takeinfo-item">
          <view class="item-label">时间：{{item.regTimeFormat}}</view>
          <!-- <view class="item-info">{{item.regTimeFormat}}</view> -->
        </view>
        <view class="takeinfo-item">
          <view class="item-label">金额：<text class="item-info_warn">￥{{item.totalFeeFormat}}</text></view>
          <!-- <view class="item-info item-info_warn">￥{{item.totalFeeFormat}}</view> -->
        </view>
      </view>
      <view class="card-foot">
        <view class="takeno-oper oper-primary" @tap="takeNo" data-idx="{{idx}}">立即取号</view>
      </view>
    </view>
  </block>
  <empty wx:if="{{takeList.length == 0}}">
    <text slot="text">暂无待取号记录</text>
  </empty>
</view>
