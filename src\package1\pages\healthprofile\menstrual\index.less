@import "../../../../resources/style/mixins";

page{
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.binduser-btn_line{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}

.patInfo-list{
  background: #fff;
  .patInfo-listitem{
    padding: 29rpx 30rpx;
    display: flex;
    position: relative;
    
    &.patInfo-listitem_none{
      display: none;
    }

    &:before{
      content: ' ';
      position: absolute;
      left: 30rpx;
      right: 0;
      top: 0;
      border-top: 1px solid @hc-color-border;
    }
    &:first-child:before{
      display: none;
    }

    .listitem-head{
      width: 200rpx;
      display: flex;
      align-items: center;
      .textBreak();

      .list-title{
        flex: 1;
        font-size: 30rpx;
        color: #353535;
        font-weight: 400;
        line-height: 1;
        padding-right: 12rpx;
        position: relative;

        &.require {
          position: relative;
          &:after {
            content: '*';
            color: #F76260;
            font-size: 30rpx;
          }
        }

        &.list-title_select:before{
          content: ' ';
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          border-bottom: 10rpx solid @hc-color-title;
          border-right: 10rpx solid @hc-color-title;
          border-top: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body{
      flex: 1;
      padding-left: 30rpx;
      position: relative;
      .picker{
        display: flex;
        align-items: center;
        .picker-info{
          flex: 1;
          font-size: 30rpx;
          color: #353535;
          font-weight: 400;
          line-height: 1;
        }
        .item-arrow{
          width: 17rpx;
          height: 17rpx;
          border-right: 5rpx solid #C7C7CC;
          border-bottom: 5rpx solid #C7C7CC;
          -webkit-transform: translateX(-8rpx) rotate(-45deg);
          transform: translateX(-8rpx) rotate(-45deg);
        }
      }

      .textBreak();
    }
    .patInfo-add{
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #3ECEB6;
      line-height: 42rpx;
      text-align: center;
    }
  }

  .listitem_accest{
    color: red;
  }

  .listitem_accest .listitem-body:before{
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.afterscan-operbtnbox{
  margin: 42rpx 40rpx;
}

.binduser-btn_line{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}