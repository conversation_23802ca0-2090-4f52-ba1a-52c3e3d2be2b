<view class="p-page">
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  <block wx:if="{{orderList.length > 0}}">

    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view class="title-btn">
          {{item[0].titleName}}
        </view>
        <block
          wx:for="{{item || []}}"
          wx:for-item="itm"
          wx:key="index"
        >
          <view
            class="list-item"
            @tap="bindGoDetail({{itm}})"
          >
            <!-- <view class="item-icon">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
            </view> -->
            <view class="item-main">
              <view class="main-tit">
                <text>{{itm.videoName}}</text>
              </view>
            </view>
            <view class="item-extra">
              <view class="extra-tit color-{{itm.videoStatus}}">{{ itm.videoStatus == '0' ? '未读' : '已读' }}</view>
              <!-- <view class="extra-txt">{{itm.payedTime}}</view> -->
            </view>
          </view>
        </block>

      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>