@import "../../../resources/style/mixins";

page{
}

.p-page{
}

.m-list{
  
  .list-tit{
    padding: 24rpx 30rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }
  .list{
    background-color: #fff;
    padding: 0 20rpx;
  }
  .list-item{
    display: flex;
    align-items: center;
    padding: 25rpx 10rpx;
    border-top:2rpx solid @hc-color-border;
    &:first-child{
      border-top: 0;
    }
  }
  .item-bd{
    flex: 1;
  }
  .bd-tit{
    font-size: 37rpx;
    color: @hc-color-title;
  }
  .bd-txt{
    margin-top: 10rpx;
    font-size: 28rpx;
    color: @hc-color-text;
    .textBreak();
  }
  .item-ft{
    width: 46rpx;
    height: 46rpx;
    image{
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
}

.m-form{
  .form-tit{
    padding: 24rpx 30rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }
  .form{
    background-color: #fff;
    padding: 0 30rpx;
  }
  .form-item{
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-top:2rpx solid @hc-color-border;
    &:first-child{
      border-top: 0;
    }
  }
  .item-label{
    font-size: 34rpx;
    color: @hc-color-title;
    width: 5.3em;
  }
  .item-input{
    flex: 1;
    font-size: 34rpx;
    input{
      width: 100%;
      display: block;
      color: #333;
    }
  }
}
.m-btn{
  padding: 60rpx 40rpx;
  .btn{
    font-size: 36rpx;
    color:#fff;
    text-align: center;
    line-height: 94rpx;
    background-color: @hc-color-primary;
    border-radius: 10rpx;
  }
}

.binduser-btn_line{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}


.bindcard-list{
  background: #fff;
  .bindcard-listitem{
    padding: 30rpx;
    display: flex;
    position: relative;

    &.bindcard-listitem_none{
      display: none;
    }

    &:before{
      content: ' ';
      position: absolute;
      left: 30rpx;
      right: 0;
      top: 0;
      border-top: 2rpx solid @hc-color-border;
    }
    &:first-child:before{
      display: none;
    }

    .listitem-head{
      width: 200rpx;
      display: flex;
      align-items: center;
      .textBreak();

      .list-title{
        flex: 1;
        font-size: 30rpx;
        color: @hc-color-title;
        padding-right: 12rpx;
        position: relative;
        line-height: 1;

        &.list-title_select:before{
          content: ' ';
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          border-bottom: 10rpx solid @hc-color-title;
          border-right: 10rpx solid @hc-color-title;
          border-top: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body{
      flex: 1;
      padding-left: 30rpx;
      position: relative;
      .textBreak();
    }
  }

  .listitem_accest{
    color: red;
  }

  .listitem_accest .listitem-body:before{
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.m-content{
  font-size: 30rpx;
  color: @hc-color-title;
}

.m-mt-20{
  margin-top: 20rpx;
}

.o-error{
  color: #ff613b;
}

.o-disabled{
  background: #ddd;
}

.m-operbtnbox{
  margin: 42rpx 40rpx;
}
.hc-toptip{
  padding: 40rpx 30rpx 40rpx 120rpx;
  color: @hc-color-warn;
  font-size: 30rpx;
  background: url('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/warn.png') no-repeat;
  background-size: 60rpx;
  background-position: 30rpx center;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  box-shadow: 0px 6px 14px 0px rgba(241,243,246,1);
}