<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import { validator } from '@/utils/utils';
  import { CURSOR_SPACING, CHILD_MAX_AGE } from '@/config/constant';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class ReportList extends wepy.page {
    config = {
      navigationBarTitleText: '我要推荐',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      // 'empty': Empty,
    };

    onLoad(Option) {
      this.grid = Option.grid;
      this.pid = Option.pid;
      this.optionsData = Option;
    }

    data = {
      CURSOR_SPACING,
      optionsData: {},
      
      placeholderColor: '',
      errorColor: 'red',

      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      hisConfig: {
        relationTypes: [],
        patientTypes: [],
        idTypes: [],
        patCards: [],
      },

      imgWidth: 0,
      imgHeight: 0, 

      idCardPath: '',
      idCardToken: '',

      idTypesIdx: 0,
      patCardsIdx: 0,

      isNewCard: 0,
      isNoCard: 0,
      patientName: '',
      patManName: '',
      medicalCode: '',
      idType: '1',
      birthday: '',
      idNo: '',
      patientAddress: '',
      patCardNo: '',
      patientMobile: '',
      relationType: '5',
      patientType: '0',

      isQueryCard: '0',
      isReadOnlyMobile: false,
      queryPatientMobile: '',
      patientMobile: '',

      // 倒计时剩余数
      leftTime: 60,
      staticLeftTime: 60,
      // 倒计时是否完成
      isClocking: false,
      // 倒计时计时器
      clockTimer: 0,
      grid: '',
      pid: '',
    };

    validator(id){
      let validate = {
        patientName: {
          regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
          errTip: '请输入2-8位合法姓名',
        },
        patManName: {
          regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{0,8}$/,
          errTip: '请输入2-8位合法姓名',
        },
        // medicalCode: {
        //   regexp: /\d{10}/,
        //   errTip: '请输入正确的病历号',
        // },
        patientMobile: {
          regexp: /^1\d{10}$/,
          errTip: '请输入正确的手机号',
        },
        // patientAddress: {
        //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
        //   errTip: '请输入有效的住址',
        // },
      };
      const value = this.getFormData();

      let hasErr = false;
      for(let o in value){
        const obj = validate[o];
        if(obj && obj.regexp){
          let thisErr = false;
          if(typeof obj.regexp === 'function'){
            const retObj = obj.regexp(value);
            if(!retObj.ret){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          } else {
            if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          }
          if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
            this.errorElement[obj.errTarget || o] = true;
            this.toptip = obj.errTip || '';
            const errTimer = setTimeout(() => {
              this.toptip = '';
              this.$apply();
              clearTimeout(errTimer);
            }, 2000);
            break;
          }
        }
      }

      return hasErr;
    }

    getFormData(){
      const { patientName, patManName, medicalCode, patientAddress, patientMobile } = this;

      return {
        patientName,
        patientAddress,
        patientMobile,
        patManName,
        medicalCode,
      };
    }

    async adduser() {
      
      const value = this.getFormData();
      const { optionsData = {} } = this;

      if(optionsData.patientName === value.patientName){
        wepy.showModal({
          title: '提示', //提示的标题,
          content: '被推荐人不能是本人', //提示的内容,
          showCancel: false, //是否显示取消按钮,
          cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
          cancelColor: '#000000', //取消按钮的文字颜色,
          confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
          confirmColor: '#3CC51F', //确定按钮的文字颜色,
          success: res => {}
        });
        return;
      }
      wepy.showLoading();

      const param = {
        patName: value.patientName,
        patManName: value.patManName,
        medicalCode: value.medicalCode,
        patMobile: value.patientMobile,
        pid: optionsData.patHisNo,
        grid: optionsData.patCardNo,
        patientId: optionsData.patientId,
        recommendName: optionsData.patientName,
        extFields: value.patientAddress,
      }
      const { code } = await Api.add(param);
      wepy.hideLoading();
      if (code == 0) {
        wx.showModal({
          title: '温馨提示',
          content: '推荐成功',
          showCancel: false,
        }).then(() => {
          this.$apply();
          wepy.navigateBack({
            delta: 1
          })
        });
      }
      this.$apply();
    }

    methods = {
      getAuth() {
        this.hasErr = this.validator();
        if(this.hasErr){
          return false;
        }
        this.adduser();
      },
      formSubmit(e){
          
      },
      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        this[id] = value;
      },
      resetThisError(e){
        const { id } = e.currentTarget;
        this.errorElement[id] = false;
      },
      goBack() {
        wepy.navigateBack({
          delta: 1
        });
      }
    };

    events = {
      
    };

  }
</script>