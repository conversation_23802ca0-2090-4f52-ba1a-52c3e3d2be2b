<view class="p-page">
  <view class="m-info">
    <view class="info-box">
      <view class="info">
        <view class="info-hd">
          <image
            mode="widthFix"
            src="{{docDetail.doctorImg || (docSex === 'M' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' )}}"
            alt=""
          ></image>
        </view>
        <view class="info-bd">
          <view class="bd-tit">
            <view class="tit-lt">{{docDetail.doctorName}}</view>
          </view>
          <view class="bd-txt">{{deptName}}  {{docDetail.doctorTitle}}</view>
        </view>
      </view>
    </view>
  </view>

  <view hidden="{{tabIndex === 0}}">
    <view class="m-intro">
      <view class="intro-box">
        <view class="intro-item">
          <view class="item-tit">擅长方向</view>
          <view class="item-text">
            {{docDetail.skill || '暂未添加'}}
          </view>
        </view>
        <view class="intro-item">
          <view class="item-tit">个人介绍</view>
          <view class="item-text">
            <block wx:if="{{docDetail.summary}}">
              <wxparser rich-text="{{docDetail.summary}}" />
            </block>
            <block wx:if="{{!docDetail.summary}}">暂无介绍</block>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>