<style lang="less" src="../queryuserinfo/index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy";
import Message from "@/components/message/index";
import TopTip from "@/components/toptip/index";
import { CURSOR_SPACING, CHILD_MAX_AGE } from "@/config/constant";
import {
  validator,
  getAgeByBirthday,
  getBirthdayByIdCard
} from "@/utils/utils";
import * as Api from "../queryuserinfo/api";
import * as WxmpRsa from "../../../utils/rsa/JSEncrypt.js";

export default class QueryOtherType extends wepy.page {
  config = {
    navigationBarTitleText: "绑定就诊卡",
    navigationBarBackgroundColor: '#fff',
  };

  components = {
    message: Message,
    toptip: TopTip
  };

  onLoad(option) {
    this.initData(option);
    this.getHisConfig();
    if (option.qryType == 1) {
      wx.setNavigationBarTitle({
        title: "注册登录"
      });
    }
  }

  onUnload() {
    clearTimeout(this.clockTimer);
  }

  onShow() {
    this.debounceFunc = this.debounce(this.userInfoChange.bind(this), 500);
    this.peopleList = [];
    this.initClock;
    this.getSecret();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    publicKey: "",
    CURSOR_SPACING,

    options: {},

    placeholderColor: "",
    errorColor: "red",

    errorElement: {}, // 发生错误的元素
    hasErr: true, // 是否存在校验错误
    toptip: "",
    isShowPhoneInfo: false,
    isOtherType: true, // 是否选择其它证件类型
    hisConfig: {
      relationTypes: [],
      patientTypes: [],
      idTypes: [],
      patCards: []
    },

    imgWidth: 0,
    imgHeight: 0,

    idCardPath: "",
    idCardToken: "",

    idTypesIdx: 0,
    patCardsIdx: 0,

    isNewCard: 0,
    isNoCard: 0,
    patientName: "",
    idType: "",
    birthday: "",
    idNo: "",
    patientAddress: "",
    patientMobile: "",
    pid: "",
    relationType: "5",
    patientType: "0",
    qryType: "",
    // 倒计时剩余数
    leftTime: 60,
    staticLeftTime: 60,
    // 倒计时是否完成
    isClocking: false,
    // 倒计时计时器
    clockTimer: 0,
    // 验证码
    sendCode: "",
    verifyCode: "",
    // 相关联系人列表
    peopleList: [],
    mobileReadonly: false
  };

  async getSecret() {
    const { code, data, msg } = await Api.getSecret();
    if (code == 0 && data.secret) {
      if (data.resultCode == "0") {
        this.publicKey = data.secret;
      } else {
        wepy.showModal({
          title: "提示", //提示的标题,
          content: data.resultMessage || "未查询到公钥", //提示的内容,
          showCancel: false, //是否显示取消按钮,
          cancelText: "取消", //取消按钮的文字，默认为取消，最多 4 个字符,
          cancelColor: "#000000", //取消按钮的文字颜色,
          confirmText: "确定", //确定按钮的文字，默认为取消，最多 4 个字符,
          confirmColor: "#3CC51F", //确定按钮的文字颜色,
          success: res => {}
        });
      }
    }
  }

  initData(options) {
    console.log("adult", options);
    const {
      name = "",
      idNo = "",
      pid = "",
      address = "",
      idCardPath = "",
      idCardToken = "",
      isNewCard = "0",
      isNoCard = "0",
      qryType = "2"
    } = options;
    this.patientName = name;
    this.idNo = idNo;
    this.patientAddress = address;
    this.idCardPath = idCardPath;
    this.idCardToken = idCardToken;
    this.isNewCard = isNewCard;
    this.isNoCard = isNoCard;
    this.options = options;
    this.qryType = qryType;
    console.log(this.patientName);
  }
  async getHisConfig() {
    const { code, data = {}, msg } = await Api.getHisConfig();
    if (code == 0) {
      if (data.idTypes) {
        data.idTypes = data.idTypes.slice(1);
      }
      this.idType = data.idTypes[0].dictKey;
      this.hisConfig = data;
      this.$apply();
    }
  }

  initClock() {
    this.leftTime = 60;
    this.staticLeftTime = 60;
    this.isClocking = false;
    this.clockTimer = 0;
    this.sendCode = "";
    this.verifyCode = "";
    this.$apply();
  }

  // 获取验证码
  async getVerifyCode() {
    const { isClocking } = this;
    if (isClocking) {
      return false;
    }
    wepy.showLoading({ title: "请稍候", mask: true });
    const value = this.getFormData();
    value.mobile = value.patientMobile;
    // const { JSEncrypt } = WxmpRsa;
    WxmpRsa.JSEncrypt.prototype.setPublicKey(this.publicKey);
    value.mobile = WxmpRsa.JSEncrypt.prototype.encrypt(value.patientMobile);
    value.type = 0;
    value.grid = this.grid || "";
    delete value.patientMobile;
    const { code, data, msg } = await Api.sendMsgAndValidate(value);
    wepy.hideLoading();
    if (code == 0 && data.resultCode == 0) {
      wepy.showToast({
        title: "发送成功",
        icon: "success"
      });
      this.clock();
      this.$apply();
    } else {
      await wepy.showModal({
        title: "提示",
        content: data.resultMessage || "验证码发送失败，请尝试重试",
        showCancel: false
      });
    }
  }

  /**
   * 倒计时
   */
  clock() {
    this.clockTimer = setTimeout(() => {
      let { leftTime, staticLeftTime = 60 } = this;
      --leftTime;
      if (leftTime <= 0) {
        // 倒计时结束
        this.leftTime = staticLeftTime;
        this.isClocking = false;
        this.$apply();
      } else {
        this.leftTime = leftTime;
        this.isClocking = true;
        this.clock();
        this.$apply();
      }
    }, 1000);
  }

  validator(id) {
    const validate = {
      patientName: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9 ][\u4e00-\u9fa5\w\s ]*[\u4e00-\u9fa5_a-zA-Z0-9 ]$/,
        errTip: "请输入合法姓名"
      },
      idNo: {
        regexp: (() => {
          const regexp = this.hisConfig.idTypes[this.idTypesIdx].regexp;
          if (typeof regexp === "function") {
            return val => regexp(val.idNo);
          } else {
            return /^\S+$/;
          }
        })(),
        errTip:
          this.hisConfig.idTypes[this.idTypesIdx].errTip ||
          `${this.hisConfig.idTypes[this.idTypesIdx].dictValue}不能为空`
      },
      patientAddress: {
        regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]{0,}$/,
        errTip: "请输入有效的住址"
      },
      patientMobile: {
        regexp: !this.mobileReadonly ? /^1\d{10}$/ : "",
        errTip: "请输入正确的手机号"
      },
      pid: {
        regexp: /^(|\d{3,8})$/,
        errTip: "请输入正确pid号"
      }
    };

    if (this.qryType == 1) {
      validate.verifyCode = {
        regexp: /^\d{6}$/,
        errTip: "请输入正确验证码"
      };
    }

    const value = this.getFormData();

    let hasErr = false;
    for (let o in value) {
      const obj = validate[o];
      if (obj && obj.regexp) {
        let thisErr = false;
        if (typeof obj.regexp === "function") {
          const retObj = obj.regexp(value);
          if (!retObj.ret) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        } else {
          if (
            typeof obj.regexp.test === "function" &&
            !obj.regexp.test(value[o])
          ) {
            // 添加非本人时，不需要验证手机号
            if (this.qryType == 2 && o == "patientMobile") {
              continue;
            }
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        }
        if (
          (!id && hasErr) ||
          (obj.errTarget && obj.errTarget == id && thisErr)
        ) {
          // 提交时弹框提示
          this.errorElement[obj.errTarget || o] = true;
          this.toptip = obj.errTip || "";
          const errTimer = setTimeout(() => {
            this.toptip = "";
            this.$apply();
            clearTimeout(errTimer);
          }, 2000);
          break;
        }
      }
    }

    return hasErr;
  }

  getFormData() {
    const {
      patientName,
      idNo,
      verifyCode,
      patientAddress,
      patientMobile,
      pid,
      idType,
      isNewCard,
      isNoCard,
      idTypesIdx,
      patCardsIdx,
      hisConfig
    } = this;
    const patCardType =
      this.isNewCard == 1 ? "21" : hisConfig.patCards[patCardsIdx].dictKey;
    console.log("formData里的调this", this);
    return {
      patientName,
      idNo,
      idType,
      birthday: "",
      patientAddress,
      patientMobile,
      relationType: "5",
      patientType: "0",
      patCardType,
      isNewCard,
      isNoCard,
      verifyCode,
      pid,
      patHisNo: pid
    };
  }

  async queryRelationPatients() {
    const value = this.getFormData();
    const { JSEncrypt } = WxmpRsa;
    WxmpRsa.JSEncrypt.prototype.setPublicKey(this.publicKey);
    value.mobile = WxmpRsa.JSEncrypt.prototype.encrypt(value.patientMobile);
    delete value.patientMobile;
    const { code, data = {}, msg } = await Api.queryRelationPatients({
      ...value,
      patName: value.patientName,
      qryType: 2,
      idType: this.idType
    });
    wx.hideLoading();
    let { items = [] } = (data.itemList || [])[0] || {};
    if (code == 0 && data.resultCode == 0) {
      wx.showToast({
        title: "查询成功",
        icon: "success",
        duration: 1000,
        success: ""
      });
      items.map(item => {
        if (item.idType) {
          item.idType = Number(item.idType.substr(-2));
        }
        return item;
      });
      if (this.qryType == 1 || items.length <= 1) {
        // 绑定本人或 查到的家庭成员小于等于一个
        wx.navigateTo({
          url: `/pages/bindcard/adduser/index?isNewCard=${
            items.length >= 1 ? 0 : 1
          }&idNo=${this.idNo}&idType=${this.idType}&patientName=${
            this.patientName
          }&patientMobile=${this.patientMobile}&qryType=${
            this.qryType
          }&pid=${value.pid || ""}&address=${this.patientAddress}&isOtherType=1`
        });
        return;
      } else if (items.length > 1) {
        // 查询到多个人的信息
        items = items
          .map(item => {
            if (!item.pid) {
              item.pid = value.pid;
            }
            return item;
          })
          .filter(item => {
            return item.patName && item.idNo;
          });
        this.peopleList = items;
      }
    } else {
      wepy.showModal({
        title: "提示",
        content: data.resultMessage || "未查询到您的用户信息",
        showCancel: false
      });
    }
    this.$apply();
  }

  async queryMobileInfo(param) {
    this.mobileReadonly = true;
    const { code, data = {}, msg } = await Api.queryRelationPatients(
      { ...param, qryType: 1, idType: this.idType },
      false
    );
    let { items = [] } = (data.itemList || [])[0] || {};
    if (items.length === 1) {
      const { telephone = "", grid = "" } = items[0];
      // if (!telephone || !/^1\d{10}$/.test(telephone)) {
      //   return false;
      // }
      this.grid = grid;
      this.patientMobile = telephone;
      this.mobileReadonly = true;
    } else {
      const { idNo = "", patName = "" } = param;
      if (idNo && patName) {
        this.mobileReadonly = false;
      }
    }
    this.$apply();
  }

  userInfoChange() {
    // 输入姓名身份证号码时 如果是身份证类型 判断身份证正确之后 查询用户信息
    // if (this.idTypesIdx != 0) {
    //   // 非身份证
    //   return;
    // }
    let validateResult = true;
    const param = { idNo: this.idNo || "", patName: this.patientName };
    for (let name in param) {
      let result = true;
      const value = param[name];
      if (name === "idNo") {
        if (!validator.idCard(value)) {
          validateResult = false;
        }
      } else if (!name) {
        validateResult = false;
      }
    }
    if (!validateResult) {
      return;
    }
    this.queryMobileInfo(param);
  }

  debounce(fn, delay) {
    let timer = null;
    return () => {
      let args = arguments;
      let context = this;

      if (timer) {
        clearTimeout(timer);

        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      } else {
        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      }
    };
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    gotoUpload() {
      // 跳转上传身份证
      wepy.navigateBack();
    },
    setImgSize(e) {
      const { width, height } = e.detail;
      const basScale = 370 / 214;
      let scale = width >= height * basScale ? 370 / width : 214 / height;
      scale = scale > 1 ? 1 : scale;
      this.imgWidth = width * scale;
      this.imgHeight = height * scale;
    },
    async getFilePathByToken() {
      // 根据token换取imgpath
      const { code, data, msg } = await Api.getFilePathByToken({
        token: this.idCardToken,
        cardType: 0
      });
      if (code == 0 && data.url) {
        this.idCardPath = res.data.url;
        this.$apply();
      }
    },
    reUpload() {
      wepy.navigateBack();
    },
    resetThisError(e) {
      const { id } = e.currentTarget;
      this.errorElement[id] = false;
    },
    actionSheetType(e) {
      console.log("action", e);
      const { prop = "" } = e.target.dataset || {};
      if (prop) {
        let listData = this.hisConfig[prop] || [];
        if (this.isOtherType) {
          listData = listData.filter(v => v.dictKey != 1);
        }
        wepy
          .showActionSheet({
            itemList: listData.map(v => v.dictValue)
          })
          .then(res => {
            console.log(res);
            this.mobileReadonly = false;
            this.idNo = "";
            const idxKey = `${prop}Idx`;
            this[idxKey] = res.tapIndex;
            this.idType = this.hisConfig.idTypes[this.idTypesIdx].dictKey;
            this.$apply();
          });
      }
    },
    changeIdType(e) {
      console.log("e", e);
      const { prop = "" } = e.target.dataset || {};
      if (prop) {
        let listData = this.hisConfig[prop] || [];
        if (this.isOtherType) {
          listData = listData.filter(v => v.dictKey != 1);
        }
        const { value = "" } = e.detail;
        this.mobileReadonly = false;
        this.idNo = "";
        const idxKey = `${prop}Idx`;
        this[idxKey] = value;
        this.idTypesIdx = value;
        this.idType = this.hisConfig.idTypes[value].dictKey;
        this.$apply();
      }
    },
    inputTrigger(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      this[id] = value;
      if (id === "idNo" || id === "patientName") {
        this.debounceFunc(e);
      }
    },
    async formSubmit(e) {
      const value = this.getFormData();
      console.log("formData", value);
      this.hasErr = this.validator();
      if (this.hasErr) {
        return false;
      }
      // wx.showLoading({
      //   title: '加载中',
      // });
      // wepy.showLoading({ title: '请稍候', mask: true });
      if (this.qryType == 1) {
        value.mobile = value.patientMobile;
        const { JSEncrypt } = WxmpRsa;
        // WxmpRsa.JSEncrypt.prototype.setPublicKey(this.publicKey);
        // value.mobile = WxmpRsa.JSEncrypt.prototype.encrypt(value.patientMobile);
        value.type = 1;
        value.smsCode = value.verifyCode;
        value.grid = this.grid || "";
        delete value.patientMobile;
        const { code, data, msg } = await Api.sendMsgAndValidate(value);
        // wepy.hideLoading();
        if (code == 0 && data.resultCode == 0) {
          // this.pushData();
          console.log("校验验证码成功");
        } else {
          await wepy.showModal({
            title: "提示",
            content: data.resultMessage || "验证码校验失败，请尝试重试",
            showCancel: false
          });
          return false;
        }
      }
      this.peopleList = [];
      this.queryRelationPatients();

      // this.validateVerifyCode()
    },

    sendVerifyCode() {
      // 发送验证码
      if (!this.mobileReadonly) {
        if (!/^1\d{10}$/.test(this.patientMobile)) {
          wepy.showModal({
            title: "提示",
            content: "请输入正确的手机号码",
            showCancel: false
          });
          return false;
        }
      }
      this.getVerifyCode();
    }

    // bindViewTap() {
    //   wx.navigateTo({
    //     url: `/pages/bindcard/userinfo/index?idNo=${this.idNo}&patName=${this.patientName}&patientMobile=${this.patientMobile}`
    //   })
    // }
  };
}
</script>
