@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.doctorRemark {
  font-weight: bold;
  color: @hc-color-title !important;

  .lugu {
    color: @hc-color-primary;
  }

  .kaifu {
    color: @hc-color-link;
  }
}

.top-tips {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx;
  color: @hc-color-error;
  font-size: 24rpx;
  background-color: #fff;
}

.doc-tips {
  background-color: @hc-color-white;
  border-bottom: 2rpx solid @hc-color-border;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: @hc-color-text;

  .num {
    color: @hc-color-error;
  }
}

.m-info {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx 32rpx;
  flex-shrink: 0;
  background-color: #fff;

  .info-date {
    flex: 1;
    padding-right: 20rpx;
    font-size: 30rpx;
    color: @hc-color-text;
  }

  .info-date_vsb {
    visibility: hidden;
  }

  .info-tab {
    display: flex;
    padding: 8rpx;
    line-height: 36rpx;
    overflow: hidden;
    flex: 1;
    border-radius: 8rpx;
    background: rgba(45, 102, 111, 0.10);
  }

  .tab-li {
    padding: 12rpx 0;
    text-align: center;
    width: 50%;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.70);
    font-weight: 600;

    &.active {
      border-radius: 8rpx;
      color: #2D666F;
      background-color: #fff;
      box-shadow: 0px 1rpx 2rpx 0px rgba(0, 0, 0, 0.20);
    }
  }
}

.m-search {
  display: flex;
  width: 100%;
  height: 112rpx;
  flex-direction: row;
  padding: 24rpx 32rpx;
  align-items: center;
  // box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  flex-shrink: 0;

  .search-ipt {
    flex: 1;
    height: 82rpx;
    padding-left: 60rpx;
    padding-top: 15rpx;
    padding-bottom: 15rpx;
    position: relative;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 45rpx;
    overflow: hidden;
    box-sizing: border-box;
  }

  .ipt {
    width: 100%;
    font-size: 30rpx;
    height: 52rpx;
    line-height: 52rpx;
  }

  .ipt-icon {
    position: absolute;
    left: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 28rpx;
    height: 28rpx;
    background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/home/<USER>") no-repeat 50% 50%;
    background-size: 100% 100%;
  }

  .ipt-icon-cancel {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 28rpx;
    height: 28rpx;
  }

  .search-extra {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 10rpx;
  }

  .extra-cancel {
    margin-left: 32rpx;
    font-size: 30rpx;
    color: @hc-color-title;
  }

  .extra-itm {
    position: relative;
    margin-left: 20rpx;
    width: 40rpx;
    height: 40rpx;
    padding: 10rpx;
  }

  .itm-qr {
    width: 40rpx;
    height: 40rpx;
  }

  .itm-notice {
    width: 35rpx;
    height: 40rpx;
  }

  .itm-notice-num {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    padding: 0;
    background-color: red;
    color: #fff;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    overflow: hidden;
    text-align: center;
    font-size: 24rpx;
    border-radius: 50%;
    transform: translate(40%, -40%);
  }
}

// 弹窗提示
.desc-modal-mask {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  .desc-modal {
    width: 670rpx;
    border-radius: 24rpx;
    background-color: @hc-color-white;

    .desc-title {
      text-align: center;
      height: 48rpx;
      padding: 40rpx 0 32rpx;
      font-size: 34rpx;
      font-weight: 600;
      color: @hc-color-title;
    }

    .desc-content {
      width: 100%;
      max-height: 700rpx;
      // min-height: 200rpx;
      box-sizing: border-box;
      padding: 0 32rpx 32rpx 32rpx;
      color: @hc-color-info;
    }

    .desc-content-info {
      text-align: center;
      line-height: 1.7;
    }

    .address {
      font-weight: 600;
      color: @hc-color-title;
    }

    .phone {
      color: @hc-color-assist;
    }

    .desc-footer {
      height: 96rpx;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;

      // border-top: 2rpx solid @hc-color-border;
      >view {
        flex: 1;
        font-size: 34rpx;
        font-weight: 500;
        color: @hc-color-gray;
        text-align: center;
        margin: 24rpx 0;

        &+view {
          border-left: 2rpx solid @hc-color-border;
        }
      }

      .agree {
        color: @hc-color-primary;
      }
    }
  }
}

.date-container {
  overflow: auto;
}

//日期收拢模式
.m-date-scroll {
  display: none;
  position: relative;
  padding: 16rpx 24rpx 24rpx;
  background-color: #fff;
  box-shadow: 0px 1rpx 3rpx 0px rgba(0, 0, 0, 0.08);

  &.active {
    display: block;
  }

  .date-box {
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    flex-direction: column;
    padding-right: 0;
  }

  .date {
    flex: 1;
    display: flex;
    padding-bottom: 10rpx;
    padding-right: 88rpx;
    flex-direction: column; // 改为纵向排列
    padding-right: 0; // 移除右侧padding
  }

  .date-row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 16rpx; // 增加行间距

    &:last-child {
      margin-bottom: 0;
    }
  }

  .date-item {
    width: 14.28%; // 7项等宽
    flex-shrink: 0;
    margin: 0;
    box-sizing: border-box; 
    padding: 0 4rpx; // 增加左右间距

    // 保持原有激活状态
    &.active {
      background: linear-gradient(180deg, #30A1A6 0%, #2F848B 100%);
      border-radius: 8rpx;
    }
  }

  .item-week {
    font-size: 24rpx;
    padding: 10rpx 0;
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400;
    text-align: center;
  }

  .item-box {
    width: 100%;
    height: auto;
    padding: 16rpx 0;
    margin: 0 auto;
    font-size: 34rpx;
    font-weight: 600;
    color: @hc-color-title;
  }

  .item-day {
    font-size: 28rpx;
    color: @hc-color-title;
    line-height: 32rpx;
    line-height: 1.2;
    text-align: center;
    
  }

  .item-status {
    font-size: 20rpx;
    line-height: 24rpx;
    margin-top: 8rpx;
    color: rgba(0, 0, 0, 0.4);
    font-weight: normal;
    text-align: center;

    &.on {
      color: #2D666F;
    }
  }

  .date-item {
    &.on {
      .item-day {
        color: @hc-color-title;
      }
    }

    &.active {
      border-radius: 8rpx;
      background: linear-gradient(180deg, #30A1A6 0%, #2F848B 100%);
      color: #fff;

      .item-day {
        color: #fff;
      }

      .item-status {
        color: #fff;
      }

      .item-week {
        font-weight: 400;
        color: #fff;

      }
    }
  }
}

//日期展开模式
.m-date {
  display: none;
  // background-color: #fff;
  // border-top: 2rpx solid @hc-color-border;

  &.active {
    display: block;
  }

  .date-box {
    overflow-x: visible !important; 
  }

  .week {
    padding: 16rpx 0;
    display: flex;
    // border-bottom: 2rpx solid @hc-color-border;
  }

  .week-item {
    flex-basis: 14.28%;
    text-align: center;
    color: rgba(0, 0, 0, 0.4);
    font-size: 24rpx;
  }

  .date {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 10rpx;
  }

  .date-item {
    flex-basis: 14.28%;
    text-align: center;
    margin-bottom: 10rpx;
    padding-top: 15rpx;
  }

  .item-box {
    width: 88rpx;
    height: 68rpx;
    padding: 12rpx 0;
    margin: 0 auto;
  }

  .item-day {
    font-size: 34rpx;
    color: @hc-color-text;
    line-height: 36rpx;
    font-weight: 600;
  }

  .item-status {
    font-size: 20rpx;
    line-height: 24rpx;
    font-size: 18rpx;
    color: rgba(0, 0, 0, 0.4);
  }

  .date-item {
    &.on {
      .item-day {
        color: @hc-color-title;
      }

      .item-status {
        color: @hc-color-primary;
      }
    }

    &.active {
      .item-box {
        border-radius: 8rpx;
        background-color: @hc-color-primary;
        color: #fff;
      }

      .item-day {
        color: #fff;
      }

      .item-status {
        color: #fff;
      }
    }
  }
}

.list-container {
  overflow: auto;
  // flex-shrink: 0
  // margin-top: 20rpx;
}

//医生列表
.m-list {
  // margin: 24rpx 0x;
  background: #F2F4F4;
  margin-top: 24rpx;

  &.list-all-box {
    margin: 24rpx;
    overflow: auto;

    .list-item {
      margin: 0 0 16rpx;

      &:last-child {
        margin: 0;
      }

      .item-bd {
        .info-lt {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .list-item {
    display: block;
    padding: 40rpx;
    margin: 0 24rpx;
    border-radius: 8rpx;
    background: #fff;
    margin-bottom: 16rpx;
    box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
  }

  .item-box {
    display: flex;

    &.item-box-bd {
      padding-bottom: 24rpx;
      margin-bottom: 24rpx;
      border-bottom: 1px solid rgba(0, 0, 0, 0.10);
    }
  }

  .item-other {
    font-size: 26rpx;
    color: rgba(19, 19, 19, 0.90);
  }

  .other-date {
    &:after {
      display: inline;
      content: "、";
    }

    &:last-child {
      &:after {
        content: "";
      }
    }
  }

  .list-item {
    &:last-child {
      border-bottom: 0;
    }
  }

  .item-hd {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    overflow: hidden;
    text-align: center;
    margin-right: 16rpx;
    width: 98rpx;
    height: 98rpx;

    image {
      vertical-align: top;
    }
  }

  .item-bd {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
  }

  .disabled {
    color: @hc-color-gray;
  }

  .rt-arrow {
    width: 17rpx;
    height: 17rpx;
    margin-left: 20rpx;
    border-right: 4rpx solid #c7c7cc;
    border-bottom: 4rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }

  .bd-info {
    display: flex;
    flex-direction: column;
  }

  .info-top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .rt-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .list-num {
        margin-right: 24rpx;
        color: rgba(0, 0, 0, 0.70);
        font-size: 28rpx;
        font-weight: 600;

        .num {
          color: rgba(63, 150, 157, 1);
        }
      }
    }
  }

  .lt-title {
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    margin-right: 16rpx;
    white-space: nowrap;
    font-size: 32rpx;
    color: @hc-color-title;
    font-weight: 600;

    .title-name {
      margin-right: 16rpx;
      font-weight: 600;
      font-size: 32rpx;
    }

    .title-doctor {
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.70);
      font-weight: normal;
    }

  }

  .lt-text {
    color: @hc-color-text;
    font-size: 28rpx;
  }

  .info-ft {
    margin-top: 4rpx;

    .lt-text {
      font-size: 28rpx;
    }
  }

  .has-num {
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 32rpx;
    background: #E7AA35;
    border-radius: 35rpx;
    padding: 4rpx 16rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #fff;
  }

  .has-wait {
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 32rpx;
    background: @hc-color-primary;
    border-radius: 60rpx;
    padding: 4rpx 16rpx;
    font-weight: 600;
    font-size: 20rpx;
    color: @hc-color-white;
    margin-right: 16rpx;
  }

  .rt-num {
    font-size: 28rpx;
    color: @hc-color-title;
    margin-right: 10rpx;
  }

  .unit-label {
    display: inline-block;
    background-color: @hc-color-warn;
    border-radius: 100rpx;
    line-height: 40rpx;
    white-space: nowrap;
    padding: 0 12rpx;
    color: #fff;
    font-size: 28rpx;

    .label-disabled {}
  }

  .bd-extra {
    padding-top: 8rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.40);
  }

  .list-item.no-extra {
    .lt-text {
      margin-top: 20rpx;
    }

    .bd-info {
      align-items: center;
    }
  }
}

.unit-fold {
  display: none;
  position: absolute;
  right: 0;
  top: 6rpx;
  bottom: 0;
  width: 88rpx;
  font-size: 36rpx;
  line-height: 1;
  color: @hc-color-primary;
  background: rgb(255 255 255 / 85%);
  // background-color: #fff;
  // box-shadow: -4rpx 0 2rpx 0 rgba(0, 0, 0, 0.06);
  z-index: 1;
  opacity: 0.85;
  overflow: hidden;

  image {
    width: 40rpx;
    height: 40rpx;
    vertical-align: top;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    &.rotate-img {
      transform: translate(-50%, -50%) rotate(-180deg);
    }
  }

  &.active {
    display: block;
  }
}

.unit-fold-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.m-tips {
  position: fixed;
  right: 0;
  bottom: 120rpx;
  background-color: #f5fefe;
  border-top-left-radius: 100rpx;
  border-bottom-left-radius: 100rpx;
  display: flex;
  color: @hc-color-primary;
  align-items: center;
  border: 2rpx solid @hc-color-primary;
  border-right: 0;
  padding: 0 20rpx;
  line-height: 60rpx;
  height: 60rpx;
  z-index: 9;

  .tips-icon {
    font-size: 34rpx-lg;
  }

  .tips-text {
    font-size: 30rpx;
    margin-left: 10rpx;
  }
}

//挂号确认弹窗
.m-reg-docinfo-popup {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  z-index: -9;
  visibility: hidden;

  .reg-docinfo-popup-mask {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0);
    transition: background 0.3s;
    z-index: 0;
  }

  .reg-docinfo-popup {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    background-color: #fff;
    transition: transform 0.3s 0.1s;
    transform: translateY(100%);
    border-radius: 32rpx 32rpx 0 0;
  }

  &.active {
    z-index: 999;
    visibility: visible;

    .reg-docinfo-popup-mask {
      background-color: rgba(0, 0, 0, 0.6);
    }

    .reg-docinfo-popup {
      transform: translateY(0%);
    }
  }

  .popup-form-box {
    max-height: 1000rpx;
    overflow-y: auto;
  }

  .popup-form {
    padding-bottom: 320rpx;
  }

  .popup-item {
    // border-top: 2rpx solid @hc-color-border;
    // margin-left: 30rpx;
    margin: 0 32rpx;

    &:first-child {
      border-top: 0;
    }
  }

  .info {
    display: flex;
    align-items: center;
    // padding: 45rpx 30rpx 45rpx 0;
    padding: 0rpx 32rpx 24rpx 0rpx;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  }

  .info-hd {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 110rpx;
    height: 154rpx;
    border-radius: 8rpx;
    overflow: hidden;

    image {
      vertical-align: top;
      border-radius: 8rpx;
    }
  }

  .info-bd {
    flex: 1;
  }

  .info-bd-item {
    display: flex;
    font-size: 30rpx;
    margin-bottom: 8rpx;
    margin-left: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-bd-label {
    color: @hc-color-title;
  }

  .info-bd-text {
    flex: 1;
  }

  .opt {
    padding: 30rpx 0;
  }

  .opt-tit {
    font-size: 34rpx;
    .textBreak();
  }

  .opt-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 30rpx;
    margin-left: -24rpx;
    margin-bottom: -30rpx;
  }

  .opt-list-item {
    margin-left: 32rpx;
    margin-bottom: 24rpx;
  }

  .opt-list-item-add {
    width: 200rpx;
    height: 110rpx;
    background: #f6f7f9;
    border-radius: 16rpx;
    text-align: center;
    font-size: 48rpx;
    color: rgba(0, 0, 0, 0.5);
    line-height: 100rpx;
  }

  .close {}

  .popup-bottom-btn {
    position: absolute;
    left: 0;
    bottom: 48rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    text-align: center;
    box-sizing: border-box;

    .popup-submit {
      line-height: 100rpx;
      color: @hc-color-white;
      font-size: 34rpx;
      font-weight: 600;
      background-color: @hc-color-primary;
      width: 100%;
      border-radius: 24rpx;
      margin-bottom: 24rpx;

      &.disabled {
        background-color: #dddddd;
      }
    }

    .popup-cancel {
      line-height: 100rpx;
      color: @hc-color-title;
      font-size: 34rpx;
      font-weight: 600;
      background: rgba(0, 0, 0, 0.04);
      width: 100%;
      border-radius: 24rpx;
    }
  }

  .popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 30rpx 30rpx;
    font-size: 36rpx;
    color: @hc-color-border;
    line-height: 1;
    z-index: 9;

    image {
      width: 36rpx;
      height: 36rpx;
    }
  }

  .unit-label {
    width: 200rpx;
    height: 110rpx;
    line-height: 110rpx;
    // border: 2rpx solid @hc-color-primary;
    background: #f8f9fb;
    color: @hc-color-title;
    border-radius: 24rpx;
    text-align: center;
    font-size: 28rpx;

    &.active {
      // border: 2rpx solid @hc-color-primary;
      background-color: @hc-color-primary;
      color: #fff;
      font-weight: 600;
    }
  }

  .unit-label-text {
    display: inline-block;
    vertical-align: top;
    width: 2em;
    white-space: nowrap;
    text-overflow: clip;
    overflow: hidden;
  }

  .unit-select {
    padding: 0 30rpx;
    line-height: 70rpx;
    border: 2rpx solid @hc-color-border;
    color: @hc-color-text;
    border-radius: 70rpx;
    text-align: center;
    font-size: 30rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.active {
      border: 2rpx solid @hc-color-primary;
      background-color: @hc-color-primary;
      color: #fff;
    }
  }

  .m-top-tips {
    display: none;
    position: fixed;
    right: 0;
    left: 0;
    padding: 40rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 6rpx 14rpx 0 rgba(0, 0, 0, 0.08);
    border-bottom: 2rpx solid @hc-color-border;
    z-index: 1000;
    color: @hc-color-warn;
    align-items: center;

    &.active {
      display: flex;
    }

    .tips-icon {
      line-height: 1;
      font-size: 60rpx;
    }

    .tips-text {
      flex: 1;
      font-size: 30rpx;
      margin-left: 30rpx;
    }
  }
}