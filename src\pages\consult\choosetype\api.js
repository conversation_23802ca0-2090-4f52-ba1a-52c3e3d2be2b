import { post } from '@/utils/request';

export const getCardList = (param) => post('/api/user/patientslist', param);

// 判断同一个PID下面是否存在多人注册
export const judgeUserExsist = (param) => post('/api/chat/judgeUserExsist', param);

// 查询当前患者是否已存在聊天会话（同病人永远有且只有一条有效会话记录）
export const queryPatChat = (param) => post('/api/chat/queryPatChat', param);

// 创建咨询
export const createChat = (param) => post('/api/chat/creatChat', param);



