@import "../../../../resources/style/mixins";

page {
}

.p-page {
}

.m-list {
  padding: 24rpx;
  overflow: hidden;
  .list-item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0px 1rpx 2rpx 0px rgba(0, 0, 0, 0.08);

    &.list-item-S,
    &.list-item-L {
    }
    &.list-item-F {
    }
    &.list-item-C {
    }
  }
  .item-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    overflow: hidden;
    border-radius: 50%;
    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-status {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }
  .item-main {
    flex: 1;
  }
  .main-tit {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
  }
  .main-txt {
    font-size: 28rpx;
    color: @hc-color-text;
    margin-top: 8rpx;
  }
  .item-extra {
    margin-top: 24rpx;
  }
  .extra-tit {
    margin-top: 8rpx;
    color: @hc-color-text;
    .fee {
      font-weight: 600;
      color: @hc-color-assist;
    }
  }
  .extra-txt {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .list-item {
    .item-icon {
      background-color: rgba(63, 150, 157, 1);
    }
    &.list-item-success,
    &.list-item-lock {
      .item-icon {
        background-color: rgba(63, 150, 157, 1);
      }
    }
    &.list-item-fail {
      .item-icon {
        background-color: rgba(197, 93, 93, 1);
      }
    }
    &.list-item-cancel {
      .item-icon {
        background-color: @hc-color-text;
      }
    }
    &.list-item-abnormal {
      .item-icon {
        background-color: rgba(231, 170, 53, 1);
      }
    }
  }
  .unit-label {
    margin-left: 16rpx;
    display: inline-block;
    font-size: 22rpx;
    padding: 4rpx 8rpx 4rpx 8rpx;
    color: #fff;
    font-weight: bold;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 4rpx;
  }
  .isComplete {
    color: #2D666F;
    background: rgba(63, 150, 157, 0.15);
  }
  .unComplete{
    color: #2A2A2A;
    background: rgba(0, 0, 0, 0.07);
  }
}
