@import "../../../resources/style/mixins.less";

page {
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.container {
  height: 100vh;
  min-height: 100vh;
  // background-color: #fff;
}

.form-container {
  background-color: #fff;
  border-radius: 24rpx;
  margin: 0 32rpx 24rpx;

  input {
    height: 96rpx;
    line-height: 96rpx;
    color: rgba(0, 0, 0, 0.7);
    font-size: 32rpx;
    padding-left: 32rpx;
    border-radius: 16rpx;
    &:focus {
      border: 1px solid #3eceb6;
    }
  }
}

.patInfo-card {
  position: relative;
  .patInfo-card-top {
    padding: 40rpx 32rpx;
  }
  .patInfo-card-info {
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 20rpx;
    .info-name {
      line-height: 48rpx;
      padding-right: 30rpx;
      font-size: 64rpx;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
    }
    .info-other {
      color: #2d2d2d;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 42rpx;
      padding-right: 30rpx;
    }
  }

  .patInfo-card-other {
    color: rgba(0, 0, 0, 0.7);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 42rpx;
    // padding-bottom: 10rpx;
  }
}

.patInfo-list {
  // background: #fff;
  .patInfo-listitem {
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    position: relative;

    &.patInfo-listitem_none {
      display: none;
    }

    &::after {
      content: " ";
      position: absolute;
      left: 30rpx;
      right: 30rpx;
      bottom: 0;
      height: 1rpx;
      border-top: 2rpx solid @hc-color-border;
    }

    // &:first-child:before {
    //   display: none;
    // }

    &.no-after:after {
      display: none;
    }

    .listitem-head {
      display: flex;
      align-items: center;
      padding-bottom: 24rpx;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 28rpx;
        color: #353535;
        font-weight: 400;
        line-height: 1;
        padding-right: 12rpx;
        position: relative;

        &.require {
          position: relative;
          &:after {
            content: "*";
            color: #f76260;
            font-size: 30rpx;
          }
        }

        &.list-title_select:before {
          content: "切换证件类型";
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          font-size: 32rpx;
          color: @hc-color-primary;
          // border-bottom: 10rpx solid @hc-color-title;
          // border-right: 10rpx solid @hc-color-title;
          // border-top: 10rpx solid transparent;
          // border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body {
      flex: 1;
      // padding-left: 30rpx;
      position: relative;
      background: #f6f7f9;
      border-radius: 16rpx;
      .picker {
        display: flex;
        align-items: center;
        .picker-info {
          flex: 1;
          font-size: 30rpx;
          color: #353535;
          font-weight: 400;
          line-height: 1;
        }
        .item-arrow {
          width: 17rpx;
          height: 17rpx;
          border-right: 5rpx solid #c7c7cc;
          border-bottom: 5rpx solid #c7c7cc;
          -webkit-transform: translateX(-32rpx) rotate(-45deg);
          transform: translateX(-32rpx) rotate(-45deg);
        }
      }

      .textBreak();
    }
    .patInfo-add {
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #3eceb6;
      line-height: 42rpx;
      text-align: center;
    }
  }

  .listitem_accest {
    color: red;
  }

  .listitem_accest .listitem-body:before {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.patInfo-part {
  padding: 20rpx 30rpx;
  // background-color: #f5f5f5;

  .list-title {
    &.require {
      position: relative;
      &:after {
        content: "*";
        color: #f76260;
        font-size: 28rpx;
      }
    }
  }
}

.patInfo-tips {
  padding: 20rpx 40rpx;
  color: #989898;
  background-color: #f5f5f5;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.patInfo-btn {
  margin: 40rpx;
}
.binduser-btn_line {
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}

.m-delete-member {
  // justify-content: center;
  align-items: flex-end;
  // border-bottom: 2rpx solid @hc-color-border;
  color: red;
}

.binduser-radio {
  display: inline-block;
  width: 150rpx;
  margin-left: 10rpx;
  &:first-child {
    margin-left: 0;
  }
}
.binduser-radio_object {
  transform-origin: 0 30%;
  transform: scale(0.7);
}
.binduser-radio_text {
  font-size: 30rpx;
}
button::after {
  border: none;
}
.mb-32 {
  margin-bottom: 32rpx;
}
.picker-wrap {
  height: 96rpx;
  line-height: 96rpx;
  color: rgba(0, 0, 0, 0.7);
  font-size: 32rpx;
  padding-left: 32rpx;
  border-radius: 16rpx;
  .placeholder-text {
    color: rgba(0, 0, 0, 0.5) !important;
    font-size: 32rpx !important;
    font-weight: 400 !important;
  }
}
