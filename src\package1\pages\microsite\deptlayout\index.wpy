<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class Layout extends wepy.page {
    config = {
      navigationBarTitleText: '科室分布',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.getBuildList();
    }

    data = {
      emptyConfig: {
        show: true,
      },
      tabIndex: 0,
      buildList: [],
      floorList: [],
    };

    methods = {
      changeProp(e){
        const { prop } = e.currentTarget.dataset;
        this[prop] = !this[prop];
      },
      changeBuild(buildId, index){
        this.tabIndex = index;
        this.getBuildInfo(buildId);
      },
    };
    /**
     * 获取楼群列表
     */
    async getBuildList(){
      const { data = {}, code } = await Api.getBuildList();
      if(code == 0){
        const { buildList = [] } = data;
        if(buildList.length > 0){
          this.buildList = buildList;
          this.emptyConfig.show = false;
          this.$apply();
          this.getBuildInfo(buildList[0].buildId);
        }
      }
    }

    /**
     * 获取楼栋科室信息
     */
    async getBuildInfo(buildId){
      const { data = {}, code } = await Api.getBuildInfo({ buildId });
      if(code == 0){
        const { floorList = [] } = data;
        this.floorList = floorList;
        this.$apply();
      }
    }
  }
</script>
