@import "../../../resources/style/mixins";

page {}

.p-page {
  padding-bottom: 30rpx;
  .page-top{
    margin-top: 110rpx;
    padding: 48rpx 24rpx;
    .status{
      display: flex;
      padding: 24rpx 24rpx 8rpx;
      align-items: center;
      color: #3F969D;
      font-weight: 600;
      font-size: 48rpx;
      image{
        margin-right: 16rpx;
        width: 64rpx;
        height: 64rpx;
      }
    }
    .tips{
      color: rgba(0, 0, 0, 0.50);
      font-size: 28rpx;
    }
  }
}

.m-code {
  display: none;
  background-color: #fff;
  padding: 30rpx;

  &.active {
    display: block;
  }

  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }

  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;

    p {
      margin: 20rpx 0;
    }
  }

  .code-img {
    margin-top: 20rpx;

    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry {
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;

  .retry-btn {
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color: @hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border: 2rpx solid @hc-color-primary;
  }
}


.m-list {
  margin: 0 24rpx 0 24rpx;
  padding: 32rpx 0;
  background-color: #fff;

  .list{
    margin: 0 30rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
  }

  &:last-child{
    padding-top: 0;
    .list{
      border: none;
    }
  }

  .list-title{
    padding: 0 0 24rpx 32rpx;
    color: rgba(0, 0, 0, 0.90);
    font-size: 28rpx;
    font-weight: 600;
  }

  .list-tit {
    padding: 0 30rpx;
    color: rgba(0, 0, 0, 0.40);
    font-size: 32rpx;
  }

  .m-list-retry {
    margin-bottom: 24rpx;
    display: flex;
    justify-content: space-between;
    padding-right: 20rpx;
  }

  .list-retry-time {
    color: rgba(0, 0, 0, 0.4);
    font-size: 32rpx;
  }

  .list-item-img {
    padding: 17rpx 30rpx;
  }

  .list-item {
    padding-bottom: 24rpx;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    
    &:last-child{
      padding: 0;
    }
  }
  .content-list{
    flex-direction: column;
    align-items: flex-start;
    .item-label{
      margin-bottom: 24rpx;
    }
  }

  .item-label {
    margin-right: 96rpx;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.4);
    min-width: 5em;
  }

  .item-value {
    color: @hc-color-title;
    font-size: 32rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow: hidden;
  }

  .unit-price {
    font-size: 48rpx;
  }

  .m-upload-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 30rpx;

    .m-upload-item {
      position: relative;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      margin: 0 24rpx 24rpx 0rpx;
      width: 190rpx;
      height: 190rpx;
      background-color: #f5f5f5;
      overflow: hidden;
      text-align: center;
      border-radius: 8rpx;

    }
  }
}


.afterscan-operbtnbox {
  margin: 42rpx 40rpx;

  .binduser-btn_line {
    background: @hc-color-primary;
    color: #fff;
    border-radius: 10rpx;
    text-align: center;
    font-size: 36rpx;
    //padding: 22rpx 0;
  }
}