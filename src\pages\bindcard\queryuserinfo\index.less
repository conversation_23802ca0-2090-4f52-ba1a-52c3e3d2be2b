@import "../../../resources/style/mixins";

page {
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}
.top-msg{
  padding: 32rpx 24rpx;
  text-align: center;
  font-size: 24rpx;
  color: #BE8014;
  background: #FFFAF1;
}
.binduser-btn_line {
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #fff;
  border-radius: 76rpx;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  height: 100rpx;
  line-height: 100rpx;
}
.agreement-box{
  text-align: center;
  label{
    vertical-align: bottom;
  }
  .content{
    color: #259eff;
  }
}
.bind-card-container {
  border-radius: 24rpx;
}

.bindcard-list {
  margin-bottom: 16rpx;
  background: #fff;
  .bindcard-listitem {
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    position: relative;
    &.tel-listitem{
      display: block;
    }
    .bindcard-listitem-box{
      display: flex;
      align-items: center;
    }

    &.bindcard-listitem_none {
      display: none;
    }

    &::after {
      content: " ";
      position: absolute;
      left: 30rpx;
      right: 30rpx;
      bottom: 0;
      height: 1rpx;
      border-top: 2rpx solid @hc-color-border;
    }

    // &:first-child:after {
    //   display: none;
    // }

    &.no-after:after {
      display: none;
    }

    .listitem-head {
      display: flex;
      align-items: center;
      width: 200rpx;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 32rpx;
        color: @hc-color-title;
        padding-right: 12rpx;
        position: relative;
        line-height: 1;
        height: 100%;

        &.require {
          position: relative;

          &::before {
            content: "*";
            color: #f76361;
            font-size: 32rpx;
          }
        }

        &.list-title_select:before {
          content: "切换证件类型";
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          font-size: 32rpx;
          color: @hc-color-primary;

          // border-bottom: 10rpx solid @hc-color-title;
          // border-right: 10rpx solid @hc-color-title;
          // border-top: 10rpx solid transparent;
          // border-left: 10rpx solid transparent;
        }
      }
    }

    .listitem-body {
      flex: 1;
      // padding-left: 30rpx;
      position: relative;
      .textBreak();

      .m-verifycode-btn {
        position: absolute;
        top: 0;
        right: 10rpx;
        color: #3eceb6;
        height: 1.4rem;
        line-height: 1.4rem;

        &:active {
          color: #666;
        }
      }
      .area-box{
        height: 96rpx;
        line-height: 96rpx;
        font-size: 32rpx;
        .placeholder-text{
          color: rgba(0, 0, 0, 0.40);
        }
      }
    }
  }

  .listitem_accest {
    color: red;
  }

  .listitem_accest .listitem-body:before {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.m-content {
  height: 96rpx;
  line-height: 96rpx;
  color: rgba(0, 0, 0, 0.9);
  font-size: 32rpx;

  &:focus {
    border: 1px solid #3eceb6;
  }
}

.m-mt-20 {
  margin-top: 20rpx;
}

.o-error {
  color: #ff613b;
}

.o-disabled {
  background: #ddd;
}

.afterscan-cardviewbox {
  background: #fff;
  padding: 20rpx 0 44rpx;
}

.afterscan-cardview {
  margin: auto;
  width: 390rpx;
  border-radius: 10rpx;
  border: 2rpx solid @hc-color-primary;
  overflow: hidden;

  .cardview-imgbox {
    width: 370rpx;
    height: 214rpx;
    overflow: hidden;
    margin: 9rpx auto 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .cardview-img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .afterscan-reupload {
    background: @hc-color-primary;
    color: #fff;
    text-align: center;
    height: 52rpx;
    line-height: 52rpx;
    font-size: 26rpx;
  }
}

.afterscan-opertip {
  display: flex;

  .opertip-msg {
    flex: 1;
    color: #ffa14e;
    padding: 24rpx 0 36rpx 30rpx;
    font-size: 28rpx;
  }

  .opertip-imgbox {
    display: flex;
    align-items: flex-end;

    .opertip-img {
      width: 80rpx;
      height: 112rpx;
      margin: 0 26rpx;
    }
  }
}

.afterscan-operbtnbox {
  padding: 64rpx 24rpx;
  // position: absolute;
  width: 100%;
  box-sizing: border-box;
  bottom: 0;
}

.binduser-radio {
  margin-left: 110rpx;

  &:first-child {
    margin-left: 0;
  }
}

.binduser-radio_object {
  transform-origin: 0 30%;
  transform: scale(0.7);
}

.binduser-radio_text {
  font-size: 30rpx;
}

.listitem-footer {
  color: #2D666F;
  position: absolute;
  font-size: 32rpx;
  right: 24rpx;
  z-index: 2;

  .verify-code {
    line-height: 1.4rem;
    height: 1.4rem;
  }

  .second-code {
    color: #2D666F;
  }
}

.listitem-footer.left-time {
  // background-color: #fff;
  color: #8590a6;
  // border: 1px #8590a6 solid;
  // width: 3em;
  text-align: center;
}

// 显示亲人列表

.m-card {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 4rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;

  &:after {
    content: " ";
    position: absolute;
    right: 30rpx;
    top: 50%;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translate(-8rpx, -50%) rotate(-45deg);
  }

  .card-info {
    padding: 39rpx 0 36rpx;
  }

  .info-main {
    display: flex;
    align-items: flex-start;
    padding-left: 2rpx;
  }

  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .name {
    font-size: 37rpx;
    font-weight: bold;
    //height: 37rpx;
  }

  .status {
    font-size: 20rpx;
    height: 32rpx;
    line-height: 32rpx;

    margin-left: 15rpx;

    border-radius: 4rpx;
    padding: 0 7rpx;
  }

  .status_1 {
    color: #ff6fa1;
    border: 2rpx solid #ff6fa1;
  }

  .status_2 {
    color: #259eff;
    border: 2rpx solid #259eff;
  }

  .info-extra {
    margin-top: 20rpx;
    color: @hc-color-text;
    font-size: 28rpx;
  }
}

.m-tips {
  // margin-left: 30rpx;
  padding-bottom: 16rpx;
  font-size: 26rpx;
  // text-align: center;
  // border-top: 2rpx solid @hc-color-border;
}

.m-color-fail {
  color: rgba(0, 0, 0, 0.40);
  // text-align: right;
  // text-decoration: underline;
}

// 其它证件类型
page {
  height: 100%;
}

.other-types {
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  color: @hc-color-primary;
  font-size: 28rpx;
  position: absolute;
  left: 0;
  bottom: 100rpx;
}

button::after {
  border: none;
}