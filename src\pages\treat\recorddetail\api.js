import { post } from '@/utils/request';
import { REQUEST_QUERY } from "@/config/constant";

export const orderDetail = (param) => post('/api/oppay/orderdetail', param);

export const manualNotify = (param) => post('/api/order/manualnotify', param);

export const guideinfo = (param) => post('/api/oppay/guideinfo', param);
export const orderListPay = (param) =>
  post(
  `/api/customize/getPayInfo?_route=h${REQUEST_QUERY.platformId}
  &cardNo=${param.cardNo}
  &patName=${param.patName}
  &parterId=${param.parterId}
  &passwd=${param.passwd}`,
  param
);