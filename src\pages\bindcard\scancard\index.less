@import "../../../resources/style/mixins";

page {
  width: 100%;
  overflow-x: hidden;
  background: #f6f7f9;
}
.scancard-tips {
  padding: 30rpx 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: end;
  .scancard-tips-content {
    padding-bottom: 20rpx;
    color: #ffa14e;
    font-size: 24rpx;
    word-break: break-all;
  }
  .scan-tips-img {
    width: 220rpx;
    height: 120rpx;
  }
}
.scancard-block {
  padding: 30rpx 0 30rpx;
  background-color: #fff;
  border-radius: 32rpx;
  margin: 0 32rpx;
  text-align: center;
}
.scancard-imgbox {
  position: relative;
  width: 560rpx;
  height: 346rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border-radius: 10rpx;
  .scan-img {
    max-width: 560rpx;
    border-radius: 10rpx;
  }
  .re-upload {
    position: absolute;
    bottom: 0;
    height: 68rpx;
    background: #3eceb6;
    border-radius: 0px 0px 10rpx 10rpx;
    font-size: 24rpx;
    line-height: 68rpx;
    text-align: center;
    color: #ffffff;
  }
}

.scan-require {
  width: 560rpx;
  line-height: 1.7;
  text-align: left;
  margin: 32rpx 32rpx 0;
  color: rgba(0, 0, 0, 0.7);
  font-size: 26rpx;
  .scan-require-title {
    color: #353535;
    font-size: 28rpx;
  }
  .scan-require-item {
    position: relative;
    color: #888888;
    font-size: 28rpx;
  }
}

.scancard-btnbox {
  margin: 86rpx 40rpx 0;
}
.binduser-btn_line {
  background: @hc-color-primary;
  color: #fff;
  text-align: center;
  font-size: 34rpx;
  padding: 8rpx;
  border-radius: 16rpx;
  & + .binduser-btn_line {
    margin-top: 50rpx;
  }
}
.nocard {
  background: #fff;
  color: #3eceb6;
  font-size: 34rpx;
  padding: 8rpx;
  border-radius: 16rpx;
}
// 其它证件类型
.other-types {
  text-align: center;
  padding-top: 32rpx;
  color: #3986ff;
  font-size: 28rpx;
  line-height: 42rpx;
}

button::after {
  border: none;
}
