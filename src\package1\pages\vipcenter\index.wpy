<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from 'wepy';
import * as Api from './api';
import * as Utils from '@/utils/utils';
import { PRIMARY_COLOR } from '@/config/constant';

export default class GuideIndex extends wepy.page {
  config = {
    navigationBarTitleText: 'vip中心',
    navigationBarBackgroundColor: '#fff',
  };
  components = {};

  data = {
    patientInfo: {},
    tips: {},
  };

  onLoad() {}
  onShow() {
    this.getPatientsList();
    this.getNoteProfile();
  }

  methods = {
    onJoin() {
      this.saveVipRecord();
    },
  };
  getPatientsList = async () => {
    const { code, data = {} } = await Api.getPatientsList({ isLogin: '1' });
    const { cardList = [] } = data;
    const patientInfo = cardList.length ? cardList[0] : {};
    this.patientInfo = patientInfo;
    this.$apply();
    if (patientInfo.patCardNo) {
      return;
    }
    wx.showModal({
      title: '提示',
      content: '您还尚未绑定任何就诊人，绑定后可继续操作。',
      showCancel: true,
      confirmText: '立即绑定',
      confirmColor: PRIMARY_COLOR,
      success: (res) => {
        console.log(res, 'res');
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/bindcard/queryuserinfo/index?qryType=1',
          });
          return;
        }
        wx.navigateBack();
      },
    });
  };
  saveVipRecord = async () => {
    const { patientId, patientName, patHisNo, patCardNo } = this.patientInfo;
    const { data, code } = await Api.saveVipRecord({
      patientId,
      patientName,
      patHisNo,
      patCardNo,
    });
    if (code === 0) {
      wepy.showModal({
        title: '温馨提示',
        content: this.tips,
        showCancel: false,
        confirmText: '确定',
      });
    }
  };
  //获取提示语
  async getNoteProfile() {
    const {
      code,
      data = {},
      msg,
    } = await Api.getNoteProfile({
      profileKey: 'getAlertNoteProfile_vipNotice',
    });
    if (code == 0) {
      this.tips = data.profileValue || '';
      this.$apply();
    }
  }
}
</script>
