<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  import { INPATIENT_HAS_CARD } from '@/config/constant';

  import * as Api from '../../api';

  export default class Add extends wepy.component {
    props = {};

    data = {
      // 门诊就诊人数据
      patientData: {},
      // 手动输入数据
      inputData: {
        patientName: '',
        admissionNum: '',
      },
      isInputMode: true,
      inpatientHasCard: INPATIENT_HAS_CARD,
    };

    onLoad(options) {
      this.getPatientList();
    }

    methods = {
      bindChangeUser(item = {}){
        this.changeUser(item);
      },
      bindInputName(e){
        const { value = '' } = e.detail;
        this.inputData.patientName = value;
        this.clearChecked();
        this.isInputMode = true;
      },
      bindInputNo(e){
        const { value = '' } = e.detail;
        this.inputData.admissionNum = value;
        this.clearChecked();
        this.isInputMode = true;
      },
      async bindAddUser(){
        const that = this;
        if (wepy.$instance.globalData.isSDKVersion){
          const app = this.$root.$parent;
          let templateIds =  []; 
          app.getTemplateId().then((templateId) => {     
            templateIds.push(templateId);
            if(templateId){
              wx.requestSubscribeMessage({
                tmplIds: templateIds,
                success(res) {
                  if (res[templateId] == 'accept'){
                    const subscribeList = [{
                      'status': res[templateId],
                      'templateId': templateId, 
                    }];
                    const param = {
                      subscribeList: JSON.stringify(subscribeList),
                    }
                    app.save(param);
                  } 
                },
                fail(res) {
                  console.log('fail', res);
                },
                complete(){
                  that.addUser();
                }  
              });    
            } else {
              that.addUser();
            }
          });
        } else {
          that.addUser();
        }
      }
    }

    /**
     * 清除就诊卡选中
     */
    clearChecked() {
      const { patientData: { cardList = [] } = {} } = this;
      for (let i in cardList) {
        cardList[i].checked = false;
      }
    }

    /**
     * 切换就诊人选中
     * @param item
     */
    changeUser(item = {}) {
      const { patientData: { cardList = [] } = {} } = this;
      for (let i in cardList) {
        cardList[i].checked = !cardList[i].checked && cardList[i].patientId === item.patientId;
      }
      this.patientData = {
        ...this.patientData,
        cardList,
      };
      this.inputData = {
        patientName: '',
        admissionNum: '',
      };
      this.isInputMode = false;
      this.$apply();
    }

    /**
     * 获取就诊人列表
     */
    async getPatientList() {
      const { code, data = {} } = await Api.getPatientList();
      this.patientData = data;
      this.$apply();
    }

    /**
     * 绑定住院人
     */
    async addUser() {
      const { inpatientHasCard, isInputMode } = this;
      if (inpatientHasCard) {
        let patientItem = {};
        //有卡住院模式
        if (isInputMode) {
          // 手动输入住院号绑卡
          const { patientName = '', admissionNum = '' } = this.inputData;
          if (!patientName || !admissionNum) {
            wepy.showModal({
              title: '提示',
              content: '请选择就诊人添加或者通过住院号快速添加',
              showCancel: false,
            });
            return;
          }
          patientItem = { patientName, admissionNum };
        } else {
          // 通过已经绑定的就诊卡绑卡
          const { patientData: { cardList = [] } = {} } = this;
          let checkedItem = '';
          for (let i in cardList) {
            if (cardList[i].checked) {
              checkedItem = cardList[i];
              break;
            }
          }
          if (!checkedItem) {
            wepy.showModal({
              title: '提示',
              content: '请选择就诊人添加或者通过住院号快速添加',
              showCancel: false,
            });
            return;
          }
          const { patientName = '', patCardNo = '' } = checkedItem;
          patientItem = { patientName, patCardNo };
        }
        const { code, data = {} } = await Api.addUser(patientItem);
        if (code !== 0) {
          return;
        }
        wepy.navigateBack();
      } else {
        // 无卡住院模式
      }
    }
  }
</script>
