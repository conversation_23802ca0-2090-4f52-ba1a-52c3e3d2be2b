<view class="p-page" @tap="hidden">
  <view>
    <!-- <view class='header' wx:if="{{!isEnd}}">
        <view class="tip-item">
          <view class="head-tip">进行中</view>
          <text @tap='openModal'>结束咨询</text>
        </view>
      </view> -->
      <!-- scroll-with-animation="true"  -->
    <!-- <view class="tips" wx:if="{{showTips}}">
      <text>为方便医生了解病情，请尽快完善您的健康档案</text>
      <view class="tips-btn" @tap="toHealthProfile">去完善</view>
    </view> -->
    <scroll-view id="scroll-content" class="{{isInputFocus && list.length <= 3 ? 'scroll-bottom' : ''}}" scroll-into-view="{{viewId ? viewId : ''}}" enable-flex scroll-y
      style="{{scrollStyle}}"
      @tap='hidePlus' bindscrolltoupper="scrollTop" bindscroll="scroll">
      <view class="content">
        <view class="m-loading" wx:if="{{isLoading}}">
          <image class="m-loading-image" mode="widthFix"
                  src="REPLACE_IMG_DOMAIN/media/images/common/loading_img.gif"></image>
          加载中...
        </view>
        <block wx:for="{{list}}" wx:key="{{index}}">
          <block wx:if="{{item.type == 'SYSTEM' && item.userIsShow == 1}}">
            <view wx:if="{{item.time}}" class="date">{{item.time}}</view>
            <view id="id-{{item.id}}" class='msg'>
              <wxparser rich-text="{{item.content}}" />
            </view>
          </block>
          <block wx:if="{{item.isRevoke}}">
            <view class="invoke-block">{{item.messageUserType == 0 ? item.sendUserName : '你'}}撤回了一条信息</view>
          </block>
          <block wx:else>
            <block wx:if="{{item.time}}">
                <view class="date">{{item.time}}</view>
            </block>
            <view id="id-{{item.id}}" wx:if="{{item.messageUserType == 0}}" class='left'>
              <view class='img'>
                <image wx:if="{{item.headImg}}" src="{{item.headImg}}" />
                <image wx:if="{{!item.headImg}}" mode="widthFix"
                  src="REPLACE_IMG_DOMAIN/his-miniapp/p242/defaultHeadImg.png"></image>
              </view>
              <view class="content-block">
                <view class="name">{{item.sendUserName}}</view>
                <block wx:if="{{item.type == 1}}">
                  <view class='text'>
                    <i />
                    <view class="content-box">
                       <!-- <wxparser rich-text="{{item.content}}" ></wxparser> -->
                       <rich-text nodes="{{item.content}}"></rich-text>
                    </view>
                  </view>
                </block>
                <block wx:elif="{{item.type == 3}}">
                  <view @tap="previewImg" data-preurl="{{item.content}}" class="text image-block" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image mode="widthFix" src='{{item.content}}' />
                    </view>
                  </view>
                </block>
                <block wx:elif="{{item.type == 5}}">
                  <view class="text expression" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image class="expression-image" src='{{item.content}}' />
                    </view>
                  </view>
                </block>
                <block wx:elif="{{item.type == 6}}">
                  <!-- 视频聊天 -->
                  <view @tap="queryVideoStatus" class="text" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image class="video-image" src='https://zxxymp.cn/hospital/his-miniapp/p242/consult/video.png' />
                      <view>发起了视频问诊</view>
                    </view>
                  </view>
                </block>
                <!-- <block wx:elif="{{item.type == 7}}">
                  <view @tap="queryVideoStatus" class="text" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image class="video-image" src='https://zxxymp.cn/hospital/his-miniapp/p242/consult/video.png' />
                      <view>发起了视频问诊</view>
                    </view>
                  </view>
                </block> -->
              </view>
            </view>
            <view id="id-{{item.id}}" wx:elif="{{item.messageUserType == 1}}" class='right'>
                <!-- @tap="previewImg" -->
                <!-- <image src='{{item.headImg}}' /> -->
              <view class="tip no {{chatWxs.isRevoke(item.createTime) ? '' : 'nocopy'}}"
                wx:if="{{chatWxs.isShow(item.id, showId)}}">
                <i />
                <!-- <text @tap.stop="delOrRe({{item}}, 'del')">删除</text> -->
                <text @tap.stop="delOrRe({{item}}, 'revoke')">撤销</text>
              </view>
              <view class="chat-content" @tap.stop="longtap({{item.id}})">
                <block wx:if="{{item.type == 1}}">
                  <view class='text'>
                    <i />
                    <view class="content-box">
                      <view>
                        <view>{{item.content}}</view>
                        <!-- <view>{{chatWxs.getObj(item.content).itemNames}}</view> -->
                      </view>
                    </view>
                  </view>
                </block>
                <block wx:elif="{{item.type == 3}}">
                  <view @tap="previewImg" data-preurl="{{item.content}}" class="text image-block" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image mode="widthFix" src='{{item.content}}' />
                    </view>
                  </view>
                </block>
                <block wx:elif="{{item.type == 5}}">
                  <view class="text expression" wx:if="{{item.content}}">
                    <i />
                    <view class="content-box">
                      <image class="expression-image" src='{{item.content}}' />
                    </view>
                  </view>
                </block>
                <view wx:else class='text' @longpress="longtap({{item.id}})">
                  <i /><text>{{item.content}}</text>
                  <view class="tip {{chatWxs.isRevoke(item.createTime) ? '' : 'no'}}"
                    wx:if="{{chatWxs.isShow(item.id, showId)}}">
                    <i />
                    <text @tap="copy({{item.content}})">复制</text>
                    <!-- <text @tap="delOrRe({{item.id}}, 'del')">删除</text> -->
                    <text @tap="delOrRe({{item.id}}, 'revoke')">撤销</text>
                  </view>
                </view>
                <view class='img'>
                  <open-data type="userAvatarUrl" />
                </view>
              </view>
            </view>
          </block>
        </block>
        <view class="invoke-block" wx:if="{{isEnd}}">本次咨询已结束，感谢您的使用。</view>
      </view>
    </scroll-view>

  </view>
  <view class="operation-box operation-btn-area" wx:if="{{isEnd}}">
    <button class="btn" @tap="toRegister">再次咨询</button>
  </view>
  <view class='operation-box' wx:if="{{!isEnd}}">
    <view class="m-robot-message-list" wx:if="{{robotMessageList.length > 0 && isInputFocus}}">
      <block wx:for="{{robotMessageList}}" wx:key="{{index}}">
        <wxparser class="m-robot-message-item" rich-text="{{item.lightContent}}" @tap="sendRobotMessage({{item}})"></wxparser>
      </block>
    </view>
    <view class="operation-content">
      <view class='top'>
        <textarea type="text" class="input" cursor-spacing="14" value='{{msgText}}' show-confirm-bar="{{false}}" confirm-type="send" @input="inputMsg"
          @confirm='sendMsg' @focus="focusTest" @blur="blurTest" auto-height fixed maxlength="500"></textarea>
        <image src='REPLACE_IMG_DOMAIN/ih-miniapp/plus.png' @tap='showPlus'></image>
        <image src='REPLACE_IMG_DOMAIN/ih-miniapp/icon-send-primary.png' @tap='sendMsg' wx:if="{{msgText}}"></image>
        <image src='REPLACE_IMG_DOMAIN/ih-miniapp/icon-send-disabled.png' wx:else></image>
      </view>
      <view class='bottom' wx:if="{{showPlus}}">
        <view class="picture" @tap='picture(1)'>
          <image src='REPLACE_IMG_DOMAIN/ih-miniapp/tp.png' />
          <view class="icon-des">图片</view>
        </view>
        <view class="picture" @tap='picture(2)'>
          <image src='REPLACE_IMG_DOMAIN/ih-miniapp/camera.png' />
          <view class="icon-des">拍照</view>
        </view>
        <view class="picture" @tap='showExpression'>
          <image src='REPLACE_IMG_DOMAIN/ih-miniapp/expression.png' />
          <view class="icon-des">表情</view>
        </view>
      </view>
    </view>
    <view wx:if="{{isShowExpression}}" class="expression-block">
      <view wx:for="{{expressionList}}" wx:key="{{index}}" class="expression-item" @tap="sendExpression({{item.url}})">
        <image src="{{item.url}}" mode="widthFix" style="transform: scale({{item.scale}})" />
      </view>
    </view>
  </view>
</view>
</view>