@import "../../../resources/style/mixins";

page {}

.p-page {
  padding-bottom: 30rpx;
}



.m-list {
  padding: 30rpx 0;
  margin-top: 20rpx;
  background-color: #fff;

  .list-tit {
    padding: 0 30rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .list {
    padding-top: 17rpx;
  }

  .list-item {
    padding: 17rpx 30rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;

  }

  .item-label {
    color: @hc-color-text;
    min-width: 5em;
  }

  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    text-align: right;
    overflow: hidden;
    margin-left: 20rpx;
  }

  .unit-price {
    font-size: 48rpx;
  }


}

.m-button {
  padding: 0px 20rpx 0px 20rpx;

  .button {
    border-radius: 10rpx;
    font-size: 4vw;
    margin-top: 50rpx;
    background: #3ECEB6;
    color: #FFFFFF;
  }
}