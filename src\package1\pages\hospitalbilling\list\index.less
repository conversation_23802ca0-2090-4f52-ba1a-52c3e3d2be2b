@import '../../../../resources/style/mixins';
page {
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}
.p-page {
  width: 100%;
  height: 100%;

  .settlement{
    padding-top: 18rpx;
  }
  .settlement-item {
    margin: 24rpx 32rpx;
    padding: 32rpx;
    background-color: #fff;
    border-radius: 24rpx;

    .status-title {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.9);
      font-weight: 600;
      font-size: 28rpx;
      margin-bottom: 32rpx;
    }

    .status-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }

    .name {
      color: rgba(0, 0, 0, 0.9);
      font-weight: 600;
      font-size: 36rpx;
      line-height: 1.5em;
    }

    .info{
      margin-top: 8rpx;
      color: rgba(0, 0, 0, 0.7);
      font-size: 28rpx;
    }
  }
}
