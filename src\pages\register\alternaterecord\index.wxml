<view class="p-page">
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{WxsUtils.convertListStatus(item.effectiveFlag === '0' ? 'S' : 'C')}}"
          @tap="bindGoDetail({{item}})"
        >
          <view class="item-status">
            <view class="item-icon">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{WxsUtils.convertListStatus(item.effectiveFlag === '0' ? 'S' : 'C')}}.png"></image>
            </view>
            <text>{{item.effectiveFlag === '0' ? '候补提交成功' : '取消候补成功'}}</text>
          </view>
          <view class="item-title">{{item.doctorName}} | {{item.deptName}}</view>
          <view class="item-extra">就诊人：{{item.patName}}</view>
          <view class="item-extra">候补就诊时间：{{item.lineTime}}</view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>