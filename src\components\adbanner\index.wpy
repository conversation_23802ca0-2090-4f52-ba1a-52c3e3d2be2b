<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import * as Api from './api';

export default class AdBanner extends wepy.component {
  props = {
    adColor: {
      type: String,
      default: '#fff',
    },
    adClass: {
      type: String,
      default: 'com-adbanner-swiper',
    },
    interval: {
      type: Number,
      default: 3000,
    },
    duration: {
      type: Number,
      default: 1000,
    },
  };
  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    adManualClose: false,
    adList: [],
    status: '1',
    interval: 3000,
    duration: 1000,
    autoplay: true,
    circular: true,
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    onCloseAd() {
      this.adManualClose = true;
      wepy.setStorageSync('adManualClose', 1);
      this.$apply();
    },
    onClickAd(item = {}) {
      const { linkType, linkUrl = '', title = '', linkRef = '' } = item;
      if (linkType == 2) {
        // H5
        const url = encodeURIComponent(linkUrl);
        wepy.navigateTo({
          url: `/pages/webview/index?weburl=${url}&title=${title}`,
        });
        return false;
      } else if (linkType == 3) {
        // 外部小程序
        if (!linkRef) {
          return;
        }
        wepy.navigateToMiniProgram({
          appId: linkRef,
          path: linkUrl || '',
          envVersion: item.envVersion || 'trial', // develop trial release
        });
        return;
      } else if (linkType == 0) {
        // 不跳转
        return;
      }
      // 小程序内链接
      wepy.navigateTo({
        url: linkUrl,
      });
    },
  };
  onLoad() {
    this.adManualClose = wepy.getStorageSync('adManualClose');
    if (wepy.getStorageSync('adManualClose')) {
      return;
    }
    this.getAdList();
  }
  async getAdList() {
    const { code, data = [] } = await Api.getAdList();
    if (code == 0) {
      const _currentPages = getCurrentPages();
      let { route = '' } = _currentPages[_currentPages.length - 1];
      // 检查检验报告配置的pageType共用
      if (route.indexOf('pages/report/examine/index' === 0)) {
        route = route.replace('pages/report/examine/index', 'pages/report/analysis/index');
      }
      // 当后台配置为/pages/usercenter/home/<USER>
      if (route.indexOf('packageA/pages/usercenter/home/<USER>' === 0)) {
        route = route.replace('packageA/pages/usercenter/home/<USER>', 'pages/usercenter/home/<USER>');
      }
      // 过滤掉不是当前页面的广告位配置
      const list = data.filter((item = {}) => {
        const { pageType = '' } = item;
        return pageType.indexOf(`/${route}`) === 0;
      });
      let stempStatus = '1';
      // 抽离出来items数组
      const adList = list.reduce((pre, item = {}) => {
        const { items = [], status = '1' } = item;
        pre.push(...items);
        stempStatus = status;
        return pre;
      }, []);
      this.circular = adList.length > 1;
      this.status = stempStatus;
      this.adList = adList;
      this.$apply();
    }
  }
}
</script>
