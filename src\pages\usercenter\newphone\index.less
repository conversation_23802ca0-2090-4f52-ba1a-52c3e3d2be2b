@import "../../../resources/style/mixins";

.warm-tip {
  font-size: 28rpx;
  color: @hc-color-primary;
  padding: 30rpx 30rpx 40rpx;
}
.m-content {
  color: #000;
}
.btn {
  margin: 72rpx 0;
  .submit-btn {
    color: @hc-color-white;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    font-size: 34rpx;
    margin: 0 24rpx;
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 76rpx;
    &.hidden-btn{
      opacity: 0.4;
    }
  }
  .o-disabled {
    background-color: #ddd;
  }
}
.new-phone {
  margin-top: 20rpx;
  .phone-listitem {
    // background-color: @hc-color-white;
    padding: 0 32rpx;
    display: flex;
    position: relative;
    height: 112rpx;
    align-items: center;
    .o-error {
      color: @hc-color-warn;
    }

    &:after {
      content: " ";
      position: absolute;
      left: 32rpx;
      right: 32rpx;
      bottom: 0;
      border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
    }
    // &:last-child:after{
    //   display: none;
    // }

    .listitem-head {
      width: 136rpx;
      display: flex;
      align-items: center;
      margin-right: 14rpx;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 32rpx;
        color: @hc-color-title;
        position: relative;
        line-height: 1;
        &::before{
          content: '*';
          color: #f76260;
          font-size: 28rpx;
        }
      }
    }
    .listitem-body {
      flex: 1;
      font-size: 30rpx;
      color: @hc-color-info;
      padding-left: 30rpx;
      position: relative;
      .textBreak();
    }
    .listitem-bd {
      flex: 1;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.70);
      padding-left: 30rpx;
      position: relative;
      .textBreak();
    }
    .listitem-ft {
      font-size: 32rpx;
      color: #3986FF;
      position: relative;
      .textBreak();
      // &:before {
      //   content: "|";
      //   color: @hc-color-info;
      //   position: relative;
      //   height: 88rpx;
      //   margin-right: 25rpx;
      // }
      .list-info {
        color: @hc-color-info;
      }
    }
  }
}
