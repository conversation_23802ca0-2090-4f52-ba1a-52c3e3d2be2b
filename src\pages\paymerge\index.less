@import "../../resources/style/mixins.less";

page {
  background: @hc-color-bg;
}

.p-page {
  padding-bottom: 130rpx;
}

// UI调整版本
.ui-userinfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  margin: 32rpx 32rpx 0;

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
  }
  .name {
    margin-left: 32rpx;
    font-weight: 600;
    font-size: 64rpx;
    color: #2d2d2d;
  }

  .login-msg {
    margin-left: 32rpx;
    font-weight: 600;
    font-size: 32rpx;
    line-height: 54rpx;
    color: #2d2d2d;
    flex: 1;
  }
}
.ui-menu-function {
  display: flex;
  flex-direction: column;
  border-radius: 32rpx;
  margin: 32rpx;
  .title {
    padding: 32rpx;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.9);
  }
  .menu {
    display: flex;
    flex-wrap: wrap;
    .menu-item {
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.7);
      text-align: center;
      flex: 1;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 16rpx;
      padding: 16rpx 24rpx;
      box-sizing: border-box;
      .icon {
        width: 60rpx;
        height: 60rpx;
      }
      &.active {
        background: #3eceb6;
        color: #ffffff;
        z-index: 1;
      }
    }
    .menu-item + .menu-item {
      margin-left: 32rpx;
    }
  }
}
.ui-link-list {
  margin: 32rpx;
  .link-item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background: #ffffff;
    border-radius: 24rpx;
    .name {
      font-weight: 600;
      font-size: 36rpx;
      color: rgba(0, 0, 0, 0.9);
    }
    .time {
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.7);
    }
    .cast {
      font-weight: 600;
      font-size: 28rpx;
      line-height: 42rpx;
      color: #ffb040;
      margin-top: 10rpx;
    }
  }
}
