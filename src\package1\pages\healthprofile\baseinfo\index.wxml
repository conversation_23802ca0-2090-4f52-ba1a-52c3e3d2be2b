<form bindsubmit="formSubmit" report-submit='true'>
  <view class="bindcard-list">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title require">姓名</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.name ? 'o-error' : ''}}" placeholder="请输入姓名"
          cursor-spacing="{{CURSOR_SPACING}}" disabled="{{isDisabledBaseInfo}}"
          placeholder-style="color:{{errorElement.name ? errorColor : placeholderColor}}" maxlength="8"
          id="name" @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.name}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title require">身份证号</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.idNo ? 'o-error' : ''}}" type="text" placeholder="请输入身份证号"  disabled="{{isDisabledBaseInfo}}"
          placeholder-style="color:{{errorElement.idNo ? errorColor : placeholderColor}}" id="idNo" maxlength="18"
          cursor-spacing="{{CURSOR_SPACING}}" @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.idNo}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">职业</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.profession ? 'o-error' : ''}}" type="text" placeholder="请输入职业"
          maxlength="30" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.profession ? errorColor : placeholderColor}}" id="profession"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.profession}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">学历</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.education ? 'o-error' : ''}}" type="text" placeholder="请输入学历"
          maxlength="30" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.education ? errorColor : placeholderColor}}" id="education"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.education}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">民族</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.nation ? 'o-error' : ''}}" type="text" placeholder="请输入民族" maxlength="30"
          cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.nation ? errorColor : placeholderColor}}" id="nation"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.nation}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">住址</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.address ? 'o-error' : ''}}" type="text" placeholder="请输入地址"
          maxlength="30" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.address ? errorColor : placeholderColor}}" id="address"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.address}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">身高(cm)</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.height ? 'o-error' : ''}}" type="text" placeholder="请输入身高" maxlength="30"
          cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.height ? errorColor : placeholderColor}}" id="height"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.height}}" />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">体重(KG)</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.weight ? 'o-error' : ''}}" type="text" placeholder="请输入体重" maxlength="30"
          cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.weight ? errorColor : placeholderColor}}" id="weight"
          @input="inputTrigger" @focus="resetThisError" value="{{baseinfo.weight}}" />
      </view>
    </view>
  </view>

  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">保存</button>
  </view>
</form>

<toptip :toptip.sync="toptip" />