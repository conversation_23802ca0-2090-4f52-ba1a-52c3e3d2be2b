<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import WxsUtils from "../../wxs/utils.wxs";
import * as Api from "./api";
export default class ServiceBill extends wepy.page {
  config = {
    navigationBarTitleText: "自助开单",
    navigationBarBackgroundColor: '#fff'
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  components = {
    outpatient: Outpatient,
    empty: Empty
  };
  data = {
    // url参数
    options: {},
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {},
      notShowChangeBtn: false,
      registerShow: false
      // change: true
    },
    // 就诊人列表
    outpatient: {},
    // 当前就诊人信息
    currentPatient: {},
    billInfoList: []
  };

  onLoad() {}
  onShow() {
    //获取切换是否显示
    this.getChooseBtn();
    const { patientId = "" } = this.$wxpage.options;
    this.options = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
    // this.getBillInfo();
  }
  // 获取自助开单信息
  async getBillInfo() {
    const { patientId, patCardNo } = this.options;
    if (!patientId) {
      return;
    }
    const { code, data = [], msg } = await Api.queryZzxmcx({ grid: patCardNo });
    if (code !== 0) {
      return;
    }
    const newList = data.map(item => {
      return { ...item, je: Number(item.je) };
    });
    this.billInfoList = newList;
    this.$apply();
  }

  //开单
  async getQueryZzkd(params) {
    const { patCardNo, patHisNo, patientId, patientName } = this.options;
    try {
      const { code, data = {}, msg } = await Api.queryZzkd({
        pid: patHisNo,
        patCardNo,
        patientId,
        patientName,
        bizType: "selfhelp_order",
        cfgid: params.cfgid,
        czlx: params.czlx,
        zfid: params.zfid,
        cfid: params.cfid,
        je: params.je,
        extFieldsViews: JSON.stringify({
          pid: patHisNo,
          xmmc: params.xmmc,
          bzxx: params.bzxx
        })
      });
      if (code === 0) {
        const { orderId = "" } = data;
        this.registerPayOrder(orderId);
        return;
      }
    } catch (error) {
      wepy.hideLoading();
      wepy.showToast({
        title: error,
        icon: "none"
      });
    }
  }

  /**
   * 创建支付订单
   * @param item
   */
  async registerPayOrder(orderId) {
    let bizContent;
    try {
      bizContent = JSON.stringify(this.getBizContent() || []);
    } catch (e) {
      bizContent = "[]";
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent
    });
    if (code !== 0) {
      return;
    }
    const { payOrderId = "" } = data;
    // const payOrderId = "2206290024200000006";
    wepy.navigateTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=ZZKD`
    });
  }

  getBizContent() {
    const { options = {} } = this || {};
    return [
      { key: "费用类型", value: "自助开单" },
      { key: "就诊人姓名", value: options.patientName || "" },
      { key: "就诊卡号", value: options.patCardNo || "" }
    ];
  }

  events = {
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient;
        this.outpatientConfig.infoShow = true;
        this.outpatientConfig.notShowChangeBtn = true;
        this.$apply();
      }
      this.getBillInfo();
    }
  };
  //通过profileValue判断按钮是否显示
  async getChooseBtn() {
    const { data = {}, code } = await Api.getNoteProfile({
      profileKey: "billSelf_displayPat"
    });
    if (code === 0 && data.profileValue === "1") {
      this.outpatientConfig.registerShow = true;
    }
  }
  methods = {
    bindCreateOrder(param) {
      if (param.bzxx) {
        wx.showModal({
          title: "温馨提示",
          content: param.bzxx,
          success: res => {
            if (res.confirm) {
              this.getQueryZzkd(param);
            } else {
            }
          }
        });
      } else {
        this.getQueryZzkd(param);
      }
    }
    // getRecordList() {
    //   wepy.navigateTo({
    //     url: `/pages/servicebill/servicebilllist/index`
    //   });
    // }
  };
}
</script>


