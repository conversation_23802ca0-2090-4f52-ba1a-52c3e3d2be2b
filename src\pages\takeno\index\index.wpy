<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { REQUEST_QUERY } from "@/config/constant";
import * as Utils from "@/utils/utils";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import * as Api from "./api";

export default class Amount extends wepy.page {
  config = {
    navigationBarTitleText: "在线取号",
    navigationBarBackgroundColor: '#fff'
  };

  components = {
    outpatient: Outpatient,
    empty: Empty
  };

  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
  }

  data = {
    patientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    patient: {},
    user: {},
    takeList: []
  };

  methods = {
    async takeNo(e) {
      const { idx } = e.currentTarget.dataset;
      const takeItem = this.takeList[idx] || {};
      const { code, data = {}, msg } = await Api.takeNo(takeItem);

      if (code == 0) {
        const { orderId = "" } = data;
        this.registerPayOrder(orderId, takeItem);
      }
    }
  };

  events = {
    "outpatient-change-user": function(item = {}) {
      if(item){
        this.patientConfig.infoShow = true;
        this.changeUser(item);
      }
    }
  };

  async changeUser(user = {}) {
    this.user = user;
    this.getTakeList();
  }

  /**
   * 获取待取号列表
   */
  async getTakeList() {
    const { patientId = "" } = this.user;
    const { hisId = "", platformId = "", platformSource = "" } = REQUEST_QUERY;
    const { code, data = {}, msg } = await Api.getTakeList({
      patientId,
      hisId,
      platformId,
      platformSource
    });
    if (code == 0) {
      const { takeList = [] } = data;
      this.takeList = takeList.map(item => {
        const { totalFee = 0 } = item;
        item.totalFeeFormat = (totalFee / 100).toFixed(2);
        item.regTimeFormat = Utils.getVisitTime(item);
        return item;
      });
      this.$apply();
    }
  }

  async registerPayOrder(orderId = "", item = {}) {
    const bizContent = this.getBizContent(item);
    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent: JSON.stringify(bizContent)
    });
    if (code == 0) {
      const { payOrderId = "" } = data;
      wepy.navigateTo({
        url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=YYQH`
      });
    }
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent(item) {
    return [
      { key: "费用类型", value: "预约取号" },
      { key: "就诊科室", value: item.deptName },
      { key: "医生名称", value: item.doctorName },
      { key: "就诊时段", value: Utils.getVisitTime(item) },
      { key: "就诊人", value: item.patientName },
      { key: "就诊卡号", value: item.patCardNo }
    ];
  }
}
</script>
