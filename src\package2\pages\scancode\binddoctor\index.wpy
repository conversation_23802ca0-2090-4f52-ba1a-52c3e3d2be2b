<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>
<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { PRIMARY_COLOR } from '@/config/constant';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '家辉云平台',
      navigationBarBackgroundColor: '#2F848B',
      navigationBarTextStyle: 'white',
    }

    onLoad(options) {
      wx.hideHomeButton();
      this.acount = options.acount;
      this.$apply();
    }

    onShow() {
      this.getPatientsList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      acount: '',
      patientInfo: {},
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      cancel () {
        wx.exitMiniProgram();
      },
      async sure() {
        if (this.patientInfo.patientId) {
          const { code, data = {} } = await Api.add({
            account: this.acount,
            patientTableId: this.patientInfo.patientId,
          });
          if (code === 0) {
            wx.showToast({
              title: '关联成功',
              icon: 'success',
              duration: 2000,
            });
            setTimeout(() => {
              wx.exitMiniProgram();
            }, 2000);
          }
          return;
        }
        wx.showModal({
          title: "提示",
          content: "您尚未登录，请先登录",
          showCancel: false,
          confirmText: "注册登录",
          confirmColor: PRIMARY_COLOR,
          success: res => {
            if (res.confirm) {
              wepy.navigateTo({
                url: "/pages/bindcard/queryuserinfo/index?qryType=1&from=doctor"
              });
              return;
            }
          }
        });
      },

    }
    getPatientsList = async () => {
      const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
      if (code === 0) {
        const { cardList = [] } = data;
        this.cardList = cardList;
        const userCard = cardList.filter(v => v.relationType === 1);
        const patientInfo = cardList.length ? userCard[0] : {};
        this.patientInfo = patientInfo;
        this.$apply();
      }
    }
  }
</script>