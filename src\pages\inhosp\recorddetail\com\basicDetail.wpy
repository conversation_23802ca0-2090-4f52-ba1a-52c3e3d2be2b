<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <view class="list-tit">补缴信息</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.totalRealFee}}">
        <view class="item-label">补缴金额</view>
        <view class="item-value">
          <view class="unit-price">￥{{WxsUtils.formatMoney(detailData.totalRealFee, 100)}}</view>
        </view>
      </view>
      <view class="list-item" wx:if="{{detailData.hisName}}">
        <view class="item-label">医院名称</view>
        <view class="item-value">{{detailData.hisName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.patientName}}">
        <view class="item-label">住院人</view>
        <view class="item-value">{{detailData.patientName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.admissionNum}}">
        <view class="item-label">住院号</view>
        <view class="item-value">{{detailData.admissionNum}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.patCardNo}}">
        <view class="item-label">病历号</view>
        <view class="item-value">{{detailData.patCardNo}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.hisOrderNo}}">
        <view class="item-label">医院单号</view>
        <view class="item-value">{{detailData.hisOrderNo}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.orderId}}">
        <view class="item-label">平台单号</view>
        <view class="item-value">{{detailData.orderId}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.agtOrdNum}}">
        <view class="item-label">支付流水号</view>
        <view class="item-value">{{detailData.agtOrdNum}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.payedTime}}">
        <view class="item-label">支付时间</view>
        <view class="item-value">{{detailData.payedTime}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import wepy from 'wepy';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Utils from '@/utils/utils';

  export default class BasicDetail extends wepy.component {
    data = {};

    components = {};

    wxs = {
      WxsUtils: WxsUtils,
    };

    props = {
      detailData: {
        type: Object,
        default: {},
      }
    };

    onLoad(options) {
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {};
  }
</script>
