<view>
  <!-- <view class="header">
    <view>查询范围：</view>
    <picker class="range" range="{{rangeList}}" range-key="value" value="{{rangeIndex}}" bindchange="onRangeChange">
      {{rangeList[rangeIndex].value}}
    </picker>
  </view> -->
  <view class="record-list" wx:for="{{recordList}}" wx:key="index">
      <view class="wgt-folding">
        <view class="wgt-folding-tit" bindtap="bindExpand('{{index}}')">
          <view class="item-header">
            <view>{{item.createTime}}</view>
            <view class="f-1">{{item.accountName}}</view>
          </view>
          <view class="wgt-folding-expand {{item.isExpand ? 'active' : ''}}">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/icon-shouqi.png"></image>
          </view>
        </view>
        <view class="wgt-folding-content">
          <view wx:if="{{item.isExpand}}" class="item-content">
            {{item.content}}
          </view>
        </view>
      </view>
  </view>
</view>
<!-- <toptip :toptip.sync="toptip" /> -->
