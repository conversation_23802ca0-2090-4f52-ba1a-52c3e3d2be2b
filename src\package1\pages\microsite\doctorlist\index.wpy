<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class DoctorList extends wepy.page {
    config = {
      navigationBarTitleText: '医生列表',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.deptId = options.deptId || '';
      this.deptName = options.deptName || '';
       wx.setNavigationBarTitle({
          title: options.deptName || '医生列表'
        });
      this.getDoctorList({ deptId: options.deptId });
    }

    data = {
      emptyConfig: {
        show: true,
      },
      doctorList: [],
      doctorNum: 0,
      deptId: '',
      deptName: '',
    };

    methods = {
      toDoctorIntro(item = {}) {
        const { doctorId = '' } = item;
        const query = Utils.jsonToQueryString({ deptId: this.deptId, doctorId, deptName: this.deptName });
        wepy.navigateTo({
          url: `/package1/pages/microsite/doctorinfo/index?${query}`,
        });
      },
    };
    /**
     * 获取医院信息
     */
    async getDoctorList(param){
      const { data = {}, code } = await Api.getDoctorList(param);
      if(code == 0){
        const { doctorlist = [], doctorNum = 0 } = data;
        if(doctorlist.length > 0){
          this.doctorList = doctorlist;
          this.doctorNum = doctorNum;
          this.emptyConfig.show = false;
          this.$apply();
        }
      }
    }
  }
</script>
