<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Api from "./api";
import Empty from "@/components/empty/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import { REQUEST_QUERY } from "@/config/constant";
import { validator as validatorFun } from "@/utils/utils";
import { uploadFile } from "@/utils/request";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "开单缴费",
  };

  components = {
    empty: Empty,
  };

  wxs = {
    WxsUtils: WxsUtils,
  };

  onLoad(options) {
    this.getPatientsList(options);
    wx.hideHomeButton();
    console.log(this.tempFilePaths.length, "=====28");
  }

  onShow() {
    this.initCameraAuth();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    emptyConfig: {
      show: true,
    },
    errorText: "暂无可检测项目，请联系医生处理",
    patientInfo: {},
    productList: [],
    productObj: {},
    products: [],
    isUrgent: false,
    otherSelected: null,
    otherFee: "",
    otherName: "",
    cardList: [],
    myInfo: {},
    urlParams: {},
    showModal: false,
    payTypeArray: ["微信支付", "门诊缴费"],
    payTypeIndex: null,
    payType: "请选择支付方式",
    nativePath: [],
    tempFilePaths: [],
    currentPicCount: [],
    showPop: false,
    selectedType: "微信支付",
    hideCamera: true,
    showcontent: false,
    sampleNumber: "",
  };
  getSetting = async () => {
    let getSettingRes;
    try {
      getSettingRes = await wepy.getSetting();
    } catch (e) {
      getSettingRes = e;
    }
    console.log("getSettingRes", getSettingRes);
    const auth = getSettingRes.authSetting || {};
    if (auth["scope.camera"]) {
      return true;
    } else {
      let authorizeRes;
      try {
        authorizeRes = await wepy.authorize({
          scope: "scope.camera",
        });
      } catch (e) {
        authorizeRes = e;
      }
      console.log("authorizeRes", authorizeRes);
      if (authorizeRes.errMsg !== "authorize:ok") {
        return false;
      } else {
        return true;
      }
    }
  };
  initCameraAuth = async () => {
    const scopeCamera = await this.getSetting();
    if (!scopeCamera) {
      wepy.showToast({ title: "请点击右上角图标授权相机", icon: "none" });
    } else {
      this.hideCamera = false;
      this.$apply();
    }
  };
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    async openSetting() {
      if (!this.hideCamera) return;
      const scopeCamera = await this.getSetting();
      if (!scopeCamera) {
        wepy.openSetting({
          success: (res) => {
            if (res.authSetting["scope.camera"]) {
              this.hideCamera = false;
              this.$apply();
            }
          },
        });
      } else {
        this.hideCamera = false;
        this.$apply();
      }
    },
    onCameraError(e) {
      console.error(e);
      this.hideCamera = true;
      this.$apply();
    },
    onCameraScanCode(e) {
      this.myInfo["sampleNumber"] = e.detail.result;
      this.hideCamera = true;
      this.$apply();
    },
    introduction(item) {
      console.log(item, "=====67");
      wepy.setStorageSync("product-introduction", item);
      wepy.navigateTo({
        url: `/package2/pages/scancode/productinfo/index`,
      });
    },
    showPopView() {
      this.showPop = true;
    },
    bindClosePop() {
      this.showPop = false;
    },
    bindChangePop(e) {
      const val = e.detail.value;
      this.selectedType = this.payTypeArray[val];
      this.payType = this.payTypeArray[val];
      this.payTypeIndex = this.payTypeArray[val] === "微信支付" ? 1 : 2;
      this.$apply();
    },
    confirmType() {
      this.payType = this.selectedType;
      this.payTypeIndex = this.payType === "微信支付" ? 1 : 2;
      this.showPop = false;
      this.$apply();
    },
    longTapImg(e) {
      const { url } = e.currentTarget.dataset;
      wx.showActionSheet({
        itemList: ["保存到手机", "删除"],
        success: (res) => {
          const actionList = ["save", "delete"];
          this.bindItemTap({ action: actionList[res.tapIndex] }, url);
        },
        fail: (res) => {
          console.log(res.errMsg);
        },
      });
    },
    viewImage(e) {
      const { path, url } = e.currentTarget.dataset;
      wepy.previewImage({
        urls: this.tempFilePaths || [],
        current: url || "",
      });
    },
    async updateImg() {
      let that = this;
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 3 - (this.tempFilePaths.length || 0),
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths;
        wepy.showLoading({ title: "发送中...", mask: true });
        tempFilePaths.forEach((v) => {
          this.pushImg(v);
        });
      }
    },

    closeModal() {
      this.showModal = false;
      this.$apply();
    },
    selctItem(idx) {
      this.productList[idx].selected = !this.productList[idx].selected;
      if (this.productList[idx].selected) {
        this.products.push({
          parproductName: this.productList[idx].parproductName || "",
          productName: this.productList[idx].productName,
          productprice: this.productList[idx].productprice,
          productId: this.productList[idx].productId,
        });
      } else {
        this.products = this.products.filter(
          (v) => v.productId !== this.productList[idx].productId
        );
      }
    },
    selOther(boolean) {
      this.otherSelected = boolean;
      if (!boolean) {
        this.otherFee = "";
        this.otherName = "";
      }
    },
    selUrgent(boolean) {
      if (!this.isUrgent && boolean) {
        wepy
          .showModal({
            title: "温馨提示", //提示的标题,
            content: "加急项目需支付2000.00元，是否确认加急？", //提示的内容,
            confirmText: "加急", //确定按钮的文字，默认为取消，最多 4 个字符,
            confirmColor: "#2F848B", //确定按钮的文字颜色,
          })
          .then(async (res) => {
            if (res.confirm) {
              this.isUrgent = true;
              this.$apply();
            } else {
              this.isUrgent = false;
              this.$apply();
            }
          });
      }
      this.isUrgent = boolean;
      this.$apply();
    },
    feeInputValue(e) {
      this.otherFee = e.detail.value;
      this.$apply();
    },
    nameInputValue(e) {
      this.otherName = e.detail.value;
      this.$apply();
    },
    inputTrigger(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      this.myInfo[id] = value;
      this.$apply();
    },
    async confirm() {
      const { hisId = "", platformId = "" } = REQUEST_QUERY;
      if (!this.validator()) return;
      if (this.otherSelected) {
        this.products.push({
          productName: "其他",
          remark: this.otherName,
          productprice: this.otherFee * 100,
        });
      }
      const params = {
        totalFee: this.getTotalFee(),
        bizType: "dna_gene_pay",
        ...(this.myInfo || {}),
        deptId: this.productObj.allianceId,
        deptName: this.productObj.institutionName,
        hisOrderNo: this.productObj.docaccount,
        hisSerialNo: this.productObj.docname,

        payCertificate: JSON.stringify(this.tempFilePaths),
        extFieldsViews: JSON.stringify({
          isMerge: this.isUrgent ? "1" : "",
          docaccount: this.productObj.docaccount, //开单账号
          docname: this.productObj.docname, //开单账号名称
          reportMailingType: this.productObj.reportMailingType, // 邮寄类型2 邮寄 其他不邮寄
          reportMailingFees: this.productObj.reportMailingFees, // 邮寄费用
          products: this.products,
          regType: this.urlParams.regType || "",
        }),
      };
      if (this.productObj.xcxOutpatientPaymentFlag == 1) {
        params.payMethod = this.payType;
      }

      if (this.patientInfo.patientId) {
        params.patientId = this.patientInfo.patientId;
        params.patientName = this.patientInfo.patientName;
        // params.mobile = this.patientInfo.patientName; //手机号码
        params.idNumber = this.patientInfo.idNo;
      }
      const { code, data = {} } = await Api.saveOrder(params);
      if (code === 0) {
        const { subcode, subdatas = {} } = await Api.getContract({
          id: this.productObj.allianceId,
        });
        if (
          this.payTypeIndex === 2 &&
          this.productObj.xcxOutpatientPaymentFlag == 1
        ) {
          wepy.redirectTo({
            url: `/package2/pages/scancode/recorddetail/index?orderId=${data.orderId}&regType=${this.urlParams.regType}`,
          });
          return;
        }
        this.registerPayOrder(data.orderId);
      }
    },
  };

  async pushImg(v) {
    const { code, data } = await uploadFile("/api/files/uploadpic", v);
    if (code === 0) {
      this.tempFilePaths.push(data.url);
      this.$apply();
    } else {
      wx.showToast({
        title: "图片上传失败",
        icon: "none",
        duration: 1400,
      });
    }
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent() {
    const { queryStringPrams = {} } = this || {};
    return [
      { key: "费用类型", value: "扫码缴费" },
      // { key: "医生姓名", value: this.productObj.docname || "" },
      {
        key: "就诊人",
        value: this.patientInfo.patientName || this.myInfo.patientName || "",
      },
      {
        key: "项目名称",
        value: {
          feeDetail: { key: "费用明细", value: this.getTotalFee() || 0 },
          list: [
            ...this.products,
            { key: "加急", value: this.isUrgent ? 200000 : 0 },
            { key: "快递费", value: this.productObj.reportMailingFees },
          ],
        },
      },
    ];
  }

  async add(options, id) {
    const params = {
      account: options.acount,
      patientTableId: id,
    };
    const { code, data = {} } = await Api.add(params);
  }

  async registerPayOrder(orderId = "") {
    let bizContent;
    try {
      bizContent = JSON.stringify(this.getBizContent() || []);
    } catch (e) {
      bizContent = "[]";
    }
    const {
      code,
      data = {},
      msg,
    } = await Api.registerPayOrder({ orderId, bizContent });
    if (code == 0) {
      const { payOrderId = "" } = data;
      this.products = this.products.filter((v) => v.productName !== "其他");
      wepy.navigateTo({
        url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=SMKD&regType=${this.urlParams.regType}`,
      });
    }
  }
  getTotalFee() {
    let productsTotal = this.products.reduce((pre, current) => {
      return pre + Number(current.productprice);
    }, 0);
    let urgentTotal = 0;
    if (this.isUrgent) {
      urgentTotal = 200000;
    }
    return (
      productsTotal + urgentTotal + Number(this.productObj.reportMailingFees)
    );
  }
  validator() {
    const result = { ret: true, tip: "" };
    if (!this.patientInfo.patientId) {
      const { patientName, idNumber, mobile } = this.myInfo;
      if (!patientName) {
        wepy.showToast({
          title: "请输入受检者姓名", //提示的内容,
          icon: "none", //图标,
          duration: 2000, //延迟时间,
        });
        return;
      }
      if (!idNumber) {
        wepy.showToast({
          title: "请输入证件号码", //提示的内容,
          icon: "none", //图标,
          duration: 2000, //延迟时间,
        });
        return;
      }
      /*
      if(!validatorFun.idCard(idNumber).ret){
        wepy.showToast({
          title: '请输入正确的身份证号码', //提示的内容,
          icon: 'none', //图标,
          duration: 2000 //延迟时间,
        });
        return
      }
      */
      if (!validatorFun.mobile(mobile).ret) {
        wepy.showToast({
          title: "请输入正确的手机号", //提示的内容,
          icon: "none", //图标,
          duration: 2000, //延迟时间,
        });
        return;
      }
    }
    if (!this.products.length && !this.otherSelected) {
      wepy.showToast({
        title: "请选择需要检测的项目", //提示的内容,
        icon: "none", //图标,
        duration: 2000, //延迟时间,
      });
      return;
    }
    if (this.otherSelected) {
      if (!this.otherFee) {
        wepy.showToast({
          title: "请输入其他项目金额", //提示的内容,
          icon: "none", //图标,
          duration: 2000, //延迟时间,
        });
        return;
      }
      if (this.otherFee) {
        var reg =
          /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (!reg.test(this.otherFee)) {
          wepy.showToast({
            title: "请输入正确的金额格式", //提示的内容,
            icon: "none", //图标,
            duration: 2000, //延迟时间,
          });
          return;
        }
      }
      if (!this.otherName) {
        wepy.showToast({
          title: "请备注其他项目名称", //提示的内容,
          icon: "none", //图标,
          duration: 2000, //延迟时间,
        });
        return;
      }
    }

    if (!this.payTypeIndex && this.productObj.xcxOutpatientPaymentFlag == 1) {
      wepy.showToast({
        title: "请选择支付方式", //提示的内容,
        icon: "none", //图标,
        duration: 2000, //延迟时间,
      });
      return;
    }

    if (
      this.payTypeIndex &&
      this.payTypeIndex === 2 &&
      this.tempFilePaths.length === 0
    ) {
      wepy.showToast({
        title: "请上传门诊缴费凭证", //提示的内容,
        icon: "none", //图标,
        duration: 2000, //延迟时间,
      });
      return;
    }

    return true;
  }
  urlToObj(url) {
    let obj = {};
    let str = url.slice(url.indexOf("?") + 1);
    let arr = str.split("&");
    for (let j = arr.length, i = 0; i < j; i++) {
      let arr_temp = arr[i].split("=");
      obj[arr_temp[0]] = arr_temp[1];
    }
    return obj;
  }
  getPatientsList = async (options) => {
    const url = decodeURIComponent(options.q);
    const params = this.urlToObj(url);
    if (params.from == "doctor") {
      wepy.navigateTo({
        url: `/package2/pages/scancode/binddoctor/index?acount=${params.acount}`,
      });
      return;
    }
    const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
    if (code === 0) {
      const { cardList = [] } = data;
      this.cardList = cardList;
      // const userCard = cardList.filter(v => v.relationType === 1);
      // const patientInfo = cardList.length ? userCard[0] : {};
      const userCard = cardList.filter((v) => v.relationType === 1)[0];
      const patientInfo = {};
      this.patientInfo = patientInfo;
      // if (params.regType == 1) {
      //   this.showModal = true;
      // }
      this.urlParams = params;
      this.$apply();
      // 对url中携带的参数提取处理
      // const params = {
      //   acount: 'liyanlai',
      //   allianceId: 12
      // }
      this.getProductList(params);
      if (userCard.patCardNo) {
        this.add(params, userCard.patientId);
        return;
      }
      // wx.showModal({
      //   title: "提示",
      //   content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
      //   showCancel: true,
      //   confirmText: "立即绑定",
      //   confirmColor: PRIMARY_COLOR,
      //   success: res => {
      //     if (res.confirm) {
      //       wepy.navigateTo({
      //         url: "/pages/bindcard/queryuserinfo/index?qryType=1"
      //       });
      //       return;
      //     }
      //     wx.navigateBack();
      //   }
      // });
    }
  };
  async getProductList(params) {
    const { code, data = {} } = await Api.getProductList(params);
    if (code === 0) {
      if (data.resultCode == 1) {
        this.showcontent = false;
        this.emptyConfig.show = true;
        this.errorText = data.resultMessage;
        this.$apply();
        return;
      }
      this.productObj = data;
      if (data.products.length > 0) {
        this.productList = data.products.map((item) => {
          return { ...item, selected: false };
        });
        this.showcontent = true;
        this.emptyConfig.show = false;
        this.$apply();
        return;
      }
      // this.emptyConfig.show = false;
      // this.$apply();
    } else {
      this.showcontent = false;
      this.emptyConfig.show = true;
      this.$apply();
    }
  }
  bindItemTap(item = {}, url) {
    const { action = "" } = item;
    console.log("item", item);
    if (!url) {
      wepy.showModal({
        title: "提示",
        content: "请选择有效图片",
        showCancel: false,
      });
      return;
    }
    if (action === "save") {
      this.saveImage(url);
    } else if (action === "delete") {
      this.deleteImg(url);
    }
  }
  async saveImage(url) {
    // 保存图片
    wepy.showLoading({ title: "保存中", mask: true });
    wx.downloadFile({
      url: url || "",
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: (res) => {
            wepy.hideLoading();
            wx.showToast({
              title: "保存成功",
              icon: "success",
              duration: 1000,
              success: () => {},
            });
          },
          fail: (res) => {
            wepy.hideLoading();
            console.log("res1", res);
            if (res.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
              // this.getSetting();
              return;
            }
            wepy.showModal({
              title: "提示",
              content: "保存图片失败",
              showCancel: false,
            });
          },
        });
      },
      fail: function (res) {
        console.log("res2", res);
        wepy.hideLoading();
        wepy.showModal({
          title: "提示",
          content: "保存图片失败",
          showCancel: false,
        });
      },
    });
  }
  deleteImg(url) {
    this.tempFilePaths = this.tempFilePaths.filter((v) => v !== url);
    this.$apply();
  }
}
</script>
