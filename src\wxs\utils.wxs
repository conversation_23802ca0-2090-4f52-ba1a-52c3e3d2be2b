// 时间对应表
var TIME_MAP = { '1': '上午', '2': '下午', '3': '晚上', '4': '白天', '5': '全天' };
var IS_NEED_ENDTIME = true;

module.exports = {
  isShow: function (id, showId) {
    if (id == showId) {
      return true;
    } else {
      return false;
    }
  },
  sliceString: function (string, len) {
    return string.slice(len);
  },
  getWeekDay: function (date) {
    var weekDayMap = { '1': '一', '2': '二', '3': '三', '4': '四', '5': '五', '6': '六', '0': '日' };
    var weekDay = date ? weekDayMap[(getDate(date).getDay() || 0).toString()] : '';
    return weekDay;
  },
  formatMoney: function (moneyString = '0', mark = 100) {
    console.log('moneyString', moneyString);
    var moneyNumber = parseFloat(moneyString || '0');
    if (typeof moneyNumber === 'number' && typeof mark === 'number') {
      return parseFloat(moneyNumber / mark).toFixed(2);
    }
    return 0;
  },
  // 获取就诊时间
  // visitDate 就诊日期
  // visitWeekName 星期
  // visitPeriod  时段
  // visitBeginTime 开始时间
  // visitEndTime 结束时间
  // @param resData
  // @returns {string}
  getVisitTime: function (visitDate, visitWeekName, visitPeriod, visitBeginTime, visitEndTime) {
    var visitTime = '';
    if (visitBeginTime) {
      visitTime = visitDate + (visitWeekName ? ' ' + visitWeekName + '' : '') + (TIME_MAP['' + visitPeriod + ''] ? ' ' + TIME_MAP['' + visitPeriod + ''] + '' : '') + visitBeginTime;
      if (IS_NEED_ENDTIME) {
        visitTime += '~' + visitEndTime;
      }
    } else {
      visitTime = visitDate + (visitWeekName ? ' ' + visitWeekName + '' : '') + (TIME_MAP['' + visitPeriod + ''] ? TIME_MAP['' + visitPeriod + ''] : '');
    }
    return visitTime;
  },
  getTimeSlot: function (visitPeriod, visitBeginTime, visitEndTime) {
    if (visitBeginTime) {
      if (visitPeriod) {
        var timeSolt = TIME_MAP[visitPeriod + ''] + ' ' + visitBeginTime;
        if (IS_NEED_ENDTIME) {
          timeSolt += '~' + visitEndTime;
        }
      } else {
        if (IS_NEED_ENDTIME) {
          return visitBeginTime + '~' + visitEndTime;
        } else {
          return visitBeginTime;
        }
      }
    } else {
      return TIME_MAP[visitPeriod + ''];
    }
  },
  convertListStatus: function (status) {
    var statusMap = {
      'S': 'success',
      'F': 'fail',
      'L': 'lock',
      'C': 'cancel',
      'P': 'abnormal',
      'H': 'abnormal',
      'Z': 'abnormal',
      'E': 'abnormal',
      '0': 'abnormal',
      '1': 'success',
      '2': 'success',
      '4': 'success',
      '3': 'fail',
      '5': 'fail',
      '7': 'fail',
      '6': 'cancel',
    };

    return statusMap[status] || '';
  },
  medicalListStatus: function (status) {
    var statusMap = {
      '1': 'success',
      '2': 'success',
      '3': 'cancel',
      '4': 'cancel',
      '5': 'success',
      '6': 'success',
    };

    return statusMap[status] || '';
  },
  
  /**
   * 格式化日期
   * @param {string|number} date - 日期字符串或时间戳
   * @return {string} 格式化后的日期字符串
   */
  formatDate: function(date) {
    if (!date) {
      return '未知';
    }
    
    // 如果是数字（时间戳），转换为日期字符串
    if (typeof date === 'number') {
      var dateObj = getDate(date);
      return dateObj.getFullYear() + '-' + 
             ((dateObj.getMonth() + 1) < 10 ? '0' : '') + (dateObj.getMonth() + 1) + '-' + 
             (dateObj.getDate() < 10 ? '0' : '') + dateObj.getDate();
    }
    
    // 如果是字符串，截取前10位（日期部分）
    if (typeof date === 'string' && date.length >= 10) {
      return date.slice(0, 10);
    }
    
    return date;
  }
};