@import "../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}
.home-page-box{
  padding-bottom: 24rpx;
  // overflow-y: auto;
}
.p-page {
  height: calc(100vh - 110rpx);
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
  .nav{
    font-size: 34rpx;
    font-weight: 600;
    .capsule-box{
      font-size: 34rpx;
      display: flex;
      align-items: center;
      text-align: left;
      padding-left: 24rpx;
    }
    
  }
  .nav-img{
    width: 345rpx;
    height: 40rpx;
  }

  .home-bg{
    width: 100%;
    position: absolute;
    top: 0;
    z-index: -1;
  }

  &.unscroll {
    overflow: hidden;
  }
}

.user-info {
  margin: 316rpx 24rpx 0;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 8rpx;
  z-index: 9;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.08);

  .user-info-top{
    display: flex;
    padding-bottom: 32rpx;
    justify-content: space-between;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  }
  .user-info-bottom{
    display: flex;
    padding-top: 24rpx;
    justify-content: space-between;
    align-items: center;
    .line{
      width: 1px;
      height: 19rpx;
      background: #000;
    }
    .bottom-btn{
      font-size: 28rpx;
      display: flex;
      align-items: center;
      image{
        margin-right: 10rpx;
        width: 27rpx;
        height: 28rpx;
      }
      text{
        line-height: 28rpx;
      }
    }
  }

  .left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .avatar {
      width: 100rpx;
      height: 100rpx;
      margin-right: 24rpx;

      image {
        border-radius: 50%;
        width: 100%;
        height: 100%;
      }
    }

    .title-tips {
      .title-tips-text {
        font-size: 36rpx;
        color: #2d2d2d;
        font-weight: 600;
      }

      .title-tips-info {
        font-size: 24rpx;
        color: @hc-color-info;
      }

      .trangle {
        margin-left: 16rpx;
        width: 18rpx;
        height: 18rpx;
      }
    }
  }

  .right {
    display: flex;
    flex-direction: row;
    align-items: center;

    .scan-code {
      width: 64rpx;
      height: 64rpx;
      border-radius: 12rpx;
      margin-left: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.btn-default {
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 22rpx;
  border-radius: 41rpx;
  word-break: keep-all;
  color: #2D666F;
  border: 1px solid #2D666F;
}

.title {
  font-weight: 600;
  font-size: 32rpx;
}

.desc {
  margin-top: 8rpx;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.50);
}

.card {
  display: flex;
  border-radius: 24rpx;
  background: @hc-color-white;
  margin: 0 32rpx;
}

.card+.card {
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.card-1 {
  display: flex;
  border-radius: 24rpx;
  background: @hc-color-white;
  flex-direction: row;
  padding: 32rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: @hc-color-title;
}

.card-1+.card-1 {
  margin-left: 16rpx;
}

.flex-1 {
  flex: 1;
}

.bg-1 {
  background: linear-gradient(180deg,
      rgba(62, 206, 182, 0.5) 0%,
      rgba(62, 206, 182, 0.2) 100%);
}

.bg-2 {
  background: linear-gradient(180deg,
      rgba(57, 134, 255, 0.5) 0%,
      rgba(57, 134, 255, 0.2) 100%);
}

.quick-menu {
  border-radius: 8rpx;
  display: flex;
  padding: 48rpx 24rpx;
  margin: 16rpx 24rpx;
  background-color: #fff;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
  

  .menu-item {
    flex: 1;
  }
}

.swiper {
  background-color: @hc-color-white;
  height: 84rpx;
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .swiper-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: @hc-color-info;
    height: 100%;
    width: 100%;

    .msg {
      width: 100%;
      padding-left: 10rpx;
      .ellipsis();
    }

    .unread-dot {
      .circle (16rpx);
      background: #fc7f60;
      margin-left: 12rpx;
    }
  }
}

.function-menu{
  padding: 32rpx;
  flex-direction: column;
  .quick-menu-title{
    position: relative;
    margin-bottom: 32rpx;
    font-size: 32rpx;
    &::before{
      display: inline-block;
      margin-right: 12rpx;
      content: '';
      width: 4rpx;
      height: 22rpx;
      border-radius: 4rpx;
      background: #3F969D;
    }
    .more{
      position: absolute;
      right: 0;
      color: #2F858C;
      font-size: 24rpx;
    }
  }
  .menu-item{
    flex: 0 0 25%;
  }
  .function-menu-list{
    display: flex;
    &.list-pt{
      padding-top: 32rpx;
    }
    .large-icon{
      width: 80rpx;
      height: 80rpx;
    }
    .mt-5{
      font-weight: normal;
      margin-top: 12rpx;
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.7);
    }
  }
}
.files-menu{
  margin-bottom: 0;
  .quick-menu-title{
    margin: 0;
    line-height: 32rpx;
  }
  .function-menu-list{
    display: flex;
    flex-direction: column;
    .file-list{
      display: flex;
      padding: 32rpx 0;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
      &:last-child{
        padding-bottom: 0;
        border: none;
      }
      .content-box{
        flex: 1;
      }
      .large-icon{
        margin-left: 24rpx;
        flex: 0 0 122rpx;
        width: 122rpx !important;
        height: auto;
        border-radius: 8rpx;
        image{
          border-radius: 8rpx;
        }
        &.center{
          margin: 0 24rpx 0 0;
        }
      }
    }
    .content-box{
      .content{
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.90);
        line-height: 42rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .read-num{
        margin-top: 8rpx;
        font-size: 20rpx;
        color: rgba(0, 0, 0, 0.40);
        & > text:first-child{
          margin-right: 10rpx;
        }
      }
    }
  }
}
.doctor-list{
  .function-menu-list{
    width: 100%;
    overflow-x: auto;
    .menu-item{
      margin-right: 40rpx;
      &:last-child{
        margin: 0;
      }
    }
    .img-bg-box{
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
      background: linear-gradient(180deg,rgba(235, 249, 250, 1) 0, rgba(61, 182, 188, 1) 100%);
      .extra{
        position: absolute;
        padding: 0px 20rpx;
        transform: translate(-50%, -50%);
        left: 50%;
        bottom: -20rpx;
        font-size: 20rpx;
        color: #FFF;
        font-weight: 600;
        background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
        border-radius: 34rpx;
        word-break: keep-all;
      }
      .large-icon{
        width: 120rpx;
        height: 120rpx;
        image{
          border-radius: 50%;
        }
      }

    }
    .large-icon{
      width: 120rpx;
      height: 120rpx;
    }
    .mt-5{
      color: #000;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
  .menu-item {
    flex: 0 0 130rpx;
  }
}

.menu-item {
  font-size: 24rpx;
  color: @hc-color-title;
  text-align: center;
  position: relative;
}

.unread-radius{
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: @hc-color-error;
  color: #ffffff;
  position: absolute;
  top: 0rpx;
  left: 106rpx;
}

.menu-title {
  font-size: 25rpx;
  color: @hc-color-text;
  margin-top: 10rpx;
}

.large-icon {
  width: 90rpx;
  height: 90rpx;

  &.center {
    margin: 0 auto;
  }

  image {
    width: 100%;
    height: 100%;
  }
}

.mr-16 {
  margin-right: 32rpx;
}

.mt-5 {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.90);
  font-size: 32rpx;
  font-weight: 600;
}
.min-tips{
  color: rgba(0, 0, 0, 0.40);
  font-size: 22rpx;
}

.order {
  display: flex;
  flex-direction: row;
  margin-bottom: 20rpx;
  margin: 0 32rpx 20rpx;
}

.card-2 {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background-image: url(REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/menu-bg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 100rpx;

  .title {
    font-size: 24rpx;
    font-weight: 600;
    line-height: 60rpx;
    text-align: center;
  }

  .tag {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rpx 10rpx;
    border-radius: 20rpx;
    font-weight: 600;
    font-size: 16rpx;
    width: 22rpx;
    color: #fff;

    &.bg-1 {
      background: @hc-color-primary;
    }

    &.bg-2 {
      background: @hc-color-link;
    }

    &.bg-3 {
      background: @hc-color-assist;
    }
  }

  .bg-image {
    position: absolute;
    width: 100%;
    height: 100rpx;
    right: 0;
    bottom: 0;
    border-radius: 16rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.card-2+.card-2 {
  margin-left: 12rpx;
}

.card-3 {
  flex-direction: column;

  .title {
    padding: 16rpx 32rpx;
  }
}

.menu {
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 40vw;
  overflow: hidden;

  &.is-expand {
    height: auto;
  }
}

.menu-service {
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  // height: 67vw;
  height: auto;
  overflow: hidden;

  &.is-expand {
    height: auto;
  }
}

.pos-relative {
  position: relative;
}

.menu-unread {
  position: absolute;
  min-width: 32rpx;
  background: #ff613b;
  border: 2rpx solid #ffffff;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 20rpx;
  line-height: 28rpx;
  border-radius: 50%;
  color: #ffffff;
  top: -1px;
  margin-left: 60rpx;
}

.middle-icon {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;

  image {
    width: 100%;
    height: 100%;
  }

  .will-open {
    box-sizing: border-box;
    position: absolute;
    right: -10px;
    border-radius: 54rpx;
    padding: 0rpx 8rpx;
    font-weight: 600;
    font-size: 10rpx;
    line-height: 24rpx;
    color: @hc-color-primary;
    background: #ffffff;
    border: 2rpx solid #3eceb6;
  }
}

.small-icon {
  width: 40rpx;
  height: 40rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.xsmall-icon {
  width: 32rpx;
  height: 32rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.card-4 {
  padding: 24rpx 32rpx;
}

.card-item {
  display: flex;
  background: @hc-color-bg;
  border-radius: 16rpx;
  padding: 16rpx;
  align-items: center;

  .title {
    font-size: 26rpx;
    color: @hc-color-text;
    padding-left: 12rpx;
    font-weight: 400;
  }
}

.card-item+.card-item {
  margin-left: 20rpx;
}

.link {
  font-size: 26rpx;
  text-align: center;
  color: @hc-color-link;
  padding-bottom: 24rpx;
}

.barcode {
  text-align: center;
  padding-left: 0 !important;
  padding-right: 0 !important;

  image {
    margin: 0 auto;

    width: 640rpx;
    // height: 290px;
    &.qrcode {
      max-width: 330rpx;
      max-height: 330rpx;
    }
  }
}

.no-title {
  color: @hc-color-title;
}

.m-card-msg {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  margin: 20rpx;
  background-color: @hc-color-white;
  border-radius: 4rpx;

  .msg-icon {
    align-self: flex-start;
    margin-left: 5rpx;
    margin-top: 7rpx;
    width: 26rpx;
    height: 28rpx;
    background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/home/<USER>") no-repeat 50% 50%;
    background-size: 100% 100%;
  }

  .msg-content {
    flex: 1;
    margin-right: 30rpx;
    overflow: hidden;
  }

  .msg-text {
    margin-left: 20rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    margin-top: 5px;
    .ellipsis();

    &:first-child {
      margin-top: 0;
    }
  }

  .msg-arrow {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }
}

.m-pop-code {
  padding: 30rpx;

  .pop-hd {
    font-size: 34rpx;
  }

  .hd-tit {
    color: @hc-color-title;
  }

  .hd-txt {
    color: @hc-color-text;
  }

  .pop-bd {
    padding: 30rpx 0;

    image {
      width: 100%;
    }
  }

  .pop-ft {
    font-size: 30rpx;
    color: @hc-color-text;
  }
}

// 弹窗样式
.desc-modal-mask {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  .desc-modal-box{
    padding: 48rpx 24rpx;
    width: 670rpx;
    border-radius: 8rpx;
    background: linear-gradient(180deg, #2D666F 0%, #FFF 100%);
  }
  
  .desc-modal, .desc-modal-new {
    border-radius: 8rpx;
    background-color: @hc-color-white;
    .code-tips{
      padding-bottom: 32rpx;
      text-align: center;
      font-size: 22rpx;
      color: rgba(0, 0, 0, 0.40);
    }

    .input {
      padding: 0 40rpx 40rpx 40rpx;
    }

    .desc-title {
      text-align: center;
      height: 116rpx;
      padding: 32rpx 0;
      font-size: 34rpx;
      font-weight: 600;
      box-sizing: border-box;
      color: @hc-color-title;
    }

    .desc-content {
      width: 100%;
      max-height: 700rpx;
      min-height: 200rpx;
      box-sizing: border-box;
      padding: 32rpx 32rpx 0;
      color: @hc-color-info;
      font-size: 34rpx;
      .code-name{
        margin-bottom: 32rpx;
        color: #050505;
        font-size: 48rpx;
        font-weight: 600;
      }
    }

    .phone {
      color: @hc-color-assist;
    }
    
  }
  .desc-footer {
    margin-top: 40rpx;
    padding: 24rpx;
    border-radius: 49rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    >view {
      font-size: 34rpx;
      font-weight: 600;
      color: @hc-color-white;
      text-align: center;

      &+view {
        border-left: 2rpx solid @hc-color-border;
      }
    }

    .agree {
      color: @hc-color-primary;
    }
  }
  
  .desc-modal-new{
    width: 670rpx;
    .desc-content{
      padding-top: 0;
      color: @hc-color-text;
    }
    .fzlj{
      margin-top: 40rpx;
      padding: 24rpx;
      border-radius: 49rpx;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      font-size: 34rpx;
      font-weight: 600;
      color: @hc-color-white;
      text-align: center;
    }
    .desc-footer{
      padding: 0;
      border-radius: 0;
      background: #fff;
      height: 116rpx;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      border-top: 1rpx solid @hc-color-border;
      >view {
        flex: 1;
        font-size: 34rpx;
        font-weight: 600;
        color: @hc-color-title;
        text-align: center;
        padding: 32rpx 0;

        &+view {
          border-left: 1rpx solid @hc-color-border;
        }
      }

      .agree {
        color: @hc-color-primary;
      }
    }
  }
}

.tab-wrap {
  margin: 40rpx 32rpx;
  border-radius: 24rpx;
  height: 80rpx;
  display: flex;
  font-size: 32rpx;
  font-weight: 400;
  align-items: center;
  color: @hc-color-title;
  background-color: @hc-color-bg;

  .tab-item {
    flex: 1;
    line-height: 80rpx;
    text-align: center;

    &:first-child {
      border-radius: 24rpx 0 0 24rpx;
    }

    &:last-child {
      border-radius: 0 24rpx 24rpx 0;
    }

    &.active {
      color: @hc-color-white;
      font-weight: 600;
      background-color: @hc-color-primary;
    }
  }
}