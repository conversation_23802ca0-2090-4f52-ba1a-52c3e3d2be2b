@import "../../../../resources/style/mixins";

page{
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.header {
  display: flex;
  justify-content: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: @hc-color-white;

  .range {
    width: 200rpx;
    text-align: center;
  }
}
.record-list {
  .wgt-folding-tit {
    background: @hc-color-white;
    border: 2rpx solid @hc-color-border;
  }
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    font-weight: normal;
    border-bottom: 2rpx solid #E8E8E8;
    > view {
      flex: 1;
    }
  }
  .item-content {
    padding: 20rpx 30rpx;
    background: #ffffff;
    color: #000000;
    margin-bottom: 10rpx;
  }
}

.wgt-folding{
  .wgt-folding-tit{
    position: relative;
    padding:0 30rpx;
    font-weight:600;
    font-size:34rpx;
    color:#000000;
  }
  .wgt-folding-expand{
    position: absolute;
    right: 30rpx;
    top:50%;
    transform: translateY(-50%) rotate(-180deg);
    width: 24rpx;
    line-height: 0;
    image{
      width: 24rpx;
      vertical-align: top;
    }
    &.active{
      transform: translateY(-50%);
    }
  }
  .wgt-folding-content{

  }
}
