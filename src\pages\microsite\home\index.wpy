<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import NavBar from "@/components/navbar/index";
  import NavTab from '@/components/navtab/index';


  export default class Home extends wepy.page {
    config = {
      navigationBarTitleText: '医院信息',
      navigationStyle: 'custom',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      "nav-bar": NavBar,
      'nav-tab': NavTab,
    };

    onLoad(options) {
      this.getHisInfo();
    }

    data = {
      hisInfo: {},
      hisImgBannerHeight: 0, // 图片占位高度
      hisImgHeight: 0, // 图片高度
      // tab类型
		  navTabType: 'microsite',
    };

    methods = {
      navigateToMap() {
        wx.getLocation({
          type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
          success: function (res) {
            // success
            wx.openLocation({
              latitude: 28.21385, // 纬度，范围为-90~90，负数表示南纬
              longitude: 112.983725, // 经度，范围为-180~180，负数表示西经
              scale: 28, // 缩放比例
              name:"湖南家辉遗传专科医院",
              address:"湖南省长沙市开福区湘雅路街道湘雅路72号"
            })
          }
        })
      },
      makePhoneCall(e) {
        const { phone } = e.target.dataset;
        if(phone){
          wepy.makePhoneCall({ phoneNumber: phone });
        }
      },
      navigateTo(e) {
        console.log(e, '======60')
        const { url, type } = e.currentTarget.dataset;
        if (url) {
          if (type == '1') {
            wepy.navigateTo({
              url: `/pages/webview/index?pid=&weburl=${url}`
            });
          } else {
            wepy.navigateTo({ url });
          }
          return false;
        } else {
          wepy.showModal({
            title: '提示',
            content: '功能开发中，敬请期待...',
            showCancel: false,
            confirmText: '确定',
          });
          return;
        }
      },
      preview(url){
        if(url){
          wepy.previewImage({
            urls: [url],
          });
        }
      },
      goBack() {
        this.$back(1);
      }
    };

    async getHisInfo() {
      const { data = {}, code } = await Api.getHisInfo();
      if(code == 0){
        const { address } = data;
        const ad = address ? address.split('】') : [];
        this.hisInfo = data;
        this.$apply();
      }
    };

  }
</script>
