@import '../../../../resources/style/mixins.less';
page {
  height: 100%;
  background: #424242;
  .title {
    padding: 227rpx 30rpx 45rpx;
    font-size: 42rpx;
    font-weight: 600;
    color: #ffffff;
    line-height: 59rpx;
    text-align: center;
  }
  .describe {
    text-align: center;
    font-size: 34rpx;
    color: #ffffff;
    line-height: 48rpx;
    margin-bottom: 70rpx;
  }
  .card {
    margin: 0 auto;
    width: 690rpx;
    padding: 30rpx 0 38rpx;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 10rpx;
    .card-title {
      text-align: center;
      font-size: 34rpx;
      font-weight: 600;
      color: @hc-color-title;
      line-height: 48rpx;
    }
    .card-info {
      padding: 28rpx 30rpx;
      background: #fafafa;
      display: flex;
      align-items: center;
      image {
        height: 100rpx;
        width: 100rpx;
        margin-right: 20rpx;
        border-radius: 50rpx;
      }
      .info-text {
        flex: 1;
        .text-title {
          font-size: 32rpx;
          font-weight: 600;
          color: @hc-color-title;
          margin-bottom: 5rpx;
        }
        .text-des {
          font-size: 26rpx;
          color: @hc-color-text;
        }
      }
      .btn-small {
        width: 110rpx;
        height: 60rpx;
        background: @hc-color-primary;
        border-radius: 10rpx;
        font-size: 30rpx;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .card-content {
      padding: 22rpx 30rpx 0;
      font-size: 30rpx;
      color: #666666;
      line-height: 42rpx;
      .ellipsisLn(4);
    }
  }
  .close {
    height: 70rpx;
    width: 70rpx;
    margin-top: 110rpx;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
}
