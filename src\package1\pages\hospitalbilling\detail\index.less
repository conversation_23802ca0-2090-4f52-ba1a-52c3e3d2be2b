@import '../../../../resources/style/mixins';
.p-page {
  background-color: #f6f7f9;

  .card-info {
    margin-bottom: 24rpx;
  }
  .form-info {
    background: #ffffff;
    padding: 32rpx 32rpx 8rpx;
    .title {
      color: rgba(0, 0, 0, 0.9);
      font-weight: 600;
      font-size: 28rpx;
    }
    .choose-img {
      display: flex;
      flex-wrap: wrap;
      margin-top: 32rpx;
      .img-item {
        width: 212rpx;
        height: 212rpx;
        margin-bottom: 24rpx;
        position: relative;
        margin-right: 16rpx;
        &:nth-child(3n + 3) {
          margin-right: -16rpx;
        }
        > image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .btn {
    padding: 48rpx 32rpx;

    > view {
      background-color: #3eceb6;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      border-radius: 16rpx;
      color: #fff;
      font-weight: 600;
      font-size: 34rpx;
    }
    .cancel {
      background-color: rgba(0, 0, 0, 0.04);
      color: rgba(0, 0, 0, 0.7);
    }
  }
}
