<!--  -->
<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveDetailMixin from '@/mixins/live/detailMixin';
import DoctorInfo from '../comm/doctorInfo';

import * as Api from './api';
export default class LiveHome extends wepy.page {
  config = {
    navigationBarTitleText: '直播详情'
  };
  // 在mixin中处理了对直播详情的请求
  mixins = [LiveDetailMixin];
  data = {
    id: '',
  };

  components = {
    'doctor-info': DoctorInfo
  };

  methods = {
    onSetConcern() {
      this.reqConcernLive();
    },
    onCancelConcern() {
      this.reqUnConcernLive();
    }
  };

  events = {};

  watch = {};

  computed = {};

  onLoad(options) {
    const { id } = options;
    this.id = id;
  }

  onShow() {}
  async reqConcernLive() {
    const { code, data } = await Api.concernLive({ liveId: this.id });
    if (code == 0) {
      wx.showToast({
        title: '设置成功', //提示的内容,
        icon: 'success', //图标,
        duration: 2000, //延迟时间,
      });
      this.mixinGetLiveDetail(this.id);
    }
  }
  async reqUnConcernLive() {
    const { code, data } = await Api.unConcernLive({ liveId: this.id });
    if (code == 0) {
      wepy.showToast({
        title: '取消成功', //提示的内容,
        icon: 'success', //图标,
        duration: 2000, //延迟时间,
      });
      this.mixinGetLiveDetail(this.id);
    }
  }
}
</script>
