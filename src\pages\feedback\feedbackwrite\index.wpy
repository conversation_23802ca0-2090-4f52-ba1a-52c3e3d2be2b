<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import { uploadFile } from "@/utils/request";
import * as Api from "./api";
import * as Utils from "@/utils/utils";

export default class ForzenSemen extends wepy.page {
  config = {
    navigationBarTitleText: "意见反馈",
    navigationBarBackgroundColor: '#fff'
  };

  data = {
    choosetypeValue: "",
    feedbackName: "",
    feedbackDesc: "",
    imageObj: [],
    pid: "",
    chooseIndex: null,
    suggestTypeList: ['投诉', '建议']
  };

  onLoad(options) {
    this.pid = options.pid;
  }
  onShow() {}
  methods = {
    pickerChange(e) {
      if(e.detail.value === '0'){
        this.chooseIndex = 0;
        this.choosetypeValue = "1";
      }else{
        this.chooseIndex = 1;
        this.choosetypeValue = "2";
      }
    },
    getFeebackName(e) {
      this.feedbackName = e.detail.value;
      this.$apply();
    },
    getFeebackDesc(e) {
      this.feedbackDesc = e.detail.value;
      this.$apply();
    },
    actionSheetTap(index = 0, phototype = "") {
      wx.showActionSheet({
        itemList: ["删除"],
        success: res => {
          this.removeItem({ phototype, index });
        },
        fail: res => {
          console.log(res.errMsg);
        }
      });
    },
    previewImage(item) {
      wepy.previewImage({
        urls: this.imageObj || [],
        current: item
      });
    },
    delete(url) {
      const urls = this.imageObj.filter(i => i !== url);
      this.imageObj = urls;
      this.$apply();
    },
    async chooseImage() {
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 1,
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"]
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths[0];
        wepy.showLoading({ title: "发送中...", mask: true });
        const { code, data } = await uploadFile(
          "/api/files/uploadpic",
          tempFilePath
        );
        if (code === 0) {
          this.imageObj = this.imageObj.concat(data.url);
          this.$apply();
        } else {
          wx.showToast({
            title: "图片上传失败",
            icon: "none",
            duration: 1400
          });
        }
      }
    },
    submitFeedBack() {
      let imgs = "";
      const {
        choosetypeValue,
        feedbackName,
        feedbackDesc,
        imageObj,
        pid
      } = this;
      if (imageObj.length > 0) {
        imgs = imageObj.join(",");
      }
      const params = {
        pid,
        feedbackType: choosetypeValue,
        feedbackObject: feedbackName,
        feedbackContent: feedbackDesc,
        feedbackImg: imgs
      };
      this.subFeedBack(params);
    }
  };
  async subFeedBack(params) {
    if (!params.feedbackType) {
      wepy.showModal({
        title: "温馨提示",
        content: "请选择反馈类型",
        showCancel: false
      });
      return;
    }
    if (!params.feedbackContent) {
      wepy.showModal({
        title: "温馨提示",
        content: "请选择反馈内容",
        showCancel: false
      });
      return;
    }
    wepy.showLoading({ title: "提交中...", mask: true });
    const { code, data } = await Api.getFeedSave(params);
    wepy.hideLoading();
    if (code === 0) {
      wepy.showToast({
        title: "提交成功",
        icon: "success"
      });
      setTimeout(() => {
        wepy.navigateTo({
          url: `/pages/feedback/feedbackdetail/index?id=${data.id}`
        });
      }, 1000);
    } else {
      await wepy.showModal({
        title: "提示",
        content: data.resultMessage || "提交失败",
        showCancel: false
      });
    }
  }
}
</script>
