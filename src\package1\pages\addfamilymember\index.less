@import "../../../resources/style/mixins.less";

page {
  width: 100%;
  overflow-x: hidden;
  background: #f2f4f4;
}

.container {
  // height: 100vh;
  // min-height: 100vh;
  // background-color: #fff;
  overflow-y: auto;
}

.form-container {
  background-color: #fff;
  border-radius: 8rpx;
  margin: 0 24rpx;

  input {
    height: 80rpx;
    line-height: 80rpx;
    color: rgba(0, 0, 0, 0.7);
    font-size: 32rpx;
    padding-left: 32rpx;
    border-radius: 16rpx;
    &:focus {
      border: 1px solid #3eceb6;
    }
  }
}

.patInfo-card {
  position: relative;
  margin-bottom: 24rpx;
  background-color: #fff;
  .patInfo-card-top {
    padding: 40rpx 24rpx;
  }
  .patInfo-card-info {
    position: relative;
    padding-bottom: 8rpx;
    .info-name {
      font-size: 40rpx;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
    }
    .info-other {
      color: #2d2d2d;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 42rpx;
      padding-right: 30rpx;
    }
  }

  .patInfo-card-other {
    color: rgba(0, 0, 0, 0.4);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 42rpx;
    .birthday-box{
      margin: 0 20rpx;
    }
    // padding-bottom: 10rpx;
  }
}

.patInfo-list {
  // background: #fff;
  .patInfo-listitem {
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    position: relative;

    &.patInfo-listitem_none {
      display: none;
    }

    // &:first-child:before {
    //   display: none;
    // }

    &.no-after:after {
      display: none;
    }

    .picker-info{
      height: 80rpx;
      line-height: 80rpx;
      color: rgba(0, 0, 0, 0.7);
      font-size: 32rpx;
      padding-left: 32rpx;
    }
    .placeholder-text{
      color: rgba(0, 0, 0, 0.5);
    }

    .listitem-head {
      display: flex;
      align-items: center;
      padding-bottom: 24rpx;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.70);
        font-weight: 500;
        line-height: 1;
        padding-right: 12rpx;
        position: relative;

        &.require {
          position: relative;
          &::before {
            content: "*";
            color: #f76260;
            font-size: 30rpx;
          }
        }

        &.list-title_select:before {
          content: "切换证件类型";
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          font-size: 32rpx;
          color: @hc-color-primary;
          // border-bottom: 10rpx solid @hc-color-title;
          // border-right: 10rpx solid @hc-color-title;
          // border-top: 10rpx solid transparent;
          // border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body {
      flex: 1;
      // padding-left: 30rpx;
      position: relative;
      border-radius: 8rpx;
      border: 2rpx solid rgba(0, 0, 0, 0.20);
      .picker {
        display: flex;
        align-items: center;
        .picker-info {
          flex: 1;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.70);
          font-weight: 400;
          line-height: 1;
        }
        .item-arrow {
          width: 17rpx;
          height: 17rpx;
          border-right: 5rpx solid rgba(0, 0, 0, 0.2);
          border-bottom: 5rpx solid rgba(0, 0, 0, 0.2);
          -webkit-transform: translateX(-32rpx) rotate(-45deg);
          transform: translateX(-32rpx) rotate(-45deg);
        }
      }

      .textBreak();
    }
    .patInfo-add {
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #3eceb6;
      line-height: 42rpx;
      text-align: center;
    }
  }

  .listitem_accest {
    color: red;
  }

  .listitem_accest .listitem-body:before {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.patInfo-part {
  padding: 32rpx 32rpx 2rpx;
  // background-color: #f5f5f5;

  .list-title {
    font-size: 32rpx;
    font-weight: 500;
    &.require {
      position: relative;
      &:after {
        content: "*";
        color: #f76260;
        font-size: 28rpx;
      }
    }
  }
}

.patInfo-tips {
  padding: 20rpx 40rpx;
  color: #989898;
  background-color: #f5f5f5;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.patInfo-btn {
  margin: 40rpx;
}
.binduser-btn_line {
  border-radius: 76rpx;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #fff;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  //padding: 22rpx 0;
}
.cancel-btn_line {
  border-radius: 76rpx;
  background: rgba(0, 0, 0, 0.04);
  color: @hc-color-text;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  //padding: 22rpx 0;
}

.m-delete-member {
  // justify-content: center;
  align-items: flex-end;
  // border-bottom: 2rpx solid @hc-color-border;
  color: red;
}

.binduser-radio {
  display: inline-block;
  width: 150rpx;
  margin-left: 10rpx;
  &:first-child {
    margin-left: 0;
  }
}
.binduser-radio_object {
  transform-origin: 0 30%;
  transform: scale(0.7);
}
.binduser-radio_text {
  font-size: 30rpx;
}
button::after {
  border: none;
}
