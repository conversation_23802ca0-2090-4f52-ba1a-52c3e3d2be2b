<block wx:if="{{config.infoShow}}">
  <view class="wgt-user-box">
    <view class="wgt-user-main">
      <view class="wgt-user-main-info">
        <view class="wgt-user-main-info-tit">{{patient.activePatient.inpatientName}}</view>
        <view class="wgt-user-main-info-label"></view>
      </view>
      <view class="wgt-user-main-btn" @tap="bindShow">切换住院人</view>
    </view>
    <view class="wgt-user-extra">住院号：{{patient.activePatient.admissionNum || patient.activePatient.patCardNo}}</view>
    <view class="wgt-user-extra" wx:if="{{patient.activePatient.deptName}}">住院科室：{{patient.activePatient.deptName}}</view>
    <view class="wgt-user-extra"
      wx:if="{{patient.activePatient.admissionWard || patient.activePatient.bedNo}}"
    >床位：{{patient.activePatient.admissionWard}}·{{patient.activePatient.bedNo}}</view>
    <view class="wgt-user-extra" wx:if="{{patient.activePatient.doctorName}}">病床医生：{{patient.activePatient.doctorName}}</view>
  </view>
</block>

<view class="wgt-user-pop-box {{config.show ? 'active' : ''}}">
  <view class="wgt-user-pop-mask"></view>
  <view class="wgt-user-pop">
    <view class="wgt-user-pop-title">切换住院人</view>
    <view class="wgt-user-pop-list">
      <block
        wx:for="{{patient.list || []}}"
        wx:key="index"
      >
        <view
          class="wgt-user-pop-list-item"
          @tap="bindChangeUser({{item}})"
        >
          <view class="wgt-user-pop-list-item-main">
            <view class="wgt-user-pop-list-item-name">{{item.inpatientName}}</view>
            <view class="wgt-user-pop-list-item-label"></view>
          </view>
          <view class="wgt-user-pop-list-item-num">住院号：{{item.admissionNum || item.patCardNo}}</view>
          <view class="wgt-user-pop-list-item-ipt {{item.inpatientId === patient.activePatient.inpatientId ? 'active' : ''}}">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/user-icon-checked.png"></image>
          </view>
        </view>
      </block>
    </view>
    <view class="wgt-user-pop-opt">
      <block wx:if="{{patient.leftAppendNum > 0}}">
        <view
          class="wgt-user-pop-opt-item"
          @tap="bindGoBindUser"
        >添加住院人</view>
      </block>
      <view
        class="wgt-user-pop-opt-item"
        @tap="bindGoUserList"
      >管理住院人</view>
    </view>
    <view class="wgt-user-pop-close" @tap="bindClose"></view>
  </view>
</view>