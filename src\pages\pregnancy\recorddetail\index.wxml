<view class="p-page">
	<detail-status :config.sync="statusConfig">
		<block slot="title">{{statusConfig.statusName}}</block>
		<block slot="text">
			<view>{{statusConfig.text}}</view>
		</block>
	</detail-status>
	<view class="m-list" wx:if="{{detailData.fcheckDate}}">
		<view class="list">
			<view class="list-item" wx:if="{{detailData.fcheckDate}}">
				<view class="item-label">初审时间</view>
				<view class="item-value">{{detailData.fcheckDate}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.scheckDate}}">
				<view class="item-label">复审时间</view>
				<view class="item-value">{{detailData.scheckDate}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.fcheckReason}}">
				<view class="item-label">描述</view>
				<view class="item-value">{{detailData.fcheckReason}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.scheckReason}}">
				<view class="item-label">描述</view>
				<view class="item-value">{{detailData.scheckReason}}</view>
			</view>
		</view>
	</view>
	<view class="m-list">
		<view class="list-tit">申请信息</view>
		<view class="list">
			<view class="list-item" wx:if="{{detailData.applicantName	}}">
				<view class="item-label">申请人</view>
				<view class="item-value">{{detailData.applicantName }}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.projectName}}">
				<view class="item-label">申请项目</view>
				<view class="item-value">{{detailData.projectName}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.createTime}}">
				<view class="item-label">申请时间</view>
				<view class="item-value">{{detailData.createTime}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.cancelDate}}">
				<view class="item-label">取消时间</view>
				<view class="item-value">{{detailData.cancelDate}}</view>
			</view>
		</view>
	</view>
	<view class="m-list">
		<view class="list-tit">基本信息</view>
		<view class="list">
			<view class="list-item" wx:if="{{detailData.womanName}}">
				<view class="item-label">女方姓名</view>
				<view class="item-value">{{detailData.womanName}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.womanAge}}">
				<view class="item-label">年龄</view>
				<view class="item-value">{{detailData.womanAge}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.womanIdNo}}">
				<view class="item-label">身份证号</view>
				<view class="item-value">{{tuoMingWonmentIdNo}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.manName}}">
				<view class="item-label">男方姓名</view>
				<view class="item-value">{{detailData.manName}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.manAge}}">
				<view class="item-label">年龄</view>
				<view class="item-value">{{detailData.manAge}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.manIdNo}}">
				<view class="item-label">身份证号</view>
				<view class="item-value">{{tuoMingmanIdNo}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.address}}">
				<view class="item-label">联系地址</view>
				<view class="item-value">{{detailData.address}}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.womanMobile	}}">
				<view class="item-label">联系电话（女方）</view>
				<view class="item-value">{{detailData.womanMobile }}</view>
			</view>
			<view class="list-item" wx:if="{{detailData.manMobile	}}">
				<view class="item-label">联系电话（男方）</view>
				<view class="item-value">{{detailData.manMobile }}</view>
			</view>
		</view>
	</view>
	<view class="m-list">
		<view class="list-tit">上传的资料</view>
		<view class="photo-image">
			<view class="photo-image-title">
				上传的女方身份证正反面照片
			</view>
			<view class="m-upload-list">
				<block wx:for="{{imageObj[1]}}" wx:key="index">
					<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'idCard')">
						<image
						 class="m-upload-image"
						 src="{{item.filePath}}"
						 @tap="previewImage({{item}}, 'idCard')"
						/>
						<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" />
					</view>
				</block>
			</view>
		</view>
		<view class="photo-image">
			<view class="photo-image-title">
				上传的结婚证照片
			</view>
			<view class="m-upload-list">
				<block wx:for="{{imageObj[2]}}" wx:key="index">
					<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'marry')">
						<image
						 class="m-upload-image"
						 src="{{item.filePath}}"
						 @tap="previewImage({{item}}, 'marry')"
						/>
						<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" />
					</view>
				</block>
			</view>
		</view>
		<view class="photo-image">
			<view class="photo-image-title">
				{{detailData.otherIntroduceName}}
			</view>
			<view class="m-upload-list">
				<block wx:for="{{imageObj[3]}}" wx:key="index">
					<view class="m-upload-item" @longpress="actionSheetTap({{index}}, 'other')">
						<image
						 class="m-upload-image"
						 src="{{item.filePath}}"
						 @tap="previewImage({{item}}, 'other')"
						/>
						<view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" />
					</view>
				</block>
			</view>
		</view>

		<block wx:if="{{detailData.ouContentName}}">
			<view class="ouContent-info">
				<rich-text nodes="{{detailData.ouContentName}}" />
				<view style="margin-top:10px">{{detailData.ouContentInfo}}</view>
			</view>
		</block>
	</view>
	<view class="m-list-button">
		<button
		 wx:if="{{detailData.status === '0' || detailData.status === '1'}}"
		 class="button"
		 @tap="canelApply"
		>取消申请
		</button>
	</view>
	<view class="m-list-button">
		<button
		 wx:if="{{detailData.status === '6' || detailData.status === '3' || detailData.status === '5'}}"
		 class="button-primary"
		 @tap="retrayApply"
		>重新申请
		</button>
	</view>
</view>

