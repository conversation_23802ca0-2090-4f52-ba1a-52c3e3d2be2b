import { REQUEST_QUERY } from "@/config/constant";
import { post } from "@/utils/request";

export const doctorDetail = (param) =>
  post("/api/register/doctordetail", param);

export const addFav = (param) => post("/api/favorite/addmyfavorite", param);

export const cancelFav = (param) =>
  post("/api/favorite/cancelmyfavorite", param);

export const getQrCode = (param) => post("/api/qrcode/wxqrcode", param);

export const dateScheduleList = (param) =>
  post(
    `/api/register/dateschedulelist?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const scheduleList = (param) =>
  post(`/api/register/schedulelist?_route=h${REQUEST_QUERY.platformId}`, param);

export const registerConfirm = (param) =>
  post("/api/register/registerconfirm", param);

export const generatorOrder = (param) =>
  post("/api/register/generatororder", param);

export const registerPayOrder = (param) =>
  post("/api/register/registerpayorder", param);

export const getRelationList = (param) =>
  post(
    `/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const confirmWatiQueue = (param) =>
  post(
    `/api/customize/confirmWatiQueue?_route=h${REQUEST_QUERY.platformId}`,
    param
  );
