<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Empty from "@/components/empty/index";
// import NavTab from "@/components/navtab/index";
import { PRIMARY_COLOR } from "@/config/constant";
import { getVisitTime, formatMoney } from "@/utils/utils";
import WxsUtils from "../../wxs/utils.wxs";
import * as Api from "./api";

const URL_MAP = {
  1: "/pages/register/recorddetail/index", //就诊提醒
  2: "/pages/register/recorddetail/index", //锁号待支付提醒
  // 3: "/pages/takeno/index/index", //取号提醒
  4: "/pages/survey/surveydetail/index", //满意度调查提醒
  5: "/pages/report/reportlist/index", //检验报告发布提醒
  6: "/pages/report/reportlist/index", //检查报告发布提醒
  7: "/pages/treat/untreatlist/index", //门诊待缴费提醒
  // 8: "/pages/recharge/amount/index", //就诊卡余额不足补缴提醒
  9: "/pages/todo/noticeDetail/index" // 医生发送通知
  // 9: "/pages/inpatient/amount/index", //住院押金余额不足补缴提醒
  // 10: "/pages/queue/queue/index" //排队提醒
};

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "消息中心",
    navigationBarBackgroundColor: '#fff',
  };

  wxs = {
    WxsUtils: WxsUtils
  };
  components = {
    // "nav-tab": NavTab,
    empty: Empty
  };

  onLoad(options) {
    this.getNoticeList();
    this.getTodoList();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    notices: [],
    todoList: [],
    emptyConfig: {
      show: true
    },
    showId: "",
    // tab类型
    navTabType: "msg",
    // 是否展开
    isExpandNotice: true,
    isExpandTodo: true,
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    longtap(id) {
      this.showId = id;
    },
    hidden() {
      this.showId = "";
    },
    async deleteTodo(id) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "确定删除该待办吗？",
        cancelText: "暂不删除",
        confirmText: "确定删除",
        cancelColor: "#989898",
        confirmColor: PRIMARY_COLOR
      });
      if (showModalRes.cancel) {
        this.showId = "";
        this.$apply();
        return;
      }
      const { code, data = {}, msg } = await Api.deleteTodo({ id });
      if (code == 0) {
        await wepy.showToast({
          title: "删除成功",
          icon: "success"
        });
        const timer = setTimeout(() => {
          clearTimeout(timer);
          this.getNoticeList();
          this.getTodoList();
        }, 2000);
      }
      this.$apply();
    },
    handleToggleExpand(prop) {
      if (!prop) return;
      this[prop] =  !this[prop];
    }
  };
  events = {};

  getDetail(item) {
    const { msgType } = item;
    let { detail } = item;
    if (msgType != "9") {
      detail = JSON.parse(detail || "{}");
    }
    console.log(detail);
    let detailObj = {};
    if (msgType === "1" || msgType === "3") {
      // 就诊提醒 取号提醒
      const {
        visitDate,
        visitPeriod,
        visitBeginTime,
        visitEndTime,
        deptName,
        doctorName,
        orderId
      } = detail;
      const visitTime = getVisitTime({
        visitDate,
        visitPeriod,
        visitBeginTime,
        visitEndTime
      });
      if (msgType === "1") {
        // 就诊提醒
        detailObj = {
          text: `您预约了${visitTime}${deptName}${doctorName}，请及时前往医院就诊`,
          url: `${URL_MAP[msgType]}?orderId=${orderId}`
        };
      } else if (msgType === "3") {
        // 取号提醒
        detailObj = {
          text: `您预约了${visitTime}${deptName}${doctorName}，请及时取号`,
          url: `${URL_MAP[msgType]}?orderId=${orderId}`
        };
      }
    } else if (msgType === "2") {
      // 锁号提醒
      const { deptName, doctorName, orderId } = detail;
      detailObj = {
        text: `${deptName}${doctorName}医生的在线挂号等待支付，请在有效时间内完成支付`,
        url: `${URL_MAP[msgType]}?orderId=${orderId}`
      };
    } else if (msgType === "4") {
      // 满意度调查提醒
      const { orderId, examId } = detail;
      detailObj = {
        text: "请您为医院就医体验进行评价",
        url: `${URL_MAP[msgType]}?orderId=${orderId}&id=${examId}`
      };
    } else if (msgType === "5" || msgType === "6") {
      // 检查检验报告发布提醒
      const { patientId } = detail;
      detailObj = {
        text: "您的报告已发布，请及时查看报告结果",
        url: `${URL_MAP[msgType]}?patientId=${patientId}`
      };
    } else if (msgType === "7") {
      // 门诊待缴费提醒
      const { patientId } = detail;
      detailObj = {
        text: "您有一笔门诊缴费等待支付",
        url: `${URL_MAP[msgType]}?patientId=${patientId}`
      };
    } else if (msgType === "9") {
      detailObj = {
        text: item.title,
        url: `${URL_MAP[msgType]}?todoId=${item.id}`,
        noIcon: true
      };
    }
    //  else if (msgType === "8") {
    //   // 就诊卡余额不足补缴提醒
    //   const { patientId } = detail;
    //   detailObj = {
    //     text: "您的就诊卡余额不足，请及时充值",
    //     url: `${URL_MAP[msgType]}?patientId=${patientId}`
    //   };
    // }
    // else if (msgType === "9") {
    //   // 住院押金余额不足补缴提醒
    //   const { needAmount, patientId } = detail;
    //   detailObj = {
    //     text: `您的住院押金余额不足${formatMoney(
    //       needAmount,
    //       100
    //     )}元，请及时补缴`,
    //     url: `${URL_MAP[msgType]}?patientId=${patientId}`
    //   };
    // }
    // else if (msgType === "10") {
    //   // 排队提醒
    //   const { patientId } = detail;
    //   detailObj = {
    //     text: "马上就要排到您的号了，请及时前往科室/药房",
    //     url: `${URL_MAP[msgType]}?patientId=${patientId}`
    //   };
    // }
    return detailObj;
  }

  /**
   * 获取重要通知
   */
  async getNoticeList() {
    const { code, data = {}, msg } = await Api.getNoticeList();
    if (code != 0) {
      return;
    }
    const { notices = [] } = data;
    notices.map((item, id) => {
      if (item.msgType == 8) {
        notices.splice(id, 1);
      }
    });
    this.notices = notices;
    if (notices.length > 0 || this.todoList.length > 0) {
      this.emptyConfig.show = false;
    }
    if (this.todoList.length <= 0 && notices.length <= 0) {
      this.emptyConfig.show = true;
    }
    this.$apply();
  }

  /**
   * 获取待办消息
   */
  async getTodoList() {
    const { code, data = {}, msg } = await Api.getTodoList();
    if (code != 0) {
      return;
    }
    const { recordList = [] } = data;
    if (recordList.length > 0 || this.notices.length > 0) {
      this.emptyConfig.show = false;
    }
    if (recordList.length <= 0 && this.notices.length <= 0) {
      this.emptyConfig.show = true;
    }
    console.log(recordList);
    this.todoList = recordList.map((item,id) => {
      if (item.msgType == 8) {
        recordList.splice(id, 1);
      }
      const detail = this.getDetail(item);
      return {
        ...item,
        ...detail
      };
    });
    this.$apply();
  }

  /**
   * 阅读消息
   */
  async updateReadFlag() {
    const { code, data = {}, msg } = await Api.updateReadFlag();
    if (code != 0) {
      return;
    }
    this.$apply();
  }
}
</script>
