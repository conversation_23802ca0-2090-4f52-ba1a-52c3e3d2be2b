<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { PRIMARY_COLOR } from '@/config/constant';


  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '',
    };

    data = {
      weburl: '',
      patHisNo: '',
    }

    onLoad(options){
      this.getCardList(options.type);
      this.$apply();
    }

    onShow(){
    }

    onUnload() {
      wepy.redirectTo({
        url: '/pages/home/<USER>',
      });
    }

    async getCardList(type){
      const { data = {} } = await Api.getCardList();
      const { cardList = [] } = data;
      this.cardList = cardList;
      let isLogin = false;
      if (cardList.length > 0) {
        cardList.forEach((item) => {  
          if (item.relationType == 1) {
            isLogin = true;
            this.weburl = type!='home'?`https://ssyy.zxxyyy.cn/#/operation/list?pid=${item.patHisNo}&grid=${item.patCardNo}`:`https://ssyy.zxxyyy.cn/#/facecheck/signCheck?pid=${item.patHisNo}&grid=${item.patCardNo}`;
          }
        })
      }
      if(!isLogin){
        const showModalRes = await wepy.showModal({
          title: '提示',
          content: '请登录后再操作',
          showCancel: false,
          confirmText: '确定',
          confirmColor: PRIMARY_COLOR,
        });
        if (showModalRes.confirm) {
          wepy.redirectTo({
            url: '/pages/bindcard/index/index',
          });
        }
      }
      this.$apply();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      weburl: '',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
