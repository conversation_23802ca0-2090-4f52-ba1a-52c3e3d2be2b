@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.m-title {
  margin: 24rpx 24rpx 0;
  padding: 24rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: @hc-color-title;
  background: @hc-color-white;
  border-radius: 8rpx 8rpx 0 0;
}

.m-card {
  padding: 0 32rpx;
  background-color: #fff;
  .card-info {
    padding: 40rpx 0 16rpx;
  }
  .info-main {
    display: flex;
    align-items: flex-start;
  }
  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .name {
    font-size: 40rpx;
    font-weight: bold;
  }
  .status {
    color: #2D666F;
    margin-left: 16rpx;
    border: none;
    padding: 4rpx 8rpx;
    font-size: 20rpx;
    border-radius: 4rpx;
    background: rgba(45, 102, 111, 0.10);
  }
  .info-extra {
    margin-top: 8rpx;
    color: rgba(0, 0, 0, 0.40);
    font-size: 28rpx;
  }
}

.m-qrcode {
  padding: 0 24rpx;
  margin: 0 24rpx;
  display: flex;
  justify-content: center;
  background: #fff;
  box-sizing: border-box;

  .qrimage {
    height: 176rpx !important;
    width: 100% !important;
  }
}

.m-userinfo {
  background-color: #fff;
  margin: 0 24rpx;
  border: 0 0 8rpx 8rpx;
  .userinfo-item {
    display: flex;
    font-size: 32rpx;
    padding: 0 24rpx;
    margin: 32rpx 0;
  }
  .item-tit {
    flex: 1;
    color: @hc-color-info;
    min-width: 6em;
  }
  .item-txt {
    color: rgba(0, 0, 0, 0.9);
    text-align: right;
  }
}

.m-list {
  padding: 0 32rpx 48rpx;
  width: 100%;
  box-sizing: border-box;
  bottom: 0;
  &.abs {
    position: absolute;
  }
  .list-item {
    display: block;
    padding-left: 30rpx;
    background-color: #fff;
  }
  .item {
    display: flex;
    align-items: center;
    padding: 20rpx 0 20rpx 0;
    margin-right: 35rpx;
    border-bottom: 2rpx solid #f4f9f9;
    overflow: hidden;
    min-height: 56rpx;
  }
  .list-item:last-child {
    .item {
      border-bottom: none;
    }
  }
  .item-hd {
    flex: 1;
    color: @hc-color-title;
    font-size: 34rpx;
  }
  .item-bd {
    color: @hc-color-text;
    font-size: 30rpx;
    margin-left: 20rpx;
    position: relative;

    &.disabled:after {
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1;
      opacity: 0;
    }
  }
  .item-ft {
    margin-left: 25rpx;
    width: 15rpx;
    height: 15rpx;
    border-right: 4rpx solid #c2c9c9;
    border-bottom: 4rpx solid #c2c9c9;
    transform: translateX(-8rpx) rotate(-45deg);
  }
}

.m-btn {
  display: flex;
  .btn-item {
    font-weight: 600;
    font-size: 34rpx;
    border-radius: 16rpx;
    text-align: center;
    background-color: #fff;
  }
  .primary-btn {
    color: @hc-color-primary;
    flex: 1;
  }
  .default-btn {
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 76rpx;
    background: rgba(0, 0, 0, 0.04);
    color: @hc-color-text;
    flex: 1;
  }
}

.m-edit-block {
  margin: 0 32rpx 200rpx;
  padding: 0 32rpx;
  background: @hc-color-white;
  border-radius: 0 0 24rpx 24rpx;
  .m-edit-title {
    position: relative;
    background-color: @hc-color-white;
    text-align: center;
    color: @hc-color-link;
    font-size: 28rpx;
    border-top: 2rpx solid rgba(0, 0, 0, 0.08);
    padding: 32rpx;
  }
  .item-arrow {
    position: absolute;
    top: 42rpx;
    right: 50rpx;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    -webkit-transform: translateX(-8rpx) rotate(45deg);
    transform: translateX(-8rpx) rotate(45deg);
  }
}

.patInfo-list {
  background: @hc-color-white;
  margin: 0 32rpx;
  .patInfo-listitem {
    padding: 18rpx 32rpx;
    display: flex;
    position: relative;
    color: @hc-color-text;
    font-size: 32rpx;

    &.patInfo-listitem_none {
      display: none;
    }

    // &:before{
    //   content: ' ';
    //   position: absolute;
    //   left: 30rpx;
    //   right: 0;
    //   top: 0;
    //   border-top: 1px solid @hc-color-border;
    // }
    &:first-child:before {
      display: none;
    }

    .listitem-head {
      width: 200rpx;
      display: flex;
      align-items: center;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 32rpx;
        color: @hc-color-info;
        font-weight: 400;
        line-height: 1;
        padding-right: 12rpx;
        position: relative;

        &.require {
          position: relative;
          &:after {
            content: "*";
            color: #f76260;
            font-size: 30rpx;
          }
        }

        &.list-title_select:before {
          content: " ";
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          border-bottom: 10rpx solid @hc-color-title;
          border-right: 10rpx solid @hc-color-title;
          border-top: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body {
      flex: 1;
      padding-left: 30rpx;
      position: relative;
      text-align: right;
      .picker {
        display: flex;
        align-items: center;
        .picker-info {
          flex: 1;
          font-size: 32rpx;
          color: @hc-color-text;
          font-weight: 400;
          line-height: 1;
        }
        .item-arrow {
          width: 17rpx;
          height: 17rpx;
          border-right: 5rpx solid #c7c7cc;
          border-bottom: 5rpx solid #c7c7cc;
          -webkit-transform: translateX(-8rpx) rotate(-45deg);
          transform: translateX(-8rpx) rotate(-45deg);
        }
      }

      .textBreak();
    }
    .patInfo-add {
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #3eceb6;
      line-height: 42rpx;
      text-align: center;
    }
  }

  .listitem_accest {
    color: red;
  }

  .listitem_accest .listitem-body:before {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.patInfo-part {
  padding: 20rpx 30rpx;

  .list-title {
    &.require {
      position: relative;
      &:after {
        content: "*";
        color: #f76260;
        font-size: 30rpx;
      }
    }
  }
}

.patInfo-tips {
  padding: 20rpx 40rpx;
  color: #989898;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 37rpx;
}

.patInfo-btn {
  margin: 40rpx;
}
.binduser-btn_line {
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  //padding: 22rpx 0;
}
