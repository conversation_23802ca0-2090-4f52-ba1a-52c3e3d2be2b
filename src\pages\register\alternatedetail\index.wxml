<view class="p-page">
  <detail-status :config.sync="statusConfig" :leftTime.sync="leftTime">
    <block slot="title">{{statusConfig.statusName}}</block>
    <block slot="text">
      <view>{{statusConfig.text}}</view>
    </block>
  </detail-status>
  <!-- <view class="m-code {{detailData.status === 'S' ? 'active' : ''}}">
    <view class="code-tit">就诊凭条 </view>
    <view class="code-img">
      <block wx:if="{{detailData.patCardNo}}">
        <image
          mode="widthFix"
          src="https://wechat.jiahuiyiyuan.com/barcode?msg={{detailData.patCardNo}}&type={{codeType}}&mw=.60"
          alt=""
        ></image>
      </block>
    </view>
  </view> -->
  <basic-detail :detailData.sync="detailData"></basic-detail>
  <block wx:if="{{detailData.effectiveFlag === '0'}}">
    <view class="m-cancel">
      <view class="cancel-btn" @tap="bindCancelOrder">取消候补</view>
    </view>
  </block>
</view>
