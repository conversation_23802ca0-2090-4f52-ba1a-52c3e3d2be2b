<view>
  <view class="g-head">
    <view class="m-video-content">
        <video class="video-play"
          src="{{liveDetail.url}}"
          poster="{{liveDetail.banner}}"
          title="{{liveDetail.name}}"
          binderror="onVideoError"
          bindplay="onVideoPlay"
        ></video>
        <cover-view class="u-tt">{{liveDetail.name}}　</cover-view>
        <cover-view class="u-plays">播放数 : {{liveDetail.plays}}　</cover-view>
    </view>
    <view class="m-video-operation">
        <view class="u-item" @tap="likeToggle({{id}})">
            <image class="img-like" 
              src="{{isLike ? 'REPLACE_EHOSPITAL_DOMAIN/circle-praise-active.png' : 'REPLACE_EHOSPITAL_DOMAIN/circle-praise.png'}}" 
              lazy-load="false"
            />
            <text class="u-num {{isLike ? 'active' : ''}}">{{likes}}</text>
        </view>
        <view class="u-item" @tap="goComment">
            <image class="img-comments"
              src="REPLACE_EHOSPITAL_DOMAIN/circle-message.png" 
              lazy-load="false"
            />
            <text class="u-num">{{comments}}</text>
        </view>
        <!-- 点击触发转发 -->
        <!-- <button class="u-item btn-share" open-type="share">
            <image class="img-share"
              src="REPLACE_EHOSPITAL_DOMAIN/icon-share-rect.png" 
              lazy-load="false"/>
        </button> -->
    </view>
  </view>

  <doctor-info :info.sync="doctorInfo" />

  <view class="g-wrap-gray"></view>

  <view class="g-body" wx:if="{{liveDetail.type == 'video' || liveDetail.type == 'VIDEO'}}">
      <view class="comments-num">共{{comments}}条评论</view>
      <block wx:if="{{commentsList.length > 0}}"
        wx:for="{{commentsList}}"
        wx:key="{{item.id}}"
      >
        <!-- 评论 -->
        <view class="item comments-item">
          <text class="comments-name">{{item.fromName ? item.fromName : '**患者'}}</text><text class="content">: {{item.comment}}</text>
        </view>
        <!-- 回复 -->
        <block wx:if="{{item.replyComments.length > 0}}"
          wx:for="{{item.replyComments}}"
          wx:for-index="replyIndex"
          wx:for-item="replyItem"
          wx:key="{{replyItem.id}}"
        >
          <view class="item reply-item">
            <text class="reply-name">{{replyItem.fromName ? replyItem.fromName : '医生'}}</text><text class="content"> 回复 {{replyItem.toName}} : {{replyItem.comment}}</text>
          </view>
        </block>
      </block>

      <view class="loadMore"
        wx:if="{{comments > 0}}"
        hidden="{{!hasMoreData}}"
        @tap="getMoreComments({{id}}, {{pageNum+1}})"
      >更多评论</view>
  </view>

  <view class="g-foot-box" wx:if="{{liveDetail.type == 'video' || liveDetail.type == 'VIDEO'}}">
    <view class="g-foot">
      <input class="u-input"
        placeholder="请输入"
        confirm-type="send"
        placeholder-class="input-placeholder"
        bindconfirm="send({{id}}, {{inputValue}})"
        bindblur="onBlur"
        value="{{inputValue}}"
        bindinput="onInput"
        focus="{{focus}}"
        cursor-spacing="16"
      />
      <button class="u-btn" @tap="send({{id}}, {{inputValue}})">发送</button>
    </view>
  </view>
</view>