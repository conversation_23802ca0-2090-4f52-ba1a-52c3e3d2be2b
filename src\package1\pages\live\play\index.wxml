<image class="play-poster" wx:if="{{!liveUrl}}" src="{{doctorInfo.doctorImage || 'REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png'}}" mode="aspectFill" lazy-load="false">
</image>
<live-player wx:else class="play-box" src="{{liveUrl}}" mode="live" autoplay="true" object-fit="contain"
  @statechange="onPlayStateChange" @error="onPlayError" />
<cover-view class="doctor-info-box">
  <doctor-info :info.sync="doctorInfo" />
</cover-view>
<online-num :count.sync="plays" />
<chat-list :list.sync="barrages" />
<cover-view class="operate-box">
  <cover-view class="input-box">
    <cover-view wx:if="{{!inputFocus}}" class="input" @tap="onFocus">{{chatMsgText || '点击输入'}}</cover-view>
    <input wx:else class="input" focus="{{inputFocus}}" value="{{chatMsgText}}" placeholder="点击输入" placeholder-class="input-placeholder"
      @input="onInputMsg" @blur="onInputBlur" @confirm="onInputConfirm" cursor-spacing="15" confirm-type="send"
      maxlength="50" />
    <cover-image class="input-send" src="REPLACE_EHOSPITAL_DOMAIN/icon-live-send.png" @tap.stop="onSendMsg" />
  </cover-view>
  <!-- <button class="btn-share" open-type="share">
    <cover-image class="icon-share" src="REPLACE_EHOSPITAL_DOMAIN/icon-share.png" />
  </button> -->
</cover-view>
<cover-image class="btn-close" src="REPLACE_EHOSPITAL_DOMAIN/icon-close-grey.png" @tap="onQuit" />
<cover-view class="heart-box">
  <cover-image class="btn-heart" src="REPLACE_EHOSPITAL_DOMAIN/icon-live-heart.png" @tap="onSendHeart" />
  <!-- <cover-image class="heart-text" src="REPLACE_EHOSPITAL_DOMAIN/live-heart-text.png" @tap="onSendHeart" /> -->
  <cover-view class="heart-text" @tap="onSendHeart" >{{likes || 0}}</cover-view>
</cover-view>