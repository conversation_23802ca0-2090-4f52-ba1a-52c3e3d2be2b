<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from "wepy";
import { PRIMARY_COLOR } from "@/config/constant";

export default class User extends wepy.component {
  props = {
    // 控制显示隐藏
    config: {
      type: Object,
      default: {
        infoShow: false, //默认就诊人块是否显示
        show: false, //切换就诊人pop显示,
        notShowChangeBtn: false, //控制切换按钮是否展示
      },
      twoWay: true,
      registerShow: false,
    },
    // 就诊人列表
    patient: {
      type: Object,
      default: {
        cardList: [],
      },
      twoWay: true,
    },
    // 无就诊人是否弹窗提醒绑定就诊人
    emptyNotice: {
      type: Boolean,
      default: true,
    },
    change: {
      type: Boolean,
      default: true,
    },
    login: {
      type: Boolean,
      default: false,
      twoWay: true,
    },
    isHome: {
      type: Boolean,
      default: false,
    },
  };

  onLoad() {
    const pages = getCurrentPages(); //获取加载的页面
    const currentPage = pages[pages.length - 1]; //获取当前页面的对象
    const url = currentPage.route; //当前页面url
    if (url == "pages/home/<USER>") {
      this.noauth = "2"; // 首页不授权
    }
  }

  data = {
    noauth: "",
  };

  events = {
    "outpatient-get-patient": function (item = {}) {
      this.getPatient(item);
    },
  };

  methods = {
    bindShow() {
      this.config.show = true;
      this.$emit("changeInfoshow", false);
    },
    bindClose() {
      this.config.show = false;
      this.$emit("changeInfoshow", true);
    },
    /**
     * 添加就诊人
     */
    bindGoBindUser() {
      this.config.show = false;
      wepy.navigateTo({
        url: "/pages/usercenter/userlist/index", // pages/bindcard/scancard/index
      });
    },
    bindGoUserList() {
      this.config.show = false;
      wepy.navigateTo({
        url: "/pages/usercenter/userlist/index",
      });
    },
    /**
     * 切换默认就诊人
     * @param item
     */
    bindChangeUser(item = {}) {
      this.changeUser(item);
    },
    getRecordList() {
      wepy.navigateTo({
        url: `/pages/servicebill/servicebilllist/index`,
      });
    },
  };

  async getPatient(item = {}) {
    item.noauth = this.noauth;
    const app = this.$root.$parent;
    const { code, data = {} } = await app.getOutpatient(item);
    if (code !== 0) {
      return;
    }
    this.patient = data;
    if (this.emptyNotice && data.cardList.length == 0) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
        showCancel: true,
        confirmText: "立即绑定",
        confirmColor: PRIMARY_COLOR,
      });
      if (showModalRes.confirm) {
        wepy.navigateTo({
          url: "/pages/bindcard/queryuserinfo/index?qryType=1",
        });
      } else {
        wepy.navigateBack({
          fail: (res) => {
            wepy.reLaunch({
              url: "/pages/home/<USER>",
            });
          },
        });
      }
      return;
    }
    if (data.cardList.length && this.isHome) {
      const list = data.cardList.filter((item) => item.isDefault === 1);
      this.$emit("refresh-home", list[0]);
    }
    const { activePatient = {}, loginPatient = {} } = data;
    !this.login
      ? this.$emit("outpatient-change-user", activePatient)
      : this.$emit("outpatient-change-user", loginPatient);
    this.$apply();
  }

  async changeUser(item = {}) {
    const { activePatient = {} } = this.patient || {};
    if (item.patientId === activePatient.patientId) {
      return;
    }
    const app = this.$root.$parent;
    const { code, data = {}, msg } = await app.setDefaultOutpatient(item);
    if (code !== 0) {
      return;
    }
    this.patient = data;
    this.config.show = false; // 关闭切换窗口

    this.$emit("outpatient-change-user", data.activePatient);
    this.$emit("changeInfoshow", true);

    this.$apply();
  }
}
</script>
