<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';

  import { TYPE_MAP, CODE_TYPE, TIME_MAP } from '@/config/constant';
  import DetailStatus from '@/components/detailstatus/index';
  import RefundList from '@/components/refundlist/index';
  import * as Utils from '@/utils/utils';

  import BasicDetail from './com/basicDetail';
  import PayDetail from './com/payDetail';
  import * as Api from './api';

  const NORMAL_MAP = ['S', 'F', 'L', 'C'];
  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '挂号详情',
    };

    data = {
      // 页面参数
      options: {},
      // 订单详情
      detailData: {},
      // 顶部状态配置
      statusConfig: {},
      // 退款列表
      refundList: [],
      // 订单状态是否异常，用来确定是否需要重发
      isAbnormal: false,
      // 缴费信息是否展开,默认S状态收起
      payIsExpand: true,
      // 条形码格式
      codeType: '',
      // 剩余时间大于0
      leftTimeFlag: false,
      // 剩余支付时间
      leftTime: '00:00',
    };

    components = {
      'detail-status': DetailStatus,
      'refund-list': RefundList,
      'basic-detail': BasicDetail,
      'pay-detail': PayDetail,
    };

    props = {};

    onLoad(options) {
      this.options = options;
      this.codeType = CODE_TYPE;
      const { orderId = '' } = options;
      this.orderDetail({ orderId });
    }

    onUnload() {
      this.leftTimer && clearTimeout(this.leftTimer);
    }

    events = {
      'set-navigationbar-color': (param) => {
        wepy.setNavigationBarColor(param);
      }
    }

    methods = {
      /**
       * 重发订单状态查询
       */
      bindRetryOrder(){
        this.retryOrder();
      },
      /**
       * 取消订单
       */
      bindCancelOrder(){
        this.cancelOrder();
      },
      /**
       * 继续支付
       */
      bindContinuePay(){
        this.registerPayOrder();
      },
    };

    async orderDetail(item = {}) {
      const { orderId = '' } = item;
      const { code, data = {}, msg } = await Api.orderDetail({ orderId });
      if (code !== 0) {
        return;
      }
      const { status = '' } = data;
      data.visitTime = Utils.getVisitTime(data);
      this.detailData = data;
      this.refundList = data.refundList || [];
      this.statusConfig = this.getStatus() || {};
      if (NORMAL_MAP.indexOf(status) === -1) {
        this.isAbnormal = true;
      }
      if (status === 'S') {
        this.payIsExpand = false;
      } else {
        this.payIsExpand = true;
      }
      if(data.status === 'L' && data.leftPayTime && data.leftPayTime > 0){
        this.getLeftTime(data.leftPayTime || 0);
      }
      this.$apply();
    }

    /**
     * 获取订单描述文案
     */
    getStatus() {
      const { bizType, status, refundStatus } = this.detailData;
      let stsObj = {};

      // 需要转成map
      if (bizType == 2) {
        // 当班挂号
        if (status == 'S') {
          stsObj = {
            statusName: '挂号成功',
            text: '现场门诊请在预约当天至少提前15分钟前往医院挂号科室排队候诊。互联网门诊无需来院，请在预约当天注意留意微信小程序上的信息，互联网门诊具体就诊时间已诊室通知为准，谢谢您的理解！',
          };
        } else if (status == 'L') {
          stsObj = {
            statusName: '锁号成功',
            text: '请在锁号的时间内完成支付，否则将取消号源。',
          };
        } else if (status == 'F') {
          stsObj = {
            statusName: '挂号失败',
            text: '您的退款申请已受理。',
          };
        } else if (status == 'P') {
          stsObj = {
            statusName: '付款完成，调用医院支付接口中',
            text: '',
          };
        } else if (status == 'H' || status == 'Z') {
          stsObj = {
            statusName: '挂号异常',
            text: `操作超时，请咨询医院窗口。`,
          };
        } else if (status == 'C') {
          stsObj = {
            statusName: '取消成功',
            text: '挂号已取消，如需就诊请重新挂号。',
          };
        } else if (status == undefined) {
          stsObj = {
            statusClassName: '',
            statusName: '',
            text: '',
          };
        } else {
          stsObj = {
            statusName: '挂号异常',
            text: `操作超时，请咨询医院窗口。`,
          };
        }
      } else if (bizType == 1) {
        // 预约挂号
        if (status == 'S') {
          stsObj = {
            statusName: '预约挂号成功',
            text: '请提前到科室排队候诊，需要打印发票，请到医院服务窗咨询打印。',
          };
        } else if (status == 'L') {
          stsObj = {
            statusName: '锁号成功',
            text: '请在锁号的时间内完成支付，否则将取消号源。',
          };
        } else if (status == 'F') {
          stsObj = {
            statusName: '预约挂号失败',
            text: '您的退款申请已受理。',
          };
        } else if (status == 'P') {
          stsObj = {
            statusName: '付款完成，调用医院支付接口中',
            text: '',
          };
        } else if (status == 'H' || status == 'Z') {
          stsObj = {
            statusName: '预约挂号异常',
            text: `操作超时，请咨询医院窗口。`,
          };
        } else if (status == 'C') {
          stsObj = {
            statusName: '预约挂号取消成功',
            text: '预约挂号已取消，如需就诊请重新挂号。',
          };
        } else if (status == undefined) {
          stsObj = {
            statusName: '',
            text: '',
          };
        } else {
          stsObj = {
            statusName: '预约挂号异常',
            text: `操作超时，请咨询医院窗口。`,
          };
        }
      } else {
        stsObj = {
          statusClassName: '',
          statusName: '',
          text: '',
        };
      }

      return {
        ...stsObj,
        status,
        hasRefund: refundStatus == 1 || refundStatus == 2,
      };
    }

    /**
     * 重发订单状态查询
     */
    async retryOrder() {
      const { orderId = '', bizType = '' } = this.detailData;
      const type = bizType == 2 ? 'DBGH' : 'YYGH';
      const { code, data = {}, msg } = await Api.manualNotify({
        orderId,
        bizType: TYPE_MAP[type] || 'default',
      });
      if (code !== 0) {
        return;
      }
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&time=15&from=detail`
      });
    }

    async cancelOrder() {
      const showModalRes = await wepy.showModal({
        title: '取消号源提示',
        content: '取消后您将失去本号源，是否确认取消？',
        cancelText: '确定取消',
        confirmText: '保留号源',
        cancelColor: '#989898',
        confirmColor: '#3ECDB5',
      });
      if (showModalRes.confirm) {
        return false;
      }
      const { orderId = '' } = this.detailData;
      const { code, data = {}, msg } = await Api.cancelOrder({ orderId });
      if (code !== 0) {
        return;
      }

      wepy.showToast({
        title: '取消成功',
        icon: 'success',
      });

      this.leftTimer && clearTimeout(this.leftTimer);
      this.orderDetail({ orderId });
      wepy.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }

    /**
     * 获取支付页展示信息
     */
    getBizContent(item = {}) {
      const time = Utils.getTimeSlot(item);

      return [
        { key: '费用类型', value: item.registerTypeName },
        { key: '就诊科室', value: item.deptName },
        { key: '医生名称', value: item.doctorName },
        { key: '就诊日期', value: `${item.scheduleDate} ${item.visitWeekName}` },
        { key: '就诊时段', value: time },
        { key: '就诊人', value: item.patientName },
        { key: '就诊卡号', value: item.patCardNo },
      ];
    }

    /**
     * 获取支付订单号
     */
    async registerPayOrder() {
      let regType;
      let bizContent;
      const { orderId = '', bizType = '', patientName = '', patCardNo = '' } = this.detailData;
      regType = bizType == 1 ? 'YYGH' : 'DBGH';
      try {
        bizContent = JSON.stringify(this.getBizContent({
            ...this.detailData,
            patientName,
            patCardNo
          }) || []);
      } catch (e) {
        bizContent = "[]";
      }

      const { code, data = {}, msg } = await Api.registerPayOrder({ orderId, bizContent });
      if (code !== 0) {
        return;
      }
      const { payOrderId = '' } = data;
      wepy.redirectTo({
        url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=${regType}`
      });
    };

    getLeftTime(time = 0) {
      if (time <= 0) {
        this.leftTimer && clearTimeout(this.leftTimer);
        this.leftTimeFlag = false;
        this.leftTime = '00:00';
        this.$apply();
        return;
      }

      const minute = (`00${Math.floor(time / 60)}`).substr(-2);
      const second = (`00${Math.floor(time % 60)}`).substr(-2);

      this.leftTime = `${minute}:${second}`;
      this.leftTimeFlag = true;
      this.leftTimer = setTimeout(()=> {
        this.getLeftTime(--time);
      }, 1000);
      this.$apply();
    };
  }
</script>
