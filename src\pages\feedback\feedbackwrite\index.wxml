<view class="write-container">
  <view class="feed-write">
    <view class="feed-box">
      <view class="title">
        <span class="start">*</span>反馈类型
      </view>
      <picker bindchange="pickerChange" id="ZR" value="{{chooseIndex}}" range="{{suggestTypeList}}">
        <view class="picker">
          <input class="picker-info" type="text" disabled
            value="{{suggestTypeList[chooseIndex]}}" placeholder="请选择反馈类型" />
          <view class="item-arrow"></view>
        </view>
      </picker>
    </view>
    <view class="feed-box">
      <view class="title">
        <span style="opacity: 0">*</span>反馈对象
      </view>
      <input class="fedd-input" maxlength="100" placeholder="请输入部门或工作人员"  @input="getFeebackName" placeholder-style="color:rgba(0, 0, 0, 0.4)" value="{{feedbackName}}"/>
    </view>
  </view>
  <view class="feed-write">
    <view class="feed-box feed-textarea">
      <view class="title">
        <span class="start">*</span>反馈内容
      </view>
      <textarea class="fedd-texarea"  maxlength="100" placeholder="您可详细描述您需要反馈的内容，将有助于工作人员更好的核实、处理和回复您的反馈，谢谢~"  @input="getFeebackDesc" placeholder-style="color:rgba(0, 0, 0, 0.4)" value="{{feedbackDesc}}"/>
    </view>
     <view class="feed-box feed-textarea">
      <view class="title">
        <span style="opacity: 0">*</span>资料图片
      </view>
      <view class="feed-img">
        <view class="m-upload-list">
          <block wx:for="{{imageObj}}" wx:key="index">
            <view class="m-upload-item" @longpress="actionSheetTap({{index}}, '')" >
              <image class="m-upload-image"  src="{{item}}" @tap="previewImage({{item}})"/>
              <view class="m-upload-tips-image {{item.isUploadSuccess ? 'm-upload-sucess' : 'm-upload-queue'}}" @tap="delete({{item}})"></view>
            </view>
          </block>
          <view class="m-upload-item m-upload-icon" @tap="chooseImage()" wx:if="{{imageObj.length < 9}}">
             <image class="zhanwei" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/zanwei.png" />
          </view>
        </view>
        <view class="img-desc-content" wx:if="{{!imageObj.length}}">添加图片，有助于更好核实您所反馈的事项（最多可上传9张）</view>
      </view>
    </view>
  </view>
  <view class="m-bottom" @tap="submitFeedBack">
    <view>提交</view>
  </view>
</view>