<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";
import Empty from "@/components/empty/index";
import { PRIMARY_COLOR } from "@/config/constant";

export default class GuideIndex extends wepy.page {
  config = {
    navigationBarTitleText: "胚胎查询",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    empty: Empty
  };

  data = {
    emptyConfig: {
      show: true
    },
    patientInfo: {},
    tips: {},
    embroyList: []
  };

  onLoad() {}
  onShow() {
    this.getPatientsList();
    // this.getNoteProfile();
    // this.queryPtzp()
  }

  methods = {
    checkpdf(url) {
      wx.showLoading({
        title: "下载中",
        mask: true
      });
      wx.downloadFile({
        url,
        timeout: "10000",
        success: function(res) {
          const filePath = res.tempFilePath;
          wx.openDocument({
            filePath: filePath,
            fileType: "pdf",
            success: function(res) {
              wx.hideLoading();
            }
          });
        },
        fail: function(res) {
          wx.hideLoading();
          wx.showToast({
            title: "打开失败，请稍后再试",
            icon: "none"
          });
        }
      });
    }
  };
  getPatientsList = async () => {
    const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
    const { cardList = [] } = data;
    const patientInfo = cardList.length ? cardList[0] : {};
    this.patientInfo = patientInfo;
    this.$apply();
    if (patientInfo.patCardNo) {
      this.queryPtzp(patientInfo);
      return;
    }
    wx.showModal({
      title: "提示",
      content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
      showCancel: true,
      confirmText: "立即绑定",
      confirmColor: PRIMARY_COLOR,
      success: res => {
        console.log(res, "res");
        if (res.confirm) {
          wx.navigateTo({
            url: "/pages/bindcard/queryuserinfo/index?qryType=1"
          });
          return;
        }
        wx.navigateBack();
      }
    });
  };

  async queryPtzp(patientInfo) {
    const params = { pid: patientInfo.patHisNo, grid: patientInfo.patCardNo };
    const { code, data = [] } = await Api.queryPtzp(params);
    if (code == 0) {
      console.log(data);
      if (data.length > 0) {
        this.emptyConfig.show = false;
        this.$apply();
      }
      this.embroyList = data;
      this.$apply();
    } else {
      return;
    }
  }
}
</script>
