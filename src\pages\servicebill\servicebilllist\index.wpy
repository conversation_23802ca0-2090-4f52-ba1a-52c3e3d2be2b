<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '自助开单记录',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 挂号记录列表
      orderList: []
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'empty': Empty
    };

    props = {};

    onLoad(options) {
      this.getOrderList();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到挂号详情页
       * @param item
       */
      bindGoDetail(item){
        const { id = '' } = item;
        wepy.navigateTo({
          url: `/pages/servicebill/servicebilldetail/index?orderId=${id}`,
        });
      },
    };

    async getOrderList(word) {
      const { code, data = {} } = await Api.orderList({bizType: 'selfhelp_order'});
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      // data.historyList.map((item) => {
      //   item.extFieldsViews = JSON.parse(item.extFieldsViews);
      // })
      this.orderList = data.historyList || [];
      this.$apply();
    }
  }
</script>
