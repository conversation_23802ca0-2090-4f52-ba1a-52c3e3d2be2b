<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy";
import TopTip from "@/components/toptip/index";
import * as WxmpRsa from "../../../utils/rsa/JSEncrypt.js";
import BottomLogo from '@/components/bottomlogo/index';


import { CURSOR_SPACING, CHILD_MAX_AGE, DOMAIN } from "@/config/constant";
import {
  validator,
  getAgeByBirthday,
  getBirthdayByIdCard,
  encryptionObjToMd5
} from "@/utils/utils";
import * as Api from "./api";
import md5 from "md5";

export default class AdultBind extends wepy.page {
  config = {
    navigationBarTitleText: "绑定就诊卡",
    navigationBarBackgroundColor: '#fff',
  };

  components = {
    toptip: TopTip,
    'bottom-logo': BottomLogo
  };

  async getNoteProfileTitle(param) {
    const { code, data = {}, msg } = await Api.getNoteProfile(param);
    if (code == 0) {
      // this.promotTitle = data.profileValue;
      this.promotTitle = "若手机号码有误，请联系医院工作人员处理。"
      this.$apply();
    }
  }

  onLoad(option) {
    this.getNoteProfileTitle({
      profileKey: "getAlertNoteProfile_loginPhoneNotice",
      hisId: 242
    });
    this.initData(option);
    this.getHisConfig();
    if (option.qryType == 1) {
      wx.setNavigationBarTitle({
        title: "添加就诊人"
      });
    }
    this.initClock();
  }

  onShow() {
    // this.patientName = '';
    // this.idNo = '';
    // this.patientAddress = '';
    // this.patientMobile = '';
    // this.pid = '';
    // this.verifyCode = '';
    // this.peopleList = [];
    this.debounceFunc = this.debounce(this.userInfoChange.bind(this), 500);
    this.peopleList = [];
    // this.initClock();
    // this.getSecret();
  }

  async getSecret() {
    const { code, data, msg } = await Api.getSecret();
    if (code == 0 && data.secret) {
      if (data.resultCode == "0") {
        this.publicKey = data.secret;
      } else {
        wepy.showModal({
          title: "提示", //提示的标题,
          content: data.resultMessage || "未查询到公钥", //提示的内容,
          showCancel: false, //是否显示取消按钮,
          cancelText: "取消", //取消按钮的文字，默认为取消，最多 4 个字符,
          cancelColor: "#000000", //取消按钮的文字颜色,
          confirmText: "确定", //确定按钮的文字，默认为取消，最多 4 个字符,
          confirmColor: "#3CC51F", //确定按钮的文字颜色,
          success: res => {}
        });
      }
    }
  }

  onUnload() {
    clearTimeout(this.clockTimer);
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    publicKey: "",
    CURSOR_SPACING,

    options: {},

    placeholderColor: "rgba(0, 0, 0, 0.40)",
    errorColor: "red",

    errorElement: {}, // 发生错误的元素
    hasErr: true, // 是否存在校验错误
    toptip: "",
    isShowPhoneInfo: false,
    isOtherType: false, // 是否选择其它证件类型
    hisConfig: {
      relationTypes: [],
      patientTypes: [],
      idTypes: [
        {
          dictKey: "1",
          dictValue: "身份证",
          sortNo: 0,
          regexp: validator.idCard,
          errTip: "请输入18位身份证",
          inputType: "idcard"
        }
        // {dictKey: "10", dictValue: "警官证", sortNo: 1},
        // {dictKey: "3", dictValue: "护照", sortNo: 3},
        // {dictKey: "4", dictValue: "军官证", sortNo: 4},
        // {dictKey: "8", dictValue: "其他法定有效证件", sortNo: 5},
        // {dictKey: "9", dictValue: "士兵证", sortNo: 6},
      ],
      patCards: []
    },

    imgWidth: 0,
    imgHeight: 0,

    idCardPath: "",
    idCardToken: "",

    idTypesIdx: 0,
    patCardsIdx: 0,

    isNewCard: 1,
    isNoCard: 0,
    patientName: "",
    idType: "1",
    birthday: "",
    idNo: "",
    patientAddress: "",
    patientMobile: "",
    pid: "",
    relationType: "1",
    patientType: "0",
    qryType: "",
    // 倒计时剩余数
    leftTime: 60,
    staticLeftTime: 60,
    // 倒计时是否完成
    isClocking: false,
    // 倒计时计时器
    clockTimer: 0,
    // 验证码
    sendCode: "",
    verifyCode: "",
    // 相关联系人列表
    peopleList: [],
    // 手机号不能修改
    mobileReadonly: true,
    grid: "",
    promotTitle: "",
    address: '',
    areaCode: [],
    areaName: [],
    agreementchecked: false,
    isSubmit: false
  };
  initData(options) {
    if (options.name) {
      options.name = options.name.replace(" ", "");
    }
    const {
      name = "",
      idNo = "",
      pid = "",
      address = "",
      idCardPath = "",
      idCardToken = "",
      isNewCard = "0",
      isNoCard = "1",
      qryType = "2",
      isOtherType = 0,
      isScan = 0
    } = options;
    this.patientName = name;
    this.idNo = idNo;
    this.patientAddress = address;
    this.idCardPath = idCardPath;
    this.idCardToken = idCardToken;
    this.isNewCard = isNewCard || '1';
    this.isNoCard = isNoCard;
    this.options = options;
    this.qryType = qryType;
    this.isOtherType = parseInt(isOtherType);
    this.isScan = isScan;
    if (parseInt(isOtherType)) {
      this.idTypesIdx = 1;
    }
    if (idNo && name) {
      this.userInfoChange();
    }
    this.$apply();
  }

  initClock() {
    this.leftTime = 60;
    this.staticLeftTime = 60;
    this.isClocking = false;
    this.sendCode = "";
    this.verifyCode = "";
    this.$apply();
  }

  async getHisConfig() {
    const { code, data = {}, msg } = await Api.getHisConfig();
    if (code == 0) {
      if (data.idTypes) {
        // data.idTypes = data.idTypes.slice(0, 6); //最多6项
        data.idTypes = data.idTypes.filter(v => {
          if (v.dictKey == 1) {
            (v.regexp = validator.idCard), (v.errTip = "请输入18位身份证");
            v.inputType = "idcard";
            return true;
          }
          return false;
        });
      }
      // data.idTypes = this.hisConfig.idTypes;

      this.hisConfig = data;
      this.$apply();
    }
  }

  // 获取验证码
  async getVerifyCode() {
    const { isClocking } = this;
    if (isClocking) {
      return false;
    }
    wepy.showLoading({ title: "请稍候", mask: true });
    const form = {
      phone: this.patientMobile,
      msgKey: md5(this.patientMobile+'2023')
    }
    const { code, data, msg } = await Api.sendMsgAndValidate(form);
    wepy.hideLoading();
    if (code == 0 && data.resultCode == 0) {
      wepy.showToast({
        title: "发送成功",
        icon: "success"
      });
      this.clock();
      this.$apply();
    } else {
      await wepy.showModal({
        title: "提示",
        content: data.resultMessage || "验证码发送失败，请尝试重试",
        showCancel: false
      });
    }
  }

  /**
   * 倒计时
   */
  clock() {
    this.clockTimer = setTimeout(() => {
      let { leftTime, staticLeftTime = 60 } = this;
      --leftTime;
      if (leftTime <= 0) {
        // 倒计时结束
        this.leftTime = staticLeftTime;
        this.isClocking = false;
        this.$apply();
      } else {
        this.leftTime = leftTime;
        this.isClocking = true;
        this.clock();
        this.$apply();
      }
    }, 1000);
  }

  validator(id) {
    
    const validate = {
      patientName: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9_ ]{2,20}$/,
        errTip: "请输入合法姓名"
      },
      idNo: {
        regexp: (() => {
          const regexp = this.hisConfig.idTypes[this.idTypesIdx].regexp;
          if (typeof regexp === "function") {
            return val => regexp(val.idNo);
          } else {
            return /^\S+$/;
          }
        })(),
        errTip:
          this.hisConfig.idTypes[this.idTypesIdx].errTip ||
          `${this.hisConfig.idTypes[this.idTypesIdx].dictValue}不能为空`
      },
      patientMobile: {
        regexp: !this.mobileReadonly ? /^1\d{10}$/ : "",
        errTip: "请输入正确的手机号"
      },
      // pid: {
      //   regexp: /^(|\d{3,8})$/,
      //   errTip: "请输入正确pid号"
      // },
    };
    if (this.qryType == 1) {
      validate.verifyCode = {
        regexp: /^\d{6}$/,
        errTip: "请输入正确验证码"
      };
    }
    
    if (!this.areaCode.length) {
      validate.pro = {
        regexp: !this.areaCode.length,
        errTip: "请选择家庭住址所在省市区"
      };
    }

    const value = this.getFormData();
    
    let hasErr = false;
    for (let o in value) {
      const obj = validate[o];
      if (obj && obj.regexp) {
        let thisErr = false;
        if (typeof obj.regexp === "function") {
          const retObj = obj.regexp(value);
          if (!retObj.ret) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        } else if (typeof obj.regexp.test === "function" &&
          !obj.regexp.test(value[o]))
        {
            // 添加非本人时，不需要验证手机号
          if (this.qryType == 2 && o == "patientMobile") {
            continue;
          }
          hasErr = true;
          thisErr = true;
          if (id && id == o) {
            this.errorElement[id] = true;
          }
        } else if (typeof obj.regexp === 'boolean') {
          hasErr = true;
          thisErr = true;
          if (id && id == o) {
            this.errorElement[id] = true;
          }
        }
        
        if (
          (!id && hasErr) ||
          (obj.errTarget && obj.errTarget == id && thisErr)
        ) {
          // 提交时弹框提示
          this.errorElement[obj.errTarget || o] = true;
          this.toptip = obj.errTip || "";
          const errTimer = setTimeout(() => {
            this.toptip = "";
            this.$apply();
            clearTimeout(errTimer);
          }, 2000);
          break;
        }
      }
    }

    return hasErr;
  }

  getFormData() {
    const {
      patientName,
      idNo,
      verifyCode,
      patientAddress,
      patientMobile,
      pid,
      isNewCard,
      isNoCard,
      idTypesIdx,
      patCardsIdx,
      hisConfig,
      address,
      areaName
    } = this;
    const idType = this.hisConfig.idTypes[idTypesIdx].dictKey;
    const patCardType =
      this.isNewCard == 1 ? "21" : hisConfig.patCards[patCardsIdx].dictKey;
    return {
      patientName,
      idNo,
      idType,
      birthday: "",
      patientMobile,
      relationType: "1",
      patientType: "0",
      patCardType,
      isNewCard: '1',
      isNoCard,
      verifyCode,
      pid,
      address,
      patientAddress: address,
      pro: areaName[0] || '',
      city: areaName[1] || '',
      area: areaName[2] || '',
      patHisNo: pid
    };
  }

  async queryRelationPatients() {
    const value = this.getFormData();
    // const qryType = this.qryType;
    const idType = this.hisConfig.idTypes[this.idTypesIdx].dictKey;
    const { JSEncrypt } = WxmpRsa;
    // this.publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClQkb7bFUfjfew9lUl8RC5ZkkQgoHOhOu6R2r1qv0A2fpPeMh9Atz7oVn8w+NyLwa80pWXGKgCvmcnWW67MLl0YI0IMQOWCKJRk3p9Bo2jjYlJLcR02BicicBwauTOS13w9c/FcwiVWemmBYqhdP7dgdKXujwlZPBezajEn+OTywIDAQAB';
    WxmpRsa.JSEncrypt.prototype.setPublicKey(this.publicKey);
    value.mobile = WxmpRsa.JSEncrypt.prototype.encrypt(value.patientMobile);
    delete value.patientMobile;
    const { code, data = {}, msg } = await Api.queryRelationPatients({
      ...value,
      patName: value.patientName,
      qryType: 2,
      idType
    });
    // wx.hideLoading();
    let { items = [] } = (data.itemList || [])[0] || {};
    if (code == 0 && data.resultCode == 0) {
      wx.showToast({
        title: "查询成功",
        icon: "success",
        duration: 1000,
        success: ""
      });
      items.map(item => {
        if (item.idType) {
          item.idType = Number(item.idType.substr(-2));
        }
        return item;
      });
      if (this.qryType == 1 || items.length <= 1) {
        // 绑定本人或 查到的家庭成员小于等于一个
        wx.navigateTo({
          url: `/pages/bindcard/adduser/index?isNewCard=${
            items.length >= 1 ? 0 : 1
          }&idNo=${this.idNo}&idType=${this.idType}&patientName=${
            this.patientName
          }&patientMobile=${this.patientMobile}&qryType=${
            this.qryType
          }&pid=${value.pid || ""}&address=${this.patientAddress}&isScan=${
            this.isScan
          }`
        });
        return;
      } else if (items.length > 1) {
        // 查询到多个人的信息
        items = items
          .map(item => {
            if (!item.pid) {
              item.pid = value.pid;
            }
            return item;
          })
          .filter(item => {
            return item.patName && item.idNo;
          });
        this.peopleList = items;
      }
    } else {
      wx.navigateTo({
        url: `/pages/bindcard/adduser/index?isNewCard=${
          items.length >= 1 ? 0 : 1
        }&idNo=${this.idNo}&idType=${this.idType}&patientName=${
          this.patientName
        }&patientMobile=${this.patientMobile}&qryType=${
          this.qryType
        }&pid=${value.pid || ""}&address=${this.patientAddress}&isScan=${
          this.isScan
        }`
      });
      // wepy.showModal({
      //   title: "提示",
      //   content: data.resultMessage || "未查询到您的用户信息",
      //   showCancel: false
      // });
    }
    this.$apply();
  }

  async queryMobileInfo(param) {
    this.mobileReadonly = true;
    const { code, data = {}, msg } = await Api.queryRelationPatients(
      { ...param, qryType: 1 },
      false
    );
    if(code === 0 && data.resultCode !== '0'){
      wepy.showModal({
        title: '提示',
        content: data.resultMessage || '',
        showCancel: false,
      });
      return;
    }
    let { items = [] } = data;
    this.patientMobile = "";
    if (items.length === 1) {
      const { telephone = "", grid = "" } = items[0];
      // if (!telephone || !/^1\d{10}$/.test(telephone)) {
      //   return false;
      // }
      this.patientMobile = telephone;
      this.grid = grid;
      // ocr进入时可编辑手机号
      this.mobileReadonly = telephone ? !this.idCardPath : false;
    } else {
      const { idNo = "", patName = "" } = param;
      if (idNo && patName) {
        this.mobileReadonly = false;
      }
    }
    this.$apply();
  }

  userInfoChange() {
    // 输入姓名身份证号码时 如果是身份证类型 判断身份证正确之后 查询用户信息
    if (this.idTypesIdx != 0) {
      // 非身份证
      return;
    }
    let validateResult = true;
    const param = { idNo: this.idNo || "", patName: this.patientName };
    for (let name in param) {
      let result = true;
      const value = param[name];
      if (name === "idNo") {
        if (!validator.idCard(value)) {
          validateResult = false;
        }
      } else if (!name) {
        validateResult = false;
      }
    }
    if (!validateResult) {
      return;
    }
    this.queryMobileInfo(param);
  }

  debounce(fn, delay) {
    let timer = null;
    return () => {
      let args = arguments;
      let context = this;

      if (timer) {
        clearTimeout(timer);

        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      } else {
        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      }
    };
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    bindchange(e) {
      
      this.agreementchecked = !this.agreementchecked;
      console.log(this.agreementchecked, '======593')
    },
    goAgreement(e) {
      const { url } = e.currentTarget.dataset;
      wx.downloadFile({
        // 示例 url，并非真实存在
        url: `${DOMAIN}/${url}`,
        success: function (res) {
          const filePath = res.tempFilePath;
          wx.openDocument({
            filePath: filePath,
            success: function (res) {
              wx.hideLoading();
            }
          })
        }
      })

    },
    inputTriggerAddress(e) {
      this.address = e.detail.value;
    },
    bindRegionChange(e) {
      console.log(e.detail)  
      // code=["110000", "110100", "110105"],value=["北京市", "北京市", "朝阳区"]
      if (e.detail.value) {
        this.areaCode = e.detail.code;
        this.areaName = e.detail.value;
      }
    },
    gotoUpload() {
      // 跳转上传身份证
      wepy.navigateBack();
    },
    setImgSize(e) {
      const { width, height } = e.detail;
      const basScale = 370 / 214;
      let scale = width >= height * basScale ? 370 / width : 214 / height;
      scale = scale > 1 ? 1 : scale;
      this.imgWidth = width * scale;
      this.imgHeight = height * scale;
    },
    async getFilePathByToken() {
      // 根据token换取imgpath
      const { code, data, msg } = await Api.getFilePathByToken({
        token: this.idCardToken,
        cardType: 0
      });
      if (code == 0 && data.url) {
        this.idCardPath = res.data.url;
        this.$apply();
      }
    },
    reUpload() {
      wepy.navigateBack();
    },
    resetThisError(e) {
      const { id } = e.currentTarget;
      this.errorElement[id] = false;
    },
    checkOtherTypes() {
      wepy.redirectTo({
        url: `/pages/bindcard/queryothertype/index?isNewCard=${
          this.isNewCard
        }&isNoCard=${this.isNoCard}&qryType=${this.qryType}&isOtherType=${
          !this.isOtherType ? 1 : 0
        }`
      });
    },
    actionSheetType(e) {
      console.log("action", e);
      const { prop = "" } = e.target.dataset || {};
      if (prop) {
        let listData = this.hisConfig[prop] || [];
        if (this.isOtherType) {
          listData = listData.filter(v => v.dictKey != 1);
        }
        wepy
          .showActionSheet({
            itemList: listData.map(v => v.dictValue)
          })
          .then(res => {
            console.log(res);
            this.mobileReadonly = false;
            this.idNo = "";
            const idxKey = `${prop}Idx`;
            this[idxKey] = res.tapIndex + 1;
            this.$apply();
          });
      }
    },
    inputTrigger(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      this[id] = value;
      if (id === "idNo" || id === "patientName") {
        this.debounceFunc(e);
      }
    },
    async formSubmit(e) {
      if(this.isSubmit) return;
      this.isSubmit = false;
      const value = this.getFormData();
      this.hasErr = this.validator();
      if (this.hasErr) {
        return false;
      }

      if (!this.agreementchecked) {
        wepy.showToast({
          title: "请勾选用户隐私协议",
          icon: "none"
        });
        return
      }

      /*************验证码**********/
      // 第一次登录
      if (this.qryType == 1) {
        const form = {
          phone: this.patientMobile,
          msgKey: md5(this.patientMobile+'2023msg'),
          code: this.verifyCode
        }
        const { code, data, msg } = await Api.checkMsgAndValidate(form);
        // wepy.hideLoading();
        if (code == 0 && data.resultCode == 0) {
          // this.pushData();
          console.log("校验验证码成功");
        } else {
          await wepy.showModal({
            title: "提示",
            content: data.resultMessage || "验证码校验失败，请尝试重试",
            showCancel: false
          });
          return false;
        }
      }
      this.peopleList = [];
      const { code, data } = await Api.bindCard(value);
      if (code === 0) {
        wx.showToast({
          title: "登录成功",
          icon: "success",
          duration: 2000,
          success: () => {
            if (this.options.from === 'doctor') {
              wepy.navigateBack();
            } else {
              wepy.reLaunch({ url: "/pages/home/<USER>" });
            }
          }
        });
      }
      
      // this.queryRelationPatients();

      // this.validateVerifyCode()
    },

    sendVerifyCode() {
      // 发送验证码
      if (!this.mobileReadonly) {
        if (!/^1\d{10}$/.test(this.patientMobile)) {
          wepy.showModal({
            title: "提示",
            content: "请输入正确的手机号码",
            showCancel: false
          });
          return false;
        }
      }
      this.getVerifyCode();
    }

    // bindViewTap() {
    //   wx.navigateTo({
    //     url: `/pages/bindcard/userinfo/index?idNo=${this.idNo}&patName=${this.patientName}&patientMobile=${this.patientMobile}`
    //   })
    // }
  };
}
</script>
