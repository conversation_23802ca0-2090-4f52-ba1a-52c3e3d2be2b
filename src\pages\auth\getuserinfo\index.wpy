<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import { login } from '@/utils/request';
  import { apiFormatPromisify } from '@/utils/utils';
  import * as Api from './api';
  import BottomLogo from '@/components/bottomlogo/index';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '授权登录',
    };
    components = {
      'bottom-logo': BottomLogo,
    }
    data = {
      agreeStatus: true,
    };

    onLoad(){
      this.appLogin();
    }

    onHide(){
      wepy.loginingStatus = 'resolved';
    }

    onUnload(){
      wepy.loginingStatus = 'resolved';
    }

    async appLogin() {
      const { code: loginCode, data: loginData = {} } = await apiFormatPromisify(wx.login)();
      if (loginCode !== 0 || !loginData.code) {
        wx.showToast({
          title: '获取用户授权错误',
          icon: 'none',
        });
        this.methods.cancelAuth();
        return false;
      }
      this.code = loginData.code;
    }

    methods = {
      changeStatus(e) {
        this.agreeStatus = e.detail.value[0] == 'checked';
      },
      async bindGetUserInfo(e){
        let encryptedData = '';
        let iv = '';
        let userInfo = {
          nickName: '默认用户',
          avatarUrl: 'REPLACE_STATIC_DOMAIN/hc-baby-hd.png',
        };
        if (wx.getUserProfile) {
          const { code, data } = await apiFormatPromisify(wx.getUserProfile)({
            desc: '用于完善用户资料',
            lang: 'zh_CN',
          });
          if (code === 0) {
            encryptedData = data.encryptedData || '';
            iv = data.iv || '';
            if (data.userInfo) {
              userInfo = data.userInfo;
            }
          } else {
            this.methods.cancelAuth();
            return false;
          }
        }
        wepy.setStorageSync('userInfo', userInfo);
        const { url = '' } = this.$wxpage.options || {};
        await login(null, { encryptedData, iv, code: this.code });
        wepy.redirectTo({
          url: `${decodeURIComponent(url)}`,
        });
      },
      async cancelAuth() {
        const url = (getCurrentPages()[getCurrentPages().length - 2] || {}).route || 'pages/home/<USER>';
        wepy.reLaunch({
          url: `/${url}`,
        });
      }
    };

    events = {
    };
  }
</script>
