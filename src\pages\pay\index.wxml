<view class="p-page">
  <!-- <block wx:if="{{orderInfo.showLeftTime && leftTimeFlag}}">
    <view class="m-lefttime">
      <view class="lefttime">剩余支付时间 {{leftTime}}</view>
    </view>
  </block> -->
  <view class="page-top-box">
    <view class="m-price">
      <view class="price-main">
        <view class="main-txt">{{WxsUtils.formatMoney(orderInfo.selfFee,100)}}元</view>
        <view wx:if="{{options.type === 'SMKD'}}" class="pay-tips">支付金额</view>
        <view wx:if="{{orderInfo.showLeftTime && leftTimeFlag}}" class="lefttime">请在 {{leftTime}} 秒内完成支付</view>
      </view>
      <block wx:if="{{orderInfo.medicareFlag}}">
        <view class="price-extra">
          <view class="extra-item">
            <view class="item-tit">订单总额</view>
            <view class="item-txt">¥{{WxsUtils.formatMoney(orderInfo.totalFee,100)}}</view>
          </view>
          <view class="extra-item">
            <view class="item-tit">医保报销</view>
            <view class="item-txt text-info">- ¥{{WxsUtils.formatMoney(orderInfo.medicareFee,100)}}</view>
          </view>
        </view>
      </block>
    </view>

    <block wx:if="{{orderInfo.goodsName && orderInfo.goodsName.length > 0}}">
      <view class="m-order">
        <block
          wx:for="{{orderInfo.goodsName}}"
          wx:key="index"
        >
          <view class="order-item">
            <view class="item-tit">{{item.key}}</view>
            <view class="item-txt">{{item.value}}</view>
          </view>
        </block>
        <block wx:if="{{options.type === 'SMKD'}}">
          <view class="order-item">
            <view class="item-tit">{{productList.feeDetail.key}}</view>
            <view class="item-txt extra-item">金额：<text class="extra">¥{{WxsUtils.formatMoney(productList.feeDetail.value,100)}}</text></view>
          </view>
          <view class="tb-box">
            <view class="tb-head tb-tr">
              <view class="td">项目名称</view>
              <view class="td">金额（元）</view>
            </view>
            <view class="tb-body tb-tr" wx:for="{{productList.list}}" wx:key="indx" wx:for-item="itm" wx:if="{{itm.value || itm.productprice || (itm.productName && item.key != '快递费')}}">
              <view class="td" wx:if="{{itm.productName === '其他'}}">（其他）{{itm.remark}}</view>
              <view class="td" wx:else><block wx:if="{{itm.parproductName}}">{{itm.parproductName}}—</block>{{itm.key || itm.productName}}</view>
              <view class="td">{{ itm.value ? WxsUtils.formatMoney(itm.value, 100) : WxsUtils.formatMoney(itm.productprice, 100)}}</view>
            </view>
          </view>
        </block>
      </view>
    </block>
    
  </view>
  
  <form @submit="bindChoosePayMode" report-submit="{{true}}">
    <view hidden="{{1 == 1}}">
      <input type="text" name="NO_NAME" value="0" />
    </view>
    <block wx:if="{{!orderInfo.showLeftTime || (orderInfo.showLeftTime && leftTimeFlag)}}">
      <block wx:if="{{orderInfo.payModeList && orderInfo.payModeList.length > 1}}">
        <view class="m-mode">
          <view class="mode-tit">请选择支付方式</view>
          <view class="mode">
            <block
              wx:for="{{orderInfo.payModeList || []}}"
              wx:key="index"
            >
              <button
                formType="submit"
                class="mode-item"
                data-mode="{{item}}"
              >
                <view class="item-icon">
                  <image mode="widthFix" src="{{item.logoUri}}"></image>
                </view>
                <view class="item-bd">
                  <view class="bd-tit">{{item.payModeName}}</view>
                  <view class="bd-txt">{{item.remark}}</view>
                </view>
                <view class="item-ft"></view>
              </button>
            </block>
          </view>
        </view>
      </block>
      <block wx:if="{{orderInfo.payModeList && orderInfo.payModeList.length == 1}}">
        <view class="m-mode">
          <view class="mode-btn">
            <button
              formType="submit"
              class="btn"
              data-mode="{{orderInfo.payModeList[0]}}"
            >{{orderInfo.payModeList[0].payModeName}}</button>
          </view>
        </view>
      </block>
    </block>
    <view class="cancal-box">
      <view class="btn" @tap="goBack">取消</view>
    </view>
  </form>

</view>


