<view class="ask">
  <view class="ask-tabs">
    <view class="tab-item {{ activeIndex == index ? 'active' : '' }}" wx:for="{{tabs}}" wx:key="{{index}}" @tap="checkThis({{index}})">
      <view class="title">
        <!-- <text class="badge">{{item.num}}</text> -->
        {{item.text}}
      </view>
      <text class="active-line" wx:if="{{activeIndex == index}}"></text>
    </view>
  </view>
  <view class="content" wx:if="{{activeIndex == 0}}">
    <view class="ask-list" wx:if="{{waitingList.length > 0}}">
      <block wx:for="{{waitingList}}" wx:key="{{index}}">
        <view class="ask-item" @tap="toChat({{item}})">
          <view class="item-header">{{item.registerDate}}</view>
          <view class="item-content">
            <view class="con-img">
              <image mode="widthFix" src="{{item.doctorImg || 'REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png'}}" />
            </view>
            <view class="con-right">
              <view class="r-row">
                <text class="name">会诊</text>
                <text class="doc-name">{{item.doctorName}}</text>
              </view>
              <view class="r-row">{{item.doctorHospitalName}}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <empty1 :config.sync="emptyConfig1">
      <text slot="text">暂未查询到相关信息</text>
    </empty1>
  </view>
  <view class="content" wx:if="{{activeIndex == 1}}">
    <view class="ask-list" wx:if="{{endList.length > 0}}">
      <block wx:for="{{endList}}" wx:key="{{index}}">
        <view class="ask-item" @tap="toChat({{item}})">
          <view class="item-header">{{item.registerDate}}</view>
          <view class="item-content">
            <view class="con-img">
              <image mode="widthFix" src="{{item.doctorImg || 'REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png'}}" />
            </view>
            <view class="con-right">
              <view class="r-row">
                <text class="name">会诊</text>
                <text class="doc-name">{{item.doctorName}}</text>
              </view>
              <view class="r-row">{{item.doctorHospitalName}}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <empty2 :config.sync="emptyConfig2">
      <text slot="text">暂未查询到相关信息</text>
    </empty2>
  </view>
  <!-- <nav-tab :type="navTabType"></nav-tab> -->
</view>