<view class="p-page">
  <detail-status :config.sync="statusConfig">
    <block slot="title">{{detailData.statusName}}</block>
    <block slot="text">
      <view>{{statusConfig.text}}</view>
    </block>
  </detail-status>
  <view class="page-main">
    <view class="m-code {{detailData.status === 'S' ? 'active' : ''}}" wx:if="{{detailData.status === 'S'}}">
      <view class="code-tit">就诊凭条</view>
      <view class="code-img">
        
        <!-- <block wx:if="{{detailData.patCardNo}}">
          <image mode="widthFix" src="https://wechat.jiahuiyiyuan.com/barcode?msg={{detailData.patCardNo}}&type=code128&mw=.60" alt=""></image>
        </block> -->

        <block wx:if="{{detailData.patCardNo}}">
          <image mode="widthFix" src="https://wechat.jiahuiyiyuan.com/barcode?msg={{detailData.patCardNo}}&type=code128&mw=.60&t={{timestamp}}"></image>
        </block>
        
      </view>
    </view>
    <!-- <block wx:if="{{isAbnormal}}">
      <view class="m-retry">
        <view class="retry-btn" @tap="bindRetryOrder">点击重试刷新</view>
      </view>
    </block> -->
    <refund-list :refundList.sync="refundList"></refund-list>
    <basic-detail :detailData.sync="detailData"></basic-detail>
    <med-detail :detailData.sync="detailData" :isExpand.sync="medIsExpand" wx:if="{{detailData.itemList && detailData.itemList.length}}"></med-detail>
    <!-- <med-detail :detailData.sync="detailData" :isExpand.sync="medIsExpand"></med-detail> -->
    <!-- <guide-detail :guideData.sync="guideInfo" :isExpand.sync="guideIsExpand"></guide-detail> -->
    <!-- <view class="ad-treat">
      <view class="ad-content">儿科用药禁忌知多少？</view>
      <view class="main-btn">去看看</view>
    </view> -->
    <pay-detail :detailData.sync="detailData" :isExpand.sync="payIsExpand"></pay-detail>
  </view>
</view>