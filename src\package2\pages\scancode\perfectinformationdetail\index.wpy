<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import WxsUtils from "../../../../wxs/utils.wxs";
import { REQUEST_QUERY } from "@/config/constant";
import { validator as validatorFun } from "@/utils/utils";
import { uploadFile } from "@/utils/request";

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '样本信息',
	}

  components = {
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  data={
    moreArray: [
      {
        value: 'FSH',
        name: 'FSH',
        checked: false
      },
      {
        value: 'LH',
        name: 'LH',
        checked: false
      },
      {
        value: '睾酮',
        name: '睾酮',
        checked: false
      },
      {
        value: 'E2',
        name: 'E2',
        checked: false
      },
      {
        value: 'PRL',
        name: 'PRL',
        checked: false
      },
      {
        value: 'INHB',
        name: 'INHB',
        checked: false
      }
    ],
    lessArray: [
      {
        value: 'FSH',
        name: 'FSH',
        checked: false
      },
      {
        value: 'LH',
        name: 'LH',
        checked: false
      },
      {
        value: '睾酮',
        name: '睾酮',
        checked: false
      },
      {
        value: 'E2',
        name: 'E2',
        checked: false
      },
      {
        value: 'PRL',
        name: 'PRL',
        checked: false
      },
      {
        value: 'INHB',
        name: 'INHB',
        checked: false
      }
    ],
    tempFilePaths: [],
    sampleNumber: '',
    expressnumber: '',
    files: [],
    xbtemplateJson: {},
    field1: '',
    field2: '',
    field3: '',
    orderId: '',
    productId: '',
    deptId: '',
    perfectInfoDetail: {},
    patientName: '', 
    idNumber: '', 
    mobile: ''
  }

  onLoad(options) {
    const { orderId, productId, deptId, sampleNumber, deptName, patientName, idNumber, mobile } = options;
    this.productId = productId;
    this.orderId = orderId;
    this.deptId = deptId;
    this.deptName = deptName;
    this.mobile = mobile;
    this.patientName = patientName;
    this.idNumber = idNumber;
    console.log(patientName, idNumber, mobile, '=======112')
    if(sampleNumber){
      this.handleDetail(sampleNumber);
    }
  }

	onShow() {}

  methods = {
    viewImage(e) {
      const { path, url } = e.currentTarget.dataset;
      wepy.previewImage({
        urls: this.tempFilePaths || [],
        current: url || '',
      });
    },
    showReport() {
      wx.downloadFile({
        url: this.perfectInfoDetail.reportPath,
        success: function (res) {
          const filePath = res.tempFilePath;
          wx.openDocument({
            filePath: filePath,
            fileType: "pdf",
            success: function (res) {
              wx.hideLoading();
            }
          })
        },fail: function(res) {
        }
      })
    }
  }

  async handleDetail(sampleNumber) {
    const { code, data = {}, msg } = await Api.getBySamplenumber({ id: sampleNumber });
    this.perfectInfoDetail = data;
    this.expressnumber = data.expressnumber || '';
    this.sampleNumber = data.sampleNumber;
    const informObj = JSON.parse(data.informedConsentForm);
    this.field1 = informObj.field1;
    this.field2 = informObj.field2.split(',');
    this.field3 = informObj.field3.split(',');
    this.field2.forEach(v => {
      this.moreArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    this.field3.forEach(v => {
      this.lessArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    this.tempFilePaths = data.files ? data.files.split(',') : [];
    this.$apply();
  }
  
}
</script>
		
