@import "../../resources/style/mixins";

page {
}

.p-page {
  height: 100vh;
  overflow-y: auto;
  .page-top-box{
    // background: linear-gradient(180deg, rgba(45, 102, 111, 1) 0%, #fff 100%);
    background: url('REPLACE_IMG_DOMAIN/his-miniapp/images/pay-bg.png') no-repeat;
    background-size: cover;
  }
}

.m-lefttime {
  .lefttime {
    width: 100%;
    line-height: 70rpx;
    height: 70rpx;
    background-color: @hc-color-warn;
    color: #fff;
    text-align: center;
    z-index: 9;
  }
}
.m-price {
  .price-main {
    padding: 40rpx 24rpx;
    text-align: center;
    .lefttime {
      font-size: 28rpx;
      color: @hc-color-white;
    }
    .pay-tips{
      color: rgba(255, 255, 255, 0.78);
      font-size: 28rpx;
    }
  }
  .main-tit {
    font-weight: 600;
    font-size: 64rpx;
    color: @hc-color-title;
  }
  .main-txt {
    font-size: 64rpx;
    color: @hc-color-white;
    font-weight: 600;
  }
  .price-extra {
    // border-top: 2rpx solid @hc-color-border;
    // margin: 0 30rpx;
    overflow: hidden;
    background-color: #fff;
    overflow: hidden;
    margin: 0 32rpx;
    border-radius: 24rpx;
    padding: 0 32rpx;
    margin-bottom: 24rpx;
  }
  .extra-item {
    display: flex;
    font-size: 30rpx;
    margin: 20rpx 0;
  }
  .item-tit {
    flex: 1;
    color: @hc-color-text;
  }
  .item-txt {
    color: @hc-color-title;
    &.text-info {
      color: @hc-color-primary;
    }
  }
}

.m-order {
  background-color: #fff;
  overflow: hidden;
  margin: 0 48rpx;
  border-radius: 12rpx 12rpx 8rpx 8rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0px 4rpx 12rpx 0px rgba(0, 0, 0, 0.08);
  .order-item {
    display: flex;
    font-size: 32rpx;
    margin: 24rpx 0;
    &:first-child{
      margin-top: 0;
    }
    &:last-child{
      margin: 0;
    }
  }
  .extra-item{
    text-align: right;
    font-weight: 600;
  }
  .extra{
    color: #D2962B;
  }
  .tb-box{
    border: 1rpx solid rgba(0, 0, 0, 0.08);
  }
  .tb-tr{
    display: flex;
    justify-content: space-between;
    .td{
      flex: 1;
      border-right: 1rpx solid rgba(0, 0, 0, 0.08);
      &:last-child{
        text-align: right;
        border: none;
      }
    }
  }
  .tb-head{
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
    .td{
      padding: 8rpx 16rpx;
      color: rgba(0, 0, 0, 0.90);
      font-size: 24rpx;
      font-weight: 600;
    }
  }
  .tb-body{
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
    &:last-child{
      border: none;
    }
    .td{
      padding: 16rpx;
      color: rgba(0, 0, 0, 0.70);
      font-size: 24rpx;
      word-break: break-all;
    }
  }
  .item-tit {
    flex: 0 0 208rpx;
    color: @hc-color-info;
  }
  .item-txt {
    flex: 1;
    color: @hc-color-title;
  }
}

.tips {
  font-size: 26rpx;
  color: @hc-color-info;
  padding: 40rpx 32rpx;
}
.bold {
  font-weight: 600;
}

.m-mode {
  overflow: hidden;
  margin: 64rpx 24rpx 24rpx;
  .mode-tit {
    font-size: 28rpx;
    padding: 15rpx 30rpx;
    color: @hc-color-text;
  }
  .mode {
    background-color: #fff;
    border-radius: 24rpx;
  }
  .mode-item {
    .clearButton();

    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 30rpx 30rpx 0;
    margin-left: 30rpx;
    border-top: 2rpx solid @hc-color-border;
    &:first-child {
      border-top: 0;
    }
  }
  .item-icon {
    width: 50rpx;

    image {
      width: 100%;
      height: 30rpx;
      vertical-align: top;
    }
  }
  .item-bd {
    flex: 1;
    margin-left: 35rpx;
    margin-right: 30rpx;
  }
  .bd-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .bd-txt {
    font-size: 26rpx;
    color: @hc-color-text;
  }
  .item-ft {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c2c9c9;
    border-bottom: 5rpx solid #c2c9c9;
    transform: translateX(-8rpx) rotate(-45deg);
  }
  .btn {
    .clearButton();
    font-size: 34rpx;
    font-weight: 600;
    text-align: center;
    color: #fff;
    border-radius: 76rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    height: 100rpx;
    line-height: 100rpx;
  }
}
.cancal-box{
  margin: 0 24rpx;
  .btn{
    height: 100rpx;
    line-height: 100rpx;
    font-size: 34rpx;
    font-weight: 600;
    border-radius: 76rpx;
    text-align: center;
    background: rgba(0, 0, 0, 0.04);
  }
}
