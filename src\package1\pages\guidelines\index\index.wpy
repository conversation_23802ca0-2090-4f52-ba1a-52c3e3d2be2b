<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { REQUEST_QUERY, PRIMARY_COLOR } from "@/config/constant";
import empty from "@/components/empty/index";
import * as Api from "./api";
export default class GuideLines extends wepy.page {
  config = {
    navigationBarTitleText: "用药指导"
  };
  components = { empty };
  data = {
    cardList: [],
    report: null,
    // 男方，女方
    patientList: []
  };
  async onShow() {
    await this.getCardList();
  }
  initData() {
    const { outpatient = {} } = this.$parent.globalData;
    const { cardList = [] } = outpatient;
    const patient = cardList.find(item => item.relationType == 1) || {};
    const { patHisNo = "" } = patient;
    this.getPaientInfo(patient);
    this.getReport(patHisNo);
  }
  // 检测是否已登录
  async getCardList() {
    const { data = {} } = await Api.getCardList();
    const { cardList = [] } = data;
    this.cardList = cardList;
    let isLogin = false;
    if (cardList.length > 0) {
      for (let i in cardList) {
        const item = cardList[i];
        if (item.relationType == 1) {
          isLogin = true;
        }
      }
    }
    if (!isLogin && this.qryType != 1) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "请登录后再操作",
        // showCancel: false,
        confirmText: "确定",
        confirmColor: PRIMARY_COLOR
      });
      if (showModalRes.confirm) {
        wepy.redirectTo({
          url: "/pages/bindcard/index/index"
        });
      }else{
        wepy.navigateBack();
      }
    } else {
      this.initData();
    }
    this.$apply();
  }
  async getPaientInfo(patient) {
    const { hisId = "", platformId = "", platformSource = "" } = REQUEST_QUERY;
    const { patientId = "" } = patient;
    const { code, data = {}, msg } = await Api.getPatientInfo({
      hisId,
      platformId,
      platformSource,
      patientId,
      idFullTransFlag: 1
    });
    if (code !== 0) {
      return;
    }
    this.getRelationPatients(data);
  }
  async getRelationPatients(options = {}) {
    const { patientFullIdNo = "", patientName = "" } = options;
    const { code, data = {}, msg } = await Api.queryRelationPatients({
      idNo: patientFullIdNo,
      patName: patientName,
      qryType: 2
    });
    if (code !== 0) {
      return;
    }
    const { itemList = [{}] } = data;
    const { items = [] } = itemList[0];
    let patientList = [];
    items.map(item => {
      if (["男方", "女方"].indexOf(item.familyMemberName) !== -1) {
        item.patAge = '-';
        if(item.patBirth){
          const birthDate = new Date(item.patBirth.replace(/-/g, "/"));
          item.patAge = new Date().getFullYear() - birthDate.getFullYear();
        }
        patientList.push(item);
      }
    });
    this.patientList = patientList;
    this.$apply();
  }
  async getReport(pid) {
    const { code, data = {}, msg } = await Api.getReport({ pid: pid });
    if (code !== 0) {
      return;
    }
    this.report = Object.keys(data).length == 0 ? null : data;
    this.$apply();
  }
  methods = {};
}
</script>

