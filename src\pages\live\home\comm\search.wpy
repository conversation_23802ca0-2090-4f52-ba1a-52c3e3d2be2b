<!--  -->
<template>
  <view class='comm-search'>
    <image class="search-icon" src="REPLACE_EHOSPITAL_DOMAIN/live-icon-search.png" />
    <input class="search-input" @input="onInput" placeholder="直接搜索医生或内容" />
  </view>
</template>

<script>
  import wepy from 'wepy';
  export default class Search extends wepy.component {
    props = {};
    data = {};
    methods = {
      onInput(e) {
        this.$emit('onSearch', e.detail.value);
      },
    };
  }
</script>

<style lang='less'>
.comm-search {
  background: #ffffff;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  display: flex;
  align-items: center;
  margin: 0 30rpx;
  font-size: 26rpx;
  .search-icon {
    margin-right: 30rpx;
    height: 36rpx;
    width: 36rpx;
  }
  .search-input {
    width: 100%;
  }
}
</style>