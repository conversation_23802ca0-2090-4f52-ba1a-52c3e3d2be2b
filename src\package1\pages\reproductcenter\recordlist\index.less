@import "../../../../resources/style/mixins";

page {
  position: relative;
  font-size: 30rpx;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.9);
  background-color: #f6f7f9;
  font-family: PingFangSC-Regular, -apple-system-font, Helvetica Neue, Helvetica,
    sans-serif;
}

.p-page {
}

.m-list {
  padding: 40rpx 32rpx;
  overflow: hidden;
  .list-item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 24rpx;

    &.list-item-S,
    &.list-item-L {
    }
    &.list-item-F {
    }
    &.list-item-C {
    }
  }
  .item-icon {
    width: 32rpx;
    height: 32rpx;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 16rpx;
    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-main {
    flex: 1;
  }
  .main-tit {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
  }
  .main-txt {
    font-size: 28rpx;
    color: @hc-color-text;
    margin-top: 8rpx;
  }
  .item-extra {
    margin-top: 24rpx;
  }
  .extra-tit {
    font-size: 28rpx;
    margin-top: 8rpx;
    color: @hc-color-assist;
  }
  .extra-txt {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .list-item {
    .item-icon {
      background-color: @hc-color-warn;
    }
    &.list-item-success,
    &.list-item-lock {
      .item-icon {
        background-color: @hc-color-primary;
      }
      .extra-tit {
        color: @hc-color-assist;
      }
    }
    &.list-item-fail {
      .item-icon {
        background-color: @hc-color-error;
      }
    }
    &.list-item-cancel {
      .item-icon {
        background-color: @hc-color-text;
      }
    }
    &.list-item-abnormal {
      .item-icon {
        background-color: @hc-color-warn;
      }
    }
  }
  .unit-label {
    margin-left: 16rpx;
    display: inline-block;
    font-size: 22rpx;
    padding: 4rpx 16rpx;
    color: #fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 60rpx;
  }
}
