<view class="p-page page-phone">
  <view class="new-phone">
    <view class="phone-listitem">
      <view class="listitem-head">
      <text class="list-title">手机号</text>
      </view>
      <view class="listitem-bd">
        <input class="m-content" maxlength="11" value="{{phone}}" disabled />
      </view>
      <view wx:if="{{!isSendValidate}}" class="listitem-ft" @tap="getValidate">获取验证码</view>
      <view wx:if="{{isSendValidate}}" class="listitem-ft">{{leftTime}}s 后重新获取</view>
    </view>
    <view class="phone-listitem">
      <view class="listitem-head">
        <text class="list-title">验证码</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.validateCode ? 'o-error' : ''}}" type="number" placeholder="请输入验证码"
          cursor-spacing="100" @input="userIO" @focus="resetThisError"
          placeholder-style="color:{{errorElement.patCardNo ? 'color: #ccc;' : ''}}"
          id="validateCode" maxlength="6" @blur="validator" value="{{validateCode}}" 
        />
      </view>
    </view>
  </view>
  <view class="btn">
    <button class="submit-btn {{validateCode ? '' : 'hidden-btn'}}" hover-class="btn-disable" @tap="submitData">确定</button>
  </view>
</view>
<toptip :toptip.sync="toptip" />