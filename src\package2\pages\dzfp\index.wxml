<view class="p-page">
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block wx:for="{{orderList || []}}" wx:key="index">
        <view class="list-item list-item-{{item.status == 1 ? 'success' : 'abnormal'}}">
          <view class="item-main">
            <view class="main-tit">
              <view class="item-icon">
                <image class="item-status" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{item.status == 1 ? 'success' : 'abnormal'}}.png"></image>
              </view>
              <text>{{item.status == 1 ? '已开票' : '待开票'}}</text>
            </view>
            <view class="action-btn" @tap="bindQueryEleck({{item}})">{{item.status == '1' ? '查看发票' : '开票'}}</view>
          </view>
          <view class="item-extra">
            <view class="main-txt">就诊人：{{item.patName}}</view>
            <view class="main-txt">缴费时间：{{item.regDate}}</view>
            <view class="extra-tit">支付金额：<text class="fee">￥{{item.totalAmount}}</text></view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">未查询到开票数据</block>
    </empty>
  </block>
</view>