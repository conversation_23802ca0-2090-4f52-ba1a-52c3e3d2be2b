<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy"
import * as Api from "./api"

export default class WebView extends wepy.page {
  config = {
    navigationBarTitleText: ""
  }

  onLoad(options) {
    this.options = options
    this.$apply()
    this.getCardList()
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    weburl: "",
    options: {}
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {}

  async getCardList() {
    const { data } = await Api.getCardList({ noAuthOn999: true })
    const { cardList = [] } = data
    this.cardNumber = cardList.length
    let defaultUser = {}
    for (let i = 0; i < cardList.length; i++) {
      // 只显示本人信息
      if (cardList[i].relationType == 1) {
        defaultUser = cardList[i]
        break
      }
    }
    if (!defaultUser.patHisNo) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "您还尚未绑定任何就诊人，绑定后重新扫码填写问卷。",
        showCancel: false,
        confirmText: "立即绑定",
        confirmColor: "#3ECDB5"
      })
      if (showModalRes.confirm) {
        wepy.navigateTo({
          url: "/pages/bindcard/queryuserinfo/index?qryType=1"
        })
      }
      return
    } else {
      let url = "https://bcyy.zxxyyy.cn/patients-new/#/questionnaires/covid"
      if (this.options.type === "bcyy") {
        url =
          "https://bcyy.zxxyyy.cn/patients-new/#/questionnaires/pre-diagnosis"
      }
      // let url = decodeURIComponent(this.options.url || "")
      if (url) {
        this.weburl = `${url}?pid=${defaultUser.patHisNo}&grid=${
          defaultUser.patCardNo
        }`
        this.$apply()
      }
    }
  }
}
</script>
