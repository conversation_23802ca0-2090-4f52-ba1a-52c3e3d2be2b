<view class="p-page">
  <view class="m-search-content">
    <block wx:if="{{deptList.length == 0 && doctorList.length == 0}}">
      <empty>
        <block slot="text">暂未查询到相关信息</block>
      </empty>
    </block>
    <block wx:else>
      <view class="search-content">
        <block wx:if="{{doctorList.length > 0}}">
          <view class="content">
            <view class="content-title">医生</view>
            <view class="content-list">
              <view
                class="list-item"
                wx:for="{{doctorList || []}}"
                wx:for-index="index"
                wx:for-item="item"
                wx:key="index"
                @tap="bindToDocInfo({{item}})"
              >
                <view class="item-image">
                  <image
                    mode="widthFix"
                    src="{{item.doctorImg || ('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png')}}"
                  ></image>
                </view>
                <view class="item-info">
                  <view class="info-title">{{item.doctorName}}</view>
                  <view class="info-text">{{item.deptName}} | {{item.doctorTitle}}</view>
                  <!-- <view class="info-text">{{item.doctorTitle}}</view> -->
                  <view class="info-des">{{item.doctorSkill || ''}}</view>
                </view>
              </view>
            </view>
          </view>
        </block>
        <block wx:if="{{deptList.length > 0}}">
          <view class="content">
            <view class="content-title">科室</view>
            <view class="content-list">
              <view
                class="list-item"
                wx:for="{{deptList || []}}"
                wx:for-index="index"
                wx:for-item="item"
                wx:key="index"
                @tap="bindToDocList({{item}})"
              >
                <!-- <view class="item-icon">
                  <image
                    mode="widthFix"
                    src="{{item.doctorImg || ('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/dept.png')}}"
                  ></image>
                </view> -->
                <view class="item-info flex-between">
                  <view class="info-deptinfo">{{item.deptName}}</view>
                  <view class="item-arrow"></view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </block>
  </view>
</view>