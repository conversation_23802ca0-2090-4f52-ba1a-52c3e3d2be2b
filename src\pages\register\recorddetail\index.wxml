<view class="p-page {{(detailData.canPayFlag == 1 && leftTimeFlag) ? 'scroll-page' : ''}}">
  <detail-status :config.sync="statusConfig" :leftTime.sync="leftTime">
    <block slot="title">{{statusConfig.statusName}}</block>
    <block slot="text">
      <view>{{statusConfig.text}}</view>
    </block>
  </detail-status>
  <view class="p-page-main">
    <view class="m-code {{detailData.status === 'S' ? 'active' : ''}}">
      <view class="code-tit">就诊凭条</view>
      <view class="code-img"> 
        <block wx:if="{{detailData.patCardNo}}">
          <image
            mode="widthFix"
            src="https://wechat.jiahuiyiyuan.com/barcode?msg={{detailData.patCardNo}}&type=code128&mw=.60"
            alt=""
          ></image>
        </block>
      </view>
    </view>
    <!-- <block wx:if="{{isAbnormal}}">
      <view class="m-retry">
        <view class="retry-btn" @tap="bindRetryOrder">点击重试刷新</view>
      </view> -->
    </block>
    <!-- <refund-list :refundList.sync="refundList"></refund-list> -->
    <basic-detail :detailData.sync="detailData"></basic-detail>
    <pay-detail :detailData.sync="detailData" :isExpand.sync="payIsExpand"></pay-detail>
  </view>
  <!--<block wx:if="{{detailData.canPayFlag == 1 && leftTimeFlag}}">
    <view class="m-pay">
      <view class="pay-btn" @tap="bindContinuePay">继续支付</view>
    </view>
  </block>-->
  <block wx:if="{{detailData.canCancelFlag == 1}}">
    <view class="m-cancel">
      <view class="cancel-btn" @tap="bindCancelOrder">取消挂号</view>
    </view>
  </block>
</view>
<view class="btn-box" wx:if="{{detailData.canPayFlag == 1 && leftTimeFlag}}">
  <view class="pay-money">
    <text>共计：</text>
    <text>¥{{WxsUtils.formatMoney(detailData.totalRealFee, 100)}}</text>
  </view>
  <view class="btn">
    <view class="m-btn">
      <view class="cancel-btn" @tap="bindCancelOrder">取消</view>
    </view>
    <view class="m-btn">
      <view class="pay-btn" @tap="bindContinuePay">去缴费</view>
    </view>
  </view>
</view>
