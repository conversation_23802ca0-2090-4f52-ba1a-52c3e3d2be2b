@import "../../../../resources/style/mixins";

page{
  width: 100%;
  overflow-x: hidden;
  background: #fff;
}

.p-page{
  width: 100%;
  overflow-x: hidden;
}

.m-banner{
  width: 100%;
  position: relative;
  overflow: visible;

  .banner-image{
    width: 750rpx;
  }
  .his-logo{
    position: absolute;
    left: 50%;
    bottom: -84rpx;
    width: 168rpx;
    z-index: 1;
    margin-left: -84rpx;
    border-radius: 50%;
    overflow: hidden;

    .logo-img{
      width: 168rpx;
      height: 168rpx;
      border-radius: 50%;
    }
  }
}

.m-hospinfo{
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  
  .his-name{
    margin: 0 30rpx;
    padding-bottom: 20rpx;
    text-align: center;
    color: rgba(0, 0, 0, 0.90);
    font-size: 40rpx;
    font-weight: bold;
  }
  .intro-box{
    padding: 0 40rpx;
  }
  .his-intro{
    width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
  }
}
