<view class="guide-index">
  <outpatient :config.sync="outpatientConfig" :patient.sync="outpatient" :emptyNotice.sync="emptyNotice"></outpatient>
  <datecheck :dateList.sync="dateList" :checkIndex.sync="checkDateIndex" />
  <view class="table-tips" wx:if="{{guideData[checkDateIndex] && guideData[checkDateIndex].length != 0}}">
    左右滑动查看更多
  </view>
  <scroll-view class="table-box" scroll-x wx:if="{{guideData[checkDateIndex] && guideData[checkDateIndex].length != 0}}">
    <view class="guide-table">
      <view class="g-th">
        <text class="g-td name">项目名称</text>
        <text class="g-td dept">科室</text>
        <text class="g-td address">地址</text>
        <text class="g-td tips">注意事项</text>
      </view>
      <view class="g-tr" wx:for="{{guideData[checkDateIndex]}}" wx:key="{{index}}">
        <text class="g-td name">{{item.info || '-'}}</text>
        <text class="g-td dept">{{item.deptName || '-'}}</text>
        <text class="g-td address">{{item.deptLocation || '-'}}</text>
        <text class="g-td tips">{{item.attention || '-'}}</text>
      </view>
    </view>
  </scroll-view>
  <empty wx:else>
    <text slot="text">你还没有任何导诊信息</text>
  </empty>
</view>