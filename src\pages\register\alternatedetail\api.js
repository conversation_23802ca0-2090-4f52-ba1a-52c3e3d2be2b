import { post } from "@/utils/request";
import { REQUEST_QUERY } from "@/config/constant";

export const orderDetail = (param) =>
  post(
    `/api/customize/getWaitQueueDetails?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const cancelOrder = (param) =>
  post(
    `/api/customize/cancelWaitQueue?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const manualNotify = (param) => post("/api/order/manualnotify", param);
