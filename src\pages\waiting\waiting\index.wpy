<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  import { RETRY_MAP, TYPE_MAP } from '@/config/constant';
  import { sleep } from '@/utils/utils';

  import * as Api from './api';

  const statusMap = {
    MZJF: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    YYGH: {
      ABNORMAL: {
        U: '初始预登记',
        L: '锁号成功',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '预约失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    YYQH: {
      ABNORMAL: {
        U: '初始预登记',
        L: '锁号成功',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '取号失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    DBGH: {
      ABNORMAL: {
        U: '初始预登记',
        L: '锁号成功',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '挂号失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    ZYYJBJ: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '住院押金补缴失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    SCYJJN: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '住院押金缴纳失败',
      },
    },
    YFKCZ: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '充值失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    YPJS: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '药品寄送提交失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    SZZXJF: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
      },
      ALERT: {
        F: '生殖中心缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    DJJF: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
        // S:'缴费成功'
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    BLFY: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
        // S:'缴费成功'
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    ZZKD:{
       ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
        // S:'缴费成功'
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    VIP: {
      ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
        H: '调用医院接口异常',
        // S:'缴费成功'
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    },
    SMKD: {
      ABNORMAL: {
        // S: '补缴成功',
        H: '调用医院接口异常',
        Z: '调用医院接口异常',
      },
      ALERT: {
        F: '缴费失败',
      },
      HIFOND_ABNORMAL: {
        U: '初始预登记',
        P: '处理中',
      }
    }
  };
  const urlMap = {
    DBGH: '/pages/register/recorddetail/index',
    YYGH: '/pages/register/recorddetail/index',
    YYQH: '/pages/takeno/recorddetail/index',
    MZJF: '/pages/treat/recorddetail/index',
    YFKCZ: '/pages/recharge/recorddetail/index',
    ZYYJBJ: '/pages/inhosp/recorddetail/index',
    SCYJJN: '/pages/inhosp/recorddetail/index',
    YPJS: '/pages/sendmedicine/recorddetail/index',
    SZZXJF: '/package1/pages/reproductcenter/recorddetail/index',
    DJJF:'/pages/forzensemen/recorddetail/index',
    BLFY:'/package2/pages/medical/recorddetail/index',
    ZZKD:'/pages/servicebill/servicebilldetail/index',
    VIP:'/pages/consultservice/recorddetail/index',
    SMKD: '/package2/pages/scancode/recorddetail/index'
  };

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '支付处理中',
    };

    components = {};

    onLoad(options) {
      console.log(options,'option')
      this.options = options;
      const { time, type } = options;
      if (!TYPE_MAP[type]) {
        console.log('未知的业务类型');
        return;
      }

      if (typeof time !== 'undefined') {
        this.leftTime = time;
      }

      this.initPage();
    }

    onUnload() {
      // 清空计时器
      clearTimeout(this.clockTimer);
      clearTimeout(this.statusTimer);
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 页面参数
      options: {},
      // 倒计时剩余数
      leftTime: 60,
      // 结束标志
      endFlag: false,
      // 倒计时计时器
      clockTimer: 0,
      // 状态计时器
      statusTimer: 0,
      // 订单数据
      orderData: {},
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {};

    events = {};

    async initPage(){
      await sleep(1000);
      this.clock();
      this.getStatus();
      this.$apply();
    }

    /**
     * 获取订单状态
     */
    async getStatus() {
      const { orderId, type } = this.options;
      const param = {
        orderId: orderId || '',
        bizType: TYPE_MAP[type],
      };
      // 通用业务下单类型
      const COMMON_TYPE = ['YPJS', 'SZZXJF','DJJF','ZZKD','VIP','BLFY', 'SMKD'];
      const { code, data = {}, msg } = COMMON_TYPE.includes(type) ? await Api.extOrderStatus(param) : await Api.orderStatus(param);
            this.orderData = data;
//      this.orderData = { status: 'F' };
      if (code == 0) {
        this.analysisOrderStatus();
      } else {
        this.beforeNext();
      }
    }

    /**
     * 分析订单状态，确定下一步任务
     */
    analysisOrderStatus() {
      const { status = '' } = this.orderData || {};
      const { leftTime, options = {} } = this;
      const { type = '' } = options;
      if (this.endFlag) {
        // 其他地方触发结束，不继续
        return;
      }
      if (statusMap[type] && !statusMap[type]['ABNORMAL'][status]) {
        // 明确状态或者未知状态
        this.beforeNext(true);
      } else if (statusMap[type] && statusMap[type]['ALERT'][status]) {
        // 明确失败
        this.beforeNext(true);
      } else if (leftTime <= 0) {
        // 查询超时
        this.beforeNext();
      } else {
        this.statusTimer = setTimeout(() => {
          this.getStatus();
        }, 2000);
      }
    };

    /**
     * 倒计时
     */
    clock() {
      this.clockTimer = setTimeout(() => {
        if (this.endFlag) {
          return;
        }

        let { leftTime } = this;
        --leftTime;
        if (leftTime <= 0) {
          // 查询超时，跳转详情页面
          this.beforeNext();
        } else {
          this.leftTime = leftTime;
          this.clock();
          this.$apply();
        }
      }, 1000);
    }

    /**
     * 跳转之前的相应逻辑
     * @param sucFlag
     */
    async beforeNext(sucFlag) {
      this.endFlag = true;
      const { orderData = {} } = this;
      if (orderData.status === 'F') {
        // 明确失败，弹窗提示错误原因
        await wepy.showModal({
          title: '处理失败',
          content: '已为您成功办理退款，请注意查收',
          showCancel: false,
        });
      }
      this.goNext(sucFlag);
    };

    /**
     * 跳转至详情页
     * @param sucFlag
     */
    goNext(sucFlag) {
      const { type, from, orderId, id = '', regType = '' } = this.options;
      const url = urlMap[type];
      console.log(url,'url')
      if (!RETRY_MAP[type] || sucFlag || from == 'detail') {
        // 不配置重发机制以及订单状态正常或者来源是详情页的时候才跳入详情页
        console.log('GoTo Detail');
        wepy.redirectTo({
          url: `${url}?orderId=${orderId}&id=${id}&regType=${regType}`
        });
      } else {
        // 其他情况统一跳入异常页面
        console.log('GoTo Abnormal');
        wepy.redirectTo({
          url: `/pages/waiting/abnormal/index?orderId=${orderId}&type=${type}`
        });
      }
    };
  }
</script>
