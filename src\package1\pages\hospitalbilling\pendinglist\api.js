import { REQUEST_QUERY } from '@/config/constant';
import { post } from '@/utils/request';

//就诊人详情
export const getPatientInfo = (param) => post('/api/user/patientinfo',param);

export const getOutHospitalPendingList = (param) => post(`/api/osa/queryWaitSettlementRecord`, param);

export const getIsFinished = (param) => post(`/api/customize/queryWaitSettlementIsFinished?_route=h${REQUEST_QUERY.platformId}`, param);

export const getTipsText = (param) => post(`/api/register/getNoteProfile`, param);
