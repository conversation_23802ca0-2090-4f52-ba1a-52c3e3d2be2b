@import "../../resources/style/mixins";

.g-search {
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  // box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0, 0, 0, 0.03);
}

.m-search {
  display: flex;
  width: 100%;
  flex-direction: row;
  padding: 40rpx 24rpx 0;
  background-color: #fff;
  align-items: center;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  &.active {
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
  }
  .search-ipt {
    flex: 1;
    height: 82rpx;
    padding-left: 60rpx;
    padding-top: 15rpx;
    padding-bottom: 15rpx;
    position: relative;
    border: 2rpx solid rgba(0, 0, 0, 0.10);
    border-radius: 8rpx;
    overflow: hidden;
    box-sizing: border-box;
  }
  .ipt {
    width: 100%;
    font-size: 30rpx;
    height: 52rpx;
    line-height: 52rpx;
  }
  .ipt-icon {
    position: absolute;
    left: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 28rpx;
    height: 28rpx;
    background: url("REPLACE_IMG_DOMAIN/his-miniapp/images/search.png")
      no-repeat 50% 50%;
    background-size: 100% 100%;
  }
  .ipt-icon-cancel {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 28rpx;
    height: 28rpx;
  }
  .search-extra {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 10rpx;
  }
  .extra-cancel {
    margin-left: 32rpx;
    font-size: 30rpx;
    color: @hc-color-title;
  }
  .extra-itm {
    position: relative;
    margin-left: 20rpx;
    width: 40rpx;
    height: 40rpx;
    padding: 10rpx;
  }
  .itm-qr {
    width: 40rpx;
    height: 40rpx;
  }
  .itm-notice {
    width: 35rpx;
    height: 40rpx;
  }
  .itm-notice-num {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    padding: 0;
    background-color: red;
    color: #fff;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    overflow: hidden;
    text-align: center;
    font-size: 24rpx;
    border-radius: 50%;
    transform: translate(40%, -40%);
  }
}

.m-search-content {
  position: fixed;
  left: 0;
  top: 146rpx;
  bottom: 0;
  width: 100%;
  z-index: 10;
  box-sizing: border-box;
  border-top: 2rpx solid #efefef;
  overflow-y: auto;
  background-color: #fff;
  -webkit-overflow-scrolling: touch;
  .search-content {
    width: 100%;
    height: 100%;
  }
  .content {
    background-color: #fff;
    margin: 24rpx 32rpx;
    border-radius: 24rpx;
  }
  .content-title {
    color: @hc-color-title;
    padding: 24rpx 32rpx;
    font-weight: 600;
    font-size: 28rpx;
  }
  .content-list {
    padding: 0 32rpx;
  }
  .list-item {
    display: flex;
    flex-direction: row;
    // align-items: center;
    align-items: flex-start;
    padding: 24rpx 0;
    border-top: 2rpx solid @hc-color-border;
    &:first-child {
      border-top: none;
    }
  }
  .item-icon {
    display: flex;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
    image {
      width: 80rpx;
      height: 80rpx;
      vertical-align: top;
    }
  }
  .item-image {
    display: flex;
    width: 106rpx;
    height: 133rpx;
    border-radius: 8rpx;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
    image {
      vertical-align: top;
    }
  }
  .item-info {
    flex: 1;
    overflow: hidden;
  }
  .info-deptinfo {
    font-size: 32rpx;
    color: @hc-color-title;
  }
  .info-title {
    color: @hc-color-title;
    font-weight: 600;
    font-size: 36rpx;

    text {
      font-size: 28rpx;
      color: @hc-color-text;
    }

    .title {
      color: @hc-color-title;
      font-size: 36rpx;
    }

    .info-status {
      height: 38rpx;
      padding: 4rpx 16rpx;
      border-radius: 60rpx;
      font-weight: 600;
      font-size: 20rpx;

      &.has-source {
        color: @hc-color-primary;
        background: @hc-color-primary-bg;
      }
      &.no-source {
        color: @hc-color-text;
        background: @hc-color-bg;
      }
    }
  }
  .info-more {
    text-align: center;
    font-size: 26rpx;
    color: @hc-color-link;
  }
  .info-text {
    margin-top: 5rpx;
    font-size: 28rpx;
    color: @hc-color-text;
    .ellipsis();
  }
  .info-des {
    margin-top: 5rpx;
    font-size: 26rpx;
    color: @hc-color-text;
    .ellipsis();
  }
  .info-address {
    margin-top: 5rpx;
    font-weight: 600;
    font-size: 28rpx;
    .info-address-base1 {
      color: @hc-color-primary;
    }
    .info-address-base2 {
      color: @hc-color-link;
    }
  }
  .item-arrow {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }

  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
