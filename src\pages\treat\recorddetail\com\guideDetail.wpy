<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <folding :isExpand.sync="isExpand">
      <block slot="title">导诊信息</block>
      <block slot="content">
        <view class="list">
          <view class="m-table">
            <view class="table">
              <view class="tb-head">
                <view class="head-tr">
                  <view class="head-td td-2">项目名称</view>
                  <view class="head-td td-1 td-center">科室</view>
                  <view class="head-td td-1 td-center">地址</view>
                  <view class="head-td td-1 td-center">注意事项</view>
                </view>
              </view>
              <view class="tb-body">
                <block wx:if="{{guideData.length > 0}}">
                  <view class="body-tr" wx:for="{{guideData}}" wx:key="{{index}}">
                    <view class="body-td td-2">{{item.itemName}}</view>
                    <view class="body-td td-1 td-center">{{item.executeDept}}</view>
                    <view class="body-td td-1 td-center">{{item.executePlace}}</view>
                    <view class="body-td td-1 td-center">{{item.status}}</view>
                  </view>
                </block>
                <view class="body-tr empty-tr" wx:else>暂无导诊信息</view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </folding>
  </view>
</template>
<script>
import wepy from "wepy";
import Folding from "@/components/folding/index";

export default class GuideDetail extends wepy.component {
  components = {
    folding: Folding
  };
  props = {
    isExpand: {
      type: Boolean,
      default: true,
      twoWay: true
    },
    guideData: {
      type: Array,
      default: []
    }
  };
  data = {};
  onLoad() {}
  methods = {};
}
</script>
