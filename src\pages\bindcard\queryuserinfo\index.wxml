<view class="top-msg">为提供更好的服务，我院进行了系统升级，请您按如下流程进行绑定登录。本院实行实名制就诊，请确保您填写的信息真实准确。</view>
<view class="afterscan-cardviewbox" hidden="{{!idCardPath}}" @tap="reUpload">
  <view class="afterscan-cardview">
    <view class="cardview-imgbox">
      <image src="{{idCardPath}}" mode="aspectFit" @error="getFilePathByToken" class="cardview-img" @load="setImgSize"
        style="width:{{imgWidth}}rpx;height:{{imgHeight}}rpx;" />
    </view>
    <view class="afterscan-reupload">重新上传</view>
  </view>
</view>
<form bindsubmit="formSubmit" report-submit='true'>
  <view class="bind-card-container">
    <view class="bindcard-list">
      <view class="bindcard-listitem">
        <view class="listitem-head">
          <text class="list-title require">姓名</text>
        </view>
        <view class="listitem-body">
          <input class="m-content {{errorElement.patientName ? 'o-error' : ''}}" placeholder="请输入姓名"
            cursor-spacing="{{CURSOR_SPACING}}"
            placeholder-style="color:{{errorElement.patientName ? errorColor : placeholderColor}}" maxlength="20"
            id="patientName" @input="inputTrigger" @focus="resetThisError" value="{{options.name}}" />
        </view>
      </view>
      <view class="bindcard-listitem no-after">
        <view class="listitem-head">
          <view class="list-title require {{hisConfig.idTypes.length > 1 ? 'list-title_select' : ''}}" 
            @tap="actionSheetType" data-prop="{{hisConfig.idTypes.length > 1 ? 'idTypes' : ''}}" wx:if="{{isOtherType}}">
            <!-- 身份证 -->
            {{hisConfig.idTypes[idTypesIdx].dictValue}}
          </view>
          <view class="list-title require " wx:else>{{hisConfig.idTypes[idTypesIdx].dictValue}}</view>
        </view>
        <view class="listitem-body">
          <input class="m-content {{errorElement.idNo ? 'o-error' : ''}}"
            type="{{hisConfig.idTypes[idTypesIdx].inputType || 'text'}}"
            placeholder="请输入{{hisConfig.idTypes[idTypesIdx].dictValue}}号"
            placeholder-style="color:{{errorElement.idNo ? errorColor : placeholderColor}}" id="idNo" 
            maxlength="{{hisConfig.idTypes[idTypesIdx].dictKey == 1 ? 18 : -1 }}"
            cursor-spacing="{{CURSOR_SPACING}}" @input="inputTrigger" @focus="resetThisError" value="{{idNo}}" />
        </view>
      </view>
      <!-- <view class="bindcard-listitem">
        <view class="listitem-head">
          <text class="list-title">住址</text>
        </view>
        <view class="listitem-body">
          <input class="m-content {{errorElement.patientAddress ? 'o-error' : ''}}" type="text" placeholder="请输入住址"
            maxlength="30" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
            placeholder-style="color:{{errorElement.patientAddress ? errorColor : placeholderColor}}" id="patientAddress"
            @input="inputTrigger" @focus="resetThisError" value="{{options.address}}" />
        </view>
      </view> -->
    </view>
    <view class="bindcard-list" wx:if="{{qryType == 1}}">
      <view class="bindcard-listitem tel-listitem">
        <view class="bindcard-listitem-box">
          <view class="listitem-head">
            <text class="list-title require">手机号</text>
          </view>
          <view class="listitem-body">
            <block wx:if="{{!mobileReadonly || idCardPath}}">
              <input class="m-content {{errorElement.patientMobile ? 'o-error' : ''}}" style="width: 340rpx;" type="number" placeholder="请输入手机号码"
                cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError"
                placeholder-style="color:{{errorElement.patientMobile ? errorColor : placeholderColor}}" id="patientMobile"
                maxlength="11" @input="inputTrigger" disabled="{{mobileReadonly}}" value="{{patientMobile}}"  />
            </block>
            <block wx:else>
              <input class="m-content {{errorElement.patientMobile ? 'o-error' : ''}}" style="width: 340rpx;" type="number" placeholder="请输入手机号码"
                cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError"
                placeholder-style="color:{{errorElement.patientMobile ? errorColor : placeholderColor}}" id="patientMobile"
                maxlength="11" @input="inputTrigger" value="{{patientMobile}}"  disabled="{{mobileReadonly}}" />
            </block>
            <!-- <view class="m-verifycode-btn">获取验证码</view> -->
          </view>
          <block wx:if="{{qryType == 1}}">
            <view class="listitem-footer" wx:if="{{!isClocking}}">
              <text class="verify-code" @tap="sendVerifyCode">获取验证码</text>
            </view>
            <view class="listitem-footer left-time" wx:if="{{isClocking}}">
              <text class="second-code" @tap="sendVerifyCode">{{leftTime}}S</text>
              <text class="verify-code">后重新获取</text>
            </view>
          </block>
        </view>
        <block wx:if="{{mobileReadonly && !this.idCardPath && patientMobile}}">
          <!-- <view @tap="gotoUpload" class="m-tips m-color-fail">{{promotTitle}}</view> -->
          <view class="m-tips m-color-fail">{{promotTitle}}</view>
        </block>
      </view>
      
      <!-- wx:if="{{isNewCard == 0}}" -->
      <block wx:if="{{qryType == 1}}">
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <view class="list-title require">验证码</view>
          </view>
          <view class="listitem-body">
            <input class="m-content {{errorElement.verifyCode ? 'o-error' : ''}}" id="verifyCode"
              cursor-spacing="{{CURSOR_SPACING}}" @input="inputTrigger" @focus="resetThisError" placeholder="请输入验证码"
              maxlength="6"
              placeholder-style="color:{{errorElement.verifyCode ? errorColor : placeholderColor}}" />
          </view>
        </view>
      </block>
      <view class="bindcard-listitem">
        <view class="listitem-head">
          <text class="list-title require">家庭住址</text>
        </view>
        <view class="listitem-body">
          <picker mode="region" bindchange="bindRegionChange" value="{{areaCode || []}}" class="area-box">
            <view class="picker picker-wrap" style="color:{{areaCode && areaCode.length > 0?'':'#bbb'}}">
              <block wx:if="{{areaCode && areaCode.length > 0}}">
                <view class="picker-info">{{areaName[0]}}-{{areaName[1]}}-{{areaName[2]}}</view>
              </block>
              <block wx:else>
                <view class="picker-info placeholder-text" style="color:{{errorElement.pro ? errorColor : placeholderColor}}">请选择所在省市区/县</view>
              </block>
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
      </view>
      <view class="bindcard-listitem no-after">
        <view class="listitem-head">
          <text class="list-title">详细地址</text>
        </view>
        <view class="listitem-body">
          <input class="m-content {{errorElement.address ? 'o-error' : ''}}" placeholder="请输入详细地址（例如**街**号**）"
            cursor-spacing="{{CURSOR_SPACING}}"
            placeholder-style="color:{{errorElement.address ? errorColor : placeholderColor}}"
            id="address" @input="inputTriggerAddress" @focus="resetThisError"/>
        </view>
      </view>
    </view>
  </view>
  <!-- <view class="bindcard-list m-mt-20">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">病历号(PID号)</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.pid ? 'o-error' : ''}}" type="number" placeholder="病历号(初诊病人可不填)"
          cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError"
          placeholder-style="color:{{errorElement.pid ? errorColor : placeholderColor}}" id="pid" maxlength="8"
          @input="inputTrigger" />
      </view>
    </view>
  </view> -->
  <view class="agreement-box">
    <checkbox-group bindchange="bindchange">
      <checkbox color="#30A1A6" value="{{agreementchecked}}" />
      <label>已阅读并同意
        <text class="content" @tap="goAgreement" data-url="yhfwxx.pdf">《用户服务协议》</text>
        及
        <text class="content" @tap="goAgreement" data-url="xcxysxy.pdf">《隐私政策》</text>
      </label>
    </checkbox-group>
  </view>
  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">登录</button>
  </view>
</form>
<block wx:if="{{peopleList.length > 0}}">
  <view class="m-list">
    <block wx:for="{{peopleList}}" wx:key="index">
      <navigator class="m-card" url="/pages/bindcard/adduser/index?qryType={{qryType}}&idNo={{item.idNo}}&idType={{item.idType}}&patientName={{item.patientName || item.patName}}&patientMobile={{item.telephone}}&pid={{item.pid}}&address={{patientAddress}}">
        <view class="card-info">
          <view class="info-main">
            <view class="main-name">
              <view class="name">{{item.patName}}</view>
              <view class="status {{ itm.familyMemberName == '女方' ? 'status_1':'status_2'}}">{{ item.familyMemberName}}</view>
            </view>
          </view>
          <view class="info-extra">{{item.patCardTypeName || '就诊卡'}}：{{item.grid}}</view>
        </view>
      </navigator>
    </block>
  </view>
</block>

<toptip :toptip.sync="toptip" />
<bottom-logo />
