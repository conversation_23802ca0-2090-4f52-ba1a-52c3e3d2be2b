<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy";
import NavTab from "@/components/navtab/index";
import empty from "@/components/empty/index";
import { REQUEST_QUERY } from "@/config/constant";
import * as Api from "./api";

export default class Ask extends wepy.page {
  config = {
    navigationBarTitleText: "在线问诊"
  };
  components = {
    "nav-tab": NavTab,
    empty1: empty,
    empty2: empty,
  };
  data = {
    tabs: [
      { id: 1, text: "待接诊", num: 0 },
      { id: 2, text: "已结束", num: 0 }
    ],
    // 当前选项
    activeIndex: 0,
    // tabbar类型
    navTabType: "ask",
    // 待接诊列表
    waitingList: [],
    // 已结束列表
    endList: [],
    emptyConfig1: {
      show: true,
    },
    emptyConfig2: {
      show: true,
    },
    patCardNo: '',
  };
  onShow() {
    const {
      outpatient: { cardList = [] }
    } = this.$parent.globalData;
    const myself = cardList.find(item => {
      return item.relationType == 1;
    });
    const { patCardNo = "" } = myself || {};
    if(!patCardNo){
      return;
    }
    this.patCardNo = patCardNo;
    const { hisId = "" } = REQUEST_QUERY;
    this.queryFeeChatList({ patCardNo, hisId });
    this.queryFeeChatListEnd({ patCardNo, hisId });
    this.$apply();
  }
  // 获取待接诊列表
  async queryFeeChatList(param) {
    const { code, data = [] } = await Api.queryFeeChatList({
      ...param,
      isEndFeeType: "0"
    });
    if (code != 0) {
      return;
    }
    this.waitingList = data;
    if (data.length > 0) {
      this.emptyConfig1.show = false;
    }
    this.$apply();
  }
  // 获取已结束列表
  async queryFeeChatListEnd(param) {
    const { code, data = [] } = await Api.queryFeeChatList({
      ...param,
      isEndFeeType: "1"
    });
    if (code != 0) {
      return;
    }
    this.endList = data;
    if (data.length > 0) {
      this.emptyConfig2.show = false;
    }
    this.$apply();
  }
  methods = {
    checkThis(index) {
      const { hisId = "" } = REQUEST_QUERY;
      const patCardNo = this.patCardNo;
      this.activeIndex = index;
      this.emptyConfig1.show = true;
      this.emptyConfig2.show = true;
      if (index == 0) {
        this.queryFeeChatList({ patCardNo, hisId });
      } else {
        this.queryFeeChatListEnd({ patCardNo, hisId });
      }
    },
    async toChat(item) {
      if (item.isEndFeeType == 0) {
        const { code, data } = await Api.creatFeeChat({
          patCardNo: item.grid,
          ...item,
          hisId: REQUEST_QUERY.hisId
        });
        if (code != 0) {
          return;
        }
        wepy.navigateTo({
          url: `/pages/consult/chat/index?groupId=${data}&chatType=${item.chatType}`
        });
      } else {
        wepy.navigateTo({
          url: `/pages/consult/chat/index?groupId=${item.id}&doctorId=${item.doctorCode}&deptId=${item.depCode}&status=3&chatType=${item.chatType}`
        });
      }
    }
  };
}
</script>
