// @import "../../resources/style/mixins";
@import '../../../resources/style/mixins.less';

page {
  position: relative;
  font-size: 28rpx;
  height: 100%;
  overflow: hidden;
  line-height: 1.5;
  color: #000000;
  background-color: #f4f9f9;
  font-family: PingFangSC-Regular, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}

.head{
  height: 100rpx;
  line-height: 100prx;
  text-align: center;
  color: #fff;
  background: @hc-color-primary;
}

.list{
  padding: 20rpx 40rpx;
  background: #fff;
  .list-item{
    padding: 20rpx 0;
    display: flex;
    align-items: center;
    &:not(:last-child){
      border-bottom: 2rpx solid #cccccc;
    }
    .item-left{
      flex: 1;
    }
    .item-right{
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid #cccccc;
      border-bottom: 5rpx solid #cccccc;
      transform: rotate(-45deg);
    }
  }
}