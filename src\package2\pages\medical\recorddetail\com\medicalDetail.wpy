<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <view class="list-tit">复印信息</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.medicalCopyType}}">
        <view class="item-label">病历类型</view>
        <view class="item-value">{{medicalCopyTypeList[detailData.medicalCopyType]}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.zyh}}">
        <view class="item-label">住院号</view>
        <view class="item-value">{{detailData.zyh}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.inHospitalDate}}">
        <view class="item-label">入院日期</view>
        <view class="item-value">{{detailData.inHospitalDate}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.deptName}}">
        <view class="item-label">入院科室</view>
        <view class="item-value">{{detailData.deptName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.doctorName}}">
        <view class="item-label">诊疗阶段</view>
        <view class="item-value">{{detailData.doctorName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.cyclesNum}}">
        <view class="item-label">周期信息</view>
        <view class="item-value">{{detailData.cyclesNum}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.copyPurpose}}">
        <view class="item-label">复印用途</view>
        <view class="item-value">{{detailData.copyPurpose}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.copyContent}}">
        <view class="item-label">复印内容</view>
        <view class="item-value">{{detailData.copyContent}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.copyObject}}">
        <view class="item-label">复印对象</view>
        <view class="item-value">{{detailData.copyObject}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.copyNum	}}">
        <view class="item-label">复印份数</view>
        <view class="item-value">{{detailData.copyNum	}}份</view>
      </view>
      <view class="list-item" wx:if="{{detailData.remark}}">
        <view class="item-label">备注</view>
        <view class="item-value">{{detailData.remark}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.receiveType	}}">
        <view class="item-label">领取方式</view>
        <view class="item-value">{{detailData.receiveType == 1 ?'快递到家':'来院自取'	}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.addresseeInfo}}">
        <view class="item-label">收件人姓名</view>
        <view class="item-value">{{detailData.addresseeInfo.userName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.addresseeInfo}}">
        <view class="item-label">手机号码</view>
        <view class="item-value">{{detailData.addresseeInfo.mobile}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.addresseeInfo}}">
        <view class="item-label">所在地区</view>
        <view class="item-value">{{detailData.addresseeInfo.provinceName}}{{detailData.addresseeInfo.cityName}}{{detailData.addresseeInfo.areaName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.addresseeInfo}}">
        <view class="item-label">详细地址</view>
        <view class="item-value">{{detailData.addresseeInfo.addressDetail}}</view>
      </view>
    </view>
  </view>
</template>

<script>
import wepy from "wepy"
import WxsUtils from "../../../../../wxs/utils.wxs"
import * as Utils from "@/utils/utils"

export default class PayDetail extends wepy.component {
  data = {
    medicalCopyTypeList: {
      1: "住院病历",
      2: "试管病历",
      3: "人工授精病历"
    }
  }

  wxs = {
    WxsUtils: WxsUtils
  }

  props = {
    detailData: {
      type: Object,
      default: {}
    }
  }

  onLoad(options) {
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {}
}
</script>
