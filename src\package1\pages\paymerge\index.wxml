<view class="p-page">
  
  <!-- 用户信息 -->
  <block wx:if="{{patientConfig.infoShow}}">
    <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  </block>
  <block wx:else>
    <view class="ui-userinfo">
      <image src="/resources/images/default-avatar.png" class="avatar" />
      <navigator class="login-msg" url="/pages/bindcard/index/index">
        <view>hi～ </view>
        <view>初次使用，请登录注册</view>
      </navigator>
    </view>
  </block>
  
  <!-- 时间筛选 -->
  <view class="ui-menu-function">
    <view class="menu">
      <block wx:for="{{dateTypeList}}" wx:key="index">
          <view class="menu-item {{dateTypeValue === item.value  ? 'active' : ''}}"  @tap="changeDateRange('{{item}}')" data-val="{{item.value}}">
            <view>{{item.name}}</view>
          </view>
      </block>
    </view>
  </view>
  <!-- 功能列表 -->
  <view class="ui-link-list" wx:if="{{waitPayOrderList && waitPayOrderList.length > 0}}">
    <block wx:for="{{waitPayOrderList}}" wx:key="index">
      <view class="link-item" @tap="navigateTo" data-type="{{item.type}}">
        <view class="name">{{item.name}}</view>
        <!-- <view class="cast">￥{{WxsUtils.formatMoney(item.totalFee,100)}}</view> -->
        <view class="cast">￥{{item.totalFee}}</view>
      </view>
    </block>
  </view>
   <empty :config.sync="emptyConfig">
    <block slot="text">暂未查询到您的待缴费记录</block>
  </empty>
  
</view>
