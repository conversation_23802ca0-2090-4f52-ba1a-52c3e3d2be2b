import { post } from '@/utils/request';

export const getChat = (param) => post('/api/chat/queryChatInfo', param, false, false);

// 获取医生状态
export const getVideoStatus = (param) => post('/api/liveStream/getStatus', param, false, false);

export const sendMsg = (param) => post('/api/chat/sendMessage', param,false);

export const closure = (param) => post('/api/ehis/health/api/inquiry/closure', param);

export const change = (param) => post('/api/chat/revokeMessage', param);

export const isShare = (param) => post('/api/ehis/health/api/shareInquiry/agreeShareInquiry', param);

export const queryChatRobotList = (param) => post('/api/chat/queryChatRobotList', param, false);

/** 
 * 框提示语
*/
export const getNoteProfile = (param) => post('/api/register/getNoteProfile', param);


