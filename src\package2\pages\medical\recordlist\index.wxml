<view class="p-page">
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{WxsUtils.medicalListStatus(item.status)}}"
          @tap="bindGoDetail({{item}})"
        >

          <view class="item-status">
            <view class="item-icon">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{WxsUtils.medicalListStatus(item.status)}}.png"></image>
            </view>
            <text>{{statusList[item.status]}}</text>
          </view>
          <view class="item-title">{{medicalCopyTypeList[item.medicalCopyType]}}</view>
          <view class="item-extra">申请人：{{item.patientName}}</view>
          <view class="item-extra">申请时间：{{item.createTime}}</view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>