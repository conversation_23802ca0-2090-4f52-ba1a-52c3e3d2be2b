@import "../../../../resources/style/mixins";

page {}

.p-page {
  padding-bottom: 30rpx;
  // 弹窗样式
  .complete-content{
    padding: 0 24rpx;
    margin-bottom: 24rpx;
    .content-btn{
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 34rpx;
      font-weight: bold;
      color: #fff;
      border-radius: 76rpx;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    }
  }
  .desc-modal-mask {
    position: fixed;
    z-index: 100;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .desc-modal {
      padding: 48rpx 24rpx;
      width: 670rpx;
      border-radius: 8rpx;
      background-color: @hc-color-white;

      .desc-title {
        text-align: center;
        padding: 0 0 24rpx;
        font-size: 34rpx;
        font-weight: 600;
        box-sizing: border-box;
        color: @hc-color-title;
      }

      .desc-content {
        width: 100%;
        max-height: 700rpx;
        min-height: 200rpx;
        box-sizing: border-box;
        color: @hc-color-info;
        font-size: 34rpx;
        >view{
          margin-bottom: 8rpx;
        }
        .badge-box{
          position: relative;
          padding: 10rpx 40rpx 10rpx 0;
          .badge{
            position: absolute;
            top: -10rpx;
          }
        }
      }
      
    }
    .desc-footer {
      margin-top: 40rpx;
      padding: 24rpx;
      border-radius: 49rpx;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      >view {
        font-size: 34rpx;
        font-weight: 600;
        color: @hc-color-white;
        text-align: center;

        &+view {
          border-left: 2rpx solid @hc-color-border;
        }
      }
    }
  }
}
.page-main{
  padding: 32rpx 0;
  margin: 0 24rpx;
  border-radius: 8rpx;
  background-color: #fff;
  .main-product{
    padding-bottom: 32rpx;
    margin: 0 32rpx 32rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
    .title{
      margin-bottom: 32rpx;
      display: flex;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.90);
      font-size: 28rpx;
      font-weight: 600;
    }
    .title-money{
      color: rgba(0, 0, 0, 0.70);
      font-size: 24rpx;
    }
    .extra{
      color: #D2962B;
    }
  }
}
.tb-box{
  border: 1rpx solid rgba(0, 0, 0, 0.08);
}
.tb-tr{
  display: flex;
  justify-content: space-between;
  .td{
    flex: 1;
    border-right: 1rpx solid rgba(0, 0, 0, 0.08);
    &:last-child{
      text-align: right;
      border: none;
    }
  }
}
.tb-head{
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
  .td{
    padding: 8rpx 16rpx;
    color: rgba(0, 0, 0, 0.90);
    font-size: 24rpx;
    font-weight: 600;
  }
}
.tb-body{
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
  &:last-child{
    border: none;
  }
  .td{
    padding: 16rpx;
    color: rgba(0, 0, 0, 0.70);
    font-size: 24rpx;
    word-break: break-all;
  }
}
.m-code {
  display: none;
  margin: 0 32rpx 32rpx;
  padding: 0 0 32rpx 0;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  &.active {
    display: block;
  }

  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }

  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;

    p {
      margin: 20rpx 0;
    }
  }

  .code-img {
    margin-top: 20rpx;

    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry {
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;

  .retry-btn {
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color: @hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border: 2rpx solid @hc-color-primary;
  }
}

.m-list {
  padding-bottom: 32rpx;
  margin: 0 32rpx 32rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  &:last-child{
    border: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .list-tit {
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .list {
    padding-top: 32rpx;
  }

  .list-item {
    padding-bottom: 16rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    &:last-child{
      padding: 0;
    }
  }

  .item-label {
    color: rgba(0, 0, 0, .4);
    font-size: 32rpx;
    min-width: 5em;
  }

  .value-icon {
    margin-right: 16rpx;
    width: 196rpx;
    height: 196rpx;
    border-radius: 8rpx;
  }

  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow: hidden;
    margin-left: 70rpx;
  }

  .unit-price {
    font-size: 32rpx;
  }
}

.m-table {
  background-color: @hc-color-bg;

  .table {
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 4rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.08);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tb-extra {
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom: 2rpx solid #E5E5E5;
  }

  .extra-tit {
    flex: 1;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .extra-txt {
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .extra-num {
    font-size: 36rpx;
    color: @hc-color-title;
    margin-left: 20rpx;
  }

  .tb-head,
  .tb-body {}

  .tb-head {}

  .tb-body {}

  .head-tr,
  .body-tr {
    display: flex;
    align-items: stretch;
    border-bottom: 2rpx solid @hc-color-border;
    padding: 0 16rpx;
  }
  .body-tr{
    border: none;
  }

  .empty-tr {
    display: block;
    text-align: center;
    padding: 20rpx 0
  }

  .head-td,
  .body-td {
    border-right: 2rpx solid @hc-color-border;
    .textBreak();

    &:last-child{
      border: none;
      text-align: right;
    }
    &.td-1 {
      flex-basis: 22%;
    }

    &.td-2 {
      flex-basis: 40%;
    }

    &.td-center {
      text-align: center;
    }

    &.td-right {
      text-align: right;
    }
  }

  .head-td {
    padding: 8rpx 0;
    color: @hc-color-title;
    font-size: 20rpx;
    font-weight: 500;
  }

  .body-td {
    padding: 16rpx 0;
    color: @hc-color-text;
    font-size: 24rpx;
  }
}

.ad-treat {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
  background-color: #fff;
  overflow: hidden;

  .ad-content {
    float: left;
    margin-top: 5rpx;
    color: @hc-color-warn;
  }

  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}