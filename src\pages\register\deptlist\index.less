@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #fff;
  .deptlist-box{
    width: 100%;
    height: 100%;
  }
}

.area-tab {
  padding: 0 32rpx;
  height: 98rpx;
  line-height: 98rpx;
  display: flex;
  align-items: center;
  flex-direction: row;
  background: @hc-color-white;

  .area-tab-item {
    flex: 1;
    font-weight: 600;
    font-size: 32rpx;
    color: @hc-color-text;
    text-align: center;
    // border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
    &.active {
      color: @hc-color-primary;
    }
  }
}

.desc-modal-mask {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .desc-modal {
    width: 85%;
    border-radius: 8rpx;
    background-color: @hc-color-white;

    .desc-title {
      text-align: center;
      padding: 50rpx 0 24rpx;
      font-size: 34rpx;
      font-weight: 600;
      box-sizing: border-box;
      color: @hc-color-title;
    }

    .desc-content {
      width: 100%;
      max-height: 700rpx;
      min-height: 200rpx;
      box-sizing: border-box;
      color: @hc-color-info;
      font-size: 34rpx;
      padding: 0 32rpx 32rpx;
      >view{
        margin-bottom: 8rpx;
      }
    }
    
  }
  .desc-footer {
    border-top: 1rpx solid #E5E5E5;
    display: flex;

    >view {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      color: @hc-color-black;
      font-size: 36rpx;
      height: 100rpx;
      line-height: 100rpx;
      width: auto;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:last-child {
        border-left: 1rpx solid #d2d2d2;
        color: @hc-color-primary;
      }
    }
  }
}
