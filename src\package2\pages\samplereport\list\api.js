import { REQUEST_QUERY } from '@/config/constant';
import { post, get } from '@/utils/request';

export const getList = (param) => post(`/api/customize/getbeiriListN?caseid=${param.caseid}&keyName=${param.keyName}&msgKey=${param.msgKey}&_route=h242&k`, param);

// 获取健康档案基本信息
export const getBasicInfo = () => get("/api/healthFiles/getBasicInfo");

// 获取健康档案家庭信息
export const getFamilyInfo = () => get("/api/healthFiles/getFamilyInfo");