<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy"
import * as Utils from "@/utils/utils"
import Empty from "@/components/empty/index"
import WxsUtils from "../../../../wxs/utils.wxs"
import * as Api from "./api"

export default class Search extends wepy.page {
  config = {
    navigationBarTitleText: "病历复印记录",
    navigationBarBackgroundColor: '#fff',
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    // 挂号记录列表
    orderList: [],
    currentPage: 0,
    pageCount: 0,
    medicalCopyTypeList: {
      1: "住院病历",
      2: "试管病历",
      3: "人工授精病历"
    },
    statusList: {
      1: "待审核",
      2: "审核通过待支付",
      3: "已驳回",
      4: "已取消",
      5: "支付成功",
      6: "已完成"
    }
  }

  wxs = {
    WxsUtils: WxsUtils
  }

  components = {
    empty: Empty
  }

  props = {}

  onShow(options) {
    this.orderList = []
    this.getOrderList()
    this.$apply()
  }

  onReachBottom() {
    if (this.currentPage < this.pageCount) {
      this.getOrderList({
        pageNum: this.currentPage + 1
      })
    }
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    /**
     * 跳转到挂号详情页
     * @param item
     */
    bindGoDetail(item) {
      const { id = "" } = item
      wepy.navigateTo({
        url: `/package2/pages/medical/recorddetail/index?id=${id}`
      })
    }
  }

  async getOrderList(param) {
    const { code, data = {} } = await Api.orderList(param)
    if (code === 0) {
      this.currentPage = data.currentPage
      this.pageCount = data.pageCount
      this.orderList = this.orderList.concat(data.recordList || [])
      this.$apply()
    }
  }
}
</script>
