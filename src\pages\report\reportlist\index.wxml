<view class="p-page">
  <view class="m-tips">此处报告查询仅供参考，具体请以医院纸质报告为准。</view>
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  <!--<view class="m-date-range">
    <view class="m-date-item {{dateTypeValue == item.value ? 'active' : ''}}" wx:for="{{dateTypeList}}" wx:key="{{index}}" @tap="changeDateRange('{{item}}')">
      {{item.name}}
    </view>
  </view>
  <view class="m-tab" wx:if="{{reportList && reportList.length >= 0}}">
    <view class="unit-tab">
      <view
        wx:for="{{reportTypeList}}"
        wx:key="{{index}}"
        class="unit-tab-li tab-li {{reportTypeIndex === item.value ? 'active' : ''}}"
        @tap="bindChangeTabIndex({{item.value}})"
      >{{item.name}}
      </view>
    </view>
  </view>
  <view wx:if="{{reportList.length > 0}}" class="m-tab-tips">{{reportTypeList[reportTypeIndex].name}}报告，共<text class="m-warn">{{currentReportList.length}}</text>条</view>-->
  <block wx:for="{{currentReportList}}" wx:for-index="idx" wx:key="idx">
    <view class="m-report" @tap="navToDeatil" data-id="{{item.reportId}}" data-reportType="{{item.reportType}}" data-fyjg="{{item.fyjg}}" data-url="{{item.reportUrl}}" data-baseUrl="{{item.baseUrl}}" data-extFields="{{item.extFields}}">
      <view class="report-item report">
        <view class="report-title">{{item.reportName}}</view>
        <view class="report-text">{{item.reportTime}}</view>
      </view>
      <!--<text class="report">已发布</text>-->
    </view>
  </block>
  <block wx:for="{{beiriList}}" wx:for-index="idx" wx:key="idx">
    <view class="m-report" @tap="navToBeiriDeatil" data-id="{{item.id}}" data-testitem="{{item.test_item}}">
      <view class="report-item report">
        <view class="report-title">{{item.test_item}}</view>
        <view class="report-text">{{item.rpttime}}</view>
      </view>
      <!--<text class="report">已发布</text>-->
    </view>
  </block>
  <empty :config.sync="emptyConfig">
    <block slot="text">暂未查询到您的报告数据</block>
  </empty>
</view>
