@import "../../resources/style/mixins";

.bill {
  width: 100%;
  height: 100%;
}

.order-list {
  padding: 20rpx 30rpx 40rpx 30rpx;

  .container {
    background: #fff;
    padding: 40rpx 30rpx 40rpx 30rpx;
    margin-bottom: 20rpx;
    border-radius: 30rpx;

    .o-item {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      background-color: @hc-color-white;
      border-radius: 24rpx;

      .lf-check {
        font-size: 24rpx;

        .con-tit {
          font-size: 36rpx;
          font-weight: 600;
          color: @hc-color-title;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1; //规定几行显示省略号
          -webkit-box-orient: vertical;
        }

        image {
          width: 32rpx;
          height: 32rpx;

        }

        .img-gray {
          background-color: rgba(0, 0, 0, 0.13);
          border: none !important;
        }
      }

      .content {
        .btn-kd {
          background-color: #3eceb6;
          color: #fff;
          padding: 10rpx 30rpx 10rpx 30rpx;
          font-size: 28rpx;
          border-radius: 10rpx;
        }
      }

      .min-total {
        color: @hc-color-assist;
        font-size: 30rpx;
        white-space: nowrap;
        font-weight: 600;
        margin-bottom: 8rpx;
        margin-top: 8rpx;
      }
    }

    .content-tips {
      font-size: 26rpx;
      color: rgba(0, 0, 0, 0.4);
    }

    .line {
      border-bottom: 1px solid #f5f5f5;
      margin-bottom: 16rpx;
    }
  }
}

.checkrecord {
  text-align: center;
  color: #3eceb6;
}