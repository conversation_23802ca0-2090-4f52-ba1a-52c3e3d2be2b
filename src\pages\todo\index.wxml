<view class="p-page" @tap="hidden">
  <!-- <view class="m-notice" wx:if="{{notices.length > 0}}">
    <block wx:for="{{notices}}" wx:key="index">
      <view class="notice-item">
        <view class="item-tit">{{item.title}}</view>
        <view class="item-txt">{{item.content}}</view>
      </view>
    </block>
  </view> -->
  <!-- <view class="m-todo" wx:if="{{todoList.length > 0}}">
    <block wx:for="{{todoList}}" wx:key="index">
      <navigator class="todo-item" url="{{item.url}}" @longpress="longtap({{item.id}})">
        <view class="item-bd">
          <view class="bd-tit">{{item.text}}</view>
          <view class="bd-txt">{{item.createTime}}</view>
        </view>
        <view class="item-ft"></view>
        <view class="item-icon" wx:if="{{!item.noIcon}}">
          <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/todo/todo-icon.png"></image>
        </view>
        <view class="tip" wx:if="{{WxsUtils.isShow(item.id, showId)}}">
          <i/>
          <text catchtap="deleteTodo({{item.id}})">删除</text>
        </view>
      </navigator>
    </block>
  </view> -->
  
  <!-- UI调整版本-医院通知 -->
  <view class="card" wx:if="{{notices.length > 0}}">
    <view class="card-head">
        <view class="left">
          <view class="icon">
            <image class="link" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/totice.png' />
          </view>
          <text>医院通知</text>
        </view>
        <view class="right" catchtap="handleToggleExpand('isExpandNotice')">
          <text>{{isExpandNotice ? '折叠' : '展开'}}</text>
        </view>
    </view>
    <block wx:if="{{isExpandNotice}}">
      <block wx:for="{{notices}}" wx:key="index">
        <view class="card-content">
          <view class="card-main">
            <view class="card-main-text">{{item.title}}</view>
          </view>
          <view class="desc">{{item.content}}</view>
        </view>
      </block>
    </block>
  </view>

  <!-- UI调整版本-就诊消息 -->
  <view class="card" wx:if="{{todoList.length > 0}}">
    <view class="card-head">
        <view class="left">
          <view class="icon">
            <image class="primary" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/add.png' />
          </view>
          <text>就诊消息</text>
        </view>
        <view class="right" catchtap="handleToggleExpand('isExpandTodo')">
          <text>{{isExpandTodo ? '折叠' : '展开'}}</text>
        </view>
    </view>
    <block wx:if="{{isExpandTodo}}">
      <block wx:for="{{todoList}}" wx:key="index">
        <navigator class="todo-item" url="{{item.url}}" @longpress="longtap({{item.id}})">
          <view class="card-content">
            <view class="card-main">
              <view class="card-main-text">{{item.text}}</view>
              <view class="unread" wx:if="{{item.readFlag === 0}}"></view>
            </view>
            <view class="desc">{{item.createTime}}</view>
          </view>
          <!-- 删除 -->
          <view class="tip" wx:if="{{WxsUtils.isShow(item.id, showId)}}">
            <i/>
            <text catchtap="deleteTodo({{item.id}})">删除</text>
          </view>
        </navigator>
      </block>
    </block>
  </view>

  <empty :config.sync="emptyConfig">
    <block slot="text">未查询到任何消息</block>
  </empty>
  <!-- <nav-tab :type="navTabType"></nav-tab> -->
  
</view>
