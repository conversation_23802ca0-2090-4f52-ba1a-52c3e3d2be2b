@import '../../../../resources/style/mixins.less';
@upPlayZIndex: 99;
page {
  height: 100%;
  overflow: hidden;
  .play-poster {
    height: 100%;
    width: 100%;
    background: #ffffff;
  }
  .play-box {
    height: 100%;
    width: 100%;
    position: relative;
  }
  .doctor-info-box {
    position: absolute;
    top: 60rpx;
    .comm-doctor-info {
      background: transparent;
    }
  }
  .operate-box {
    position: absolute;
    bottom: 30rpx;
    left: 30rpx;
    z-index: @upPlayZIndex;
    display: flex;
    .input-box {
      width: 425rpx;
      background: rgba(0, 0, 0, 0.25);
      border-radius: 50rpx;
      margin-right: 23rpx;
      font-size: 34rpx;
      height: 50rpx;
      padding: 10rpx 26rpx;
      display: flex;
      align-items: center;
      .input {
        flex: 1;
        color: #ffffff;
      }
      .input-send {
        margin-left: 20rpx;
        height: 38rpx;
        width: 38rpx;
      }
      .input-placeholder {
        color: #ffffff;
      }
    }
    .btn-share {
      .clearButton();
      background: transparent;
      .icon-share {
        height: 70rpx;
        width: 70rpx;
      }
    }
  }

  .btn-close {
    position: absolute;
    right: 30rpx;
    bottom: 30rpx;
    z-index: @upPlayZIndex;
    height: 70rpx;
    width: 70rpx;
  }
  .heart-box {
    position: absolute;
    right: 20rpx;
    bottom: 150rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    .btn-heart {
      height: 58rpx;
      width: 58rpx;
      margin-bottom: 10rpx;
    }
    .heart-text {
      height: 36rpx;
      width: 96rpx;
      color: #ffffff;
      text-align: center;
    }
  }
}
