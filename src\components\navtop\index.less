.nav-top {
  position: relative;
  .nav-box{
    display: flex;
    position: fixed;
    font-size: 30rpx;
    align-items: flex-end;
    width: 100%;
    z-index: 99999;
    .capsule{
      height: 28px;
      border: 2rpx solid #ffffff;
      border-radius: 50px;
      margin:8px;
      display:flex;
      algin-item:center;
      width: 90px;
      .nav-back{
        font-size: 24px;
        width: 42px;
        height: 28px;
        position: relative;
        .back-confont{
          &:after {
            content: ' ';
            position: absolute;
            left: 17px;
            top: 50%;
            width: 10px;
            height: 10px;
            border-right: 5rpx solid #ffffff;
            border-bottom: 5rpx solid #ffffff;
            transform: translate(-8rpx, -50%) rotate(-225deg);
          }
        }
      }
      .back-home{
        flex: 1;
        height: 28px;
        position: relative;
        display: flex;
        align-items: center;
        image{
          margin-left: 11px;
          width: 24px;
          height: 22px;
        }
        &:before {
          content: ' ';
          height: 20px;
          width: 1rpx;
          background-color: #ffffff;
          position: absolute;
          left: 0;
          top: 5px;
          bottom: 5px;
        }
      }
    }
    .nav-title{
      flex:1;
      text-align: center;
      padding-bottom: 10px;
    }
  }
}