import { REQUEST_QUERY } from '@/config/constant';
import { post } from '@/utils/request';

export const getHisConfig = (param) => post('/api/user/getbindcardprofile', param);

export const getFilePathByToken = (param) => post('/api/files/getfileinfobytoken', param);

export const bindCard = (param) => post('/api/user/bindpatients', param);
// export const bindCard = (param) => post(`/api/user/bindpatients?_route=h${REQUEST_QUERY.platformId}`, param);

// saveFormId
export const queryRelationPatients = (param, isShowLoading = true) => post(`/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`, param, isShowLoading);

export const sendMsgAndValidate = (param) => post(`/api/customize/sendMsg?_route=h${REQUEST_QUERY.platformId}&phone=${param.phone}&msgKey=${param.msgKey}`);

export const checkMsgAndValidate = (param) => post(`/api/customize/checkMsg?_route=h${REQUEST_QUERY.platformId}&phone=${param.phone}&msgKey=${param.msgKey}&code=${param.code}`);

export const getSecret = (param) => post(`/api/customize/getSecret?_route=h${REQUEST_QUERY.platformId}`, param);


/**
 * 首页弹框提示语
 */
export const getNoteProfile = (param) =>
  post("/api/register/getNoteProfile", param);
