import { post, get } from '@/utils/request';
import { REQUEST_QUERY } from "@/config/constant";

export const orderDetail = (param) => post('/api/ext/getextorderbyid', param);

export const getBySamplenumber = (param) => post('/api/sample/get-by-samplenumber', param);

export const queryEleckStatus = (param) =>
  post(
    `/api/customize/queryEleckStatus?_route=h${REQUEST_QUERY.platformId}`,
    param
  );
export const dealEleck = (param) =>
  post(
    `/api/customize/dealEleck?_route=h${REQUEST_QUERY.platformId}`,
    param
  );
// export const queryEleck = (param) =>
//   post(
//     `/api/customize/queryEleck?_route=h${REQUEST_QUERY.platformId}`,
//     param
//   );