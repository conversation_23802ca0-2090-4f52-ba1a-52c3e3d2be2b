import wepy from "wepy";
import {
  TIME_MAP,
  IS_NEED_ENDTIME,
  REQUEST_QUERY,
  DOMAIN,
  PARTNERKEY,
} from "@/config/constant";
// import request from './monitor';
import md5 from "md5";

/**
 * 操作授权检验、请求
 * @param scopeName
 * @returns {*}
 */
export const getAuthorize = async (scopeName) => {
  let getSettingRes;
  try {
    getSettingRes = await wepy.getSetting();
  } catch (e) {
    getSettingRes = e;
  }

  if (getSettingRes.errMsg !== "getSetting:ok") {
    // 调用授权列表接口失败
    console.log("getSettingRes_fail:", getSettingRes);
    return getSettingRes;
  }

  // 调用授权列表接口成功
  const { authSetting = {} } = getSettingRes;
  if (!authSetting[scopeName]) {
    // 尚未授权
    let authorizeRes;
    try {
      authorizeRes = await wepy.authorize({
        scope: scopeName,
      });
    } catch (e) {
      authorizeRes = e;
    }

    if (authorizeRes.errMsg !== "authorize:ok") {
      if (scopeName == "scope.userInfo") {
        return authorizeRes;
      }
      // 用户拒绝授权
      console.log("authorizeRes_fail:", authorizeRes);
      let openSettingRes;
      try {
        openSettingRes = await wepy.openSetting();
      } catch (e) {
        openSettingRes = e;
      }

      if (openSettingRes.errMsg !== "openSetting:ok") {
        // 调用打开授权设置页失败
        console.log("openSettingRes_fail:", openSettingRes);
        return openSettingRes;
      }

      // 调用打开授权设置页并且设置成功，需要重新校验权限是否授予
      return await getAuthorize(scopeName);
    }

    // 用户授权成功
    return { errMsg: "ok" };
  } else {
    // 已经获得授权
    return { errMsg: "ok" };
  }
};

/**
 * 获取用户基本信息
 * @returns {*}
 */
export const getUserInfo = async () => {
  let userInfo = wepy.getStorageSync("userInfo");
  if (typeof userInfo === "string") {
    try {
      userInfo = JSON.parse(userInfo);
    } catch (error) {
      userInfo = undefined;
    }
  }
  return { userInfo };
};

/**
 * json对象转queryString键值对
 * @param json
 * @returns {*}
 */
export const jsonToQueryString = (json) => {
  if (json) {
    return Object.keys(json)
      .map((key) => {
        if (json[key] instanceof Array) {
          return Object.keys(json[key])
            .map((k) => {
              return `${key}=${json[key][k]}`;
            })
            .join("&");
        }
        return `${key}=${json[key]}`;
      })
      .join("&");
  }
  return "";
};

/**
 * 睡眠
 * @param time
 * @returns {Promise}
 */
export const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};

/**
 * 验证规则
 * @type {{idCard: ((val?)), mobile: ((val?))}}
 */
export const validator = {
  idCard(val = "") {
    val = val.replace(/(^\s*)|(\s*$)/g, "");
    val = val.toUpperCase();
    const len = (val || "").length;

    if (len == 0) {
      return { ret: false, tip: "不能为空" };
    }
    if (len != 18 && len != 15) {
      return { ret: false, tip: "格式错误" };
    }
    // 15位的身份证，验证了生日是否有效
    if (len == 15) {
      const year = val.substring(6, 8);
      const month = val.substring(8, 10);
      const day = val.substring(10, 12);
      const tempDate = new Date(year, parseFloat(month) - 1, parseFloat(day));
      if (
        tempDate.getYear() != parseFloat(year) ||
        tempDate.getMonth() != parseFloat(month) - 1 ||
        tempDate.getDate() != parseFloat(day)
      ) {
        return { ret: false, tip: "格式错误" };
      }
      return { ret: true };
    }
    // 18位的身份证，验证最后一位校验位
    if (len == 18) {
      // 身份证的最后一为字符
      const endChar = val.charAt(len - 1);
      val = val.substr(0, 17);
      const table = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const table2 = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      const cNum = [];
      for (let i = 0; i < val.length; i++) {
        cNum[i] = val.charAt(i);
      }
      let sum = 0;
      for (let i = 0; i < cNum.length; i++) {
        // 其中(cNum[i]-48)表示第i位置上的身份证号码数字值，table[i]表示第i位置上的加权因子，
        const num = cNum[i].charCodeAt(0);
        const num1 = parseInt(table[i], 10);
        sum = sum * 1 + (num - 48) * num1;
      }
      // 以11对计算结果取模
      const index = Number(sum % 11);
      // 根据模的值得到对应的校验码,即身份证上的最后一位为校验码
      const verfiyCode = table2[index];
      if (endChar != verfiyCode) {
        return { ret: false, tip: "格式错误" };
      }
      return { ret: true };
    }
  },
  mobile(val = "") {
    return {
      ret: /^1\d{10}$/.test(val),
      tip: "格式错误",
    };
  },
};

/**
 * 格式化金额
 * @param moneyString
 * @param mark
 * @returns {*}
 */
export const formatMoney = (moneyString = "0", mark = 100) => {
  try {
    const moneyNumber = parseFloat(moneyString);
    if (typeof moneyNumber === "number" && typeof mark === "number") {
      return parseFloat(moneyNumber / mark).toFixed(2);
    }
    return 0;
  } catch (e) {
    console.log("error", e); // 缺失全局异常处理
    return 0;
  }
};

/**
 * 跳转支付页
 * @param resData
 */
export const gotoPay = (resData) => {
  const { payOrderId, cashierURI, orderId, typeName } = resData;
  const { protocol, host, pathname, search } = window.location; // eslint-disable-line
  const returnUrl = `${protocol}//${host}${pathname}${search}#/waiting/waiting?orderId=${orderId}&type=${typeName}`; // eslint-disable-line

  window.location.href = `${cashierURI}?payOrderId=${payOrderId}&returnUrl=${encodeURIComponent(
    returnUrl
  )}`; // eslint-disable-line
};

/**
 * 获取就诊时间
 * visitDate 就诊日期
 * visitWeekName 星期
 * visitPeriod  时段
 * visitBeginTime 开始时间
 * visitEndTime 结束时间
 * @param resData
 * @returns {string}
 */
export const getVisitTime = (resData = {}) => {
  const {
    visitDate = "",
    visitWeekName = "",
    visitPeriod,
    visitBeginTime = "",
    visitEndTime = "",
  } = resData;
  let visitTime = "";
  if (visitBeginTime) {
    visitTime = `${visitDate}${visitWeekName ? ` ${visitWeekName}` : ""}${TIME_MAP[visitPeriod] ? ` ${TIME_MAP[visitPeriod]}` : ""
      } ${visitBeginTime}`;
    if (IS_NEED_ENDTIME) {
      visitTime = `${visitTime}-${visitEndTime}`;
    }
  } else {
    visitTime = `${visitDate}${visitWeekName ? ` ${visitWeekName}` : ""}${TIME_MAP[visitPeriod] ? ` ${TIME_MAP[visitPeriod]}` : ""
      }`;
  }
  return visitTime;
};

/**
 * 获取就诊时间段
 * visitPeriod  时段
 * visitBeginTime 开始时间
 * visitEndTime 结束时间
 * @param resData
 * @returns {string}
 */
export const getTimeSlot = (resData = {}) => {
  const { visitPeriod, visitBeginTime = "", visitEndTime = "" } = resData;
  if (visitBeginTime) {
    if (visitPeriod) {
      let timeSlot = `${TIME_MAP[visitPeriod] || null} ${visitBeginTime}`;
      if (IS_NEED_ENDTIME) {
        timeSlot = `${timeSlot}~${visitEndTime}`;
      }
      return timeSlot;
    } else {
      let timeSlot = visitBeginTime;
      if (IS_NEED_ENDTIME) {
        timeSlot = `${visitBeginTime}~${visitEndTime}`;
      }
      return timeSlot;
    }
  } else {
    return TIME_MAP[visitPeriod];
  }
};

/**
 * 获取格式化日期
 * @param dateGap {Number} 日期间隔
 * @param spacer {String} 间隔符
 * @returns {Date} {String} 返回的格式化后的日期
 */
export const getFormatDate = (dateGap = 0, spacer = "/") => {
  let date = new Date();
  date = new Date(date.setDate(date.getDate() + dateGap));
  date =
    date.getFullYear() +
    spacer +
    (date.getMonth() + 1) +
    spacer +
    date.getDate();
  return date.replace(/\b(\w)\b/g, "0$1");
};
/**
 * 根据出生日期获取生日
 * @param birthday {string} 出生日期 如：2018-09-29 或者 2018/09/29
 * @param spacer {String} 间隔符
 * @returns {Date} 返回的年龄
 */
export const getAgeByBirthday = (birthday = "", spacer = "/") => {
  if (!birthday) {
    console.log("获取年龄错误，出生日期不能为空");
    return 0;
  }
  let age = 0;
  const nowArr = getFormatDate(0, spacer).split(spacer);
  const birthArr = birthday.split(spacer);
  let yearCacl = nowArr[0] - birthArr[0];
  const monthCacl = nowArr[1] - birthArr[1];
  const dayCacl = nowArr[2] - birthArr[2];

  if (yearCacl < 0) {
    console.log("获取年龄错误，出生日期不能大于当前日期");
    return 0;
  }
  if (monthCacl > 0) {
    yearCacl += 1;
  } else if (monthCacl === 0) {
    if (dayCacl >= 0) {
      yearCacl += 1;
    }
  } else {
    yearCacl -= 1;
  }
  return yearCacl;
};
/**
 * 根据身份证获取出生日期
 * @param idno {string} 身份证号
 * @param spacer {String} 间隔符
 * @returns {Date} 返回的年龄
 */
export const getBirthdayByIdCard = (idno = "", spacer = "/") => {
  if (!idno) {
    console.log("获取年龄错误，身份证号不能为空");
    return "";
  } else if (idno.length !== 18 && idno.length !== 15) {
    console.log("获取年龄错误，身份证号长度错误，必须为18位或15位");
    return "";
  }

  return idno.length === 18
    ? `${idno.substring(6, 10)}${spacer}${idno.substring(
      10,
      12
    )}${spacer}${idno.substring(12, 14)}`
    : `19${idno.substring(6, 8)}${spacer}${idno.substring(
      8,
      10
    )}${spacer}${idno.substring(10, 12)}`;
};

/**
 * 根据身份证获取性别
 * @param idno {string} 身份证号
 * @returns {String} 返回的性别
 */
export const getSexByIdCard = (idNo = "") => {
  if (!idNo) {
    console.log("获取年龄错误，身份证号不能为空");
    return "";
  } else if (idNo.length !== 18 && idNo.length !== 15) {
    console.log("获取年龄错误，身份证号长度错误，必须为18位或15位");
    return "";
  }

  const sexBit = idNo.length === 15 ? idNo.slice(14, 15) : idNo.slice(16, 17);
  return sexBit % 2 === 1 ? "男" : "女";
};

/**
 * 页面链接封装
 * @param url 请求链接，为完整链接
 * @param options 请求体
 * @returns {*}
 */
export const enclosure = (url = "", option = {}) => {
  const login_access_token = wepy.getStorageSync("login_access_token") || "";
  const queryStr = jsonToQueryString(
    { ...REQUEST_QUERY, ...option, login_access_token } || {}
  );
  const queryUrl = `${DOMAIN}${url}${url.indexOf("?") >= 0 ? "&" : "?"
    }${queryStr}`;

  return queryUrl;
};

export const addTokenOnImgUrl = (imgs = "") => {
  const login_access_token = wepy.getStorageSync("login_access_token") || "";
  const tokenStr = `login_access_token=${login_access_token}`;
  const addToken = (imgStr) =>
    imgStr.indexOf("?") > -1
      ? `${imgStr}&${tokenStr}`
      : `${imgStr}?${tokenStr}`;
  if (Array.isArray(imgs)) {
    return imgs.map((item) => (item = addToken(item)));
  } else {
    return addToken(imgs);
  }
};

/**
 * 获取聊天显示时间
 * @param createTime {string} 时间
 * @returns {String} 返回的时间
 */
export const getChatShowTime = (createTime = "", spacer = "-", dateGap = 0) => {
  if (!createTime) {
    return "";
  } else {
    let date = new Date();
    let year = date.getFullYear();
    date = new Date(date.setDate(date.getDate() + dateGap));
    date =
      date.getFullYear() +
      spacer +
      (date.getMonth() + 1) +
      spacer +
      date.getDate();
    date = date.replace(/\b(\w)\b/g, "0$1");
    if (createTime.indexOf(date) === 0) {
      return createTime.substring(11, 16);
    } else if (createTime.indexOf(year) === 0) {
      return createTime.substring(5, 16);
    } else {
      return createTime;
    }
  }
};

/**
 * 用于把用utf16编码的字符转换成实体字符，以供后台存储
 * @param  {string} str 将要转换的字符串，其中含有utf16字符将被自动检出
 * @return {string}     转换后的字符串，utf16字符将被转换成&#xxxx;形式的实体字符
 */
export const utf16toEntities = (str) => {
  var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
  str = str.replace(patt, function (char) {
    var H, L, code;
    if (char.length === 2) {
      H = char.charCodeAt(0); // 取出高位
      L = char.charCodeAt(1); // 取出低位
      code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00; // 转换算法
      return "&#" + code + ";";
    } else {
      return char;
    }
  });
  return str;
};

/**
 * 用于把用实体字符编码的字符转换成utf16，显示emoji
 * @param  {string} str 将要转换的字符串，其中含有utf16字符将被自动检出
 * @return {string}     转换后的字符串，utf16字符将被转换成&#xxxx;形式的实体字符
 */
export const EntitiestoUtf16 = (str) => {
  var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
  str = str.replace(patt, function (char) {
    var H, L, code;
    if (char.length === 2) {
      H = char.charCodeAt(0); // 取出高位
      L = char.charCodeAt(1); // 取出低位
      code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00; // 转换算法
      return "&#" + code + ";";
    } else {
      return char;
    }
  });
  return str;
};

/**
 * 循环调用一个promise函数，并做节流处理，返回一个定时器
 * @param promiseFunction
 * @param timeout
 * @return {*|interval}
 */
export function loopRequest(promiseFunction, timeout = 3000) {
  try {
    if (typeof promiseFunction !== "function") {
      throw new Error(false);
    }
    let isRequesting = false;
    const req = () => {
      isRequesting = true;
      promiseFunction()
        .then(() => {
          isRequesting = false;
        })
        .catch(() => {
          isRequesting = false;
        });
    };
    const interval = setInterval(() => {
      if (!isRequesting) {
        req();
      }
    }, timeout);
    return interval;
  } catch (error) {
    console.error("[loopRequest] arguments[0] must be a promise function!");
  }
}

/**
 * promisify api格式数据输出
 * @param {Function} fn
 */
export const apiFormatPromisify = (fn) => {
  return function (...args) {
    return new Promise((resolve, reject) => {
      fn.call(
        wx,
        Object.assign({}, args[0] || {}, {
          success(res) {
            resolve({
              code: 0,
              data: res || {},
            });
          },
          fail(err = {}) {
            resolve({ code: -1, data: err });
          },
        })
      );
    });
  };
};

/**
 * 网络请求
 * @param
 * @returns {Promise}
 */
export const httpRequest = ({ url, data = {} }) => {
  return new Promise((resolve, reject) => {
    const reqPages = getCurrentPages() || [];
    const reqPage = (reqPages[reqPages.length - 1] || {}).route || "";

    wepy.request({
      url,
      data,
      method: data.method || "POST",
      header: {
        ...(data.header || {
          "content-type": "application/x-www-form-urlencoded",
        }),
      },
      success: (response = {}) => {
        const resPages = getCurrentPages() || [];
        const resPage = (resPages[resPages.length - 1] || {}).route || "";
        response.reqPage = reqPage;
        response.resPage = resPage;
        resolve(response);
      },
      fail: (res) => {
        const resPages = getCurrentPages() || [];
        const resPage = (resPages[resPages.length - 1] || {}).route || "";
        resolve({
          status: 600,
          reqPage,
          resPage,
        });
      },
    });
    wepy.hideLoading();
  });
};

export const filterEmoji = (name) => {
  var str = name.replace(
    /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
    ""
  );
  return str;
};

//key根据ascll码排序
function sortObj(obj) {
  const keysArr = Object.keys(obj).sort();
  let sortObj = {};
  keysArr.forEach((item, index) => {
    sortObj[keysArr[index]] = encodeURIComponent(obj[keysArr[index]]);
  });

  return { ...sortObj };
}

//过滤对象里的空数据
function filterParams(obj) {
  Object.keys(obj).forEach((item, index) => {
    if (obj[item] === "" || obj[item] === undefined || obj[item] === null) {
      delete obj[item];
    }
  });

  return obj;
}

export const encryptionObjToMd5 = (obj) => {
  const stringSignTemp =
    decodeURIComponent(
      decodeURIComponent(jsonToQueryString(sortObj(filterParams(obj))))
    ) + `&${PARTNERKEY}`;
  return {
    ...obj,
    sign: md5(stringSignTemp).toUpperCase(),
  };
};

export const tuoMing = (str, beginLen, endLen) => {
  if (str && str.length > 0 && str.length === 18) {
    const len = str.length;
    const firstStr = str.substr(0, beginLen);
    const lastStr = str.substr(endLen);
    const middleStr = str.substring(beginLen, len - Math.abs(endLen)).replace(/[\s\S]/ig, '*');
    const tempStr = firstStr + middleStr + lastStr;
    return tempStr;
  } else {
    return str;
  }
}

//身份证脱敏
export const desensitizeIDNumber = (idNumber) => {
  if (typeof idNumber !== 'string') {
    return 'Invalid Input';
  }

  // 身份证号脱敏处理
  const len = idNumber.length;
  if (len <= 4) {
    // 如果身份证号长度小于等于4，无法脱敏，直接返回原值
    return idNumber;
  } else {
    // 身份证号长度大于4，只显示前四位和后三位，中间用*代替
    const firstFour = idNumber.slice(0, 4);
    const lastThree = idNumber.slice(len - 3);
    const middleStars = '*'.repeat(len - 7);
    return firstFour + middleStars + lastThree;
  }
}

export const encryptPasswd = (cardNo, parterId,key) => {
  const rawString = `cardNo=${cardNo}&parterId=${parterId}${key}`;
  return md5(rawString).toUpperCase();
}



