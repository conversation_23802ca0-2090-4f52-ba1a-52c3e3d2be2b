@import "../../../resources/style/mixins";

page {
  width: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.container {
  height: 100vh;
  min-height: 100vh;
  background-color: #fff;
}

.index-tips {
  padding: 20rpx 30rpx;
}

.index-title {
  text-align: center;
  font-size: 40rpx;
  padding: 40rpx 0 40rpx;
  color: #000;
  font-weight: 600;
}

.index-tips-item {
  padding-bottom: 40rpx;
  font-size: 28rpx;
  line-height: 42rpx;
  color: rgba(0, 0, 0, 0.7);
}

.button-block {
  width: 100%;
  box-sizing: border-box;
  padding: 28rpx 30rpx 10rpx;
}

.primary-btn {
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #fff;
  border-radius: 76rpx;
  text-align: center;
  font-size: 34rpx;
  margin-bottom: 24rpx;
  height: 100rpx;
  line-height: 100rpx;

  &::after {
    border: none;
  }
}

.default-btn {
  border-radius: 76rpx;
  text-align: center;
  font-size: 34rpx;
  margin: 22rpx 0;
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.7);
  height: 100rpx;
  line-height: 100rpx;
  &::after {
    border: none;
  }
}
