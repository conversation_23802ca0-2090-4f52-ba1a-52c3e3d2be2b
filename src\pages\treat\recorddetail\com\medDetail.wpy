<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <folding :isExpand.sync="isExpand">
      <block slot="title">处方单</block>
      <block slot="content">
        <view class="list">
          <view class="m-table">
            <block wx:if="{{treatMergePay}}">
              <block>
                <view class="table">
                  <view class="tb-extra">
                    <view class="extra-tit">{{item.itemName}}</view>
                    <view class="extra-txt">总金额</view>
                    <view class="extra-num">{{WxsUtils.formatMoney((item.totalFee * 100), 100)}}</view>
                  </view>
                      <view class="tb-head">
                        <view class="head-tr">
                          <view class="head-td td-2">项目名称</view>
                          <view class="head-td td-1 td-center">包装价格(元)</view>
                          <view class="head-td td-1 td-center" wx:if="{{detailData.status === 'S'}}">规格</view>
                          <view class="head-td td-1 td-center">数量</view>
                          <view class="head-td td-1 td-center">金额(元)</view>
                        </view>
                      </view>

                    <view class="tb-body">
                      <block 
                        wx:for="{{detailData.itemList}}"
                        wx:for-item="item"  
                        wx:for-index="idx"
                        wx:key="idx"
                      >
                        <view class="body-tr">
                          <view class="body-td td-2">{{item.itemName}}</view>
                          <view class="body-td td-1 td-center">
                            {{WxsUtils.formatMoney(item.itemPrice, 100)}}
                          </view>
                          <view class="body-td td-1 td-center"  wx:if="{{item.spec}}" >{{item.spec}}</view>
                          <view class="body-td td-1 td-center">
                            {{detailData.status === 'S' ? item.showAmount : (item.itemNum || '')}}
                          </view>
                          
                          <view class="body-td td-1 td-center">{{WxsUtils.formatMoney(item.totalFee, 100)}}</view>
                        </view>
                      </block>
                    </view>
                </view>
              </block>
            </block>
            <block wx:else>
              <view class="table">
                <view class="tb-head">
                  <view class="head-tr">
                    <view class="head-td td-2">项目名称</view>
                    <view class="head-td td-1 td-center">包装价格(元)</view>
                    <view class="head-td td-1 td-center">数量</view>
                    <view class="head-td td-1 td-center">规格</view>
                    <view class="head-td td-1 td-center">金额(元)</view>
                  </view>
                </view>
                <view class="tb-body">
                  <block
                    wx:for="{{detailData.itemList}}"
                    wx:for-item="itm"
                    wx:for-index="idx"
                    wx:key="idx"
                  >
                    <view class="body-tr">
                      <view class="body-td td-2">{{itm.itemName}}</view>
                      <view class="body-td td-1 td-center">{{WxsUtils.formatMoney(itm.itemPrice, 100)}}</view>
                      <view class="body-td td-1 td-center">{{itm.showAmount}}</view>
                      <view class="body-td td-1 td-center">{{itm.spec}}</view>
                      <view class="body-td td-1 td-center">{{WxsUtils.formatMoney(itm.totalFee, 100)}}</view>
                    </view>
                  </block>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </folding>
  </view>
</template>

<script>
import wepy from 'wepy';
import { TREAT_MERGE_PAY } from '@/config/constant';
import Folding from '@/components/folding/index';
import WxsUtils from '../../../../wxs/utils.wxs';
import * as Utils from '@/utils/utils';

export default class MedDetail extends wepy.component {
  data = {
    treatMergePay: TREAT_MERGE_PAY,
  };
  components = {
    'folding': Folding,
  };
  wxs = {
    WxsUtils: WxsUtils,
  };

  props = {
    detailData: {
      type: Object,
          default: () => ({
      status: '', 
      itemList: []
    })
    },
    isExpand: {
      type: Boolean,
      default: true,
      twoWay: true,
    },
  };

  onLoad(options) {
    console.log(this.detailData, '======108')
  }
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {};
}
</script>
