<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Utils from "@/utils/utils";
import Empty from "@/components/empty/index";

import { PRIMARY_COLOR } from "@/config/constant";
import * as Api from "./api";

export default class DoctorList extends wepy.page {
  config = {
    navigationBarTitleText: "在线咨询"
  };

  components = {
    empty: Empty
  };

  onLoad() {
    // this.getCardList();
  }

  onShow() {
    this.cardList = [];
    this.chatList = [];
    this.patientsVoList = [];
    this.patHisNo = "";
    this.isShowConsult = false;
    if (this.$wxpage.options) this.getCardList();
  }

  data = {
    emptyConfig: {
      show: true
    },
    cardList: [],
    chatList: [],
    patientsVoList: [],
    patHisNo: "",
    isShowConsult: false,
    isShowSingleConsult: false,
    isShowTeamConsult: false
  };

  methods = {
    // 开始咨询
    openConsult(item = {}, type = "redirectTo") {
      // 是否有咨询内容 以跳转咨询详情或创建咨询
      const { groupId = "", groupName = "", chatType = '' } = item;
      const query = Utils.jsonToQueryString({ groupId, groupName });
      if (groupId) {
        const url = `/pages/consult/chat/index?groupId=${groupId}&type=${chatType}`;
        if (type === "navigateTo") {
          // 跳转
          wepy.navigateTo({ url, type: item.chatType });
        } else {
          // 重定向
          wepy.redirectTo({ url });
        }
      } else {
        const param = {
          type: 1, // 免费聊天
          flg: item.chatType == 1 ? 0 : 1, // 单人聊天 0 多人聊天 1
          chatType: item.chatType || 2
        };
        this.createChat(param);
      }
    },
    openVideo() {
      wepy.navigateTo({
        url: `/pages/consult/video/index?groupId=606426459568865280`
      });
    }
  };
  async createChat(param = {}) {
    // 创建聊天
    param.patHisNo = this.patHisNo;
    param.patientId = this.patientId;
    const { code, data = {} } = await Api.createChat(param);
    if (code == 0 && data.id) {
      wepy.navigateTo({
        url: `/pages/consult/chat/index?groupId=${data.id}`
      });
    } else {
      if (!this.isShowConsult) {
        this.emptyConfig.show = true;
      }
      this.$apply;
    }
  }
  async getCardList() {
    const { data = {} } = await Api.getCardList();
    const { cardList = [] } = data;
    this.cardList = cardList;
    let patHisNo;
    let patientId;
    if (cardList.length > 0) {
      cardList.forEach(item => {
        if (item.relationType == 1) {
          patHisNo = item.patHisNo;
          patientId = item.patientId;
        }
      });
    }
    if (patHisNo) {
      this.patHisNo = patHisNo;
      this.patientId = patientId;
      this.queryPatChat();
    } else {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "请登录后再操作",
        showCancel: false,
        confirmText: "确定",
        confirmColor: PRIMARY_COLOR
      });
      if (showModalRes.confirm) {
        wepy.navigateTo({
          url: "/pages/bindcard/index/index"
        });
      }
    }
    this.$apply();
  }
  /**
   * 当前患者是否已存在聊天会话
   */
  async queryPatChat() {
    const { data = [], code } = await Api.queryPatChat();
    const chatList = [];
    if (code == 0) {
      let isHasSingleConsult = false;
      let isHasFamilyConsult = false;
      let list = data;
      if (typeof data === "string") {
        list = JSON.parse(data) || [];
      }
      list.map(item => {
        if (!item.groupId && item.id) {
          item.groupId = item.id;
        }
        if (item.createTime || item.updateTime) {
          item.time = Utils.getChatShowTime(
            item.updateTime || item.createTime || ""
          );
        }
        return item;
      });
      if (list.length === 2) {
        this.emptyConfig.show = false;
        this.isShowConsult = true;
        this.isShowTeamConsult = true;
      } else {
        this.judgeUserExsist(list);
      }
      list.sort((a,b)=> a.chatType - b.chatType);
      this.chatList = list;
    } else {
      this.emptyConfig.show = true;
    }
    this.$apply();
  }
  /**
   * 查询是否有家人已登录
   */
  async judgeUserExsist(chatList) {
    const patHisNo = this.patHisNo;
    const { data = {}, code } = await Api.judgeUserExsist({ patHisNo });
    // flg 0:直接跳过进入单人聊天界面 1:让用户选择进去群聊还是单聊，单聊的话，没有会直接新建单聊会话。
    if (code == 0) {
      const { flg, patientsVoList = [] } = data;
      if (Number(flg) === 0 && patientsVoList.length <= 1) {
        // 直接跳过进入单人聊天界面
        let singleChat;
        // 没有单人聊天
        if (patientsVoList.length === 0) {
          singleChat = {
            chatType: 1
          };
        } else {
          singleChat = chatList[0];
        }
        this.methods.openConsult.call(this, singleChat);
      } else {
        // 有家属绑定 显示选择
        this.emptyConfig.show = false;
        this.isShowConsult = true;
        if (patientsVoList.length > 0) {
          this.patientsVoList = data.patientsVoList;
          this.isShowTeamConsult = true;
          // 如果查询到的聊天列表没有单人聊天
          if (chatList.length === 1) {
            chatList[0].chatType = 2;
            chatList.unshift({
              chatType: 1,
              groupName: "单人聊天"
            });
            // 组内聊天排前面
            chatList.sort((prev, next) => {
              return prev.chatType - next.chatType;
            });
            this.chatList = chatList;
          }
          this.$apply();
        }
      }
      this.$apply();
    }
  }
}
</script>
