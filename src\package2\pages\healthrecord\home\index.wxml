<view class="p-page">
  <view class="tips">为更好地让医生准确了解您的病情，请尽量完善以下信息。</view>
  <view class="page-content">
    <view class="content-group" @tap="goMsgPage" data-type="1">
      <view class="group-left">
        <view class="title">您的基本信息</view>
        <view wx:if="{{!basicInfo.career}}" class="text">未填写</view>
        <view wx:else class="text on">{{basicInfo.career}}/{{basicInfo.education}}/{{basicInfo.nation}}</view>
      </view>
      <view class="arron"></view>
    </view>
    <view class="content-group" @tap="goMsgPage" data-type="2">
      <view class="group-left">
        <view class="title">家族病史</view>
        <view wx:if="{{!familyHistory.info}}" class="text">未填写</view>
        <view wx:else class="text on">{{familyHistory.info}}</view>
      </view>
      <view class="arron"></view>
    </view>
    <view class="content-group" @tap="goMsgPage" data-type="3">
      <view class="group-left">
        <view class="title">就诊记录</view>
        <view wx:if="{{!visitRecord.info}}" class="text">未填写</view>
        <view wx:else class="text on">{{visitRecord.info}}</view>
      </view>
      <view class="arron"></view>
    </view>
    <view class="content-group" @tap="goMsgPage" data-type="4">
      <view class="group-left">
        <view class="title">辅助检查</view>
        <view wx:if="{{!auxiliaryExamination.info}}" class="text">未填写</view>
        <view wx:else class="text on">{{auxiliaryExamination.info}}</view>
      </view>
      <view class="arron"></view>
    </view>
    <view class="content-group" @tap="goMsgPage" data-type="5">
      <view class="group-left">
        <view class="title">病情描述</view>
        <view wx:if="{{!diseaseDescription.info}}" class="text">未填写</view>
        <view wx:else class="text on">{{diseaseDescription.info}}</view>
      </view>
      <view class="arron"></view>
    </view>
    <view class="content-group" @tap="goMsgPage" data-type="6">
      <view class="group-left">
        <view class="title">外院报告上传</view>
        <view wx:if="{{!outHisReports.reports}}" class="text">未上传</view>
        <view wx:else class="text on">已经上传{{pathLen}}张</view>
      </view>
      <view class="arron"></view>
    </view>
  </view>
</view>
