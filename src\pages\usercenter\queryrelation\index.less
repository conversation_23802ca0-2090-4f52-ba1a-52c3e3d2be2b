@import "../../../resources/style/mixins";

page {
  height: 100%;
}

.p-page {
  height: 100%;
}

.message-msg {
  flex: 1;
  color: rgba(0, 0, 0, 0.6);
  padding: 20rpx 32rpx;
  font-size: 24rpx;
  min-height: 70rpx;
}

.m-card {
  background-color: #fff;
  padding: 40rpx 24rpx;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // &:after{
  //   content: ' ';
  //   position: absolute;
  //   right: 30rpx;
  //   top: 50%;
  //   width: 17rpx;
  //   height: 17rpx;
  //   border-right: 5rpx solid #C7C7CC;
  //   border-bottom: 5rpx solid #C7C7CC;
  //   transform: translate(-8rpx, -50%) rotate(-45deg);
  // }
  .card-code {
    padding: 8rpx 16rpx;
    font-size: 28rpx;
    border-radius: 4rpx;
    color: #2D666F;
    font-weight: 600;
    line-height: 42rpx;
    border: 2rpx solid #2D666F;
  }
  .card-exit{
    color: rgba(0, 0, 0, 0.4);
  }
  .card-info {
    // padding:39rpx 0 36rpx;
  }
  .info-main {
    display: flex;
    align-items: flex-start;
    padding-left: 2rpx;
  }
  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .name {
    font-size: 36rpx;
    font-weight: bold;
  }
  .status {
    background: rgba(45, 102, 111, 0.10);
    border-radius: 4rpx;
    margin-left: 16rpx;
    font-weight: 600;
    font-size: 20rpx;
    color: #2D666F;
    padding: 4rpx 8rpx;
  }
  .info-extra {
    margin-top: 8rpx;
    color: rgba(0, 0, 0, 0.4);
    font-size: 28rpx;
  }
}

.m-adduser {
  padding: 49rpx 37rpx 48rpx 154rpx;
  background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/common/icon-add.png")
    no-repeat 30rpx center;
  background-size: 98rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;

  &:after {
    content: " ";
    position: absolute;
    right: 30rpx;
    top: 50%;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translate(-8rpx, -50%) rotate(-45deg);
  }

  .add-title {
    font-size: 34rpx;
  }
  .add-text {
    font-size: 28rpx;
    color: @hc-color-text;
  }
}

.m-tiltle {
  margin: 24rpx 32rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #000;
}
.m-query{
  .m-card{
    margin: 0 24rpx 16rpx;
    padding: 32rpx;
  }
}

.afterscan-operbtnbox {
  &.is-ab {
    position: absolute;
  }
  bottom: 64rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx;
  .binduser-btn_line {
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    color: #fff;
    border-radius: 76rpx;
    text-align: center;
    font-size: 34rpx;
  }
}
button::after {
  border: none;
}
