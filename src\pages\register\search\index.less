@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.m-search-content {
  width: 100%;
  z-index: 10;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  .search-content {
    width: 100%;
    height: 100%;
  }
  .content {
    background-color: #fff;
    margin: 24rpx 32rpx;
    border-radius: 24rpx;
  }
  .content-title {
    color: @hc-color-text;
    padding: 24rpx 32rpx;
    font-size: 28rpx;
    font-weight: 600;
  }
  .content-list {
    padding: 0 32rpx;
  }
  .list-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 24rpx 0;
    border-top: 2rpx solid @hc-color-border;
    &:first-child {
      border-top: none;
    }
  }
  .item-icon {
    display: flex;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
    image {
      width: 80rpx;
      height: 80rpx;
      vertical-align: top;
    }
  }
  .item-image {
    display: flex;
    width: 106rpx;
    height: 133rpx;
    border-radius: 8rpx;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25rpx;
    image {
      vertical-align: top;
    }
  }
  .item-info {
    flex: 1;
    overflow: hidden;
  }
  .info-deptinfo {
    font-size: 32rpx;
    color: @hc-color-title;
  }
  .info-title {
    font-size: 36rpx;
    color: @hc-color-title;
    font-weight: 600;
    text {
      font-size: 30rpx;
      color: @hc-color-text;
    }
  }
  .info-text {
    margin-top: 5rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }
  .info-des {
    margin-top: 5rpx;
    font-size: 26rpx;
    color: @hc-color-text;
    .ellipsis();
  }
  .item-arrow {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
