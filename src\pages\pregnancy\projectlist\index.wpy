<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy"
import Outpatient from "@/components/outpatient/index"
import Empty from "@/components/empty/index"
import * as Api from "./api"
export default class PrijectList extends wepy.page {
	config = {
		navigationBarTitleText: "爱心助孕项目申请",
		navigationBarBackgroundColor: '#fff'
	}
	components = {
		outpatient: Outpatient,
		empty: Empty,
	}
	data = {
		// url参数
		options: {},
		// 空就诊人列表提醒
		emptyNotice: true,
		// 就诊人配置
		outpatientConfig: {
			infoShow: false,
			show: false,
			initUser: {},
		},
		// 就诊人列表
		outpatient: {},
		// 当前就诊人信息
		currentPatient: {},
		projectList: [],
		emptyConfig: {
			show: true,
		},
	}
	async getProjectList(params) {
		const {data = [], code} = await Api.getListproject({
			pid: params.patHisNo,
		})
		if (code === 0) {
			this.projectList = data
			this.$apply()
			if (data.length > 0) {
				this.emptyConfig.show = false
				this.$apply()
			}
		}
	}
	onLoad() {}
	onShow() {
		const {patientId = ""} = this.$wxpage.options
		this.options = this.$wxpage.options
		this.$broadcast("outpatient-get-patient", {patientId})
	}
	events = {
		"outpatient-change-user": function(activePatient) {
			if (activePatient) {
				this.options = activePatient
				this.outpatientConfig.infoShow = true
				this.$apply()
				this.getProjectList(activePatient)
			}
		},
	}
	methods = {
		onClick(item = {}) {
			wepy.navigateTo({
				url: `/pages/pregnancy/agreement/index?id=${item.id}&title=${item.projectName}&pid=${this.options.patHisNo}&name=${this.options.patientName}`,
			})
		},
		queryList() {
			wx.redirectTo({
				url: `/pages/pregnancy/recordlist/index?pid=${this.options.patHisNo}`,
			})
		},
		goToNextPage(val) {
			const weburl = val === 0 ? "https://mp.weixin.qq.com/s/Vs0xFR23F12m2vxdf3ERFw" : "https://mp.weixin.qq.com/s/_lQk23MHYpilWYQPdDmGfw"
			wepy.navigateTo({
				url: `/pages/webview/index?weburl=${encodeURIComponent(weburl)}`,
			})
		},
	}
}
</script>


