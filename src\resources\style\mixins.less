@hc-color-primary: #2D666F;
@hc-color-title: #000000;
@hc-color-text: #989898;
@hc-color-info: #3986ff;
@hc-color-warn: #ffa14e;
@hc-color-error: #f76260;

@hc-color-primary: #2D666F;
@hc-color-bg: #F2F4F4;
@hc-color-border: rgba(0, 0, 0, 0.08);
@hc-color-title: rgba(0, 0, 0, 0.9);
@hc-color-text: rgba(0, 0, 0, 0.7);
@hc-color-info: rgba(0, 0, 0, 0.4);
@hc-color-warn: #ff613b;
@hc-color-assist: #ffb040;
@hc-color-tip: #ffba00;
@hc-color-white: #ffffff;
@hc-color-black: #000000;
@hc-color-split: #e0e9e9;
@hc-color-link: #3986ff;
@hc-color-gray: #888888;
@hc-color-primary-bg: rgba(62, 206, 182, 0.1);

@hc-page-gap: 32rpx;
@hc-border-radius: 24rpx;

// 矩形
.rect(@width, @height) {
  width: @width;
  height: @height;
}

// 正方形
.square(@size) {
  width: @size;
  height: @size;
}

// 圆形
.circle(@size) {
  width: @size;
  height: @size;
  border-radius: 50%;
}

// 填充
.fillAll(@position: absolute) {
  position: @position;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// 单行省略号
.ellipsis() {
  overflow: hidden;
  word-wrap: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行省略号
.ellipsisLn(@line) {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line;
}

// 文字换行
.textBreak() {
  word-wrap: break-word;
  word-break: break-all;
}

// 清空button默认样式
.clearButton() {
  width: auto;
  height: auto;
  margin: auto;
  padding: auto;
  box-sizing: content-box;
  text-align: left;
  border-radius: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  overflow: auto;
  border: none;
  box-shadow: none;

  &:before,
  &:after {
    display: none;
    width: 0;
    height: 0;
    overflow: hidden;
    margin: auto;
    padding: auto;
    box-sizing: content-box;
    text-align: left;
    font-size: inherit;
    line-height: inherit;
    background-color: transparent;
    border-radius: 0;
    border: none;
    box-shadow: none;
  }
}
