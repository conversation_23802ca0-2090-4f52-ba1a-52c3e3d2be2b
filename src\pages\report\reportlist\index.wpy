<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Utils from "@/utils/utils";
import Outpatient from "@/components/outpatient/index";
import { getFormatDate } from "@/utils/utils";
import Empty from "@/components/empty/index";
import { DOMAIN, REQUEST_QUERY } from '@/config/constant';
import * as Api from "./api";
import md5 from "md5";

export default class ReportList extends wepy.page {
  config = {
    navigationBarTitleText: "报告查询 ",
    navigationBarBackgroundColor: '#fff',
  };

  components = {
    outpatient: Outpatient,
    empty: Empty
  };

  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
  }

  data = {
    patientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    emptyConfig: {
      show: true
    },
    patient: {},
    pid: "",
    grid: "",
    reportList: [],
    currentReportList: [],
    todayDate: getFormatDate(0, "-"),
    dateTypeValue: 2,
    reportTypeIndex: 0,
    reportTypeList: [
      { value: 0, name: "全部" },
      { value: 1, name: "检查" },
      { value: 2, name: "检验" },
      { value: 3, name: "遗传" }
    ],
    login: false,
    patientId: '',
    beiriList: [],
  };

  methods = {
    navToDeatil(e) {
      console.log(e.currentTarget.dataset, "e.currentTarget.dataset");
      const {
        id = "",
        reporttype = "",
        fyjg = "",
        url,
        baseurl = "",
        extfields = ""
      } = e.currentTarget.dataset;
      if (baseurl) {
        wepy.previewImage({
          urls: [baseurl],
          current: baseurl
        });
        return;
      }
      if (reporttype == 1) {
        wepy.navigateTo({
          url: `/pages/report/examine/index?reportId=${id}&pid=${
            this.pid
          }&grid=${this.grid}&patientId=${this.patientId}&extFields=${extfields}`
        });
      } else if (reporttype == 2) {
        wepy.navigateTo({
          url: `/pages/report/analysis/index?reportId=${id}&pid=${
            this.pid
          }&grid=${this.grid}&patientId=${this.patientId}&extFields=${extfields}`
        });
      } else if (reporttype == 3) {
        // 遗传报告 pdf
        // wepy.navigateTo({
        //   url: `/pages/webview/index?weburl=${url}`,
        // });
        console.log("111");
        wx.showLoading();
        wx.downloadFile({
          url,
          success: function(res) {
            const filePath = res.tempFilePath;
            wx.openDocument({
              filePath: filePath,
              success: function(res) {
                wx.hideLoading();
              }
            });
          }
        });
      }
    },
    changeDateRange(item = {}) {
      const { value = 1, beginDate } = item;
      this.dateTypeValue = value;
      const patient = {
        patientId: this.patientId || "",
        patCardNo: this.grid || "",
        patHisNo: this.pid || ""
      };
      this.getList(patient, value);
      this.getBeiriList(patient, value);
      this.$apply();
    },
    bindChangeTabIndex(index) {
      this.reportTypeIndex = index;
      this.getCurrentReportList(index);
      this.$apply();
    },
    async navToBeiriDeatil(e) {
      const { id, testitem } = e.currentTarget.dataset;
      const {cardList} = this.patient;
      const params = {
        id,
        pregnantname: cardList[0].patientName,
        msgKey: md5(id+'2023beiripdf')
      }

      wx.showLoading();
      //读取本地文件
      wx.downloadFile({
        // 示例 url，并非真实存在
        url: `${DOMAIN}/api/customize/getbeiripdf?_route=h${REQUEST_QUERY.platformId}&id=${id}&pregnantname=${cardList[0].patientName}&msgKey=${md5(id+'2023beiripdf')}`,
        success: function (res) {
          const filePath = res.tempFilePath;
          console.log(res, '====142')
          wx.openDocument({
            filePath: filePath,
            success: function (res) {
              wx.hideLoading();
            }
          })
        }
      })
    }
  };

  events = {
    "outpatient-change-user": function(item = {}) {
      if (item) {
        this.patientConfig.infoShow = true;
        this.changeUser(item);
        this.patientId = item.patientId;
      }
    }
  };

  async changeUser(item = {}) {
    this.getList(item);
    this.getBeiriList(item);
  }

  getCurrentReportList(index = 0) {
    // 获取当前选中类型报告列表
    const reportList = this.reportList || [];
    if (reportList.length == 0) {
      return;
    }
    let currentReportList = [];
    if (index == 0) {
      // 全部
      currentReportList = reportList;
    } else {
      // 具体类型
      reportList.forEach(item => {
        if (item.type == index) {
          currentReportList.push(item);
        }
      });
    }
    this.currentReportList = currentReportList;
    this.$apply();
  }

  /**
   * 获取待缴费列表
   */
  async getList(item = {}, dateTypeValue) {
    // FIXME: 切换就诊人后，之前的选中信息没有初始化
    const { patientId = "", patCardNo: grid = "", patHisNo: pid = "" } = item;
    dateTypeValue = dateTypeValue || this.dateTypeValue;
    this.pid = pid;
    this.grid = grid;
    const param = { pid, grid, patientId };
    this.emptyConfig.show = true;
    this.currentReportList = [];
    // this.reportList = [];
    const { code, data = {} } = await Api.getList(param);
    if (code == 0) {
      this.reportList = data.reportList || [];
      if (data.reportList && data.reportList.length > 0) {
        this.getCurrentReportList(this.reportTypeIndex || 0);
        this.emptyConfig.show = false;
      }
      this.$apply();
    }
  }
  async getBeiriList(item = {}, dateTypeValue) {
    const { patientId = "", patCardNo: grid = "", patHisNo: pid = "", patientName } = item;
    const param = { patienId: patientId, pregnantname: patientName, msgKey: md5(patientId+'2023beiri') };
    const { code, data = {} } = await Api.getBeiriList(param);
    this.emptyConfig.show = true;
    if(code === 0){
      this.beiriList = data.list || [];
      this.emptyConfig.show = false;
      if(data.list.length === 0){
        this.emptyConfig.show = true;
      }
      this.$apply();
    }
  }
}
</script>
