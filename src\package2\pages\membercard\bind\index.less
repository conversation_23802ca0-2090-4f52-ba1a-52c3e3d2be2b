@import "../../../../resources/style/mixins";

page {
  background-color: @hc-color-bg;
}
.p-page {
  position: relative;

  .page-top {
    padding: 40rpx 32rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .top-title {
      font-weight: 600;
      font-size: 40rpx;
      color: #fff;
    }

    image {
      width: 170rpx;
      height: 170rpx;
    }
  }

  .page-body {
    position: relative;
    margin-top: 24rpx;
    padding: 32rpx 32rpx 0;
    border-radius: 24rpx;
    font-size: 32rpx;
    background: #fff;
    color: @hc-color-text;

    .card-panel-title {
      margin-bottom: 24rpx;
      font-size: 32rpx;
      color: @hc-color-title;
    }
  }

  .bindcard-list {
    background: #fff;
    margin-top: 10rpx;
    .bindcard-listitem {
      padding: 24rpx 0;
      display: flex;
      align-items: center;
      position: relative;

      &.bindcard-listitem_none {
        display: none;
      }

      &:before {
        content: " ";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        border-top: 2rpx solid @hc-color-border;
      }
      &:first-child:before {
        display: none;
      }

      .list-title {
        color: @hc-color-title;
      }
      .list-text {
        background: @hc-color-bg;
        border-radius: 16rpx;
        height: 96rpx;
        padding: 24rpx;
        margin-top: 24rpx;
        box-sizing: border-box;
      }

      .listitem-head {
        width: 190rpx;
        display: flex;
        align-items: center;
        .textBreak();

        .list-title {
          flex: 1;
          font-size: 32rpx;
          color: @hc-color-title;
          padding-right: 12rpx;
          position: relative;
          line-height: 1;

          &.list-title_select:before {
            content: " ";
            position: absolute;
            right: 0;
            bottom: 0;
            box-sizing: border-box;
            border-bottom: 10rpx solid @hc-color-title;
            border-right: 10rpx solid @hc-color-title;
            border-top: 10rpx solid transparent;
            border-left: 10rpx solid transparent;
          }
        }
      }
      .listitem-body {
        flex: 1;
        // padding-left: 30rpx;
        position: relative;
        .textBreak();
      }
    }

    .listitem_accest {
      color: red;
    }

    .listitem_accest .listitem-body:before {
      content: " ";
      position: absolute;
      top: 50%;
      right: 0;
      border-right: 2rpx solid @hc-color-text;
      border-bottom: 2rpx solid @hc-color-text;
      width: 16rpx;
      height: 16rpx;
      transform: translateY(-50%) rotate(-45deg);
    }
  }

  .m-content {
    padding-left: 55rpx;
  }

  .m-mt-20 {
    margin-top: 20rpx;
  }

  .o-error {
    color: #ff613b;
  }

  .o-disabled {
    background: #ddd;
  }

  .afterscan-cardviewbox {
    background: #fff;
    padding: 20rpx 0 44rpx;
  }
  .afterscan-cardview {
    margin: auto;
    width: 390rpx;
    border-radius: 10rpx;
    border: 2rpx solid @hc-color-primary;
    overflow: hidden;

    .cardview-imgbox {
      width: 370rpx;
      height: 214rpx;
      overflow: hidden;
      margin: 9rpx auto 0;
      display: flex;
      justify-content: center;
      align-items: center;

      .cardview-img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .afterscan-reupload {
      background: @hc-color-primary;
      color: #fff;
      text-align: center;
      height: 52rpx;
      line-height: 52rpx;
      font-size: 26rpx;
    }
  }

  .afterscan-opertip {
    display: flex;
    .opertip-msg {
      flex: 1;
      color: #ffa14e;
      padding: 24rpx 0 36rpx 30rpx;
      font-size: 28rpx;
    }
    .opertip-imgbox {
      display: flex;
      align-items: flex-end;
      .opertip-img {
        width: 80rpx;
        height: 112rpx;
        margin: 0 26rpx;
      }
    }
  }

  .afterscan-operbtnbox {
    left: -32rpx;
    margin-top: 64rpx;
  }

  .binduser-radio {
    margin-left: 110rpx;
    &:first-child {
      margin-left: 0;
    }
  }
  .binduser-radio_object {
    transform-origin: 0 30%;
    transform: scale(0.7);
  }
  .binduser-radio_text {
    font-size: 30rpx;
  }
  .listitem-footer {
    background-color: @hc-color-primary;
    color: #fff;
    padding: 5rpx 15rpx;
    border-radius: 3rpx;
  }
  .listitem-footer.left-time {
    background-color: #fff;
    color: #8590a6;
    width: 3em;
    text-align: center;
  }
  
}
.binduser-btn_line {
  margin: 24rpx;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #fff;
  border-radius: 76rpx;
  text-align: center;
  font-size: 34rpx;
  padding: 0;
  border: none;
  &.cancel-btn{
    padding: 24rpx;
    background: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.70);
  }
}

buttton::after {
  border: none;
}
.color-red {
  color: @hc-color-error;
}
