<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { PRIMARY_COLOR } from '@/config/constant';


  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '中信湘雅',
      navigationBarBackgroundColor: '#fff'
    };

    data = {
    }

    onLoad(options){
      this.sourceId = options.sourceId;
      this.islinkapp = options.islinkapp || '1';
      this.$apply();
    }

    onShow(){
    }
     // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      sourceId: '',
      islinkapp: '1',
    };

    async saveSource(url) {
      const { code, data } = await Api.saveSource({sourceId: this.sourceId, islinkapp: this.islinkapp, type: 'article'});
      if (Number(code) === 0) {
        wepy.navigateTo({ url: url });
      }
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navTo(url){
        this.saveSource(url)
      }
    };
  }
</script>
