<view class="title">{{liveDetail.name}}</view>
<view class="describe">直播已结束</view>
<view class="card-label">给你推荐</view>
<view class="card" wx:if="{{liveingCount == 0}}">
  <view class="card-body">
    <empty>
      <text slot="text">没有更多了</text>
    </empty>
  </view>
</view>
<view class="card" @tap="onCardTab" wx:else>
  <view class="card-body">
    <view class="body-tag">
      直播中
      <view class="body-tag-trigle"></view>
    </view>
    <image class="body-poster" src="REPLACE_EHOSPITAL_DOMAIN/live-banner.png" mode="aspectFill" />
    <view class="body-name">{{recommendedLiveDetail.doctorName}}</view>
  </view>
  <view class="card-footer">
    <view class="item-title">{{recommendedLiveDetail.name}}</view>
    <view class="item-tags-box">
      <view class="item-tag">{{recommendedLiveDetail.liveType}}</view>
    </view>
  </view>
</view>
<view class="refresh" @tap="onRefresh" wx:if="{{liveingCount > 1}}">换一批</view>
<image class="close" src="REPLACE_EHOSPITAL_DOMAIN/delete.png" @tap="onBack"></image>