@import "../../../resources/style/mixins";

page {
  background-color: #f6f7f9;
  min-height: 100vh;
}

.dyna-tab-box {
  position: absolute;
  height: 88rpx;
  left: 0;
  right: 0;
  top: 0;
  overflow-x: auto;
  overflow-y: hidden;
  border-bottom: 2rpx solid @hc-color-border;
}

.dyna-tab {
  display: flex;
  position: absolute;
  min-width: 100%;

  .dyna-tabitem {
    font-size: 34rpx;
    flex: 1;
    padding: 0 20rpx;
    text-align: center;
    position: relative;
    // min-width: 3em;
    color: @hc-color-text;
    white-space: nowrap;
    line-height: 88rpx;
    height: 88rpx;

    &.tabitem-active {
      color: @hc-color-primary;

      &:before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 2.4em;
        height: 6rpx;
        background-color: @hc-color-primary;
        z-index: 1;
      }
    }
  }
}

.dyna-noheadlist {
  padding: 40rpx 32rpx;

  .dyna-item {
    padding: 20rpx 30rpx;
    border-bottom: 2rpx solid @hc-color-border;
    display: flex;
    border-radius: 24rpx;
    background: #fff;
    border: none;
    margin-bottom: 24rpx;
    padding: 32rpx;

    .item-main {
      width: 220rpx;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        max-width: 220rpx;
        max-height: 160rpx;
      }
    }

    .item-asside {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // margin-left: 20rpx;

      .dyna-title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.2;
        font-size: 32rpx;
        color: rgba(0, 0, 0, 0.9);
      }

      .dyna-info {
        display: flex;
        padding-top: 10rpx;
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.4);

        .info-lt {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          margin-right: 20rpx;
        }
      }
    }
  }
}

.dyna-list {
  padding-top: 88rpx;

  .dyna-item {
    padding: 20rpx 30rpx;
    border-bottom: 2rpx solid @hc-color-border;
    display: flex;

    .item-main {
      width: 220rpx;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        max-width: 220rpx;
        max-height: 160rpx;
      }
    }

    .item-asside {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 20rpx;

      .dyna-title {
        font-size: 30rpx;
        color: @hc-color-title;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.2;
      }

      .dyna-info {
        display: flex;
        color: @hc-color-text;
        font-size: 24rpx;
        padding-top: 10rpx;

        .info-lt {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          margin-right: 20rpx;
        }

        .info-rt {
        }
      }
    }
  }
}
