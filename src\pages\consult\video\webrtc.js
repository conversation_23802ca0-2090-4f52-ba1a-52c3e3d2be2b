import wepy from 'wepy';
import { post } from '@/utils/request';

const rtcUtils = {
  getRtcKey: param => post('/api/liveStream/sign', param),
  getPushUrl: async param => {
    /**
     * param: {
 *  sdkappid,
 *  roomId,
 *  userId,
 *  userSig,
 *  privMapEncrypt,
 * }
     */
    console.log(param);
    const url = `https://official.opensso.tencent-cloud.com/v4/openim/jsonvideoapp?sdkappid=${param.sdkappid}&identifier=${param.userId}&usersig=${param.userSig}&random=9999&contenttype=json`;
    const body = {
      "ReqHead": {
        "Cmd": 1,                                    //命令字，固定填1
        "SeqNo": 1,                                  //请求序列号，uint32
        "BusType": 7,                               //业务类型，固定填7
        "GroupId": Number(param.roomId)             //群组Id(房间Id)，uint32
      },
      "ReqBody": {
        "PrivMap": 255,                             //非必填，明文权限位
        "PrivMapEncrypt": param.privMapEncrypt,     //必填，权限位加密串
        "TerminalType": 1,                          //必填，终端类型，对应0x109中的TERMINAL_TYPE；Android：4；ios：2；
        "FromType": 3,                              //必填，请求来源类型：1：avsdk；2：webrtc；3：微信小程序；
        "SdkVersion": 26280566                      //非必填，整型版本号
      }
    };
    const res = await wepy.request({
      url: url,
      data: body,
      method: 'POST',
    });
    if (res.data.ErrorCode || res.data["RspHead"]["ErrorCode"] != 0) {
      console.error('getRtcPushUrl error : ', res.data);
      wepy.showToast({
        title: '获取房间秘钥失败', //提示的内容,
        icon: 'none', //图标,
        duration: 1000, //延迟时间,
        mask: true, //显示透明蒙层，防止触摸穿透,
      });
      return '';
    }
    const roomSig = encodeURIComponent(JSON.stringify(res.data["RspBody"]));
    let pushUrl = `room://cloud.tencent.com?sdkappid=${param.sdkappid}&roomid=${param.roomId}&userid=${param.userId}&roomsig=${roomSig}`;
    if (param.record_id) {
      const bizbuf = encodeURIComponent(JSON.stringify({
        Str_uc_params: {
          record_id: Number(param.record_id),
        }
      }));
      pushUrl += `&bizbuf=${bizbuf}`;
    }
    console.log(pushUrl);
    return pushUrl;
  }
}

export default rtcUtils;