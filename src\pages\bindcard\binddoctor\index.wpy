<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Pop from "@/components/pop/index";
  import * as Api from './api';
  import {login} from "@/utils/request";

  export default class BindDoctor extends wepy.page {
    config = {
      navigationBarTitleText: '关联医生',
    };

    components = {
      pop: Pop
    };

    onLoad(options) {
      const {doctorId} = this.$parent.globalData;
      let arrId = '';
      if (options.scene) {
        const scene = decodeURIComponent(options.scene);
        arrId = scene.split('=')[1];
      }
      this.doctorId = arrId || doctorId;
    }

    data = {
      // 弹窗显示
      popConfig: {
        show: false
      },
      doctorId: '',
      deptId: '',
    };

    methods = {
      async confirm() {
        const login_access_token = wepy.getStorageSync('login_access_token') || '';
        if (!login_access_token) {
          await login()
        }
        const { data = {} } = await Api.getCardList();
        const { cardList = [] } = data;
        let patient= cardList.find(item => item.relationType == 1);
        if (!patient) {
          this.popConfig.show = true;
          this.$parent.globalData.doctorId = this.doctorId;
          this.$apply();
        } else {
          this.bind(patient);
        }
      }
    };
    /**
     * 关联
     */
    async bind({patHisNo: patientId, patientId: patientTableId, idNo: patientIdNo, patientName, patientSex, patientMobile}){
      const param = {
        doctorId: this.doctorId,
        patientId,
        patientTableId,
        patientIdNo,
        patientName,
        patientSex,
        patientMobile
      }
      const { data, code } = await Api.bindDoctor(param);
      if(code == 0){
        wx.showToast({
          title: '关联成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            wepy.navigateTo({
              url: `/pages/home/<USER>
            });
          }
        });
      }
    }
  }
</script>
