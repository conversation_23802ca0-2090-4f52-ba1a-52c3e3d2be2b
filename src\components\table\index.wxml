 <view class="inhosp-table">
    <view class="title" wx:if="{{title}}">{{title}}</view>
    <view class="table">
      <view class="table-title" >
        <view wx:for="{{tableHeader}}" wx:key="index">{{item}}</view>
      </view>
      <view class="table-content" wx:for="{{dataSource}}" wx:key="index" wx:for-item="data" @tap="onTap({{data}})">
        <view wx:for="{{columnKey}}" wx:key="index" wx:for-item="key">{{data[key]}}</view>
      </view>
    </view>
  </view>