<template>
  <cover-view class="comm-doctor-info">
    <cover-image class="info-avatar" src="{{info.doctorImage || 'REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png'}}" mode="aspectFill" />
    <cover-view class="info-text">
      <cover-view class="text-title">{{info.doctorName}}{{info.level || ''}}</cover-view>
      <cover-view class="text-des">{{info.deptName || ''}}</cover-view>
    </cover-view>
    <!-- <cover-view wx:if="{{!isCollect}}" class="btn-small" @tap="onChangeCollect(true)">+收藏</cover-view>
    <cover-view wx:else class="btn-small btn-grey" @tap="onChangeCollect(false)">已收藏</cover-view> -->
  </cover-view>
</template>

<script>
import wepy from 'wepy';
import { post } from '@/utils/request';
export default class DoctorInfo extends wepy.component {
  props = {
    info: {
      type: Object,
      default: {},
    }
  };

  data = {
    isCollect: false,
    hasGetCollectStatus: false,
  };

  components = {};

  methods = {
    onChangeCollect(collect) {
      const param = {
        doctorId: this.info.doctorId,
        deptId: this.info.deptId
      };
      if (collect && collect == 'true') {
        this.collect(param);
      } else {
        this.unCollect(param);
      }
    }
  };

  events = {};

  watch = {
    info(newVal) {
      if (newVal.doctorId && !this.hasGetCollectStatus) {
        this.hasGetCollectStatus = true;
        // this.getCollectStatus();
      }
    }
  };

  computed = {};

  onUnload() {
    this.hasGetCollectStatus = false;
  }

  async getCollectStatus() {
    const param = {
      doctorId: this.info.doctorId,
      deptId: this.info.deptId
    };
    const { code, data } = await post('/api/ehis/health/api/doctor/doctor', param);
    if (code == 0) {
      this.isCollect = data.isFavorite;
      this.$apply();
    }
  }
  async collect(param) {
    const { code, data } = await post('/api/ehis/user/favorite/addmyfavorite', param);
    if (code == 0) {
      this.isCollect = true;
      wepy.showToast({
        title: '收藏成功',
        icon: 'success'
      });
      this.$apply();
    }
  }
  async unCollect(param) {
    const { code, data } = await post('/api/ehis/user/favorite/cancelmyfavorite', param);
    if (code == 0) {
      this.isCollect = false;
      wepy.showToast({
        title: '取消成功',
        icon: 'success'
      });
      this.$apply();
    }
  }
}
</script>

<style lang='less'>
@import '../../../../resources/style/mixins.less';
.comm-doctor-info {
  padding: 28rpx 30rpx;
  background: #fafafa;
  display: flex;
  align-items: center;
  .info-avatar {
    height: 100rpx;
    width: 100rpx;
    margin-right: 20rpx;
    border-radius: 50rpx;
  }
  .info-text {
    flex: 1;
    margin-right: 10rpx;
    .text-title {
      font-size: 32rpx;
      font-weight: 600;
      color: @hc-color-title;
      margin-bottom: 5rpx;
    }
    .text-des {
      font-size: 26rpx;
      color: @hc-color-text;
    }
  }
  .btn-small {
    width: 110rpx;
    padding: 14rpx 0;
    background: @hc-color-primary;
    border-radius: 10rpx;
    font-size: 30rpx;
    color: #ffffff;
    text-align: center;
    &.btn-grey {
      background: @hc-color-info;
    }
  }
}
</style>