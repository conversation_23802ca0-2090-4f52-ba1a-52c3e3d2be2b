<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import WxsUtils from "../../../wxs/utils.wxs";
import { IS_SEC_ITEM } from "@/config/constant";
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "待缴费明细",
    navigationBarBackgroundColor: '#fff'
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  components = {};

  onLoad(options = {}) {
    const { isScan = '0', patHisNo = '', patCardNo = '', patientName = '', patCardType = '' } = options;
    this.isScan = isScan;
    this.patHisNo = patHisNo;
    this.patCardNo = patCardNo;
    this.patientName = patientName;
    this.patCardType = patCardType;
    this.extPropStr = JSON.stringify({ isScan, patHisNo, patCardNo, patCardType, patientName });
    this.options = options;
    console.log(options, '=====24')
    this.getOrder(options);
    this.getDetail(options)
  }

  data = {
    // 门诊待缴费数据
    IS_SEC_ITEM,
    options: {},
    orderList: [],
    isSubmit: false,
    treatDetail: {},
    listDetail: [],
    detailTotalFee: 0,
    // 扫码参数
    extPropStr: '',
    isScan: '',
    patHisNo: '',
    patCardNo: '',
    patientName: '',
    patCardType: '',
  };

  computed = {
    // 计算选中的订单金额和个数
    total: () => {
      let totalFee = 0,
        count = 0,
        mergeHisOrderNoList = [],
        hisSerilNo = '';
      for (let i in this.orderList) {
        const item = this.orderList[i];
        if (item.checked) {
          if (!hisSerilNo) {
            hisSerilNo = item.extFields.hisSerilNo;
          }
          totalFee += item.totalFee;
          count += 1;
          mergeHisOrderNoList.push(item.extFields.hisBillNo);
        }
      }
      return {
        totalFee,
        count,
        hisSerilNo,
        mergeHisOrderNo: mergeHisOrderNoList.join(",")
      };
    },
    // 计算是否全选
    isAllChecked: () => {
      for (let i in this.orderList) {
        const item = this.orderList[i];
        if (!item.checked) {
          return false;
        }
      }
      return true;
    }
  };

  methods = {
    bindCreateOrder() {
      this.createOrder();
    },
    checkThis(index) {
      this.orderList[index].checked = !this.orderList[index].checked;
      this.$apply();
    },
    showDetail(index) {
      let item = this.orderList[index];
      if (!item.itemList || item.itemList.length === 0) {
        this.getDetail(index);
      }
      item.isShowDetail = !item.isShowDetail;
      this.$apply();
    },
    checkAll() {
      let checked = true;
      if (this.isAllChecked) {
        checked = false;
      }
      this.orderList = this.orderList.map(item => {
        item.checked = checked;
        return item;
      });
      this.$apply();
    }
  };

  /**
   * 获取待缴费项目列表
   */
  async getOrder(item = {}) {
    const { patientId = "", hisOrderNo = "" } = item;
    const { code, data = {}, msg } = await Api.getOrder({
      patientId,
      transParam: JSON.stringify({ hisOrdNum: hisOrderNo }),
      ...(item.isScan == 1 ? { extPropStr: this.extPropStr } : {}),
    });
    if (code !== 0) {
      return;
    }
    let { waitOpList = [] } = data;
    waitOpList = waitOpList.map(item => {
      item.date = item.date.split(" ")[0];
      item.extFields = item.extFields ? JSON.parse(item.extFields) : '';
      item.checked = true;
      return item;
    });
    this.orderList = waitOpList;
    this.$apply();
  }
  /**
   * 获取单个项目的详情
   */
  async getDetail(option) {
    // const { extFields = {} } = this.orderList[index];
    // const { hisBillNo = "", hisSerilNo = "" } = extFields;
    const { code, data = {}, msg } = await Api.getDetail({
      ...option,
      ...(option.isScan == 1 ? { extPropStr: this.extPropStr } : {}),
    });
    if (code !== 0) {
      return;
    }
    const { itemList = [] } = data;

    this.detailTotalFee = data.totalFee; // 单位转换为分（参考原有逻辑）
    this.$apply();

    itemList.forEach(item => {
      item.itemPrice = Number(item.itemPrice) * 100;
      item.totalFee = Number(item.totalFee) * 100;
    })

    this.listDetail = itemList;
    this.$apply();
  }

  /**
   * 创建订单
   */
  async createOrder() {
    const { patientId = "", hisOrderNo = "", isScan } = this.options || {};
    const { mergeHisOrderNo = "", hisSerilNo } = this.total || {};
    const param = {
      patientId: patientId || '-1',
      hisOrderNo,
      transParam: JSON.stringify({ hisOrderNo, hisSerilNo })
    };

    if (isScan == 1) {
      param.extPropStr = this.extPropStr;
    };

    this.isSubmit = true; // 禁用提交按钮
    const { code, data = {} } = await Api.createOrder(param);
    this.isSubmit = false; // 恢复提交按钮

    if (code !== 0) {
      return;
    }
    this.treatDetail = data;
    const { orderId = "" } = data;
    this.registerPayOrder({ orderId });
  }

  /**
   * 创建支付订单
   * @param item
   */
  async registerPayOrder(item = {}) {
    let bizContent;
    const { orderId = "" } = item;
    try {
      bizContent = JSON.stringify(this.getBizContent() || []);
    } catch (e) {
      bizContent = "[]";
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent,
      bizType: 'outpatient',
      orderType: 'outpatient'
    });
    if (code !== 0) {
      return;
    }
    const { payOrderId = "" } = data;
    wepy.navigateTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=MZJF`
    });
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent() {
    const { treatDetail = {} } = this;
    return [
      { key: "费用类型", value: "门诊缴费" },
      { key: "医院名称", value: treatDetail.hisName || "" },
      { key: "就诊科室", value: treatDetail.deptName ? treatDetail.deptName : this.orderList[0].deptName ? this.orderList[0].deptName : "" },
      { key: "医生名称", value: treatDetail.doctorName ? treatDetail.doctorName : this.orderList[0].doctorName ? this.orderList[0].doctorName : "" },
      { key: "就诊人", value: treatDetail.patientName || "" },
      { key: "就诊卡号", value: treatDetail.patCardNo || "" }
    ];
  }
}
</script>
