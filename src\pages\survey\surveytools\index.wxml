<view class="p-page">
  <!-- 收样列表视图 -->
  <view class="sample-list-container" wx:if="{{showSampleList}}">
    <view class="sample-list-header">
      <view class="sample-list-title">收样列表</view>
      <view class="create-sample-btn" bindtap="createNewSample">新建</view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="sample-loading" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <text>加载中...</text>
    </view>
    
    <!-- 列表为空提示 -->
    <view class="sample-empty" wx:elif="{{!healthOrderList || healthOrderList.length === 0}}">
      <image class="empty-icon" src="../../../resources/images/empty.png" mode="aspectFit"></image>
      <text>暂无收样记录</text>
      <view class="create-sample-btn-empty" bindtap="createNewSample">创建新样本</view>
    </view>
    
    <!-- 列表内容 -->
    <scroll-view class="sample-list" scroll-y wx:else>
      <view class="sample-item" wx:for="{{healthOrderList}}" wx:key="id" bindtap="viewSampleDetail" data-id="{{item.id}}">
        <view class="sample-item-header">
          <view class="sample-id">样本问卷</view>
          <view class="sample-time">{{item.createTime || ''}}</view>
        </view>
        
        <view class="sample-item-content">
          <view class="sample-info-row">
            <view class="sample-info-label">姓名：</view>
            <view class="sample-info-value">{{item.sjzName || '-'}}</view>
          </view>
          
          <view class="sample-info-row">
            <view class="sample-info-label">身份证号码：</view>
            <view class="sample-info-value">{{item.sjzIdNum || 'xxxxxxxxxxxxxxxxxx'}}</view>
          </view>
          
          <view class="sample-info-row">
            <view class="sample-info-label">手机号码：</view>
            <view class="sample-info-value">{{item.sjzPhone || 'xxxxxxxxxx'}}</view>
          </view>
          
          <view class="sample-info-row">
            <view class="sample-info-label">样本编号：</view>
            <view class="sample-info-value">{{item.sampleId || '-'}}</view>
          </view>
          
          <view class="sample-info-row">
            <view class="sample-info-label">报告状态：</view>
            <view class="sample-info-value">
              <text class="status-tag {{item.reportStatus === 1 ? 'status-completed' : 'status-pending'}}">
                {{item.reportStatus === 1 ? '已出具' : '未出具'}}
              </text>
            </view>
          </view>
        </view>
        
        <view class="sample-item-footer">
          <view class="sample-view-btn">继续填写</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 步骤条 -->
  <view class="step-process" wx:if="{{!showSampleList}}">
    <view class="step-item {{currentStep >= 1 ? 'active' : ''}}">
      <view class="step-number">1</view>
      <view class="step-title">选择产品</view>
    </view>
    <view class="step-item {{currentStep >= 2 ? 'active' : ''}}">
      <view class="step-number">2</view>
      <view class="step-title">填写基础信息</view>
    </view>
    <view class="step-item {{currentStep >= 3 ? 'active' : ''}}">
      <view class="step-number">3</view>
      <view class="step-title">报告出具</view>
    </view>
  </view>

  <!-- 步骤1: 问卷卡片列表 -->
  <view class="question-cards" wx:if="{{!showSampleList && currentStep === 1}}">
    <view class="select-title">请选择合适的您的产品类型</view>
    <view class="question-card normal-version {{selectedType === 'normal' ? 'selected' : ''}}" bindtap="goToStep2" data-type="normal">
      <view class="card-title">全基因组检测-成人版</view>
      <!-- <view class="card-description">适合一般家庭成员使用</view> -->
    </view>
    
    <view class="question-card child-version {{selectedType === 'child' ? 'selected' : ''}}" bindtap="goToStep2" data-type="child">
      <view class="card-title">全基因组检测-儿童版</view>
      <!-- <view class="card-description">专为儿童群体设计</view> -->
    </view>
  </view>
  
  <!-- 步骤2: 基础信息表单 -->
  <view class="basic-info-form" wx:if="{{!showSampleList && currentStep === 2}}">
    <!-- 儿童版表单 -->
    <block wx:if="{{selectedType === 'child'}}">
      <view class="section-title">
        <view class="title-icon"></view>
        孩子基本信息
      </view>
      
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-item {{errors.name ? 'error' : ''}} {{focusField === 'name' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>姓名</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon name-icon"></view>
            <input class="form-input" type="text" placeholder="请输入检测者姓名" value="{{formData.name}}" bindinput="inputChange" data-field="name" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.name}}">{{errors.name}}</view>
        </view>
        
        <!-- 年龄 -->
        <view class="form-item {{errors.age ? 'error' : ''}} {{focusField === 'age' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>年龄</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon age-icon"></view>
            <input class="form-input" type="number" placeholder="请输入年龄" value="{{formData.age}}" bindinput="inputChange" data-field="age" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.age}}">{{errors.age}}</view>
        </view>
        
        <!-- 性别 -->
        <view class="form-item {{errors.gender ? 'error' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>性别</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon gender-icon"></view>
            <view class="form-select" bindtap="selectGender">
              <text class="select-text {{!formData.genderName ? 'placeholder' : ''}}">{{formData.genderName || '请选择性别'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.gender}}">{{errors.gender}}</view>
        </view>
        
        <!-- 身高 -->
        <view class="form-item {{errors.height ? 'error' : ''}} {{focusField === 'height' ? 'focused' : ''}}">
          <view class="form-label">
            <text>身高</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon height-icon"></view>
            <view class="unit-input">
              <input class="form-input" type="digit" placeholder="请输入身高" value="{{formData.height}}" bindinput="inputChange" data-field="height" bindfocus="onFieldFocus" bindblur="validateField" />
              <text class="unit-text">cm</text>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.height}}">{{errors.height}}</view>
        </view>
        
        <!-- 体重 -->
        <view class="form-item {{errors.weight ? 'error' : ''}} {{focusField === 'weight' ? 'focused' : ''}}">
          <view class="form-label">
            <text>体重</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon weight-icon"></view>
            <view class="unit-input">
              <input class="form-input" type="digit" placeholder="请输入体重" value="{{formData.weight}}" bindinput="inputChange" data-field="weight" bindfocus="onFieldFocus" bindblur="validateField" />
              <text class="unit-text">kg</text>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.weight}}">{{errors.weight}}</view>
        </view>
        
        <!-- 民族 -->
        <view class="form-item {{errors.nation ? 'error' : ''}} {{focusField === 'nation' ? 'focused' : ''}}">
          <view class="form-label">
            <text>民族</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon nation-icon"></view>
            <input class="form-input" type="text" placeholder="请输入民族" value="{{formData.nation}}" bindinput="inputChange" data-field="nation" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.nation}}">{{errors.nation}}</view>
        </view>
      </view>
      
      <!-- 监护人基本信息 -->
      <view class="section-title">
        <view class="title-icon"></view>
        监护人基本信息
      </view>
      
      <view class="form-section">
        <!-- 监护人姓名 -->
        <view class="form-item {{errors.jhrName ? 'error' : ''}} {{focusField === 'jhrName' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>姓名</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon name-icon"></view>
            <input class="form-input" type="text" placeholder="请输入监护人姓名" value="{{formData.jhrName}}" bindinput="inputChange" data-field="jhrName" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.jhrName}}">{{errors.jhrName}}</view>
        </view>
        
        <!-- 监护人证件类型 -->
        <view class="form-item {{errors.jhrIdType ? 'error' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>证件类型</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon id-type-icon"></view>
            <view class="form-select" bindtap="selectJhrIdType">
              <text class="select-text {{!formData.jhrIdType ? 'placeholder' : ''}}">{{formData.jhrIdType || '请选择证件类型'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.jhrIdType}}">{{errors.jhrIdType}}</view>
        </view>
        
        <!-- 监护人证件号码 -->
        <view class="form-item {{errors.jhrIdNumber ? 'error' : ''}} {{focusField === 'jhrIdNumber' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>证件号码</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon id-card-icon"></view>
            <input class="form-input" type="idcard" placeholder="请输入监护人证件号码" value="{{formData.jhrIdNumber}}" bindinput="inputChange" data-field="jhrIdNumber" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.jhrIdNumber}}">{{errors.jhrIdNumber}}</view>
        </view>
        
        <!-- 联系电话 (监护人) -->
        <view class="form-item {{errors.jhrPhone ? 'error' : ''}} {{focusField === 'jhrPhone' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>联系电话</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon phone-icon"></view>
            <input class="form-input" type="number" placeholder="请输入联系方式" value="{{formData.jhrPhone}}" bindinput="inputChange" data-field="jhrPhone" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.jhrPhone}}">{{errors.jhrPhone}}</view>
        </view>
      </view>
    </block>
    
    <!-- 成人版表单 -->
    <block wx:else>
      <view class="section-title">
        <view class="title-icon"></view>
        基本信息
      </view>
      
      <!-- 必填信息区域 -->
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-item {{errors.name ? 'error' : ''}} {{focusField === 'name' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>姓名</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon name-icon"></view>
            <input class="form-input" type="text" placeholder="请输入检测者姓名" value="{{formData.name}}" bindinput="inputChange" data-field="name" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.name}}">{{errors.name}}</view>
        </view>
        
        <!-- 证件类型 -->
        <view class="form-item {{errors.idType ? 'error' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>证件类型</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon id-type-icon"></view>
            <view class="form-select" bindtap="selectIdType">
              <text class="select-text {{!formData.idType ? 'placeholder' : ''}}">{{formData.idType || '请选择证件类型'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.idType}}">{{errors.idType}}</view>
        </view>
        
        <!-- 证件号 -->
        <view class="form-item {{errors.idNumber ? 'error' : ''}} {{focusField === 'idNumber' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>证件号</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon id-card-icon"></view>
            <input class="form-input" type="idcard" placeholder="请输入证件号码" value="{{formData.idNumber}}" bindinput="inputChange" data-field="idNumber" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.idNumber}}">{{errors.idNumber}}</view>
        </view>
        
        <!-- 年龄 -->
        <view class="form-item {{errors.age ? 'error' : ''}} {{focusField === 'age' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>年龄</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon age-icon"></view>
            <input class="form-input" type="number" placeholder="请输入年龄" value="{{formData.age}}" bindinput="inputChange" data-field="age" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.age}}">{{errors.age}}</view>
        </view>
        
        <!-- 性别 -->
        <view class="form-item {{errors.gender ? 'error' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>性别</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon gender-icon"></view>
            <view class="form-select" bindtap="selectGender">
              <text class="select-text {{!formData.genderName ? 'placeholder' : ''}}">{{formData.genderName || '请选择性别'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.gender}}">{{errors.gender}}</view>
        </view>
        
        <!-- 联系电话 -->
        <view class="form-item {{errors.phone ? 'error' : ''}} {{focusField === 'phone' ? 'focused' : ''}}">
          <view class="form-label">
            <text class="required">*</text>
            <text>联系电话</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon phone-icon"></view>
            <input class="form-input" type="number" placeholder="请输入联系方式" value="{{formData.phone}}" bindinput="inputChange" data-field="phone" bindfocus="onFieldFocus" bindblur="validateField" />
          </view>
          <view class="error-message" wx:if="{{errors.phone}}">{{errors.phone}}</view>
        </view>
      </view>
      
      <!-- 选填信息区域 -->
      <view class="form-section">
        <!-- 身高 -->
        <view class="form-item {{errors.height ? 'error' : ''}} {{focusField === 'height' ? 'focused' : ''}}">
          <view class="form-label">
            <text>身高</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon height-icon"></view>
            <view class="unit-input">
              <input class="form-input" type="digit" placeholder="请输入身高" value="{{formData.height}}" bindinput="inputChange" data-field="height" bindfocus="onFieldFocus" bindblur="validateField" />
              <text class="unit-text">cm</text>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.height}}">{{errors.height}}</view>
        </view>
        
        <!-- 体重 -->
        <view class="form-item {{errors.weight ? 'error' : ''}} {{focusField === 'weight' ? 'focused' : ''}}">
          <view class="form-label">
            <text>体重</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon weight-icon"></view>
            <view class="unit-input">
              <input class="form-input" type="digit" placeholder="请输入体重" value="{{formData.weight}}" bindinput="inputChange" data-field="weight" bindfocus="onFieldFocus" bindblur="validateField" />
              <text class="unit-text">kg</text>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.weight}}">{{errors.weight}}</view>
        </view>
        
        <!-- 职业 -->
        <view class="form-item {{errors.occupation ? 'error' : ''}}">
          <view class="form-label">
            <text>职业</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon occupation-icon"></view>
            <view class="form-select" bindtap="selectOccupation">
              <text class="select-text {{formData.occupationName ? '' : 'placeholder'}}">{{formData.occupationName || '请选择'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.occupation}}">{{errors.occupation}}</view>
        </view>
        
        <!-- 文化程度 -->
        <view class="form-item {{errors.education ? 'error' : ''}}">
          <view class="form-label">
            <text>文化程度</text>
          </view>
          <view class="input-wrapper">
            <view class="input-icon education-icon"></view>
            <view class="form-select" bindtap="selectEducation">
              <text class="select-text {{formData.educationName ? '' : 'placeholder'}}">{{formData.educationName || '请选择'}}</text>
              <view class="select-arrow"></view>
            </view>
          </view>
          <view class="error-message" wx:if="{{errors.education}}">{{errors.education}}</view>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 原步骤3问卷内容已移除，现在用于问卷详情页面 -->
  <view class="survey-form" wx:if="{{false}}">
    <!-- 问卷调查内容 -->
    <block>
      <view class="survey-container">
        <view class="survey-card">
          <view class="survey-head">
            <view class="survey-title">信息采集单-{{selectedType === 'child' ? '儿童版' : '成人版'}}</view>
            <view class="survey-des">
              <view style="text-indent: 2em; line-height: 1.6; color: #333; margin-bottom: 15px;">尊敬的先生/女士，您好！为帮助医生快速、准确了解您的健康状况，需要您花几分钟时间填写以下问卷。您提供的信息越详细及准确，更有利数据分析，以便精准诊断。所有数据将严格保密，请放心填写。感谢您的配合！</view>
            </view>
          </view>
          <view class="survey-body">
            <block wx:for="{{titleList}}" wx:for-index="subjectListIdx" wx:key="key" wx:for-item="question">
              <block wx:if="{{(id == '13' && (subjectListIdx <= startExpandIdx || ( subjectListIdx > startExpandIdx && isSpecialDis === false))) || id != '13'}}">
                <!-- 单选题 -->
                <block wx:if="{{question.questionsType == '0'}}">
                  <view class="question-item" id="title-{{question.titleId}}">
                    <view class="question-title">
                      {{question.titleNum}}、{{question.questionsTitle}}（单选）
                      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
                    </view>
                    <radio-group class="radio-group" data-title-id="{{question.titleId}}" data-questions-type="{{question.questionsType}}" bindchange="bindValueChange">
                      <view class="radio-group-item {{item.checked ? 'active' : ''}}" wx:for="{{question.optionList}}" wx:for-index="optionsIdx" wx:for-item="item" wx:key="optionNum">
                        <view>
                          <radio checked="{{item.checked}}" value="{{item.optionNum}}" color="#3ECEB6" disabled="{{readonly == 1}}" />
                          <label>{{item.optionContent}}</label>
                        </view>
                        
                        <!-- 单选题中的二级文本框 -->
                        <view wx:if="{{item.optionType === '1' && item.checked}}" class="secondary-content-container">
                          <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true"></textarea>
                        </view>
                        
                        <!-- 单选题中的二级选项复选框 -->
                        <view wx:if="{{item.optionType === '2' && item.checked}}" class="secondary-content-container">
                          <checkbox-group class="secondary-checkbox-group" data-title-id="{{question.titleId}}" data-option-num="{{item.optionNum}}" bindchange="bindSecondOptionChange">
                            <block wx:if="{{item.secondOptionArray && item.secondOptionArray.length > 0}}">
                              <block wx:for="{{item.secondOptionArray}}" wx:for-item="secondOption" wx:for-index="secondIdx" wx:key="secondIdx">
                                <view class="secondary-checkbox-item">
                                  <checkbox value="{{secondOption}}" color="#3ECEB6" disabled="{{readonly == 1}}" />
                                  <text>{{secondOption}}</text>
                                </view>
                              </block>
                            </block>
                            <view wx:else class="empty-secondary-options">
                              暂无二级选项
                            </view>
                          </checkbox-group>
                        </view>

                        <!-- 单选题中的二级多值填空 -->
                        <view wx:if="{{item.optionType === '3' && item.checked}}" class="secondary-content-container">
                          <view class="secondary-fill-blank-container">
                            <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                              <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                              <input
                                wx:else
                                class="fill-blank-input"
                                type="text"
                                placeholder=""
                                data-title-id="{{question.titleId}}"
                                data-option-num="{{item.optionNum}}"
                                data-index="{{part.index}}"
                                data-unique-id="{{part.uniqueId}}"
                                value="{{part.value}}"
                                bindinput="bindSecondFillBlankChange"
                                bindfocus="bindSecondFillBlankFocus"
                                maxlength="10"
                              />
                            </block>
                          </view>
                        </view>
                        
                        <view wx:if="{{item.imageUrl}}">
                          <image mode="widthFix" src="{{item.imageUrl}}"></image>
                        </view>
                        
                        <!-- 单选题中的扩展输入框 -->
                        <view wx:if="{{item.haveRemarkFrame == 1 && item.checked}}" class="radio-group-ext-panel">
                          <block wx:for="{{item.extInputList}}" wx:for-item="ext" wx:for-index="extIdx" wx:key="key">
                            <view class="radio-group-ext-input-item">
                              <label>{{ext.label}}</label>
                              <textarea maxlength="-1" placeholder="请在此输入详细内容..." placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" data-idx="{{extIdx}}" value="{{ext.value}}" data-optionnum="{{item.optionNum}}" bindinput="bindExtAnswerRemarkChange" bindblur="bindExtAnswerRemarkChange" style="width:100%;box-sizing:border-box;" auto-height="true"></textarea>
                            </view>
                          </block>
                        </view>
                      </view>
                    </radio-group>
                    
                    <!-- 附件上传 -->
                    <view wx:if="{{question.fileFlg == '1'}}" class="file-upload-section">
                      <view class="file-upload-title">
                        <text class="file-upload-title-text">{{question.fileUploadDescribe || '请上传相关附件'}}</text>
                        <text class="file-upload-title-desc">支持上传图片、PDF等文件格式</text>
                      </view>
                      <view class="file-upload-content">
                        <!-- Per-question file preview -->
                        <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                          <!-- Image item -->
                          <view class="file-item" wx:if="{{file.type === 'image'}}">
                            <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                            <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                          <!-- PDF item -->
                          <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                            <view class="pdf-icon">PDF</view>
                            <view class="pdf-name">{{file.fileName}}</view>
                            <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                        </block>

                        <!-- Upload Button -->
                        <view wx:if="{{question.imageCount < 9}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                          <view class="upload-icon-plus">+</view>
                          <text class="upload-btn-text">点击上传</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </block>
                
                <!-- 多选题 -->
                <block wx:if="{{question.questionsType == '1'}}">
                  <view class="question-item" id="title-{{question.titleId}}">
                    <view class="question-title">
                      {{question.titleNum}}、{{question.questionsTitle}}(多选，最多{{question.maxAnswer}}项)
                      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
                    </view>
                    <view>
                      <view class="checkbox {{item.checked ? 'active' : ''}}" wx:for="{{question.optionList}}" wx:key="optionNum" wx:for-item="item" wx:for-index="optionIdx">
                        <view>
                          <checkbox disabled="{{readonly == 1}}" value="{{item.optionNum}}" checked="{{item.checked}}" color="#3ECEB6" bindtap="checkboxChange" data-cur-option="{{item.optionNum}}" data-option-group="{{item.optionGroup||''}}" data-title-id="{{question.titleId}}" />
                          <text>{{item.optionContent}}</text>
                        </view>
                        
                        <!-- 多选题中的二级文本框 -->
                        <view wx:if="{{item.optionType === '1' && item.checked}}" class="secondary-content-container">
                          <textarea maxlength="-1" placeholder="{{item.secondOptionContent || '请在此输入详细信息...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" value="{{item.secondAnswerContent}}" data-optionnum="{{item.optionNum}}" bindinput="bindSecondAnswerChange" bindblur="bindSecondAnswerChange" style="width:100%;box-sizing:border-box;" auto-height="true"></textarea>
                        </view>
                        
                        <!-- 多选题中的二级选项复选框 -->
                        <view wx:if="{{item.optionType === '2' && item.checked}}" class="secondary-content-container">
                          <checkbox-group class="secondary-checkbox-group" data-title-id="{{question.titleId}}" data-option-num="{{item.optionNum}}" bindchange="bindSecondOptionChange">
                            <block wx:if="{{item.secondOptionArray && item.secondOptionArray.length > 0}}">
                              <block wx:for="{{item.secondOptionArray}}" wx:for-item="secondOption" wx:for-index="secondIdx" wx:key="secondIdx">
                                <view class="secondary-checkbox-item">
                                  <checkbox value="{{secondOption}}" color="#3ECEB6" disabled="{{readonly == 1}}" />
                                  <text>{{secondOption}}</text>
                                </view>
                              </block>
                            </block>
                            <view wx:else class="empty-secondary-options">
                              暂无二级选项
                            </view>
                          </checkbox-group>
                        </view>

                        <!-- 多选题中的二级多值填空 -->
                        <view wx:if="{{item.optionType === '3' && item.checked}}" class="secondary-content-container">
                          <view class="secondary-fill-blank-container">
                            <block wx:for="{{item.secondContentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                              <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                              <input
                                wx:else
                                class="fill-blank-input"
                                type="text"
                                placeholder=""
                                data-title-id="{{question.titleId}}"
                                data-option-num="{{item.optionNum}}"
                                data-index="{{part.index}}"
                                data-unique-id="{{part.uniqueId}}"
                                value="{{part.value}}"
                                bindinput="bindSecondFillBlankChange"
                                bindfocus="bindSecondFillBlankFocus"
                                maxlength="10"
                              />
                            </block>
                          </view>
                        </view>
                        
                        <view wx:if="{{item.imageUrl}}">
                          <image mode="widthFix" src="{{item.imageUrl}}"></image>
                        </view>
                        
                        <!-- 多选题中的备注框 -->
                        <view wx:if="{{item.haveRemarkFrame == 1 && item.checked}}" class="checkbox-remark">
                          <textarea maxlength="200" placeholder="{{item.frameDefaultText || '请在此输入详细内容...'}}" placeholder-style="color:#bbb;font-size:26rpx;" data-title-id="{{question.titleId}}" value="{{item.answerRemark}}" data-optionnum="{{item.optionNum}}" bindinput="bindAnswerRemarkChange" bindblur="bindAnswerRemarkChange" style="width:100%;box-sizing:border-box;" auto-height="true"></textarea>
                        </view>
                      </view>
                    </view>
                    
                    <!-- 附件上传 -->
                    <view wx:if="{{question.fileFlg == '1'}}" class="file-upload-section">
                      <view class="file-upload-title">
                        <text class="file-upload-title-text">{{question.fileUploadDescribe || '请上传相关附件'}}</text>
                        <text class="file-upload-title-desc">支持上传图片、PDF等文件格式</text>
                      </view>
                      <view class="file-upload-content">
                        <!-- Per-question file preview -->
                        <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                          <!-- Image item -->
                          <view class="file-item" wx:if="{{file.type === 'image'}}">
                            <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                            <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                          <!-- PDF item -->
                          <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                            <view class="pdf-icon">PDF</view>
                            <view class="pdf-name">{{file.fileName}}</view>
                            <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                        </block>
                        
                        <!-- Upload Button -->
                        <view wx:if="{{question.imageCount < 9}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                          <view class="upload-icon-plus">+</view>
                          <text class="upload-btn-text">点击上传</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </block>

                <!-- 填空题（多空填值） -->
                <block wx:if="{{question.questionsType == '18'}}">
                  <view class="question-item question-item-fillblank" id="title-{{question.titleId}}">
                    <text class="question-title question-title-fillblank">{{question.titleNum}}、</text>
                    <view class="fill-blank-title">
                      <!-- 遍历选项，直接在题目中显示填空内容 -->
                      <block wx:for="{{question.optionList}}" wx:for-item="option" wx:key="optionNum">
                        <!-- 将optionContent中的{val}替换为输入框 -->
                        <block wx:for="{{option.contentParts}}" wx:for-item="part" wx:for-index="partIndex" wx:key="partIndex">
                          <text wx:if="{{!part.isInput}}" class="fill-blank-text">{{part.text}}</text>
                          <input 
                            wx:else 
                            class="fill-blank-input" 
                            type="text" 
                            placeholder="" 
                            data-title-id="{{question.titleId}}" 
                            data-option-num="{{option.optionNum}}" 
                            data-index="{{part.index}}"
                            data-unique-id="{{part.uniqueId}}"
                            value="{{part.value}}"
                            bindinput="bindFillBlankBlur"
                            bindfocus="bindFillBlankFocus"
                            bindblur="bindFillBlankBlur"
                            maxlength="10"
                          />
                        </block>
                      </block>
                      <image class="mast-select" src="/resources/images/icon-mi.png" wx:if="{{question.required == 1}}"></image>
                    </view>
                    
                    <!-- 附件上传 -->
                    <view wx:if="{{question.fileFlg == '1'}}" class="file-upload-section">
                      <view class="file-upload-title">
                        <text class="file-upload-title-text">{{question.fileUploadDescribe || '请上传相关附件'}}</text>
                        <text class="file-upload-title-desc">支持上传图片、PDF等文件格式</text>
                      </view>
                      <view class="file-upload-content">
                        <!-- Per-question file preview -->
                        <block wx:for="{{question.uploadedFiles}}" wx:for-item="file" wx:key="url">
                          <!-- Image item -->
                          <view class="file-item" wx:if="{{file.type === 'image'}}">
                            <image class="img" src="{{file.url}}" bindtap="viewFile" data-url="{{file.url}}"></image>
                            <view class="delete-img-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                          <!-- PDF item -->
                          <view class="file-item pdf-item" wx:elif="{{file.type === 'pdf'}}" bindtap="viewFile" data-url="{{file.url}}">
                            <view class="pdf-icon">PDF</view>
                            <view class="pdf-name">{{file.fileName}}</view>
                            <view class="delete-btn" catchtap="deleteFile" data-url="{{file.url}}" data-title-id="{{question.titleId}}">×</view>
                          </view>
                        </block>
                        
                        <!-- Upload Button -->
                        <view wx:if="{{question.imageCount < 9}}" class="file-upload-btn file-item" bindtap="chooseFile" data-title-id="{{question.titleId}}">
                          <view class="upload-icon-plus">+</view>
                          <text class="upload-btn-text">点击上传</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </block>
              </block>
            </block>
          </view>
        </view>
      </view>
      
      <!-- 调整占位符高度为30rpx -->
      <view class="survey-bottom-placeholder" style="height: 30rpx;"></view>
    </block>
  </view>
  
  <!-- 原步骤4签署知情同意书内容已移除 -->
  <view class="consent-form-container" wx:if="{{false}}">
    <view class="consent-icon-wrapper">
      <view class="consent-icon"></view>
    </view>
    <view class="consent-title">签署知情同意书</view>
    <!-- 未签署状态 -->
    <block wx:if="{{signStatus !== '1'}}">
      <view class="consent-description">
        为保障您的权益，请在进行下一步前完成知情同意书的签署。点击下方按钮将跳转至官方小程序进行在线签署。
      </view>
      <view class="sign-consent-btn" bindtap="navigateToConsentMiniProgram">
        点击签署
      </view>
    </block>
    <!-- 已签署状态 -->
    <block wx:else>
      <view class="consent-description">
        您已成功签署知情同意书，系统将自动跳转到报告页面。
      </view>
    </block>
  </view>
  
  <!-- 步骤3: 报告出具 -->
  <view class="report-page" wx:if="{{!showSampleList && currentStep === 3}}">
    <!-- 样本编号卡片 -->
    <view class="sample-card">
      <view class="card-title">{{reportStatus === '0' ? '订单条码' : '样本条码'}}</view>
      <view class="barcode-area">
        <image 
          class="barcode-image"
          mode="widthFix" 
          src="https://wechatdev.jiahuiyiyuan.com/barcode?msg={{sampleCode}}&type=code128&mw=.60&hrp=none&t={{timestamp}}" 
        />
      </view>
      <!-- 只显示姓名和手机号 -->
      <view class="sample-info-text">{{sjzName}} {{sjzPhone}}</view>
      <view class="scan-tip">请在采样时出示此条码</view>
    </view>

    <!-- 功能按钮卡片 -->
    <view class="function-card">
      <view class="function-buttons">
        <view class="function-btn" bindtap="openBasicInfoModal">
          <view class="function-icon basic-info-icon"></view>
          <view class="function-text">基础信息</view>
        </view>
        <view class="function-btn" bindtap="handleQuestionnaireClick">
          <view class="function-icon survey-icon"></view>
          <view class="function-text">问卷详情</view>
          <!-- 未填写状态标志 -->
          <view class="status-badge" wx:if="{{questionnaireStatus !== '1'}}">未填写</view>
        </view>
        <view class="function-btn" bindtap="handleConsentClick">
          <view class="function-icon consent-icon"></view>
          <view class="function-text">知情同意书</view>
          <!-- 未签署状态标志 -->
          <view class="status-badge" wx:if="{{signStatus !== '1'}}">未签署</view>
        </view>
      </view>
    </view>

    <!-- 基础信息弹窗 -->
    <view class="basicinfo-modal-mask" wx:if="{{showBasicInfoModal}}">
      <view class="basicinfo-modal">
        <view class="basicinfo-modal-title">基础信息</view>
        <view class="basicinfo-table">
          <block wx:for="{{basicInfoTableRows}}" wx:key="label">
            <view class="basicinfo-row">
              <view class="basicinfo-label">{{item.label}}</view>
              <view class="basicinfo-value">{{item.value}}</view>
            </view>
          </block>
        </view>
        <view class="basicinfo-modal-close" bindtap="closeBasicInfoModal">关闭</view>
      </view>
    </view>

    <!-- 知情同意书弹窗 -->
    <view class="consent-modal-mask" wx:if="{{showConsentModal}}">
      <view class="consent-modal">
        <view class="consent-modal-title">知情同意书文件列表</view>
        <view class="consent-modal-list">
          <block wx:if="{{signFileList && signFileList.length > 0}}">
            <view class="consent-file-item" wx:for="{{signFileList}}" wx:key="id" bindtap="viewConsentPdf" data-url="{{item.signPdfUrl}}">
              <view class="file-icon">PDF</view>
              <view class="file-info">
                <view class="file-name">{{item.fileName || '知情同意书'}}</view>
                <view class="file-meta">
                  <text class="sign-time">签署时间：{{item.formattedDate || item.signDate}}</text>
                </view>
                <view class="file-status-tag">{{item.signStatus === '1' ? '已签署' : '未签署'}}</view>
              </view>
              <view class="file-action">查看</view>
            </view>
          </block>
          <block wx:else>
            <view class="consent-file-item empty">暂无知情同意书文件</view>
          </block>
        </view>
        <view class="consent-modal-close" bindtap="closeConsentModal">关闭</view>
      </view>
    </view>

    <!-- 进度时间线 -->
    <view class="process-timeline">
      <!-- 节点1：样本采集 -->
      <view class="process-item">
        <view class="process-dot {{reportStatus === '0' ? 'pending' : 'completed'}}">
          <text>1</text>
        </view>
        <view class="process-content">
          <view class="process-title">
            <text>样本采集</text>
            <text class="process-status">{{reportStatus === '0' ? '待进行' : '已完成'}}</text>
          </view>
          <view class="process-time">{{reportStatus !== '0' ? sampleTime : ''}}</view>
        </view>
      </view>
      <!-- 节点2：实验室收取样本 -->
      <view class="process-item">
        <view class="process-dot {{reportStatus === '2' || reportStatus === '3' ? 'completed' : 'pending'}}">
          <text>2</text>
        </view>
        <view class="process-content">
          <view class="process-title">
            <text>实验室收取样本</text>
            <text class="process-status">{{reportStatus === '2' || reportStatus === '3' ? '已完成' : '待进行'}}</text>
          </view>
          <view class="process-time">{{reportStatus === '2' || reportStatus === '3' ? receiveTime : ''}}</view>
        </view>
      </view>
      <!-- 节点3：实验分析 -->
      <view class="process-item">
        <view class="process-dot {{reportStatus === '3' ? 'completed' : 'pending'}}">
          <text>3</text>
        </view>
        <view class="process-content">
          <view class="process-title">
            <text>实验分析</text>
            <text class="process-status">{{reportStatus === '3' ? '已完成' : '待进行'}}</text>
          </view>
          <view class="process-time">{{reportStatus === '3' ? reportTime : ''}}</view>
        </view>
      </view>
      <!-- 节点4：报告出具 -->
      <view class="process-item">
        <view class="process-dot {{reportStatus === '3' ? 'completed' : 'pending'}}">
          <text>4</text>
        </view>
        <view class="process-content">
          <view class="process-title">
            <text>报告出具</text>
            <text class="process-status">{{reportStatus === '3' ? '已完成' : '待进行'}}</text>
          </view>
          <view class="process-time">{{reportStatus === '3' ? reportTime : ''}}</view>
        </view>
      </view>
    </view>

    <!-- 查看报告按钮 -->
    <view class="action-button {{reportStatus !== '3' ? 'disabled' : ''}}" bindtap="viewReport" wx:if="true" wx:class="{'disabled': reportStatus !== '3'}" disabled="{{reportStatus !== '3'}}">
      查看报告
    </view>
  </view>
  
  <!-- 底部按钮 -->
   <!-- 
  <view class="btn-container" wx:if="{{!showSampleList && currentStep < 5}}">
    <block wx:if="{{currentStep === 2}}">
      <view class="btn-group-step2">
        <view class="btn-skip skip-btn" bindtap="skipToMedical">跳过问卷，签署知情同意书</view>
        <view class="btn-next next-btn" bindtap="submitBasicInfo">前往问卷</view>
      </view>
    </block>
  </view>
-->
  <!-- 底部操作栏 -->
  <view class="bottom-bar-placeholder" wx:if="{{!showSampleList && currentStep === 2}}" style="height: 30rpx;"></view>
  <view class="bottom-bar" wx:if="{{!showSampleList && currentStep === 2}}">
    <!-- 步骤2的保存信息按钮 -->
    <view class="action-btn save-btn" bindtap="saveBasicInfo">保存信息</view>
  </view>
</view> 