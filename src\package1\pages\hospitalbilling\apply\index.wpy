<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { uploadFile } from "@/utils/request";
import { validator } from "@/utils/utils";
import DetailItem from "@/components/detailitem/index";
import * as Utils from "@/utils/utils";
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "出院结算申请",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    "detail-item": DetailItem
  };
  data = {
    cardTitle: "住院信息",
    billInfo: {},
    hospitalInfo: [],
    radioArray: [{ label: "否", value: 0 }, { label: "是", value: 1 }],
    expressArray: [
      { label: "顺丰标快", value: 2 },
      { label: "顺丰特快", value: 1 }
    ],
    bankCardInfo: {
      bankCardNumber: "",
      bankName: ""
    },
    invoiceMail: 0,
    addresseeInfo: {
      expressType: "",
      userName: "",
      mobile: "",
      provinceName: "",
      cityName: "",
      areaName: "",
      addressDetail: ""
    },
    hospitalDepositSlip: [],
    jzid: "",
    patCardNo: "",
    patHisNo: ""
  };
  onLoad(options) {
    this.jzid = options.jzid;
    this.patCardNo = options.patCardNo;
    this.patHisNo = options.patHisNo;
    if (options.id) {
      this.getHospitalBillInfo(options.id);
      return;
    }
    const billInfo = JSON.parse(options.billInfo);
    const newBillInfo = {
      ...billInfo,
      yjjye: Utils.formatMoney(billInfo.yjjye),
      newYjjye: billInfo.yjjye
    };
    this.billInfo = newBillInfo;
    this.hospitalInfo = [
      { label: "住院人", value: billInfo.patientName },
      { label: "身份证号码", value: billInfo.idNo },
      { label: "住院科室", value: billInfo.ryksmc }
    ];
    this.$apply();
  }
  onShow() {}

  methods = {
    postRadioChange(e) {
      let newVal = Number(e.detail.value);
      this.invoiceMail = newVal;
      // 如果选择需要邮寄，则把邮寄方式设置为标快
      if (newVal === 1) {
        this.addresseeInfo = { ...this.addresseeInfo, expressType: 2 };
      }
      this.$apply();
    },
    bindRegionChange(e) {
      const regionArray = e.detail.value;
      this.addresseeInfo = {
        ...this.addresseeInfo,
        provinceName: regionArray[0],
        cityName: regionArray[1],
        areaName: regionArray[2]
      };
    },
    bindExpressChange(e) {
      this.addresseeInfo = {
        ...this.addresseeInfo,
        expressType: e.detail.value
      };
      this.$apply();
    },
    onCardInputValue(e) {
      const { key } = e.target.dataset || {};
      this.bankCardInfo = {
        ...this.bankCardInfo,
        [key]: e.detail.value
      };
      this.$apply();
    },
    delete(url) {
      const urls = this.hospitalDepositSlip.filter(i => i !== url);
      this.hospitalDepositSlip = urls;
      this.$apply();
    },
    onAddressInputValue(e) {
      const { key } = e.target.dataset || {};
      this.addresseeInfo = {
        ...this.addresseeInfo,
        [key]: e.detail.value
      };
      this.$apply();
    },
    async onChooseImage(type, e) {
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 1,
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"]
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths[0];
        wepy.showLoading({ title: "发送中...", mask: true });
        const { code, data } = await uploadFile(
          "/api/files/uploadpic",
          tempFilePath
        );
        if (code === 0) {
          this.hospitalDepositSlip = this.hospitalDepositSlip.concat(data.url);
          this.$apply();
        } else {
          wx.showToast({
            title: "图片上传失败",
            icon: "none",
            duration: 1400
          });
        }
      }
    },
    previewImg(imgUrl) {
      wepy.previewImage({
        current: imgUrl, // 当前显示图片的http链接
        urls: this.hospitalDepositSlip // 需要预览的图片http链接列表
      });
    },
    submit() {
      const { mobile = "" } = this.addresseeInfo;
      let errorMsg = "";
      if (Object.values(this.bankCardInfo).some(v => v === "")) {
        errorMsg = "请完善住院人的银行卡信息";
      }
      if (this.hospitalDepositSlip.length <= 0) {
        errorMsg = "请拍照上传全部住院缴费押金条";
      }
      if (
        this.invoiceMail === 1 &&
        Object.values(this.addresseeInfo).some(v => v === "")
      ) {
        errorMsg = "请完善您的邮寄信息";
      }
      if (mobile && !validator.mobile(mobile).ret) {
        errorMsg = "手机号码格式有误";
      }
      if (errorMsg) {
        wx.showModal({
          title: "提示",
          content: errorMsg,
          showCancel: false,
          confirmText: "确定",
          confirmColor: "#3CC51F"
        });
        return;
      }
      this.onSaveRecord();
    }
  };

  onSaveRecord = async () => {
    const { billInfo = {} } = this;
    // console.log(this.patCardNo, "1111");
    // return;
    const params = {
      refundTotalFee: Number.parseInt(billInfo.newYjjye) || 0,
      patientId: billInfo.patientId,
      patientName: billInfo.patientName,
      patHisNo: billInfo.patHisNo ? billInfo.patHisNo : this.patHisNo,
      idType: billInfo.idType,
      idNo: billInfo.idNo,
      hospitalNo: billInfo.zyh,
      deptName: billInfo.ryksmc,
      ...this.bankCardInfo,
      bankAccountName: billInfo.patientName,
      invoiceMail: this.invoiceMail,
      hospitalDepositSlip: (this.hospitalDepositSlip || []).join(","),
      jzid: this.jzid ? this.jzid : "",
      patCardNo: this.patCardNo ? this.patCardNo : ""
    };
    if (this.invoiceMail === 1) {
      params.addresseeInfo = JSON.stringify(this.addresseeInfo);
    }
    const { data, code } = await Api.onSaveRecord({ ...params });
    if (code === 0) {
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/detail/index?id=${data.id}&jzid=${
          data.jzid
        }&patCardNo=${this.patCardNo}`
      });
    }
  };
  getHospitalBillInfo = async id => {
    const { data, code } = await Api.getHospitalBillInfo({ id });
    if (code === 0) {
      this.billInfo = {
        ...data,
        yjjye: Utils.formatMoney(data.refundTotalFee),
        ryksmc: data.deptName,
        zyh: data.hospitalNo,
        newYjjye: data.refundTotalFee
      };
      this.hospitalInfo = [
        { label: "住院人", value: data.patientName },
        { label: "身份证号码", value: data.idNo },
        { label: "住院科室", value: data.deptName }
      ];
      this.bankCardInfo = {
        bankCardNumber: data.bankCardNumber,
        bankName: data.bankName
      };
      this.addresseeInfo = data.addresseeInfo
        ? JSON.parse(data.addresseeInfo)
        : this.addresseeInfo;
      this.hospitalDepositSlip = data.hospitalDepositSlip.split(",");
      this.$apply();
    }
  };
}
</script>
