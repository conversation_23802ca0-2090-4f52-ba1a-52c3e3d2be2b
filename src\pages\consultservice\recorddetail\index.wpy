<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

import { TYPE_MAP, CODE_TYPE } from "@/config/constant";
import DetailStatus from "@/components/detailstatus/index";
import RefundList from "@/components/refundlist/index";
import WxsUtils from "../../../wxs/utils.wxs";
import Moment from "moment";
import * as Utils from "@/utils/utils";

import * as Api from "./api";

const NORMAL_MAP = ["S", "F"];
export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "龚斐团队咨询"
  };

  data = {
    // 订单详情
    detailData: {},
    // 顶部状态配置
    statusConfig: {},
    // 退款列表
    refundList: [],
    // 订单状态是否异常，用来确定是否需要重发
    isAbnormal: false,

    list: [],
    address: {},
    patientInfo: {},
    //快递信息
    mailNo: "",
    projectName: "",
    getpid: "",
    admissionNum: "",
    createTime: ""
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  components = {
    "detail-status": DetailStatus,
    "refund-list": RefundList
  };

  onLoad(options) {
    this.codeType = CODE_TYPE;
    this.orderDetail(options);
  }

  methods = {
    onApply() {
      this.debounce(this.getChatRoom(), 1500);
    }
  };

  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };

  //防抖
  debounce(fn, delay, immdiate = false) {
    let timer = null;
    let isInvoke = false;
    return function _debounce(...arg) {
      if (timer) clearTimeout(timer);
      if (immdiate && !isInvoke) {
        fn.apply(this, arg);
        isInvoke = true;
      } else {
        timer = setTimeout(() => {
          fn.apply(this, arg);
          isInvoke = false;
        }, delay);
      }
    };
  }

  async getChatRoom() {
    wepy.navigateTo({
      url: `/pages/consult/chat/index?groupId=${
        this.admissionNum
      }&vipStartDate=${this.createTime.slice(0, 10)}&type=5`
    });
  }

  async orderDetail(item = {}) {
    let extFieldsViews = {};
    const { orderId = "" } = item;
    const { code, data = {}, msg } = await Api.orderDetail({ orderId });
    if (code !== 0) {
      return;
    }
    console.log("data11110000", data);
    extFieldsViews = JSON.parse(data.extFieldsViews);
    this.projectName = extFieldsViews.projectName;
    this.admissionNum = data.admissionNum;
    this.getpid = extFieldsViews.pid;
    this.createTime = data.createTime;
    const { status = "" } = data;
    this.detailData = data;
    this.statusConfig = this.getStatus() || {};
    if (NORMAL_MAP.indexOf(status) === -1) {
      this.isAbnormal = true;
    }
    this.$apply();
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    const { status, refundStatus } = this.detailData;
    let stsObj = {};

    // 需要转成map
    if (status == "S") {
      stsObj = {
        statusName: "缴费成功",
        text: "您已成功购买龚斐教授团队的咨询服务套餐。"
      };
    } else if (status == "F") {
      stsObj = {
        statusName: "缴费失败",
        text:
          "您的VIP咨询开单缴费提交失败，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。"
      };
    } else if (status == "P") {
      stsObj = {
        statusName: "付款完成，调用医院支付接口中",
        text: ""
      };
    } else if (status == "H" || status == "Z") {
      stsObj = {
        statusName: "缴费异常",
        text: `操作超时，请咨询医院窗口为您处理。`
      };
    } else if (status == undefined) {
      stsObj = {
        statusName: "",
        text: ""
      };
    } else {
      stsObj = {
        statusName: "缴费异常",
        text: `操作超时，请咨询医院窗口为您处理。`
      };
    }

    return {
      ...stsObj,
      status,
      hasRefund: refundStatus == 1 || refundStatus == 2
    };
  }
}
</script>
