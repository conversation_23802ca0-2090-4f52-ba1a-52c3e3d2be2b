@import "../../../../resources/style/mixins";

page{
}

.list-head{
  font-size: 34rpx;
  color: #000;
  font-weight: bold;
  text-align: center;
  padding: 22rpx;

  .doct-num{
    color: #FC492E;
  }
}

//医生列表
.m-list{
  margin: 0 24rpx;
  &.list-all-box{
    padding-top: 0;
  }
  .list-item{
    display: block;
    margin: 24rpx 0 16rpx;
    padding: 40rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 1rpx 2rpx 0 rgba(0, 0, 0, 0.08);
  }
  .item-box{
    display: flex;
    align-items: center;
    
  }
  .item-other{
    margin-top: -10rpx;
    font-size: 28rpx;
    color:@hc-color-warn;
    padding-bottom: 20rpx;
  }
  .other-date{
    &:after{
      display: inline;
      content: '、';
    }
    &:last-child{
      &:after{
        content: '';
      }
    }
  }
  .list-item{
    &:last-child{
      border-bottom: 0;
    }
  }
  .item-hd{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 98rpx;
    height: 98rpx;
    margin-right: 30rpx;
    border-radius: 50%;
    overflow: hidden;
    text-align: center;
    image{
      vertical-align: top;
    }
  }
  .item-bd{
    flex: 1;
    overflow: hidden;
  }
  .bd-info{
    display: flex;
  }
  .info-lt{
    flex: 1;
  }
  .lt-title{
    color: @hc-color-title;
    font-size: 32rpx;
    font-weight: 600;
    .title{
      margin-left: 16rpx;
      color: rgba(0, 0, 0, 0.70);
      font-size: 28rpx;
      font-weight: normal;
    }
  }
  .lt-text{
    color: rgba(0, 0, 0, 0.40);
    font-size: 28rpx;
    margin-top: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .info-rt{
    margin-left: 20rpx;
  }
  .rt-num{
    font-size: 28rpx;
    color: @hc-color-title;
    margin-right: 10rpx;
  }
  .unit-label{
    display: inline-block;
    background-color: @hc-color-warn;
    border-radius: 100rpx;
    line-height: 40rpx;
    white-space: nowrap;
    padding: 0 12rpx;
    color: #fff;
    font-size: 28rpx;
    .label-disabled{
      
    }
  }
  .bd-extra{
    padding-top: 5rpx;
    color: #aaa;
    font-size: 24rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .list-item.no-extra{
    
    .lt-text{
      margin-top: 20rpx;
    }
    .bd-info{
      align-items: center;
    }
  }
}
