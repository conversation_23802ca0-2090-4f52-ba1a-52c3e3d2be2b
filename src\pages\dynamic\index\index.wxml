<view class="p-page">
  <block wx:if="{{typeList.length > 0}}">
    <view class="dyna-tab">
      <block wx:for="{{typeList}}" wx:key="index">
        <view class="dyna-tabitem" @tap="chooseAticle({{item}})">
          <view class="dyna-tabitem-content {{singleId === item.typeId ? 'on' : ''}}">
            <view>{{item.typeName}}</view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <empty :config.sync="emptyConfig">
    <block slot="text">未查询到任何文章</block>
  </empty>
  <block wx:if="{{artic.recordList && artic.recordList.length > 0}}">
    <view class="{{type == 'all'?'dyna-list':'dyna-noheadlist'}}">
      <block wx:for="{{artic.recordList}}" wx:key="index">
        <view class="dyna-item" @tap="navigateToInfo({{item}})">
          <view class="item-asside">
            <view class="dyna-title">{{item.title}}</view>
            <view class="info-rt">{{item.createTime}}</view>
            <!--<wxparser wx:if="{{item.contentType == 1}}" class="info-rt" rich-text="{{item.content}}" />-->
            <!--<web-view src="{{item.content}}" bindload="loadSuccess" binderror="loadError" wx:if="{{item.contentType == 2}}"></web-view>-->
          </view>
          <image wx:if="{{item.picUrl}}" mode="widthFix" src="{{item.picUrl}}" />
        </view>
      </block>
    </view>
  </block>
</view>
