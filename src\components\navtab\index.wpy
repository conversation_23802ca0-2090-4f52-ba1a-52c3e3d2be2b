<style lang="less" src="./index.less"></style>
<template lang="wxml">
  <cover-view class="wgt-navtab">
    <cover-view class="wgt-navtab-item {{type == 'home' ? 'active' : ''}}" @tap="goNext('home')">
      <cover-view class="wgt-navtab-item-icon ">
        <cover-image src="{{type == 'home' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/home-active.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/home.png'}}" />
      </cover-view>
      <cover-view class="wgt-navtab-item-name">首页</cover-view>
    </cover-view>
    <!-- <cover-view class="wgt-navtab-item {{type == 'counsel' ? 'active' : ''}}" @tap="goNext('counsel')">
      <cover-view class="wgt-navtab-item-icon">
        <cover-image src="{{type == 'counsel' ? 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/navtab/phone-active.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/icon/new/navtab/phone.png'}}" />
      </cover-view>
      <cover-view class="wgt-navtab-item-name">电话咨询</cover-view>
    </cover-view> -->
    <cover-view class="wgt-navtab-item {{type == 'microsite' ? 'active' : ''}}" @tap="goNext('microsite')">
      <cover-view class="wgt-navtab-item-icon">
        <cover-image src="{{type == 'microsite' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/hospital-pic.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/hospital.png'}}" />
      </cover-view>
      <cover-view class="wgt-navtab-item-name">精医名院</cover-view>
    </cover-view>
    <cover-view class="wgt-navtab-item {{type == 'usercenter' ? 'active' : ''}}" @tap="goNext('usercenter')">
      <cover-view class="wgt-navtab-item-icon">
        <cover-image src="{{type == 'usercenter' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/user-active.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/user.png'}}" />
      </cover-view>
      <cover-view class="wgt-navtab-item-name">我的</cover-view>
    </cover-view>
  </cover-view>
</template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';

  const urlMap = {
    'home': '/pages/home/<USER>',
    // 'microsite': '/package1/pages/microsite/home/<USER>',
    'usercenter': '/pages/usercenter/home/<USER>',
    'ask': '/package1/pages/ask/home/<USER>',
    'counsel': '/package1/pages/phonecounsel/index',
    'msg': '/pages/todo/index',
    'dynamic': "/pages/dynamic/index/index?type=all&typeId=&title=",
    'microsite': '/pages/microsite/home/<USER>'
  };
  export default class NavTab extends wepy.component {
    data = {
    };

    props = {
      type: {
        type: String,
        default: '',
      },
    };

    methods = {
      goNext(nextType){
        const { type } = this;
        if (nextType === type) {
          return;
        }
        wepy.redirectTo({
          url: urlMap[nextType] || '',
        });
      },
    };
  }
</script>
