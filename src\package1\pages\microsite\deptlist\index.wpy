<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import DeptList from '@/components/deptlist/index';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  const FunUrl = {
    'dept': '/package1/pages/microsite/deptinfo/index',
    'doctor': '/package1/pages/microsite/doctorlist/index',
  }

  export default class Department extends wepy.page {
    config = {
      navigationBarTitleText: '科室列表',
    };

    components = {
      'dept-list': DeptList,
      'empty': Empty,
    };

    onLoad(options) {
      this.funType = options.funType;
      this.deptListFull();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      emptyConfig: {
        show: true,
      },
      // 科室列表
      deptList: {},
      // 选中tab
      activeIdx: -2,
      // 显示历史记录
      showHistory: false,
      // 功能类型(科室介绍: dept, 医生介绍: doctor)
      funType: 'dept',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };

    events = {
      'deptlist-tap-dept': function (item) {
        const { deptId, deptName } = item || {};
        wepy.navigateTo({
          url: `${FunUrl[this.funType]}?${Utils.jsonToQueryString({ deptId, deptName })}`,
        });
      },
    };

    async deptListFull() {
      const { code, data = {}, msg } = await Api.deptListFull();
      if(code == 0){
        const { deptList = [] } = data;
        deptList[0].littleTitle = 'Genetic counseling and prenatal diagnostic clinics';
        deptList[1].littleTitle = 'Andrology Clinic';
        deptList[2].littleTitle = 'Reproductive clinics';
        deptList[3].littleTitle = 'Prenatal screening';
        const deptLists = data;
        this.activeIdx = 0;
        this.deptList = deptLists;
        this.emptyConfig.show = false;
        this.$apply();
      }
    }
  }
</script>
