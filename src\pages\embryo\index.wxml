<view class="embryo-content">
  <view class="embryo-content-top">
    <view class="embryo-name">{{patientInfo.patientName}}</view>
    <view class="embryo-number">病历号: {{patientInfo.patHisNo}}</view>
  </view>
  <view class="embryo-content-end" wx:if="{{embroyList.length > 0}}">
    <view class="embryo-tips">查询到有以下内容，点击可查看详情</view>
    <view class="embryo-list-content">
      <block wx:for="{{embroyList}}" wx:for-index="idx" wx:key="idx">
        <view class="embryo-list" style="background-color:{{idx % 2 === 0 ? '#f5f5f5' : '#fff'}}">
          <view class="embryo-list-name">{{item.emrname}}</view>
          <view class="embryo-list-time">{{item.emrtime}}</view>
          <view class="embryo-list-button" @tap="checkpdf({{item.emrdz}})">查看</view>
        </view>
      </block>
    </view>
  </view>
  <empty :config.sync="emptyConfig">
    <block slot="text">暂未查寻到相关内容</block>
  </empty>
</view>