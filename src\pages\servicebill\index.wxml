<view class="bill">
  <outpatient :config.sync="outpatientConfig" :patient.sync="outpatient" :emptyNotice.sync="emptyNotice"></outpatient>
  <view wx:if="{{billInfoList.length > 0}}">
    <view class="order-list">
      <block wx:for="{{billInfoList}}" wx:for-item="item" wx:for-index="index" wx:key="index">
        <view class="container">
          <view class="o-item">
            <view class="lf-check">
              <view class="con-tit">{{item.xmmc}}</view>
              <view class="min-total">¥{{WxsUtils.formatMoney(item.je,100)}}</view>
            </view>
            <view class="content">
              <view class="btn-kd" @tap="bindCreateOrder({{item}})">开单</view>
            </view>
          </view>
          <view class="content-tips" wx:if="{{item.bzxx}}">
            <view class="line"></view>
            <view>温馨提示：{{item.bzxx}}</view>
          </view>
        </view>
      </block>
    </view>
    <!-- <view class="checkrecord" @tap="getRecordList">查询自助开单记录</view> -->
  </view>
  <view wx:else>
    <empty>
      <text slot="text">你还没有任何住院信息</text>
    </empty>
  </view>
</view>