@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.m-list {
  overflow: hidden;
  .list-item {
    padding: 32rpx;
    display: flex;
    margin: 24rpx 32rpx;
    background-color: #fff;
    border-radius: 24rpx;
    flex-direction: column;

    .item-status {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.9);
      font-weight: 600;
      font-size: 28rpx;
      line-height: 42rpx;
      margin-bottom: 16rpx;
      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .item-title {
      font-weight: 600;
      font-size: 36rpx;
      line-height: 54rpx;
      color: rgba(0, 0, 0, 0.9);
    }
    .item-extra {
      font-size: 28rpx;
      line-height: 42rpx;
      color: rgba(0, 0, 0, 0.7);
      margin-top: 8rpx;
    }
  }
  .item-icon {
    width: 32rpx;
    height: 32rpx;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 16rpx;
    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-main {
    flex: 1;
    margin-left: 30rpx;
  }
  .main-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .main-txt {
    font-size: 30rpx;
    color: @hc-color-text;
    margin-top: 10rpx;
  }
  .item-extra {
    // text-align: right;
  }
  .extra-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .extra-txt {
    font-size: 30rpx;
    color: @hc-color-text;
    margin-top: 10rpx;
  }

  .list-item {
    .item-icon {
      background-color: @hc-color-warn;
    }
    &.list-item-success,
    &.list-item-lock {
      .item-icon {
        background-color: @hc-color-primary;
      }
      // .extra-tit{
      //   color:@hc-color-warn;
      // }
    }
    &.list-item-fail {
      .item-icon {
        background-color: @hc-color-error;
      }
    }
    &.list-item-cancel {
      .item-icon {
        background-color: rgba(0, 0, 0, 0.4);
      }
    }
    &.list-item-abnormal {
      .item-icon {
        background-color: @hc-color-warn;
      }
    }
  }
  .unit-label {
    margin-left: 5px;
    display: inline-block;
    font-size: 24rpx;
    padding: 0 6rpx;
    color: #fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 4rpx;
  }
}
