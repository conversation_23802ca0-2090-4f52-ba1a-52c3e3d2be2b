<template lang="wxml">
  <view class="p--agreement">
  <outpatient
    :config.sync="outpatientConfig"
    :patient.sync="outpatient"
    :emptyNotice.sync="emptyNotice"
  />

  <view class="list-box">
    <block wx:if="{{dataList.length > 0}}">
      <view class="tips">查询到您有以下知情告知书，点击可查看详情</view>

      <block wx:for="{{dataList}}" wx:key="{{index}}">
        <view class="item {{item.ztbz === STATUS.UNREAD.value ? 'unread' : ''}}">
          <view class="label">{{item.zyms}}</view>
          <view class="time">{{item.czsj}}</view>
          <view class="primary-btn status-btn" @tap="onItemClick({{item}})">
            {{STATUS_MAP[item.ztbz].label}}
          </view>
        </view>
      </block>
    </block>

    <empty wx:else>
      <text class="empty-text" slot="text">未查询到任何数据</text>
    </empty>
  </view>
  </view>
</template>

<style lang="less">
@import "../../../resources/style/mixins";

.list-box {
  margin: 10rpx @hc-page-gap 40rpx;
  padding: @hc-page-gap 0;
  border-radius: @hc-border-radius;
  background-color: #fff;

  .tips {
    margin-bottom: 30rpx;
    font-size: 28rpx;
    color: #666;
    text-align: center;
  }

  .item {
    display: flex;
    align-items: center;
    padding: 20rpx @hc-page-gap;

    &:nth-child(2n + 1) {
      background-color: rgba(0, 0, 0, 0.03);
    }

    &.unread {
      .status-btn {
        color: #ffffff;
        background-color: @hc-color-primary;
      }
    }

    .label {
      flex: 1;
    }

    .time {
      width: 200rpx;
      padding: 0 16rpx;
      font-size: 30rpx;
      line-height: 1.2;
      color: #999999;
      text-align: center;
    }

    .status-btn {
      width: 120rpx;
      padding: 10rpx 0;
      background-color: #fff;
      border: 1px solid @hc-color-primary;
      border-radius: 14rpx;
      font-size: 24rpx;
      text-align: center;
    }
  }
}

.empty-text {
  color: #666;
}
</style>

<script>
import wepy from "wepy";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import { REQUEST_QUERY } from "@/config/constant";
import { post } from "@/utils/request";

const STATUS = {
  UNREAD: { label: "点击阅读", value: "0" },
  READ: { label: "已读", value: "1" }
};

const STATUS_MAP = {
  [STATUS.UNREAD.value]: { ...STATUS.UNREAD },
  [STATUS.READ.value]: { ...STATUS.READ }
};

export default class PageAgreement extends wepy.page {
  config = {
    navigationBarTitleText: "知情告知书"
  };

  components = {
    outpatient: Outpatient,
    empty: Empty
  };

  data = {
    STATUS,
    STATUS_MAP,
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 就诊人列表
    outpatient: {},
    // 当前就诊人信息
    currentPatient: {},
    dataList: []
  };

  events = {
    "outpatient-change-user": activePatient => {
      if (activePatient) {
        this.currentPatient = { ...activePatient };
        this.outpatientConfig.infoShow = true;
        // this.getDataList();
        this.$apply();
      }
    }
  };

  methods = {
    onItemClick(item) {
      item.ztbz === STATUS.UNREAD.value
        ? this.readLetter(item)
        : this.openFile(item);
    }
  };

  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
  }

  async getDataList() {
    let params = {
      pid: this.currentPatient.patHisNo,
      grid: this.currentPatient.patCardNo
    };
    const { code, data = {}, msg } = await post(
      `/api/customize/queryNoticeSearch?_route=h${REQUEST_QUERY.hisId}`,
      params
    );
    if (code !== 0) return;
    this.dataList = data;
    this.$apply();
  }

  async readLetter(item) {
    let params = {
      pid: this.currentPatient.patHisNo,
      grid: this.currentPatient.patCardNo,
      zqgzid: item.zqgzid
    };
    const { code, data = {}, msg } = await post(
      `/api/customize/noticeReadNotify?_route=h${REQUEST_QUERY.hisId}`,
      params
    );
    if (code !== 0) return;
    this.openFile(item);
    this.getDataList();
  }

  openFile(item) {
    wepy.showLoading({ title: "加载中", mask: true });

    wx.downloadFile({
      url: item.pdfurl,
      success(res) {
        const filePath = res.tempFilePath;
        wx.openDocument({
          filePath,
          success() {
            wepy.hideLoading();
          }
        });
      }
    });
  }
}
</script>
