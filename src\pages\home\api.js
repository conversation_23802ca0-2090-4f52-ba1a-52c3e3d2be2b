import { post } from "@/utils/request";
import { REQUEST_QUERY } from "@/config/constant";

export const getFunctionList = (param) =>
  post("/api/homepage/getfunctionlist", param, false, false);

export const getZQSUnreadNotice = (param) =>
  post(`/api/customize/queryNoticeSearch?_route=h${REQUEST_QUERY.hisId}`, param, false, false);

export const getUnreadNotice = (param) =>
  post("/api/homepage/getunreadnotice", param, false, false);

export const getPatientsList = (param) =>
  post("/api/homepage/getpatientslist", param, false, false);

export const updateReadFlag = (param) =>
  post("/api/homepage/updatereadflag", param, false, false);

// 是否有未读消息
export const queryPatChat = (param) =>
  post("/api/chat/queryPatChat", param, false, false);

/**
 * 获取文章列表
 */
export const getarticles = (param) =>
  post("/api/msg/getarticlelist", param, false, false);

/**
 * 获取文章标签
 */
export const getTags = (param) =>
  post("/api/msg/getarticletypelist", param, false, false);

/**
 * 查询温度
 */
export const getTemp = (param) =>
  post("/api/questionphone/getquestionCurDate", param, false, false, false);

/**
 * 修改温度
 */
export const updateHeat = (param) =>
  post("/api/questionphone/updateCurHeat", param, false, false);

/**
 * 首页弹框提示语
 */
export const getNoteProfile = (param) =>
  post("/api/register/getNoteProfile", param);

/**
 * 专家列表
 */
export const getdocbyexpert = (param) => 
  post('/api/web/getdocbyexpert', param);

/**
 * 获取家庭成员关系
 */
export const getFamilyrelations = (param) => 
  post(`/api/customize/register/queryFamilyrelations?_route=h${REQUEST_QUERY.hisId}`, param);

/**
 * 获取健康样本订单列表
 * @param {Object} params - 请求参数
 * @param {String} params.hisId - 医院ID
 * @returns {Promise}
 */
export function getHealthOrderList(params = {}) {
  return post('/api/sample/getHealthOrderList', params);
}
