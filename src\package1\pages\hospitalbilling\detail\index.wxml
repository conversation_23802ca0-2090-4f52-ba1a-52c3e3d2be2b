<view class="p-page">
  <block wx:if="{{isVersion}}">
    <navtop :isBack.sync="isBack" :bgColor.sync="bgColor" :color.sync="color">
      <block slot="text">出院结算申请详情</block>
    </navtop>
  </block>
  <detail-status :config.sync="statusConfig">
    <block slot="title">{{statusConfig.statusName}}</block>
    <block slot="text">
      <view>
        <rich-text nodes="{{statusConfig.text}}" />
      </view>
    </block>
  </detail-status>

  <view class="card-info" wx:if="{{showExaminInfo}}">
    <detail-item-examine title='审核信息' :dataSource.sync="examineInfo" hasRadius="2" isBetween="1" />
  </view>

  <view class="card-info">
    <detail-item-inhospital title='住院信息' :dataSource.sync="hospitalInfo" hasRadius="2" isBetween="1" />
  </view>

  <view class="card-info">
    <detail-item-apply title="申请信息" :dataSource.sync="applyInfo" hasRadius="2" isBetween="1" />
  </view>

  <view class="card-info">
    <detail-item-bankinfo title="住院人银行卡信息" :dataSource.sync="bankCardInfo" hasRadius="2" isBetween="1" />
  </view>

   <view class="form-info card-info img-choose-box">
    <view class="title">上传的全部住院缴费押金条</view>
    <view class="choose-img">
      <view class="img-item" wx:for="{{hospitalDepositSlip}}" wx:for-item="item" wx:for-index="index">
        <image src="{{item}}" @tap="previewImg({{item}})" />
      </view>
    </view>
  </view>

  <view class="card-info" wx:if="{{addressInfo.length}}">
    <detail-item-address :title.sync="expressInfoTitle" :dataSource.sync="addressInfo" hasRadius="2" isBetween="1" />
  </view>

  <view wx:if="{{detailData.expressNumber}}">
    <detail-item-mailinfo title="发票邮寄信息" :dataSource.sync="mailInfo" hasRadius="2" isBetween="1" />
  </view>

   <view class="btn">
    <view @tap="reApply" wx:if="{{canReApply}}">重新申请</view>
    <view @tap="cancelRecord" wx:if="{{canCancelApply}}" class="cancel">取消申请</view>
    <view @tap="checkProgress" wx:if="{{detailData.expressNumber}}">查询发票快递进度</view>
  </view>
</view>