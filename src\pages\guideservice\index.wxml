<view class="guide-service-page">
  <scroll-view class="scroller-box" scroll-y wx:if="{{serviceList && serviceList.length }}">
    <block wx:for="{{serviceList}}" wx:for-index="i" wx:for-item="server" wx:key="i">
      <view class="guide-service-card">
        <view class="guide-service-card-head">
          <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/guideservice/{{server.bgImage}}" class="icon" />
          <view class="text bg{{i+1}}">{{server.typeName}}</view>
        </view>
        <view class="guide-service-card-list">
          <block wx:for="{{server.itemList}}" wx:key="index">
            <view class="guide-service-card-list-item bg{{i+1}}" @tap="navigateTo" data-url="{{item.url}}">
              {{item.name}}
            </view>
          </block>
        </view>
      </view>
    </block>
  </scroll-view>
  <empty wx:else>
    <text slot="text">你还没有任何导诊服务</text>
  </empty>
</view>