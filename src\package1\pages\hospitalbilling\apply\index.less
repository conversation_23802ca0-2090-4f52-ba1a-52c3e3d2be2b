@import '../../../../resources/style/mixins';
.p-page {
  width: 100%;
  height: 100%;

  .refund-info {
    width: 100%;
    text-align: center;
    margin: 58rpx 0 40rpx 0;
    > view {
      &:first-child {
        font-size: 32rpx;
        color: #000000;
      }
      &:last-child {
        color: #ff8a00;
        font-size: 64rpx;
        margin-top: 16rpx;
      }
    }
  }

  .tips {
    background: rgba(255, 176, 64, 0.1);
    border-radius: 24rpx;
    padding: 16rpx 24rpx;
    margin: 24rpx 32rpx;
    color: #ff8a00;
    font-size: 28rpx;
  }

  .form-info {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 0 32rpx;
    padding: 32rpx 32rpx 8rpx;
    .title {
      color: rgba(0, 0, 0, 0.9);
    }
    .form-item {
      display: flex;
      flex-direction: row;
      padding: 24rpx 0;

      &:not(:last-child) {
        box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.06);
      }

      .label {
        flex: 0 0 208rpx;
      }
    }

    .form-between {
      display: flex;
      justify-content: space-between;
    }

    .choose-img {
      display: flex;
      flex-wrap: wrap;
      margin-top: 32rpx;
      .img-item {
        width: 196rpx;
        height: 196rpx;
        margin-bottom: 24rpx;
        position: relative;
        margin-right: 16rpx;
        &:nth-child(3n + 3) {
          margin-right: -16rpx;
        }
        > image {
          width: 100%;
          height: 100%;
        }
        .delete {
          position: absolute;
          top: 18rpx;
          right: 18rpx;
          width: 28rpx;
          height: 28rpx;
        }
      }
    }

    .radio-group {
      display: flex;
      align-items: center;
      justify-items: flex-end;
      .radio-label {
        display: flex;
        align-items: center;
        justify-items: flex-end;
        margin-left: 52rpx;
        .radio-item {
          transform-origin: 0 30%;
          transform: scale(0.7);
        }
      }
    }
  }
  .picker {
    position: relative;
    .item-arrow {
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid #c7c7cc;
      border-bottom: 5rpx solid #c7c7cc;
      -webkit-transform: translateX(-32rpx) rotate(-45deg);
      transform: translateX(-32rpx) rotate(-45deg);
      position: absolute;
      right: 0;
      top: 8rpx;
    }
  }

  .input-placeholder {
    color: rgba(0, 0, 0, 0.4);
  }

  .img-choose-box {
    margin-top: 32rpx;
  }

  .btn {
    padding: 56rpx 32rpx 48rpx;
    > view {
      background-color: #3eceb6;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      border-radius: 16rpx;
      color: #fff;
      font-weight: 600;
      font-size: 34rpx;
    }
  }
}
