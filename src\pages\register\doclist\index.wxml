<view class="p-page">
  <!--<view class="top-tips">{{note}}</view>-->
  <view class="m-info">
    <!-- <view class="info-date {{tabIndex === 1 ? 'info-date_vsb' : ''}}">日期:{{selectedDay}}</view> -->
    <view class="info-tab">
      <view
        class="tab-li {{tabIndex === 0 ? 'active' : ''}}"
        @tap="bindChangeTabIndex({{0}})"
      >按日期预约
      </view>
      <view
        class="tab-li {{tabIndex === 1 ? 'active' : ''}}"
        @tap="bindChangeTabIndex({{1}})"
      >按医生预约
      </view>
    </view>
    <!--选中全部医生且之前日期为展开状态，应当隐藏收缩按钮-->
    <block wx:if="{{tabIndex === 0}}">
      <view
        class="unit-fold {{expandDate ? 'active' : ''}}"
        @tap="bindChangeExpandDate({{false}})"
      ><image class="rotate-img" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/date-arrow.png"></image></view>
    </block>
  </view>

  <!-- 搜索医生 -->
  <!--<view class="m-search">
    <view class="search-ipt">
      <view class="ipt-icon"></view>
      <input
        class="ipt"
        placeholder="搜索医生名称"
        @focus="bindSearchFocus"
        @input="bindSearchInput"
        value="{{searchValue}}"
      />
    </view>
    <view class="extra-cancel" wx:if="{{searchFocus}}" @tap="bindSearchBlur">取消</view>
  </view>-->

  <!--按日期挂号-->
  <view class="date-container" hidden="{{tabIndex === 1}}">

    <block wx:if="{{scheduleList.length > 0}}">
      <!--日期列表，横向滚动模式-->
      <view class="m-date-scroll {{expandDate ? '' : 'active'}}">
        <view class="date-box">
          <view class="date">
                  <!-- 第一行 -->
            <view class="date-row">
              <block wx:for="{{scheduleListFirstWeek}}" wx:key="index">
                <view 
                  class="date-item {{item.status == 1 ? 'on' : ''}} {{selectedDay === '' ? (item.selected === true ? 'active' : '') : (selectedDay == item.scheduleDate ? 'active' : '')}}"
                  @tap="bindChangeDate({{item}})"
                >
                  <view class="item-week">周{{item.weekDate}}</view>
                  <view class="item-box">
                    <view class="item-day">{{item.monthDay}}</view>
                    <view class="item-status {{item.status == 1 ? 'on' : ''}}">{{item.status == 1 ? '有号' : '暂未排号'}}</view>
                  </view>
                </view>
              </block>
            </view>
            <!-- 第二行 -->
            <view class="date-row">
              <block wx:for="{{scheduleListSecondWeek}}" wx:key="index">
                <view 
                  class="date-item {{item.status == 1 ? 'on' : ''}} {{selectedDay === '' ? (item.selected === true ? 'active' : '') : (selectedDay == item.scheduleDate ? 'active' : '')}}"
                  @tap="bindChangeDate({{item}})"
                >
                  <view class="item-week">周{{item.weekDate}}</view>
                  <view class="item-box">
                    <view class="item-day">{{item.monthDay}}</view>
                    <view class="item-status {{item.status == 1 ? 'on' : ''}}">{{item.status == 1 ? '有号' : '暂未排号'}}</view>
                  </view>
                </view>
              </block>
            </view>
          </view>
        </view>
      </view>

      <!--日期列表，日历展开模式-->
      <view class="m-date {{expandDate ? 'active' : ''}}">
        <view class="date-box">
          <view class="week">
            <view class="week-item">周一</view>
            <view class="week-item">周二</view>
            <view class="week-item">周三</view>
            <view class="week-item">周四</view>
            <view class="week-item">周五</view>
            <view class="week-item">周六</view>
            <view class="week-item">周日</view>
          </view>
          <view class="date">
            <block
              wx:for="{{nullDateArr}}"
              wx:key="index"
            >
              <view class="date-item">
                <view class="item-box"></view>
              </view>
            </block>
            <block
              wx:for="{{scheduleList}}"
              wx:key="index"
            >
              <view
                class="date-item {{item.status == 1 ? 'on' : ''}} {{selectedDay === false ? (item.selected === true ? 'active' : '') : (selectedDay == item.scheduleDate ? 'active' : '')}}"
                @tap="bindChangeDate({{item}})"
              >
                <view class="item-box">
                  <view class="item-day">{{item.monthDay}}</view>
                  <view class="item-status {{item.status == 1 ? 'on' : ''}}">{{item.status == 1 ? '有号' : '无号'}}</view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
    <!--按日期医生列表-->
    <block wx:if="{{scheduleDoctorList.doctorList.length > 0}}">
      <view class="m-list">
        <!-- <view class="doc-tips">当前科室出诊医生<text class="num">{{scheduleDoctorList.visitNum}}</text>人，可挂号医生<text class="num">{{scheduleDoctorList.registerNum}}</text>人。</view> -->

        <block wx:for="{{scheduleDoctorList.doctorList}}" wx:key="index">

          <view class="list-item" @tap="bindGoDocInfoWithDate({{item}})">
            <view class="item-box">
              <view class="item-hd">
                <image mode="widthFix" src="{{item.doctorImg || (docDetail.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}" alt="" />
              </view>
              <view class="item-bd">
                <view class="bd-info">
                  <view class="info-top">
                    <view class="lt-title">
                      <text class="title-name">{{item.doctorName}}</text>
                      <text class="title-doctor">{{item.doctorTitle}}</text>
                    </view>
                    <view class="rt-title">
                      <text class="list-num">余号: <text class="num">{{item.leftSource}}</text></text>
                      <text class="has-num">¥{{item.registerFee/100}}</text>
                    </view>
                    
                  </view>
                  <view class="info-ft">
                    <!--<view class="lt-text">{{item.doctorTitle}}</view>
                    <block wx:if="{{options && options.deptType === '1'}}">
                      <view class="lt-text doctorRemark">看诊地点：<text class="{{item.areaName === '麓谷总院' ? 'lugu' : 'kaifu'}}">{{item.areaName}}</text></view>
                    </block>
                    <block wx:else>
                      <view class="lt-text doctorRemark">看诊地点：<text class="lugu">互联网</text></view>
                    </block>-->
                    <view class="bd-extra">{{item.doctorSkill || '暂无'}}</view>
                  </view>
                </view>
              </view>
              <!-- <view class="rt-arrow"></view> -->
            </view>
          </view>
          <!--<view wx:else class="list-item" @tap="showGuide({{item}})">
            <view class="item-box">
              <view class="item-hd">
                <image mode="widthFix" src="{{item.doctorImg || (docDetail.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}" alt="" />
              </view>
              <view class="item-bd">
                <view class="bd-info">
                  <view class="info-top">
                    <view class="lt-title">{{item.doctorName}}</view>
                    <text class="has-num" wx:if="{{item.status == 1 && options.deptType != 3}}">可现场挂号预约</text>
                    <text class="has-num" wx:elif="{{item.status == 1}}">已满诊</text> 
                    <text class="has-wait" wx:if="{{item.leftSource === 0 && item.doctorLevel === 9}}" catchtap="showAlternateDialog({{item}})">申请候补</text>
                    <text class="has-num">{{item.checkName}}</text>
                    <text class="has-num disabled" wx:else>未出诊</text> 
                  </view>
                  <view class="info-ft">
                    <view class="lt-text">{{item.doctorTitle}}</view>
                    <block wx:if="{{options && options.deptType === '1'}}">
                      <view class="lt-text doctorRemark">看诊地点：<text class="{{item.areaName === '麓谷总院' ? 'lugu' : 'kaifu'}}">{{item.areaName}}</text></view>
                    </block>
                    <block wx:else>
                      <view class="lt-text doctorRemark">看诊地点：<text class="lugu">互联网</text></view>
                    </block>
                    <view class="lt-text">{{item.doctorSkill}}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>-->
        </block>
      </view>
    </block>
    <block wx:else>
      <empty>
        <block slot="text">医生暂未排此天班次</block>
      </empty>
    </block>
  </view>

  <!-- 弹窗提示 -->
  <!--<block wx:if="{{showDescModal}}">
    <view class="desc-modal-mask" @touchmove.stop="stop">
      <view class="desc-modal">
        <view class="desc-title">{{modal.title}}</view>
        <scroll-view class="desc-content" scroll-y>
          <block wx:if="{{modal.contentType == 'string'}}">
            <text>{{modal.content}}</text>
          </block>
          <block wx:elif="{{modal.contentType == 'object'}}">
            <block wx:for="{{modal.content}}" wx:key="{{index}}">
              <text class="phone" @tap="phoneCall({{item.phone}})" wx:if="{{item.type == 'phone'}}">{{item.phone}}</text>
              <text wx:else>{{item}}</text>
            </block>
          </block>
        </scroll-view>
        <view class="desc-footer">
          <view class="agree" @tap="hidden" >{{modal.okText}}</view>
        </view>
      </view>
    </view>
  </block>-->

  <!--按医生挂号-->
  <view class="list-container" hidden="{{tabIndex === 0}}">
    <block wx:if="{{doctorList.length > 0}}">
      <view class="m-list list-all-box">
        <block
          wx:for="{{doctorList}}"
          wx:key="index"
        >
          <view
            class="list-item"
            @tap="bindGoDocInfo({{item}})"
          >
            <view class="item-box {{item.scheduleList.length > 0 ? 'item-box-bd' : ''}}">
              <view class="item-hd">
                <image
                  mode="widthFix"
                  src="{{item.doctorImg || (docDetail.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}"
                  alt="" />
              </view>
              <view class="item-bd">
                <view class="bd-info">
                  <view class="info-lt">
                    <text class="lt-title">{{item.doctorName}}</text>
                    <text class="lt-text">{{item.doctorTitle}}</text>
                  </view>
                </view>
                <view class="bd-extra">{{item.doctorSkill || '暂无'}}</view>
              </view>
            </view>
            <view class="item-other" wx:if="{{item.scheduleList.length > 0}}">
              <text>可预约日期：</text>
              <block wx:if="{{item.scheduleList.length <= 0}}">
                <text>暂无</text>
              </block>
              <block
                wx:for="{{item.scheduleList}}"
                wx:for-item="itm"
                wx:for-index="idx"
                wx:key="idx"
              >
                <text class="other-date">{{itm.scheduleDate}}</text>
              </block>
            </view>
          </view>
        </block>
      </view>
    </block>
    <block wx:else>
      <empty>
        <block slot="text">医生暂未排此天班次</block>
      </empty>
    </block>
  </view>
  <!--<bottom-logo />-->
  <!-- 挂号弹窗提示 -->
  <block wx:if="{{areaShowConfirm}}">
    <view class="desc-modal-mask">
      <view class="desc-modal">
        <view class="desc-title">温馨提示</view>
        <scroll-view class="desc-content" scroll-y>
          <view class="desc-content-info">
            <text>您所选择的挂号看诊地点是：</text>
            <text class="address">{{queryParams.areaName}}</text>
            <view>确定是否继续？</view>
          </view> 
        </scroll-view>
        <view class="desc-footer">
          <view class="refuse" @tap="agree({{0}})" >取消</view>
          <view class="agree" @tap="agree({{1}})" >确认继续</view>
        </view>
      </view> 
    </view>  
  </block>
</view>

<!-- 挂号弹窗 -->
<view class="m-reg-docinfo-popup {{showConfirm ? 'active' : ''}}">
  <view class="reg-docinfo-popup-mask" @tap="bindToggleConfirm({{false}})"></view>
  <view class="reg-docinfo-popup">
    <view class="popup-form-box">
      <view class="popup-form">
        <view class="popup-item opt">
          <view class="opt-tit">
            <block wx:if="{{confirmData.patientList.length == 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-title">请点击下方加号添加就诊人</text>
            </block>
            <block wx:if="{{confirmData.patientList.length > 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-title">请选择就诊人</text>
            </block>
          </view>
        </view>
        <view class="popup-item info">
          <view class="info-hd">
            <image
              mode="widthFix"
              src="{{confirmData.doctorImg || (docDetail.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-F.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-M.png')}}"
              alt="" />
          </view>
          <view class="info-bd">
            <view class="info-bd-item">
              <view class="info-bd-text unit-color-title unit-fs-title" >{{confirmData.doctorName}}</view>
            </view>
            <view class="info-bd-item">
              <view class="info-bd-text unit-color-text unit-fs-text">{{confirmData.deptName}} | {{confirmData.doctorTitle}}</view>
            </view>
            <view class="info-bd-item">
              <view class="info-bd-text unit-color-text unit-fs-text">
                {{selectedDay}}
              </view>
            </view>
          </view>
        </view>
        <view class="popup-item opt">
          <view class="opt-tit">
            <block wx:if="{{activePatient.patCardNo}}">
              <text class="unit-color-title unit-fs-name unit-fw-title">{{activePatient.patientName}} 卡号:{{activePatient.patCardNo}}</text>
            </block>
          </view>
          <view class="opt-list" wx:if="{{!isInternet}}">
            <block
              wx:for="{{confirmData.patientList}}"
              wx:key="index"
            >
              <view
                class="opt-list-item unit-label {{activePatient.patientId === item.patientId ? 'active' : ''}}"
                @tap="bindChangePatient({{item}})"
              >
                <view class="unit-label-text">{{WxsUtils.sliceString(item.patientName,-2)}}</view>
              </view>
            </block>
            <block wx:if="{{confirmData.leftBindNum > 0}}">
              <view class="opt-list-item opt-list-item-add" @tap.stop="bindGoBindUser">+</view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view
      class="popup-close"
      @tap="bindToggleConfirm({{false}})"
    >
      <image src="https://hlwyy.zxxyyy.cn/hospital/his-miniapp/icon/common/close.png"></image>
    </view>
    <view class="popup-bottom-btn">
      <view
        class="popup-submit {{activePatient.patientId ? '' : 'disabled'}}"
        @tap="bindRegisterConfirm">确认候补
      </view>
      <view
        class="popup-cancel"
        @tap="bindToggleConfirm({{false}})">取消
      </view>
    </view>
    
  </view>
  <view class="m-top-tips">
    <i class="iconfont tips-icon">&#xe7d3;</i>
    <view class="tips-text">请先添加或选择就诊人</view>
  </view>
</view>

