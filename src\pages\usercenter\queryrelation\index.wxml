<view class="p-page">
  <view class="m-card">
    <view class="card-info">
      <view class="info-main">
        <view class="main-name">
          <view class="name">{{patientName}}</view>
          <view class="status" >登录用户</view>
        </view>
      </view>
      <view class="info-extra">就诊卡号：{{patCardNo}}</view>
    </view>
  </view>
  <block wx:if="{{cardList.length > 0}}">
    <view class="m-tiltle">
      就诊人下的其他家庭成员
    </view>
    <view wx:for="{{cardList}}" wx:for-index="idx" wx:for-item="card" wx:key="idx">
      <view class="m-query">
        <view class="m-card" @tap="toCode({{card}})" >
          <view class="card-info">
            <view class="info-main">
              <view class="main-name">
                <view class="name">{{card.patName}}</view>
                <!--<view class="status">{{card.familyMemberName}}</view>-->
              </view>
            </view>
            <view class="info-extra">证件号：{{card.idNumber}}</view>
            <view class="info-extra">手机号：{{card.telephone}}</view>
          </view>
          <view class="card-exit" wx:if="{{card.bandFlg === '1'}}">已添加</view>
          <view class="card-code" wx:else>
            <view>验证绑定</view>
          </view>
          
        </view>
        
      </view>
    </view>
  </block>
  <view class="message-msg">本院实行实名就诊，您只能绑定本人PID下的家庭成员就诊卡，且需验证相应就诊卡下绑定的手机号。感谢您的理解和支持。</view>
  <view class="afterscan-operbtnbox {{cardList && cardList.length < 3 ? 'is-ab' : '' }}">
    <button class="binduser-btn_line" @tap="toAdd">添加其他家庭成员</button>
  </view>
</view>
