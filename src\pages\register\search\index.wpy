<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import * as Utils from '@/utils/utils';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '搜索结果',
      navigationBarBackgroundColor: '#fff',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      deptList: [],
      doctorList: [],
      // 搜索内容
      searchValue: '',
      emptyConfig: {},
    };

    components = {
      'empty': Empty,
    };

    props = {
    };

    onLoad(options){
      const { word = '' } = options;
      this.search(word);
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到医生详情页
       * @param item
       */
      bindToDocInfo(item){
        const { deptId, doctorId } = item;
        const query = Utils.jsonToQueryString({ doctorId, deptId });
        wepy.redirectTo({
          url: `/pages/register/docinfo/index?${query}`,
        });
      },
      /**
       * 跳转到医生列表页
       * @param item
       */
      bindToDocList(item){
        const { deptId, deptName } = item;
        const query = Utils.jsonToQueryString({ deptName, deptId });
        wepy.redirectTo({
          url: `/pages/register/doclist/index?${query}`,
        });
      }
    };

    async search(word) {
      const { code, data = {}, msg } = await Api.search({ inputData: word });
      if (code !== 0) {
        return;
      }
      const { deptList = [], doctorList = [] }=data;
      this.deptList = deptList;
      this.doctorList = doctorList;

      this.$apply();
    }
  }
</script>
