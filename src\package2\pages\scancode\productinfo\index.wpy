<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class HisInfo extends wepy.page {
    config = {
      navigationBarTitleText: '产品介绍',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      console.log(options, '======20')
      this.introduction = wepy.getStorageSync('product-introduction');
    }
    onUnload() {
      wepy.removeStorage('product-introduction');
    }

    data = {
      emptyConfig: {
        show: true,
      },
      hisImgHeight: 0,
      hisInfo: {},
      introduction: ''
    };

    methods = {
    };
   
  }
</script>
