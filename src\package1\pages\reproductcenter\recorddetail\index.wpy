<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  import { TYPE_MAP, CODE_TYPE } from '@/config/constant';
  import DetailStatus from '@/components/detailstatus/index';
  import RefundList from '@/components/refundlist/index';
  import NavTop from '@/components/navtop/index';
  import * as Utils from '@/utils/utils';

  import BasicDetail from './com/basicDetail';
  import * as Api from './api';

  const NORMAL_MAP = ['S', 'F'];
  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '生殖中心缴费详情',
      navigationBarTextStyle: 'white',
      navigationStyle: 'custom',
    };

    data = {
      // 订单详情
      detailData: {},
      // 顶部状态配置
      statusConfig: {},
      // 退款列表
      refundList: [],
      // 订单状态是否异常，用来确定是否需要重发
      isAbnormal: false,
      // 缴费信息是否展开,默认S状态收起
      payIsExpand: true,
      // 条形码格式
      codeType: '',
      bgColor: '#3ECDB5',
      color: 'white',
      isBack: true,
      navHeight: '52rpx',
      topHeight: null,
      isVersion: false,
    };

    components = {
      'detail-status': DetailStatus,
      'refund-list': RefundList,
      'basic-detail': BasicDetail,
      'navtop': NavTop,
    };

    onLoad(options) {
      const res = wepy.getSystemInfoSync();
      this.navHeight = 44 + res.statusBarHeight + 'px';
      this.topHeight = 44 + res.statusBarHeight;
      this.searchHeight = 5 + res.statusBarHeight + 'px';
      this.codeType = CODE_TYPE;
      this.orderDetail(options);
    }

    formatMoney (moneyString = '0', mark = 100) {
      var moneyNumber = parseFloat(moneyString);
      if (typeof moneyNumber === 'number' && typeof mark === 'number') {
        return parseFloat(moneyNumber / mark).toFixed(2);
      }
      return 0;
    };

    events = {
      'set-navigationbar-color': (param) => {
        const isVersion = this.$parent.globalData.isVersion;
        this.isVersion = isVersion;
        if (!isVersion) {
          wepy.setNavigationBarColor(param);
        }
        this.bgColor = param.backgroundColor;
        this.color = param.frontColor;
        this.$apply();
      }
    }

    methods = {
      /**
       * 重发订单状态查询
       */
      bindRetryOrder(){
        this.retryOrder();
      },
    };

    // async getPageMsg(data) {
    //   this.inpatient_recorddetail_statusS = await this.$parent.getSystemTipsByKey('inpatient_recorddetail_statusS', data);
    //   this.inpatient_recorddetail_statusF = await this.$parent.getSystemTipsByKey('inpatient_recorddetail_statusF', data);
    //   this.inpatient_recorddetail_statusHZ = await this.$parent.getSystemTipsByKey('inpatient_recorddetail_statusHZ', data);
    // }

    async orderDetail(item = {}) {
      const { orderId = '' } = item;
      const { code, data = {}, msg } = await Api.orderDetail({ orderId });
      if (code !== 0) {
        return;
      }
      // await this.getPageMsg(data);
      const { status = '' } = data;
      this.detailData = data;
      this.detailData.totalRealFee = this.formatMoney(this.detailData.totalRealFee,100);
      this.refundList = data.refundList || [];
      this.statusConfig = await this.getStatus() || {};
      if (NORMAL_MAP.indexOf(status) === -1) {
        this.isAbnormal = true;
      }
      this.$apply();
    }

    /**
     * 获取订单描述文案
     */
    async getStatus() {
      const { status, refundStatus } = this.detailData;
      let stsObj = {};

      // 需要转成map
      if (status == 'S') {
        stsObj = {
          statusName: '生殖中心缴费成功',
          text: '您的生殖中心缴费成功，如需要打印发票和押金条，请到住院收费窗口凭此缴费记录打印。',
        };
      } else if (status == 'F') {
        stsObj = {
          statusName: '生殖中心缴费失败',
          text: '生殖中心缴费失败，您的退款申请已受理。',
        };
      } else if (status == 'P') {
        stsObj = {
          statusName: '付款完成，调用医院支付接口中',
          text: '',
        };
      } else if (status == 'H' || status == 'Z') {
        stsObj = {
          statusName: '生殖中心缴费异常',
          text: '操作超时，请咨询医院窗口为您处理。',
        };
      } else if (status == undefined) {
        stsObj = {
          statusName: '',
          text: '',
        };
      } else {
        stsObj = {
          statusName: '生殖中心缴费异常',
          text: '操作超时，请咨询医院窗口为您处理。',
        };
      }
      // const refundText = await this.$parent.getSystemTipsByKey('inpatient_recorddetail_refundText');
      return {
        ...stsObj,
        status,
        hasRefund: refundStatus == 1 || refundStatus == 2,
        // refundText
      };
    }

    /**
     * 重发订单状态查询
     */
    async retryOrder() {
      const { orderId = '' } = this.detailData;
      const type = 'ZYYJBJ';
      const { code } = await Api.manualNotify({
        orderId,
        bizType: TYPE_MAP[type] || 'default',
      });
      if (code !== 0) {
        return;
      }
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&time=15&from=detail`
      });
    }
  }
</script>
