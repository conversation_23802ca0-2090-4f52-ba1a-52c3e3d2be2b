<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Api from "./api";
export default class PackageDetail extends wepy.page {
  config = {
    navigationBarTitleText: "",
    navigationBarBackgroundColor: '#fff'
  };
  data = {
    projectInfoData: {},
    isChecked: false,
    holeContent: "",
    totalFee: 0,
    queryStringPrams: {},
    zxParams: {}
  };
  onLoad(options) {
    const queryString = JSON.parse(options.queryString);
    console.log("queryString1111", queryString);
    wx.setNavigationBarTitle({
      title: queryString.title
    });
    this.totalFee = queryString.totalFee;
    this.queryStringPrams = queryString;
    this.getProfile(queryString.profileKey);
  }
  onShow() {}
  methods = {
    checkboxChange(e) {
      this.isChecked = !this.isChecked;
    },
    onApply() {
      if (this.isChecked) {
        this.debounce(this.createOrder(), 1500);
      }
    }
  };
  queryZzxmcx = async params => {
    let { code, data = [], msg } = await Api.queryZzxmcx({
      grid: params.patCardNo,
      ywType: "VIPZX"
    });
    const { zfid = "", cfid = "", je = "", czlx = "" } = data[0] || {};
    console.log("data[0]", data[0]);
    if (code == 0) {
      const param = {
        zfid,
        cfid,
        je,
        cczlx: "0"
      };
      this.zxParams = param;
      this.$apply();
    }
  };
  createOrder = async () => {
    let params = this.data.queryStringPrams || "{}";
    await this.queryZzxmcx(params);
    const { code, data = {} } = await Api.createOrder({
      hisOrderNo: params.projectId,
      pid: params.pid,
      patCardNo: params.patCardNo,
      patientId: params.patientId,
      patientName: params.patientName,
      totalFee: Number(params.totalFee),
      bizType: "pay_consult",
      extFieldsViews: JSON.stringify({
        projectName: params.title,
        pid: params.pid
      }),
      ...this.zxParams
    });
    if (code !== 0) {
      return;
    }
    const { orderId = "" } = data;
    this.registerPayOrder(orderId);
  };

  /**
   * 创建支付订单
   * @param item
   */
  async registerPayOrder(orderId) {
    let bizContent;
    try {
      bizContent = JSON.stringify(this.getBizContent() || []);
    } catch (e) {
      bizContent = "[]";
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent
    });
    if (code !== 0) {
      return;
    }
    const { payOrderId = "" } = data;
    wepy.navigateTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=VIP`
    });
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent() {
    const { queryStringPrams = {} } = this || {};
    return [
      { key: "费用类型", value: "VIP咨询" },
      { key: "就诊人姓名", value: queryStringPrams.patientName || "" },
      { key: "病历号", value: queryStringPrams.pid || "" }
    ];
  }

  //防抖
  debounce(fn, delay, immdiate = false) {
    let timer = null;
    let isInvoke = false;
    return function _debounce(...arg) {
      if (timer) clearTimeout(timer);
      if (immdiate && !isInvoke) {
        fn.apply(this, arg);
        isInvoke = true;
      } else {
        timer = setTimeout(() => {
          fn.apply(this, arg);
          isInvoke = false;
        }, delay);
      }
    };
  }

  // 获取弹框提示语
  async getProfile(profileKey) {
    console.log(profileKey, "profileKey");
    const { code, data = {}, msg } = await Api.getNoteProfile({ profileKey });
    if (code == 0) {
      const contentText = data.profileValue;
      console.log(contentText, "contentText");
      this.holeContent = contentText;
      this.$apply();
    }
  }
}
</script>


