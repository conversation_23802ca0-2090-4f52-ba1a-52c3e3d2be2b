<view class="p-page page-news">
  <view class="m-tab">
    <view class="unit-tab">
      <block wx:for="{{tabList}}" wx:for-index="index" wx:for-item="item" wx:key="{{index}}">
        <view
          class="unit-tab-li tab-li {{tabIndex == item.typeId ? 'active' : ''}}"
          @tap="bindChangeTabIndex({{item.typeId}})"
        >{{item.typeName}}
        </view>
      </block>
    </view>
  </view>
    <block wx:for="{{articleList}}" wx:for-index="index" wx:for-item="item" wx:key="{{index}}">
      <navigator url="/package1/pages/microsite/news/artical/news/index?id={{item.articleId}}">
        <view class="item-box">
            <view class="list-box">
              <view class='list-title'>{{item.title}}</view>
              <view class='list-time'>{{item.createTime}}</view>
            </view>
            <view class="image-box">
              <image src="{{item.picUrl || 'REPLACE_IMG_DOMAIN/ih-miniapp/news1.png'}}" alt="医生头像" />
            </view>
          </view>
      </navigator>
    </block>
    <view wx:if="{{articleList.length <= 0}}">
      <empty :config.sync="emptyConfig">
        <block slot="text">暂未查询到相关信息</block>
      </empty>
    </view>
  </view>
</view>