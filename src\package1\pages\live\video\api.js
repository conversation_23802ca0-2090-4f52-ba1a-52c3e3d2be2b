import { post } from '@/utils/request';

/**
 * 给视频点赞
 */
export const onLike = (param) => post('/api/ehis/liveEvent/like', param);

/**
 * 视频播放计数
 */
export const onPlay = (param) => post('/api/ehis/liveEvent/play', param, false);

/**
 * 取消点赞
 */
export const onUnLike = (param) => post('/api/ehis/liveEvent/unlike', param);

/**
 * 发送评论
 */
export const onComments = (param) => post('/api/ehis/liveEvent/comment', param);

/**
 * 获取评论列表
 */
export const getList = (param) => post('/api/ehis/doctorLive/commentList', param);