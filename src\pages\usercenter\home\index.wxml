<!-- 自定义导航 -->

<scroll-view
  scroll-y="true"
  class="p-page {{searchFocus ? 'unscroll' : ''}}"
  bindscroll="bindScroll"
  >
    <nav-bar :bgColor.sync="navFlag" isBack="0" :color.sync="color">个人中心</nav-bar>
    <!-- UI调整版本 -->
    <block wx:if="{{cardNumber <= 0 || !hasLoad}}">
      <view class="ui-userinfo">
        <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/jiahui-logo.png" class="avatar1" />
        <view class="title-tips">
          <navigator url="/pages/bindcard/queryuserinfo/index?qryType=1">
            <text class="title-tips-text">登录/注册</text>
            <image class="trangle" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/trangle.png" />
          </navigator>
          <view class="title-tips-info">登录注册后，享受完整服务</view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="ui-userinfo">
        <image mode="widthFix" src="{{defaultUser.patientSex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/avatar-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/avatar-m.png'}}" class="avatar" />
        <view class="right">
          <view class="name">{{defaultUser.patientName}}</view>
          <navigator class="num" url="/pages/usercenter/userlist/index">我的就诊人（{{cardNumber}}人）</navigator>
        </view>
      </view>
    </block>
    
    <!--用户信息-->
    <view class="user-info" wx:if="{{cardNumber > 0 || hasLoad}}">
      <view class="card-box">
        <text>电子就诊卡</text>
      </view>
      <view class="user-box">
        <view class="user-box-left">
          <view class="user-name-box">
            <view class="name">{{defaultUser.patientName}}</view>
            <view class="user-box-right">登录用户</view>
          </view>
          <view class="code">就诊卡号：{{defaultUser.patCardNo}}</view>
        </view>
        <image mode="widFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/user-logo.png" />
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="ui-menu-function {{(cardNumber > 0 || hasLoad) ? '' : 'top-function'}}">
      <view class="title">在线就诊记录</view>
      <view class="menu">
        <block wx:for="{{menuFunctionList}}" wx:key="index">
            <view class="menu-item" @tap="navigateTo" data-type="{{item.type}}" data-url="{{item.url}}">
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/{{item.icon}}" class="icon" />
              <view>{{item.name}}</view>
            </view>
        </block>
      </view>
    </view>

    <!-- 链接菜单 -->
    <view class="ui-link-list ui-menu-function">
      <view class="title">其他功能</view>
      <block wx:for="{{linkList}}" wx:key="index">
        <view class="link-item" @tap="navigateTo" data-url="{{item.url}}" data-type="1">
          <view class="function-icon-box">
            <image class="icon" src="REPLACE_IMG_DOMAIN/his-miniapp/images/{{item.icon}}" />
            <view>{{item.name}}</view>
          </view>
          <view class="arron"></view>
        </view>
      </block>
    </view>
    

    <!-- UI调整版本-以前的代码版本，用于回溯 -->
  <block wx:if="{{false}}">
    <view class="m-wxinfo">
      <image src="{{userInfo.avatarUrl}}" class="m-wxicon" />
      <view class="m-nickname">{{userInfo.nickName}}</view>
    </view>

    <block wx:if="{{cardNumber > 0 && hasLoad}}">
      <navigator class="m-mycard" url="/pages/usercenter/userlist/index">我的就诊人({{cardNumber}}人)</navigator>
      <view class="m-card">
        <view class="card-info">
          <view class="info-main">
            <view class="main-name">
              <view class="name">{{defaultUser.patientName}}</view>
              <!--<view class="status">医保卡</view>-->
            </view>
          </view>
          <view class="info-extra">病历号：{{defaultUser.patHisNo}}</view>
        </view>
      </view>
    </block>

    <block wx:if="{{cardNumber <= 0 || !hasLoad}}">
      <navigator class="m-nocard" url="/pages/bindcard/index/index">
        <view class="nocard-btn">
          初次使用，请登录/注册
        </view>
      </navigator>
    </block>

    <view class="m-function">
      <view>在线就诊记录</view>
      <view class="function-list">
        <view class="list-item" @tap="navigateTo" data-url="/pages/register/recordlist/index">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
                      src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-register.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">挂号记录</view>
            </view>
          </view>
        </view>
        <view class="list-item" @tap="navigateTo" data-url="/pages/treat/recordlist/index">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
                      src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-treat.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">门诊缴费记录</view>
            </view>
          </view>
        </view>
        <!--<view class="list-item" @tap="navigateTo" data-url="/pages/inhosp/recordlist/index">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
                      src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-inhosp.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">扫码缴费记录</view>
            </view>
          </view>
        </view>-->
        <!--<view class="list-item" @tap="navigateTo" data-url="/pages/survey/surveylist/index?readOnly=1">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
              src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-takno.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">新冠肺炎调查登记记录</view>
            </view>
          </view>
        </view>-->
        <!--<view class="list-item" @tap="navigateTo" data-type="1" data-url="/pages/webview/index">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
              src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-takno.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">知情同意书记录</view>
            </view>
          </view>
        </view>-->
      </view>
    </view>

    <view class="m-function">
      <view class="function-list" @tap="navigateTo" data-url="/pages/usercenter/collect/index">
        <view class="list-item">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
                src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-collect.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">我的收藏</view>
            </view>
          </view>
        </view>
        <view class="list-item" @tap="navigateTo" data-url="/package1/pages/videorecord/recordlist/index">
          <view class="item">
            <view class="item-icon">
              <image style="width:60rpx;height:60rpx;"
              src="REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/icon-takno.png"></image>
            </view>
            <view class="item-main">
              <view class="main-title">推荐视频</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </block>

    <!--
      <view class="m-function">
        <view class="function-list">
          <view class="list-item">
            <view class="item">
              <view class="item-icon">
                <image style="width:60rpx;height:60rpx;"
                        src="REPLACE_IMG_DOMAIN/usercenter/icon-suggest.png"></image>
              </view>
              <view class="item-main">
                <view class="main-title">意见与反馈</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    -->
    <!-- <view class="ad-center">
      <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/ad/ad-center.png"></image>
      <view class="circle-box">
        <view class="min-circle active"></view>
        <view class="min-circle"></view>
      </view>
    </view> -->
</scroll-view>
 <nav-tab :type="navTabType"></nav-tab>
<!--<bottom-logo />-->
