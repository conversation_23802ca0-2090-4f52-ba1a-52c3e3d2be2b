@import "../../../resources/style/mixins";

page {}

.p-page {
  padding-bottom: 30rpx;
}

.m-code {
  display: none;
  background-color: #fff;
  padding: 30rpx;

  &.active {
    display: block;
  }

  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }

  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;

    p {
      margin: 20rpx 0;
    }
  }

  .code-img {
    margin-top: 20rpx;

    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry {
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;

  .retry-btn {
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color: @hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border: 2rpx solid @hc-color-primary;
  }
}

.m-tips {
  padding: 16px 10px 10px 10px;

  .m-tips-title {
    font-weight: 700;
    margin-right: 5px;
  }
}

.m-list {
  padding: 30rpx 0;
  margin-top: 20rpx;
  background-color: #fff;

  .list-tit {
    padding: 0 30rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .list {
    padding-top: 17rpx;
  }

  .list-item {
    padding: 17rpx 30rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;

  }

  .item-label {
    color: @hc-color-text;
    min-width: 5em;
  }

  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    text-align: right;
    overflow: hidden;
    margin-left: 20rpx;
  }

  .unit-price {
    font-size: 48rpx;
  }
}

.m-table {
  background-color: @hc-color-bg;

  .table {
    background-color: #fff;
    margin-bottom: 20rpx;
    border-top: 4rpx solid @hc-color-primary;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tb-extra {
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom: 2rpx solid #E5E5E5;
  }

  .extra-tit {
    flex: 1;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .extra-txt {
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .extra-num {
    font-size: 36rpx;
    color: @hc-color-title;
    margin-left: 20rpx;
  }

  .tb-head,
  .tb-body {}

  .tb-head {}

  .tb-body {}

  .head-tr,
  .body-tr {
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid @hc-color-border;
    padding: 0 15rpx;
  }

  .empty-tr {
    display: block;
    text-align: center;
    padding: 20rpx 0
  }

  .head-td,
  .body-td {
    .textBreak();

    &.td-1 {
      flex-basis: 20%;
    }

    &.td-2 {
      flex-basis: 40%;
    }

    &.td-center {
      text-align: center;
    }

    &.td-right {
      text-align: right;
    }
  }

  .head-td {
    padding: 15rpx;
    color: @hc-color-text;
    font-size: 28rpx;
  }

  .body-td {
    padding: 20rpx 15rpx;
    color: @hc-color-title;
    font-size: 30rpx;
  }
}

.ad-treat {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
  background-color: #fff;
  overflow: hidden;

  .ad-content {
    float: left;
    margin-top: 5rpx;
    color: @hc-color-warn;
  }

  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}

.main {
  padding: 20rpx;

  .list {
    margin-top: 20rpx;

    .title-box {
      display: flex;
      margin-bottom: 20rpx;

      image {
        display: flex;
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
      }

      .list-title {
        font-size: 32rpx;
        line-height: 50rpx;
      }
    }

    .table {
      border-top: 2rpx solid #ccc;
      border-left: 2rpx solid #ccc;
      background: #fff;

      .tr {
        display: flex;
        text-align: center;
        width: 100%;

        .th {
          font-weight: bold;
          font-size: 24rpx;
          line-height: 32rpx;
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
        }

        .td {
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.afterscan-operbtnbox {
  margin: 42rpx 40rpx;

  .binduser-btn_line {
    background: @hc-color-primary;
    color: #fff;
    border-radius: 10rpx;
    text-align: center;
    font-size: 36rpx;
    //padding: 22rpx 0;
  }
}