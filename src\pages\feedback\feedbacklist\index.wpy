<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";
import Empty from "@/components/empty/index";
import { PRIMARY_COLOR } from "@/config/constant";
import NavBar from "@/components/navbar/index";

export default class ForzenSemen extends wepy.page {
  config = {
    navigationBarTitleText: '意见反馈',
    navigationStyle: 'custom',
    usingComponents: {
      "wxparser": "plugin://wxparserPlugin/wxparser"
    }
  };
  
  components = {
    empty: Empty,
    "nav-bar": NavBar,
  };

  data = {
    emptyConfig: {
      show: true
    },
    patientInfo: {},
    feedList: [],
    navFlag: '',
    color: '#fff'
  };

  onLoad() {}
  onShow() {
    this.getPatientsList();
  }
  methods = {
    // 页面滚动修切换航条颜色
		bindScroll(e) {
			if (e.detail.scrollTop > 50) {
				this.navFlag = '#fff';
        this.color = '#000';
			} else {
				this.navFlag = 'transparent';
        this.color = '#fff';
			}
			this.$apply()
		},
    submitFeedBack() {
      wepy.navigateTo({
        url: `/pages/feedback/feedbackwrite/index?pid=${
          this.patientInfo.patHisNo
        }`
      });
    },
    goDetail(item) {
      console.log(item, "item");
      wepy.navigateTo({
        url: `/pages/feedback/feedbackdetail/index?id=${item.id}`
      });
    }
  };

  getFeedList = async () => {
    const { code, data } = await Api.getFeedList();
    if (code === 0) {
      if (data.length > 0) {
        this.emptyConfig.show = false;
        this.feedList = data;
        this.$apply();
        return;
      }
      this.feedList = data;
      this.emptyConfig.show = true;
      this.$apply();
    } else {
      this.emptyConfig.show = true;
    }
  };
  getPatientsList = async () => {
    const { code, data = {} } = await Api.getPatientsList({ isLogin: "1" });
    const { cardList = [] } = data;
    const patientInfo = cardList.length ? cardList[0] : {};
    this.patientInfo = patientInfo;
    this.$apply();
    if (patientInfo.patCardNo) {
      this.getFeedList();
      return;
    }
    wx.showModal({
      title: "提示",
      content: "您还尚未绑定任何就诊人，绑定后可继续操作。",
      showCancel: true,
      confirmText: "立即绑定",
      confirmColor: PRIMARY_COLOR,
      success: res => {
        if (res.confirm) {
          wepy.navigateTo({
            url: "/pages/bindcard/queryuserinfo/index?qryType=1"
          });
          return;
        }
        wx.navigateBack();
      }
    });
  };
}
</script>
