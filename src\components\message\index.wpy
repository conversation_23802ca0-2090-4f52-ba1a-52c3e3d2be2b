<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  export default class Message extends wepy.component {

    props = {
      message: String
    }

    components = {
    }

    onLoad(options) {
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
