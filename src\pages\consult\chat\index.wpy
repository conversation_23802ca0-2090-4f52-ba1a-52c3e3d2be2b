<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";
import { PRIMARY_COLOR } from "@/config/constant";
import { uploadFile } from "@/utils/request";
import ConfigMixin from "@/pages/configMixin";
import chatWxs from "./chat.wxs";

export default class Chat extends wepy.page {
  config = {
    navigationBarBackgroundColor: "#ffffff",
    navigationBarTextStyle: "black",
    navigationBarTitleText: "在线咨询",
    usingComponents: {
      wxparser: "plugin://wxparserPlugin/wxparser"
    }
  };
  mixins = [ConfigMixin];

  wxs = {
    chatWxs: chatWxs
  };
  data = {
    showTopTip: true,
    showTips: false, // 显示小提示
    scrollTop: 0, // 视图滚动位置
    isEvaluate: false,
    orderId: "",
    list: [],
    vipCode: "",
    msgText: null,
    isShow: false,
    isEnd: false,
    showPlus: false,
    interval: null,
    groupId: "",
    showId: "",
    image: "",
    inquirys: {},
    scrollTime: "",
    shareInquiry: null,
    isThanks: false,
    keyboardHeight: 0,
    robotMessageList: [], // 智能提问列表
    isInputFocus: false,
    currentPage: 1,
    pageCount: 1,
    hasPrevInfo: true,
    viewId: "",
    type: "",
    vipEndTimes: "",
    isLoading: false,
    isShowExpression: false,
    expressionList: (() => {
      let n = 1;
      return Array.from({ length: 43 }, () => {
        const scaleObj = {
          14: "1.3",
          16: "1.15"
        };
        const item = {
          scale: scaleObj[n] || 1,
          url: `https://hlwyy.zxxyyy.cn/hospital/his-miniapp/p242/emijo/emijo-${n++}.png`
        };
        return item;
      });
    })()
  };

  //获取提示语
  async getNoteProfile() {
    const { code, data = {}, msg } = await Api.getNoteProfile({
      profileKey: "getAlertNoteProfile_chatStart"
    });
    if (code == 0) {
      wx.showModal({
        title: "",
        content: data.profileValue || "",
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3CC51F"
      });
    }
  }

  computed = {
    scrollStyle() {
      let offsetHeight = 108;
      if (this.showTips) {
        offsetHeight += 88;
      }
      if (this.showPlus) {
        offsetHeight += 192;
      }
      return `height:calc(100vh - ${offsetHeight}rpx)`;
    }
  };
  watch = {
    list: (newList, oldList) => {
      let list = [];
      const nowDate = Utils.getFormatDate(0, "-");
      // const currentDate = new Date();
      // const dayTime = 24 * 60 * 60 * 1000;
      // let tempTime = 0;
      let nowSliceDate = "";
      for (let i = 0; i < newList.length; i++) {
        let item = newList[i];
        if (item.createTime) {
          nowSliceDate = item.createTime.substring(0, 10) || "";
        }
        if (nowDate == nowSliceDate) {
          item.time = item.createTime.substring(11, 16);
        } else {
          item.time = item.createTime.substring(0, 16);
        }
        // const date = new Date(item.createTime.replace(/-/g, "/"));
        // const weekDay = date.getDay();
        // // 与当前时间的时间差
        // const offsetTime = date.getTime() - currentDate.getTime();
        // // 是否是本周时间
        // const isCurrentWeek = offsetTime <= (weekDay + 1) * dayTime;
        // // 与上个时间的时间差
        // const lastOffsetTime = date.getTime() - tempTime;
        // // 间隔是否有5分钟
        // if(!tempTime || lastOffsetTime > 5 * 60 * 1000){
        //   tempTime = date.getTime();
        //   if (isCurrentWeek) {
        //     const timeHour = item.createTime.substring(11, 16);
        //     if(offsetTime <= 1 * dayTime){ // 当天
        //       item.time = timeHour
        //     }else if( offsetTime <= 2* dayTime){ // 昨天
        //       item.time = '昨天 '+ timeHour
        //     }else{ // 本周其它天
        //       item.time = '星期 ' + timeHour
        //       item.time = item.createTime;
        //     }
        //   }else{
        //     item.time = item.createTime;
        //   }
        // } else {
        //   item.time = '';
        // }
        list.push(item);
      }
      this.list = list;
      this.$apply();
    }
  };
  methods = {
    toHealthProfile() {
      wepy.navigateTo({
        url: `/package1/pages/healthprofile/profile/index?patientId=${771}&patHisNo=${151192}&patCardNo=${**********}`
      });
    },
    closeTopTip() {
      this.showTopTip = false;
    },
    openModal() {
      this.showPlus = false;
      this.isShow = true;
    },
    cancel() {
      this.isShow = false;
    },
    sure() {
      // 确认结束咨询
      this.isShow = false;
      this.closure();
    },
    showPlus() {
      this.showPlus = !this.showPlus;
      this.isShowExpression = false;
    },
    hidePlus() {
      this.showPlus = false;
      this.isShowExpression = false;
    },
    inputMsg(e) {
      const value = e.detail.value || "";
      this.msgText = e.detail.value;
      console.log("test", value.length);
      if (value.length > 0 && value.length <= 10) {
        // 查询智能提问
        // this.queryChatRobotList(value);
        this.debounceFunc();
      }
    },
    sendMsg(e) {
      if (e.detail.value) {
        this.msgText = e.detail.value;
      }
      if (!this.msgText || this.msgText == null) {
        wepy.showToast({
          title: "请先输入回复信息",
          icon: "none",
          duration: 800
        });
        return;
      }
      this.send({
        groupId: this.groupId,
        operator: "user",
        content: Utils.utf16toEntities(this.msgText),
        type: 1
      });
      this.$apply();
    },
    async callCamera() {
      // 调用相机
      const ctx = wepy.createCameraContext();
      ctx.takePhoto({
        quality: "high",
        success: res => {
          this.setData({
            src: res.tempImagePath
          });
        }
      });
    },
    showExpression() {
      // 表情
      this.showPlus = false;
      this.isShowExpression = true;
    },
    sendExpression(item) {
      // 发送表情
      if (!item) {
        return false;
      }
      this.showPlus = false;
      this.isShowExpression = false;
      this.send({
        type: 5,
        operator: "user",
        groupId: this.groupId,
        content: item || "",
        token: ""
      });
    },
    async picture(type, e) {
      let sourceType = ["album", "camera"];
      console.log("type", type);
      if (type === 2) {
        sourceType = sourceType.slice(1);
      }
      const chooseRes = await wepy.chooseImage({
        count: 1,
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType
      });
      if (chooseRes.errMsg == "chooseImage:ok") {
        const tempFilePath = chooseRes.tempFilePaths[0];
        console.log("tempFilePath", tempFilePath);
        wepy.showLoading({ title: "发送中...", mask: true });
        // const uploadRes = await wepy.uploadFile({
        //   url: Utils.enclosure('/api/files/uploadpic?serviceType=idcardocr'),
        //   filePath: tempFilePath,
        //   name: 'upfile',
        // });

        const uploadRes = await uploadFile(
          "/api/files/uploadpic",
          // '/hisServiceNew',
          tempFilePath
        );

        const data = uploadRes || JSON.parse(uploadRes);
        wepy.hideLoading();
        if (data.code == 0) {
          this.send({
            type: 3,
            operator: "user",
            groupId: this.groupId,
            content: data.data.url || "",
            token: data.data.token || ""
          });
        } else {
          await wepy.showToast({
            title: data.msg || "",
            icon: "none",
            duration: 2000
          });
        }
      }
    },
    ToMenu() {
      wepy.navigateTo({
        url: `/pages/consultservice/vipchatRecordlist/index?vipCode=${
          this.vipCode
        }`
      });
    },
    previewImg(e) {
      const arr = [];
      this.list.map(item => {
        if (item.type == 3 && item.content) {
          arr.push(item.content);
        }
      });
      const imgUrl = e.currentTarget.dataset.preurl;
      wepy.previewImage({
        current: imgUrl, // 当前显示图片的http链接
        urls: arr // 需要预览的图片http链接列表
      });
    },
    longtap(id) {
      console.log("tap", id);
      if (!this.isEnd) {
        this.showId = id;
      }
      // this.showId = id;
    },
    hidden() {
      this.showId = "";
    },
    copy(data) {
      wepy.setClipboardData({ data });
      this.showId = "";
    },
    async delOrRe(item, type) {
      const { code } = await Api.change({
        groupId: this.groupId,
        operator: "user",
        messageId: item.id,
        operType: type
      });
      if (code == 0) {
        this.revokeInfo([item]);
      }
    },
    focusTest(e) {
      console.log(e.detail.height);
      // const px2rpx = 750 / wepy.getSystemInfoSync().screenWidth;
      this.keyboardHeight = e.detail.height || 2;
      this.isInputFocus = true;
      //   .exec();
    },
    blurTest() {
      this.keyboardHeight = 0;
      this.isInputFocus = false;
      this.robotMessageList = [];
    },
    scrollTop() {
      // 滚动到顶部加载会话
      if (this.hasPrevInfo) {
        console.log("test, top");
        this.getChat("prev");
      }
    },
    scroll() {
      // 滚动时保存时间
      this.scrollTime = new Date().getTime();
    },
    sendRobotMessage(item) {
      // 发送智能信息
      this.msgText = "";
      this.send({
        groupId: this.groupId,
        operator: "user",
        content: item.questionContent,
        chatRobotId: item.chatRobotId,
        type: 1
      });
      // 关闭键盘
      this.keyboardHeight = 0;
      this.isInputFocus = false;
      this.robotMessageList = [];
    },
    queryVideoStatus() {
      // 查看视频状态
      this.getVideoInfo(true);
    },
    toRegister() {
      const scheduleDate = Utils.getFormatDate(0, "-");
      const { deptId = "", doctorId = "" } = this.$wxpage.options || {};
      wepy.reLaunch({
        url: `/pages/register/docinfo/index?deptId=${deptId}&doctorId=${doctorId}&scheduleDate=${scheduleDate}`
      });
    }
  };

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.status != 3 && options.type !== "5") {
      this.getNoteProfile();
    }
    // this.getNowTime(options.vipStartDate);
    // console.log("22222222", this.getNowTime());
    if(options.vipEndDate){
    this.setvipEndTimes(options.vipEndDate);
    }
    this.getTime(options);
    this.vipCode = options.vipCode;
    this.groupId = options.groupId;
    this.type = options.type;
    this.isEvaluate = options.status != "2";
    this.isEnd = options.status == "2" || options.status == "3";
    this.getChat();
    this.getVideoInfo();
    this.setTitle(options);
    setTimeout(() => {
      this.showTips = false;
    }, 15000);
  }

  setvipEndTimes(value) {
    const vipEndTimes = value
      .slice(0, 10)
      .replace(/-/, "年")
      .replace(/-/, "日");
    this.vipEndTimes = vipEndTimes;
  }
  setTitle(options) {
    if (!options.type) {
      return;
    }
    const titleObj = {
      1: "单独咨询",
      2: "家庭组咨询",
    };
    wx.setNavigationBarTitle({
      title: titleObj[options.type] || "在线咨询"
    });
  }
  onShow() {
    this.debounceFunc = this.debounce(this.queryChatRobotList.bind(this), 300);
    this.chatInterval = setInterval(() => this.getChat("next", true), 1000 * 2);
    this.videoInterval = setInterval(() => this.getVideoInfo(), 1000 * 3);
  }
  onHide() {
    clearInterval(this.chatInterval);
    clearInterval(this.videoInterval);
  }
  onUnload() {
    clearInterval(this.chatInterval);
    clearInterval(this.videoInterval);
    // wepy.switchTab({
    //   url: "/pages/inquiry/inquirylist/inquirylist"
    // });
  }
  //获取当前时间
  getNowTime(value) {
    let dd = new Date();
    dd.setDate(dd.getDate()); //获取AddDayCount天后的日期
    let yyyy = dd.getFullYear();
    let mm =
      dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1; //获取当前月份的日期，不足10补0
    let cc = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
    const currentDate = yyyy + "-" + mm + "-" + cc;
    console.log("11111111111", new Date(value), new Date(currentDate));
    if (new Date(value).getTime() === new Date(currentDate).getTime()) {
      wx.showModal({
        title: "温馨提示",
        content:
          "感谢您选择龚斐团队全病程健康管理咨询服务，我们是龚教授团队医疗助理，在服务期限内我们将为您提供全程连续健康管理服务，服务内容包括：一对一专属问答咨询、精准规划全流程诊疗，主动治疗/健康指导、动态管理随访提醒、院内诊疗预约。您可以在此聊天界面与我们留言沟通，我们持续关注您的动态治疗情况。祝您好孕。温馨提示：团队助理咨询回复时间：周一至周日8：00-12:00，14:00-17:00。我院病友专享服务热线：0731-84372980 【周一至周日07:30-20:00】遗传咨询热线：13975129070 非工作时间/急诊热线：0731-82355406",
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3CC51F"
      });
    }
  }
  getTime(options) {
    if (options.type !== "5") {
      return;
    }
    const date = new Date();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const times = String(this.addZero(hour)) + String(this.addZero(minute));
    if (
      Number(times) <= 800 ||
      (1200 <= Number(times) && Number(times) <= 1400) ||
      Number(times) >= 1700
    ) {
      wx.showModal({
        title: "温馨提示",
        content:
          "尊敬的病友：您好！目前为非工作时间，团队助理将在正常上班时间尽快回复。如果您有紧急情况，建议您尽快来我院或就近就诊或拨打非工作时间/急诊热线：0731-82355406。感谢您的理解和配合，祝您好孕！",
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3CC51F"
      });
    } else {
      this.getNowTime(options.vipStartDate);
    }
  }
  addZero(s) {
    return s < 10 ? `0${s}` : s;
  }
  async getVideoInfo(action) {
    // 查询视频状态
    let { code, data = {} } = await Api.getVideoStatus({
      groupId: this.groupId
    });
    if (code === 0 && data && data.status === "live") {
      // 跳转视频
      clearInterval(this.videoInterval);
      if (!action) {
        // 间隔器查询 弹窗询问
        const showModalRes = await wepy.showModal({
          title: "提示",
          content: "医生发起视频问诊，是否进入问诊",
          showCancel: true,
          confirmText: "确定",
          confirmColor: PRIMARY_COLOR
        });
        if (!showModalRes.confirm) {
          return;
        }
      }
      wepy.navigateTo({
        url: `/pages/consult/video/index?groupId=${this.groupId}&from=consult`
      });
    } else {
      if (action) {
        // 手动点击消息查询状态
        wepy.showModal({
          title: "温馨提示",
          content: "当前医生未开启视频问诊",
          showCancel: false
        });
      }
    }
  }
  async getChat(type = "", isScrollToBottom) {
    // 查询聊天
    if (this.isLoading) {
      return;
    }
    let list = this.list || [];
    const param = {};
    if (type === "next") {
      if (list[list.length - 1] && list[list.length - 1].createTime) {
        param.endTime = list[list.length - 1].createTime;
      }
    } else if (type === "prev") {
      if (list[0] && list[0].createTime) {
        param.startTime = list[0].createTime;
      }
    }
    this.isLoading = true;
    let { code, data = {} } = await Api.getChat({
      groupId: this.groupId,
      operator: "user",
      ...param
    });
    this.isLoading = false;
    if (code == 0) {
      // this.image = data.image;
      data.recordList = (data.recordList || []).reverse().map(item => {
        // 获取显示时间 当天仅显示时间 非当天显示日期 非本年显示年份
        item.time = Utils.getChatShowTime(item.createTime || "");
        // 新增
        // if (item.content.indexOf("<p>") != -1 || item.content.indexOf("</p>") != -1) {
        //   item.content = item.content.split("<p>")[1].split("</p>")[0];
        //   item.content =  item.content.replace(/</g, '小于');
        //   item.content =  item.content.replace(/>/g, '大于');
        // }
        return item;
      });
      let firstItemId;
      if (type === "next" || !type) {
        // 往下查询或初次查询
        list = list.concat(data.recordList);
      } else if (type === "prev") {
        if (list.length > 0) {
          // 查询时的第一个元素
          firstItemId = (list[0] || {}).id;
        }
        if (data.recordList.length < 15) {
          this.hasPrevInfo = false;
        }
        list = data.recordList.concat(list);
      }
      this.list = list;
      if (data.recordList.length > 0 && list.length > 0) {
        // 滚动到的元素id
        console.log("a:", type, isScrollToBottom);
        setTimeout(() => {
          this.viewId = `id-${
            !type || isScrollToBottom
              ? (list[list.length - 1] || {}).id
              : firstItemId
          }`;
          this.$apply();
        }, 500);
      } else if (type === "next") {
        // 滚动后停留时间超过20s
        if (new Date().getTime() - this.scrollTime >= 20 * 1000) {
          this.viewId = `id-${
            !type || isScrollToBottom
              ? (list[list.length - 1] || {}).id
              : firstItemId
          }`;
        }
      }
      if (data.revokeChatList && data.revokeChatList.length > 0) {
        this.revokeTimeout = setTimeout(() => {
          this.revokeInfo(data.revokeChatList);
        }, 100);
      }
      this.$apply();
    }
  }
  async send(param) {
    // 发送消息
    // wepy.showLoading({ title: "发送中...", mask: true });
    const chatlist = await Api.sendMsg(param);
    // wepy.hideLoading();
    if (chatlist.code == 0) {
      this.msgText = "";
      this.getChat("next", true);
      this.$apply();
    }
  }
  async queryChatRobotList() {
    // 智能提问列表关键字索引
    const content = Utils.utf16toEntities(this.msgText);
    if (!content) {
      return;
    }
    const param = { content };
    const data = await Api.queryChatRobotList(param);
    if (data.code == 0 && data.data.length > 0) {
      const list = data.data.map(item => {
        const regExp = new RegExp(`(${this.msgText})`, "g");
        item.lightContent = item.questionContent.replace(
          regExp,
          '<font color="#3ECDB5">$1</font>'
        );
        return item;
      });
      this.robotMessageList = list;
    } else {
      this.robotMessageList = [];
    }
    this.$apply();
  }
  debounce(fn, delay) {
    let timer = null;
    return () => {
      let args = arguments;
      let context = this;

      if (timer) {
        clearTimeout(timer);

        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      } else {
        timer = setTimeout(() => {
          // fn.apply(context, args);
          fn();
        }, delay);
      }
    };
  }
  async closure(param) {
    // 结束咨询
    const closure = await Api.closure({
      groupId: this.groupId,
      operator: "user"
    });
    if (closure.code == 0) {
      this.getChat("next");
      this.isEvaluate = false;
    }
  }
  revokeInfo(arr = []) {
    // 撤回消息
    const list = this.list;
    if (arr.length === 0 || list.length === 0) {
      return;
    }
    const idList = arr.map(item => item.id);
    list.forEach((item, index) => {
      const idx = idList.indexOf(item.id);
      if (idx != -1) {
        const currentItem = arr[idx] || {};
        currentItem.isRevoke = true;
        const { createTime } = currentItem;
        const time = Utils.getChatShowTime(createTime);
        list.splice(index, 1, currentItem);
        // list.push(currentItem);
      }
    });
    this.list = list;
  }
}
</script>