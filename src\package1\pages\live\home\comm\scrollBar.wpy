<template>
  <view class="comm-scroll-bar">
    <view class="out-item {{active == -1 && 'active-item'}}" @tap="onChange(-1)">全部</view>
    <scroll-view
      scroll-x
      class="scroll"
      scroll-with-animation
      scroll-into-view="scroll-{{srcollIds[active] || active}}"
    >
      <view class="scroll-item-box">
        <block wx:for="{{bars}}" wx:key="{{index}}">
          <view
            class="scroll-item {{active == index && 'active-item'}}"
            id="scroll-{{1 + index}}"
            @tap="onChange('{{index}}')"
          >{{item}}</view>
        </block>
        <view class="out-item scroll-item"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
// id 从1开始，scroll-into-view 总是指向选中的前一个，用户体验良好
import wepy from 'wepy';
export default class ScrollBar extends wepy.component {
  props = {
    bars: {
      type: Array,
      default: []
    },
    active: {
      type: Number,
      default: -1,
      twoWay: true
    }
  };

  data = {
    srcollIds: {
      '-1': 1,
      0: 1
    }
  };
  methods = {
    onChange(active) {
      this.active = active;
    }
  };
}
</script>

<style lang='less'>
@import '../../../../../resources/style/mixins.less';
.comm-scroll-bar {
  position: sticky;
  z-index: 99999;
  top: 0;
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: 54rpx;
  padding-top: 20rpx;
  padding-bottom: 26rpx;
  color: #666666;
  font-size: 32rpx;
  background: #ffffff;
  .out-item {
    min-width: 64rpx;
    padding: 0 27rpx;
  }
  .active-item {
    font-size: 40rpx;
    color: @hc-color-primary;
    min-width: 80rpx;
  }
  .scroll {
    flex: 1;
    .scroll-item-box {
      // width: 100%;
      white-space: nowrap;
      .scroll-item {
        display: inline-block;
        padding: 0 27rpx;
      }
    }
  }
}
</style>