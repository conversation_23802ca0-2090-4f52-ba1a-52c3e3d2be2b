<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import { jsonToQueryString } from '@/utils/utils';
  import * as Api from './api';

  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '',
    };
 
    onLoad(options){
      const q = jsonToQueryString(options);
      if(options.subType == 'videoList'){
        wepy.redirectTo({ url: `/package1/pages/videorecord/recordlist/index?pid=${options.pid || ''}&grid=${options.grid || ''}` });
      } else if (options.subType == 'live'){
        wepy.redirectTo({ url: `/package1/pages/live/home/<USER>
      } else if (options.subType == 'ksjf'){
        wepy.redirectTo({ url: `/package1/pages/paymerge/index?${q}` });
      } else {
        wepy.redirectTo({ url: "/package1/pages/bchao/index" });
      }
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      weburl: '',
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
