@import '../../../../resources/style/mixins';

.p-page {
  height: 100vh;
  overflow: auto;
  background-color: #F6F7F9;

  .list-wrap {
    padding: 0 32rpx;
  }

  .list-tips {
    margin-bottom: 18rpx;
    font-weight: 600;
    font-size: 28rpx;
    line-height: 42rpx;
  }

  .item {
    display: flex;
    align-items: center;
    padding: 32rpx;
    border-radius: 24rpx;
    background-color: #ffffff;

    +.item {
      margin-top: 32rpx;
    }

    .left-row {
      flex: 1;
    }

    .img-arrow-right {
      width: 20rpx;
      height: 20rpx;
    }

    .row {
      display: flex;
      align-items: center;


      +.row {
        margin-top: 16rpx;
      }
    }

    .title {
      font-weight: 400;
      font-size: 30rpx;
      line-height: 44rpx;
      color: rgba(0, 0, 0, 0.7);
    }

    .value {
      font-weight: 600;
      font-size: 38rpx;
      line-height: 54rpx;
      color: rgba(0, 0, 0, 0.9);
    }

    .tips {
      font-weight: 600;
      font-size: 28rpx;
      line-height: 44rpx;
      color: rgba(0, 0, 0, 0.4);
    }

    .left {
      width: 58%;

      &.gap {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          right: 60rpx;
          z-index: 1;
          width: 3rpx;
          height: 40rpx;
          background-color: rgba(0, 0, 0, 0.2);
          transform: translateY(-50%);
        }
      }
    }
  }

  .btn {
    position: fixed;
    bottom: 48rpx;
    width: 100%;

    >view {
      margin: 0 32rpx;
      background-color: #3eceb6;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      border-radius: 16rpx;
      color: #fff;
      font-weight: 600;
      font-size: 34rpx;
    }
  }
}