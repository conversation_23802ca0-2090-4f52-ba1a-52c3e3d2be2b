<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class HisInfo extends wepy.page {
    config = {
      navigationBarTitleText: '医生主页',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.deptName = options.deptName;
      this.docSex = options.sex || 'M';
      this.getDoctorInfo(options);
    }

    data = {
      emptyConfig: {
        show: true,
      },
      docDetail: {},
      deptName: '',
      docSex: ''
    };

    methods = {};
    /**
     * 获取医生信息
     */
    async getDoctorInfo(param){
      const { data = {}, code } = await Api.getDoctorInfo(param);
      if(code == 0){
        this.docDetail = data;
        this.emptyConfig.show = false;
        this.$apply();
      }
    }
  }
</script>
