@import "../../../../resources/style/mixins";

page {
  width: 100%;
  height: 100%;
  background-color: @hc-color-bg;
  overflow-y: hidden;
}

.p-page {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: hidden;
}

.g-main {
  position: relative;
  height: 82vh;
  z-index: 1;
  background-color: @hc-color-bg;
  border-radius: 32rpx;
  top: -24rpx;
  overflow-y: auto;
}
.m-banner {
  height: 340rpx;
  .banner {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
  }
  .banner-image {
    width: 100%;
    height: 508rpx;
  }
}
.m-logo {
  position: relative;
  z-index: 1;
  height: 136rpx;
  position: relative;
  overflow: visible;

  .clip-bg {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    -webkit-clip-path: polygon(0 0, 100% 60%, 100% 100%, 0 100%);
    background-color: #fff;
  }

  .logo {
    position: absolute;
    width: 148rpx;
    height: 148rpx;
    border-radius: 50%;
    left: 30rpx;
    top: -74rpx;
    overflow: hidden;
  }
  .logo-img {
    width: 148rpx;
    height: 148rpx;
  }
}
.m-info {
  position: relative;
  z-index: 1;
  padding: 0 30rpx;
  top: 32rpx;
  margin-bottom: 50rpx;
  .info-name {
    font-size: 36rpx;
    color: @hc-color-title;
    position: relative;
    display: flex;

    .logo {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 106rpx;
      height: 106rpx;
      border-radius: 50%;
      margin-right: 24rpx;
      background: rgba(53, 174, 255, 0.09);
      border: 2rpx solid rgba(152, 197, 230, 0.17);
    }
    .logo-img {
      width: 84rpx;
    }

    .m-hisname {
      font-weight: bold;
      font-size: 40rpx;
    }

    .rt-icon {
      position: relative;
      font-size: 56rpx;
      line-height: 1;
      margin-top: 5rpx;
      float: right;

      &:last-child {
        margin-right: 20rpx;
      }

      &.wx {
        color: #43a53b;
      }
      &.zfb {
        color: #0097e5;
      }
    }
  }
  .unit-tag {
    display: inline-block;
    vertical-align: middle;
    font-size: 26rpx;
    border-radius: 100rpx;
    height: 40rpx;
    padding: 0 20rpx;
    line-height: 40rpx;
    text-align: center;
    background: rgba(62, 206, 182, 0.1);
    border-radius: 59rpx;
    color: #3eceb6;
    font-size: 20rpx;
  }
  .icon-qrcode {
    position: absolute;
    right: 0;
    width: 36rpx;
    height: 36rpx;
    top: 50%;
    margin-top: -18rpx;
  }
  .info-detail {
    margin: 20rpx 0;
  }
  .detail-item {
    display: flex;
    align-items: center;
    // border-top: 2rpx solid @hc-color-border;
    padding: 16rpx 0;
  }
  .item-label {
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.4);
  }
  .item-text {
    flex: 1;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.9);
  }
  .item-icon {
    margin-left: 20rpx;

    .icon-phone {
      width: 30rpx;
      height: 32rpx;
    }
    .icon-address {
      width: 26rpx;
      height: 34rpx;
    }
  }
}
.m-media-box {
  padding: 0 30rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
}
.m-media {
  display: block;
  position: relative;
  z-index: 1;
  padding-bottom: 50rpx;
  margin-top: 20rpx;
  color: @hc-color-title;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;

  .media-hd {
    display: flex;
    padding: 30rpx 30rpx 30rpx 0;
    align-items: center;
    position: relative;

    &:after {
      content: " ";
      position: absolute;
      right: 10rpx;
      top: 50%;
      width: 17rpx;
      height: 17rpx;
      border-right: 5rpx solid #c7c7cc;
      border-bottom: 5rpx solid #c7c7cc;
      transform: translateY(-50%) rotate(-45deg);
    }
  }
  .hd-tit {
    flex: 1;
    font-size: 36rpx;
    color: #333333;
    font-weight: bold;
  }
  .hd-extra {
    font-size: 24rpx;
    color: #3986ff;
  }
  .media-bd {
    font-size: 30rpx;
    color: @hc-color-text;
    &.article {
      overflow: hidden;
      height: 120rpx;
    }
  }
}
.m-list {
  padding-bottom: 20rpx;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
  .list-hd {
    padding: 30rpx;
    font-size: 36rpx;
    color: #333333;
    font-weight: bold;
  }
  .list-bd {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .list-item {
    flex-basis: 25%;
    overflow: hidden;
    padding-top: 15rpx;
    padding-bottom: 20rpx;
    text-align: center;
  }
  .item-icon {
    .fn-icon {
      width: 80rpx;
      height: 80rpx;
    }
  }
  .item-tit {
    text-align: center;
    font-size: 24rpx;
    color: @hc-color-title;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.c-list {
  padding-bottom: 20rpx;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
  .list-hd {
    padding: 30rpx;
    font-size: 36rpx;
    color: #333333;
    font-weight: bold;
  }
  .list-bd {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 0 32rpx;
  }

  .list-item {
    flex: 1;
    box-sizing: border-box;
    border-radius: 16rpx;
    line-height: 100rpx;
    position: relative;
    background-image: url(REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/menu-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100rpx;
  }
  .list-item + .list-item {
    margin-left: 14rpx;
  }
  .item-icon {
    position: absolute;
    height: 100rpx;
    width: 100%;

    .fn-icon {
      // width: 100rpx;
      height: 100rpx;
    }
  }
  .item-tit {
    text-align: center;
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.ad-microsite {
  padding: 20rpx 0;
  overflow: hidden;
  .ad-content {
    color: @hc-color-warn;
    float: left;
    font-size: 32rpx;
  }
  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}
