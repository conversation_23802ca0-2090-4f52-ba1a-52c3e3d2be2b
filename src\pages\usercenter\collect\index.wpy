<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class DoctorList extends wepy.page {
    config = {
      navigationBarTitleText: '我的收藏',
       navigationBarBackgroundColor: '#fff',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.getCollectList();
    }

    data = {
      emptyConfig: {
        show: true,
      },
      doctorList: [],
      deptId: '',
    };

    methods = {
      toDoctorIntro(item = {}) {
        const { doctorId = '', deptId = '' } = item;
        const query = Utils.jsonToQueryString({ deptId, doctorId });
        wepy.navigateTo({
          url: `/pages/register/docinfo/index?${query}`,
        });
      },
    };
    /**
     * 获取医院信息
     */
    async getCollectList(){
      const { data = [], code } = await Api.getCollectList();
      if(code == 0){
        if(data.length > 0){
          this.doctorList = data;
          this.emptyConfig.show = false;
          this.$apply();
        }
      }
    }
  }
</script>
