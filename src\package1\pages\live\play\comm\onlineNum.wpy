<!--  -->
<template>
  <cover-view class="comm-online-num">
    <cover-image class="icon-person" src="REPLACE_EHOSPITAL_DOMAIN/fill.png"></cover-image>
    <cover-view>{{count}}</cover-view>
  </cover-view>
</template>

<script>
import wepy from 'wepy';
export default class OnlineNum extends wepy.component {
  props = {
    count: {
      type: Number,
      default: 0
    }
  };
}
</script>

<style lang='less'>
@import '../../../../../resources/style/mixins.less';
.comm-online-num {
  padding: 10rpx 30rpx;
  position: absolute;
  top: 170rpx;
  right: 30rpx;
  background: #b8b9ba;
  color: #ffffff;
  z-index: 99;
  border-radius: 33rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  .icon-person {
    height: 24rpx;
    width: 24rpx;
    line-height: 24rpx;
    margin-right: 10rpx;
  }
}
</style>