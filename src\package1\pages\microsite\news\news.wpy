<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./news.wxml"></template>

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import ConfigMixin from '@/pages/configMixin';

  import * as Api from './api';
  export default class News extends wepy.page {
    config = {
      navigationBarTitleText: '新闻公告',
    };
    components = {
      'empty': Empty,
    };
    mixins = [ConfigMixin];
    data = {
      tabIndex: '',
      tabList: [],
      emptyConfig: {
        show: true
      },
      articles: [],
    };
    methods = {
      bindChangeTabIndex(index){
        if (this.tabIndex === index) {
          return;
        }
        this.tabIndex = index;
        this.getarticles(index);
      }
    };
    async getArticleTags() {
      const { data, code } = await Api.getTags({contentType: 'hospital_bulletin'});
      this.tabList = data;
      if (data.length > 0) {
        this.tabIndex = data[0].id;
        this.getarticles(data[0].id);
      }
      this.$apply();
    }
    async getarticles(tagId){
      const { data = {}, code } = await Api.getarticles({contentType: 'hospital_bulletin', tagId});
      if(code == 0){
        this.articles = data;
        this.$apply();
      }
    }
    onLoad(options){
      this.tabIndex = options.tabIndex;
      this.getArticleTags();
    }
    onShareAppMessage() {
      return {
        title: `${this._config.name}`,
        path: `/pages/microweb/news/index?tabIndex=${this.tabIndex}`
      }
    }
  }
</script>
