// config mixins
import wepy from 'wepy';
import { post } from '@/utils/request';

export default class LiveDetailMixin extends wepy.mixin {
  data = {
    liveDetail: {},
    doctorInfo: {},
    issubscribe: 0,   // 是否关注了该直播
    isLike: false,    // 是否点赞了该视频
  };
  onLoad(options = {}) {
    const { id } = options;
    this.mixinGetLiveDetail(id);
  }
  methods = {};
  async mixinGetLiveDetail(id, showLoading = true) {
    const { code, data } = await post(
      '/api/ehis/doctorLive/doctorLive',
      { id },
      showLoading
    );
    if (code == 0) {
      this.liveDetail = data.doctorLive || {};
      const doctorInfo = data.doctor || {};
      this.doctorInfo = {
        ...doctorInfo,
        doctorName: doctorInfo.name,
        doctorImage: doctorInfo.image
      };
      this.issubscribe = data.issubscribe || 0;
      this.isLike = data.islike == 0 ? false : true;
    }
    this.$apply();
    return Promise.resolve();
  }
  /**
   * 计算目标时间和当前时间的时间戳的差值
   * @param {*} number
   */
  mixinTimeDiffWithNow(date) {
    const nowDate = new Date();
    const targetDate = new Date(date);
    return targetDate.getTime() - nowDate.getTime();
  }
}
