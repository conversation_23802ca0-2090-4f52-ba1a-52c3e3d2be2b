import wepy from 'wepy';
import * as Utils from './utils';
import { DOMAIN, REQUEST_QUERY } from '@/config/constant';

/**
 * 登录授权封装
 * @returns {*}
 */
export const login = async (noAuthOn999, reqData = {}) => {
  wepy.loginingStatus = 'pending';
  if (!reqData.code) {
    // 调用登录接口
    let loginRes;
    try {
      loginRes = await wepy.login();
    } catch (e) {
      loginRes = e;
    }
  
    if (loginRes.errMsg !== 'login:ok') {
      // 调用登录接口失败
      console.log('loginRes_fail:', loginRes);
      wepy.loginingStatus = 'resolved';
      return { errMsg: 'fail' };
    }
    reqData.code = loginRes.code;
  }

  // 获取用户基本信息
  // const getUserInfoRes = await Utils.getUserInfo();
  // if (!getUserInfoRes.userInfo) {
  //   if (noAuthOn999) {
  //     wepy.loginingStatus = '';
  //     throw '用户未授权';
  //   }
  //   // 获取用户基本信息失败
  //   console.log('getUserInfoRes_fail:', getUserInfoRes);

  //   const _currentPages = getCurrentPages();
  //   const { route = '', options = {} } = _currentPages[_currentPages.length - 1];
  //   const url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
  //   await wepy.redirectTo({
  //     url: `/pages/auth/getuserinfo/index?url=${url}`,
  //   });

  //   wepy.loginingStatus = 'rejected';
  //   return { errMsg: 'fail' };
  // }

  // 请求后端，登录授权
  const res = await post('/api/xcxcore/login', reqData);
  if (res.code !== 0) {
    // 登录失败
    console.log('res_fail', res);
    wepy.loginingStatus = 'resolved';
    return { errMsg: 'fail' };
  }
  const { login_access_token } = res.data || {};
  wepy.setStorageSync('login_access_token', login_access_token);
  wepy.loginingStatus = 'resolved';
  return { errMsg: 'ok' };
};

// 请求loading队列
const postLoadingQueue = [];
// 约定 wepy.loginingStatus 有三个状态 pending（授权中）、resolved（授权完成，可能为成功或失败）、rejected（授权拒绝，getuserinfo接口授权失败）
/**
 * 项目内post请求封装
 * @param url 请求链接，不带域名
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const post = async (url, options = {}, showLoading = true, showError = true, goAuth = true) => {
  if (wepy.loginingStatus == 'rejected') {
    return {};
  }
  if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
    await Utils.sleep(200);
    return await post(url, options);
  }

  if (showLoading) {
    postLoadingQueue.push('');
    wepy.showLoading({
      title: '加载中',
      mask: true,
    });
  }

  const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
  const login_access_token = wepy.getStorageSync('login_access_token') || '';
  const queryUrl = `${DOMAIN}${url}${(url || '').indexOf('?') >= 0 ? '&' : '?'}${queryStr}`;

  let data = {};
  let response = {};
  try {
    response = await wepy.request({
      url: queryUrl,
      data: { ...options, login_access_token },
      method: 'POST',
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
    console.log(e);
  }
  if (showLoading) {
    postLoadingQueue.pop();
    if (postLoadingQueue.length <= 0) {
      wepy.hideLoading();
    }
  }

  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
    if (data.code === 999 && goAuth) {
      // 尚未授权，其他接口触发授权登录后重新请求数据
      if (wepy.loginingStatus == 'rejected') {
        return {};
      }
      if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
        await Utils.sleep(200);
        return await post(url, options);
      }

      const loginRes = await login(options.noAuthOn999);
      if (loginRes.errMsg !== 'ok') {
        // 授权失败
        data = {
          code: 10001,
          msg: '授权失败，请稍候重试',
        };
        return data;
      }
      return await post(url, options);
    } else if (data.code === 998) {
      if (!wepy.HAS_ERROR_998) {
        wepy.HAS_ERROR_998 = true;

        const _currentPages = getCurrentPages();
        const { route = '', options = {} } = _currentPages[_currentPages.length - 1];
        const url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
        await wepy.redirectTo({
          url: `/pages/auth/developing/index?url=${url}`,
        });
      }
    }
  } else {
    // 请求失败
    data = {
      code: 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }
  if (showError && data.code !== 0 && data.code !== 998 && data.code !== 999 && data.code !== 10000) {
    await wepy.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false,
      confirmColor: '#30A1A6',
    });
  }
  return data;
};

/**
 * 项目内get请求封装
 * @param url 请求链接，不带域名
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const get = async (url, options = {}, showLoading = true, showError = true, goAuth = true) => {
  if (wepy.loginingStatus == 'rejected') {
    return {};
  }
  if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
    await Utils.sleep(200);
    return await post(url, options);
  }

  if (showLoading) {
    postLoadingQueue.push('');
    wepy.showLoading({
      title: '加载中',
      mask: true,
    });
  }

  const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
  const login_access_token = wepy.getStorageSync('login_access_token') || '';
  const queryUrl = `${DOMAIN}${url}${(url || '').indexOf('?') >= 0 ? '&' : '?'}${queryStr}`;

  let data = {};
  let response = {};
  try {
    response = await wepy.request({
      url: queryUrl,
      data: { ...options, login_access_token },
      method: 'GET',
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
    console.log(e);
  }
  postLoadingQueue.pop();
  if (postLoadingQueue.length <= 0) {
    wepy.hideLoading();
  }

  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
    if (data.code === 999 && goAuth) {
      // 尚未授权，其他接口触发授权登录后重新请求数据
      if (wepy.loginingStatus == 'rejected') {
        return {};
      }
      if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
        await Utils.sleep(200);
        return await post(url, options);
      }

      const loginRes = await login(options.noAuthOn999);
      if (loginRes.errMsg !== 'ok') {
        // 授权失败
        data = {
          code: 10001,
          msg: '授权失败，请稍候重试',
        };
        return data;
      }
      return await post(url, options);
    } else if (data.code === 998) {
      if (!wepy.HAS_ERROR_998) {
        wepy.HAS_ERROR_998 = true;

        const _currentPages = getCurrentPages();
        const { route = '', options = {} } = _currentPages[_currentPages.length - 1];
        const url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
        await wepy.redirectTo({
          url: `/pages/auth/developing/index?url=${url}`,
        });
      }
    }
  } else {
    // 请求失败
    data = {
      code: 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }
  if (showError && data.code !== 0 && data.code !== 998 && data.code !== 999 && data.code !== 10000) {
    await wepy.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false,
    });
  }
  return data;
};

/**
 * 第三方链接post请求封装
 * @param url 请求链接，为完整链接
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const thirdPost = async (url = '', options = {}, showLoading = true, showError = true) => {
  if (showLoading) {
    wepy.showLoading({
      title: '加载中',
      mask: true,
    });
  }
  let data = {};
  let response = {};
  try {
    response = await wepy.request({
      url,
      data: options,
      method: 'POST',
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
    console.log(e);
  }
  wepy.hideLoading();
  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
  } else {
    // 请求失败
    data = {
      code: response.statusCode || 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }
  if (showError && data.code !== 0) {
    await wepy.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false,
    });
  }
  return data;
};

export const uploadFile = async (url, filePath = '', showLoading = true, showError = true) => {
  if (showLoading) {
    // postLoadingQueue.push('');
    wepy.showLoading({
      title: '加载中',
      mask: true,
    });
  }
  console.log(filePath,' =======334')

  const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
  const login_access_token = wepy.getStorageSync('login_access_token') || '';
  // const queryUrl = `${DOMAIN}${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`;
  const queryUrl = `${DOMAIN}${url}`;

  let data = {};
  let response = {};
  try {
    response = await wepy.uploadFile({
      url: queryUrl,
      filePath: filePath,
      name: 'upfile',
      formData: { serviceType: 'idcardocr', isPte: 'false' },
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
    console.log(e);
  }
  wepy.hideLoading();
  // if (postLoadingQueue.length <= 0) {
  //   wepy.hideLoading();
  // }

  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = JSON.parse(response.data || '{}');
    if (data.code === 999) {
      // 尚未授权，其他接口触发授权登录后重新请求数据
      if (wepy.loginingStatus == 'rejected') {
        return {};
      }
      if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
        await Utils.sleep(200);
        return await post(url, options);
      }

      const loginRes = await login();

      if (loginRes.errMsg !== 'ok') {
        // 授权失败
        data = {
          code: 10001,
          msg: '授权失败，请稍候重试',
        };
        return data;
      }
      return await post(url, options);
    } else if (data.code === 998) {
      if (!wepy.HAS_ERROR_998) {
        wepy.HAS_ERROR_998 = true;

        const _currentPages = getCurrentPages();
        const { route = '', options = {} } = _currentPages[_currentPages.length - 1];
        const url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
        await wepy.redirectTo({
          url: `/pages/auth/developing/index?url=${url}`,
        });
      }
    }
  } else {
    // 请求失败
    data = {
      code: response.statusCode || 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }

  if (showError && data.code !== 0 && data.code !== 998 && data.code !== 999) {
    await wepy.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false,
    });
  }
  return data;
};

/**
 * 项目内post请求封装 表单提交形式
 * @param url 请求链接，不带域名
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const postByJson = async (url, options = {}, showLoading = true, showError = true) => {
  if (wepy.loginingStatus == 'rejected') {
    return {};
  }
  if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
    await Utils.sleep(200);
    return await post(url, options);
  }

  if (showLoading) {
    postLoadingQueue.push('');
    wepy.showLoading({
      title: '加载中',
      mask: true,
    });
  }

  const login_access_token = wepy.getStorageSync('login_access_token') || '';
  REQUEST_QUERY.login_access_token = login_access_token;
  const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
  const queryUrl = `${DOMAIN}${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`;

  let data = {};
  let response = {};
  try {
    response = await wepy.request({
      url: queryUrl,
      // formData: { ...options, login_access_token },
      data: { ...options, login_access_token, hisId: REQUEST_QUERY.hisId },
      method: 'POST',
      header: {
        "content-type": "application/json",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
    console.log(e);
  }
  postLoadingQueue.pop();
  if (postLoadingQueue.length <= 0) {
    wepy.hideLoading();
  }

  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
    if (data.code === 999) {
      // 尚未授权，其他接口触发授权登录后重新请求数据
      if (wepy.loginingStatus == 'rejected') {
        return {};
      }
      if (wepy.loginingStatus == 'pending' && url !== '/api/xcxcore/login') {
        await Utils.sleep(200);
        return await post(url, options);
      }

      const loginRes = await login();

      if (loginRes.errMsg !== 'ok') {
        // 授权失败
        data = {
          code: 10001,
          msg: '授权失败，请稍候重试',
        };
        // const { confirm = false } = await wepy.showModal({
        //   title: '提示',
        //   content: data.msg,
        //   showCancel: false,
        //   confirmText: '重新加载',
        // });
        // if (confirm) {
        //   wepy.reLaunch();
        // }
        return data;
      }
      return await post(url, options);
    } else if (data.code === 998) {
      if (!wepy.HAS_ERROR_998) {
        wepy.HAS_ERROR_998 = true;

        const _currentPages = getCurrentPages();
        const { route = '', options = {} } = _currentPages[_currentPages.length - 1];
        const url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
        await wepy.redirectTo({
          url: `/pages/auth/developing/index?url=${url}`,
        });
      }
    }
  } else {
    // 请求失败
    data = {
      code: response.statusCode || 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }

  if (showError && data.code !== 0 && data.code !== 998 && data.code !== 999) {
    await wepy.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false,
    });
  }
  return data;
};