<style lang="less">

</style>

<template lang="wxml">
  <view class="m-list">
    <folding :isExpand.sync="isExpand">
      <block slot="title">缴费详情</block>
      <block slot="content">
        <view class="list">
          <view class="list-item" wx:if="{{detailData.totalRealFee}}">
            <view class="item-label">交易金额</view>
            <view class="item-value">
              <view class="unit-price3">￥{{WxsUtils.formatMoney(detailData.totalRealFee, 100)}}</view>
            </view>
          </view>
          <view class="list-item" wx:if="{{detailData.bizTypeName}}">
            <view class="item-label">费用类型</view>
            <view class="item-value">{{detailData.bizTypeName}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.hisName}}">
            <view class="item-label">医院名称</view>
            <view class="item-value">{{detailData.hisName}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.hisOrderNo}}">
            <view class="item-label">医院单号</view>
            <view class="item-value">{{detailData.hisOrderNo}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.orderId}}">
            <view class="item-label">平台单号</view>
            <view class="item-value">{{detailData.orderId}}</view>
          </view>

          <view class="list-item" wx:if="{{detailData.agtOrdNum}}">
            <view class="item-label">支付流水号</view>
            <view class="item-value">{{detailData.agtOrdNum}}</view>
          </view>

          <view class="list-item" wx:if="{{detailData.payStatus}}">
            <view class="item-label">支付状态</view>
            <view class="item-value">{{detailData.payStatus == 1 ? '已支付' : '未支付'}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.payedTime}}">
            <view class="item-label">支付时间</view>
            <view class="item-value">{{detailData.payedTime}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.totalFees}}">
            <view class="item-label">总金额</view>
            <view class="item-value">
              <view class="unit-price3">￥{{WxsUtils.formatMoney(detailData.totalFees, 100)}}</view>
            </view>
          </view>

          <view class="list-item" wx:if="{{detailData.totalRealFees}}">
            <view class="item-label">实付金额</view>
            <view class="item-value">
              <view class="unit-price2">￥{{WxsUtils.formatMoney(detailData.totalRealFees, 100)}}</view>
            </view>
          </view>
          <view class="list-item" wx:if="{{detailData.totalPromFee!== undefined}}">
            <view class="item-label">优惠金额</view>
            <view class="item-value">
              <view class="unit-price3">￥{{WxsUtils.formatMoney(detailData.totalPromFee || 0, 100)}}</view>
            </view>
          </view>
          <view class="list-item" wx:if="{{detailData.costHeadID}}">
            <view class="item-label">医院单号</view>
            <view class="item-value">{{detailData.costHeadID}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.payStatues}}">
            <view class="item-label">支付状态</view>
            <view class="item-value">{{detailData.payStatues === 'S' ? '已支付' : '未支付'}}</view>
          </view>
          <view class="list-item" wx:if="{{detailData.costDate}}">
            <view class="item-label">支付时间</view>
            <view class="item-value">{{detailData.costDate}}</view>
          </view>
        </view>
      </block>
    </folding>
  </view>
</template>
 

<script>
  import wepy from 'wepy';
  import Folding from '@/components/folding/index';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Utils from '@/utils/utils';
  export default class PayDetail extends wepy.component {
    data = {};
    components = {
      'folding': Folding,
    };
    wxs = {
      WxsUtils: WxsUtils,
    };
    props = {
      detailData: {
        type: Object,
        default: {},
      },
      isExpand: {
        type: Boolean,
        default: true,
        twoWay: true,
      },
    };
    onLoad(options) {}
    // 交互事件，必须放methods中，如tap触发的方法
    methods = {};
  }
</script>
