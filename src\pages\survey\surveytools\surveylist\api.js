import { post } from '@/utils/request';
import { request } from '@/utils/request';

export const getList = (param) => post('/api/questionphone/getquestionlist', param);
export const getAnswerList = (param) => post('/api/questionphone/getquestionanswerlist', param);

/**
 * 获取样本问卷列表
 * @param {Object} params - 请求参数
 * @param {Number} params.page - 页码
 * @param {Number} params.pageSize - 每页数量
 * @returns {Promise}
 */
export function getSurveyList(params = {}) {
  return request({
    url: '/api/survey/list',
    method: 'GET',
    data: params
  });
}

/**
 * 删除样本问卷
 * @param {Object} data - 请求参数
 * @param {String} data.id - 问卷ID
 * @returns {Promise}
 */
export function deleteSurvey(data = {}) {
  return request({
    url: '/api/survey/delete',
    method: 'POST',
    data
  });
}

/**
 * 获取问卷详情
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @returns {Promise}
 */
export function getSurveyDetail(params = {}) {
  return request({
    url: '/api/survey/detail',
    method: 'GET',
    data: params
  });
}

/**
 * 获取健康样本订单列表
 * @param {Object} params - 请求参数
 * @param {String} params.hisId - 医院ID
 * @returns {Promise}
 */
export function getHealthOrderList(params = {}) {
  return post('/api/sample/getHealthOrderList', params);
} 