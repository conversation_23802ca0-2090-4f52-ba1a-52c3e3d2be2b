
<form bindsubmit="formSubmit" report-submit='true'>
  <view class="bindcard-list">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">住院人</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.patientName ? 'o-error' : ''}}" placeholder="请输入住院人姓名" 
          cursor-spacing="{{CURSOR_SPACING}}" placeholder-style="color:{{errorElement.patientName ? errorColor : placeholderColor}}"
          maxlength="8" id="patientName" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">身份证号</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.idNo ? 'o-error' : ''}}" type="idcard" placeholder="请输入住院人身份证"
          maxlength="18" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.idNo ? errorColor : placeholderColor}}"
          id="idNo" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">住院号</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.admissionNum ? 'o-error' : ''}}" type="text" placeholder="请输入住院人住院号"
          maxlength="30" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.admissionNum ? errorColor : placeholderColor}}"
          id="admissionNum" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
  </view>

  <view class="m-operbtnbox">
    <button class="binduser-btn_line {{hasErr ? 'o-disabled' : ''}}" formType="submit" @tap="getAuth">立即绑定</button>
  </view>
</form>
<toptip :toptip.sync="toptip" />
