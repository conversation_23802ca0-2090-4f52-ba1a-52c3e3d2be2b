@import "../../../resources/style/mixins";

.ask {
  min-height: calc(100vh - 96rpx);
  .ask-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    // height: 100rpx;
    background-color: @hc-color-white;
    margin: 24rpx 32rpx;
    border-radius: 24rpx;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.04);

    .tab-item {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      box-sizing: border-box;
      &:first-child {
        border-radius: 24rpx 0 0 24rpx;
      }
      &:last-child {
        border-radius: 0 24rpx 24rpx 0;
      }
      &.active {
        color: @hc-color-white;
        background: @hc-color-primary;
      }
      .title {
        position: relative;
        display: inline;
        font-size: 32rpx;
        .badge {
          display: block;
          position: absolute;
          top: -18rpx;
          right: -18rpx;
          min-width: 16rpx;
          padding: 0 10rpx;
          height: 36rpx;
          border-radius: 18rpx;
          line-height: 36rpx;
          background-color: @hc-color-error;
          font-size: 26rpx;
          text-align: center;
          color: @hc-color-white;
        }
      }
      .active-line {
        display: block;
        background-color: @hc-color-primary;
        width: 50%;
        height: 4rpx;
        border-radius: 2rpx;
        margin-left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .content {
    width: 100%;
    height: 100%;
    .filter {
      // background-color: @hc-color-white;
      margin-top: 20rpx;
      box-sizing: border-box;
      width: 100%;
      padding-left: 32rpx;
      font-size: 32rpx;
      display: flex;
      justify-content: space-between;
      &.center {
        justify-content: center;
      }
      .item {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 32rpx 20rpx 0;
        &.money {
          font-weight: 600;
          // border-top: 2rpx solid @hc-color-border;
          .val {
            color: @hc-color-assist;
          }
        }
        &.date {
          .rt {
            color: @hc-color-title;
            font-weight: 600;
            .arrow {
              margin-left: 16rpx;
              display: inline-block;
              width: 0;
              height: 0;
              border-left: 8rpx solid transparent;
              border-right: 8rpx solid transparent;
              border-bottom: 14rpx solid @hc-color-title;
              transform: translateY(-8rpx) rotate(60deg);
            }
          }
        }
      }
    }
    .detail-list {
      margin: 12rpx 32rpx 32rpx;
      border-radius: 24rpx;
      background-color: @hc-color-white;
      padding-bottom: 20rpx;
      overflow: auto;
      overflow-x: scroll;
      .tips {
        color: @hc-color-text;
        font-size: 24rpx;
        padding: 10rpx 30rpx;
        text-align: center;
        background-color: @hc-color-white;
      }
      .table {
        // border: 2rpx solid @hc-color-border;
        border-bottom-width: 0;
        width: 460px;
        .tr,
        .th {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-flow: row nowrap;
          border-bottom: 2rpx solid @hc-color-border;
          .td {
            display: inline-block;
            text-align: center;
            padding: 26rpx;
            // border-right: 2rpx solid @hc-color-border;
          }
          .name {
            flex: 3;
            text-align: left;
          }
          .type {
            flex: 2;
          }
          .price {
            flex: 1;
          }
          .num {
            flex: 1;
          }
          .money {
            flex: 1;
          }
        }
        .th {
          .td {
            color: @hc-color-title;
            font-weight: 600;
          }
        }
        .tr {
          .td {
            color: @hc-color-text;
          }
        }
      }
    }
  }
}

.tips {
  color: @hc-color-info;
  font-size: 24rpx;
  text-align: center;
}
