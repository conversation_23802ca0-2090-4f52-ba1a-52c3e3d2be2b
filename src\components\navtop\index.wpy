<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from 'wepy';
// import { JUMPAPPID } from '@/config/constant';
export default class NavTop extends wepy.component {
  props = {
    isBack: {
      type: Boolean,
      default: true
    },
    bgColor: {
      type: String,
      default: '#3ECDB5'
    },
    color: {
      type: String,
      default: 'white'
    },
  };
  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    navHeight: '52px',
    bgColor: '#3ECDB5',
    color: 'white'
  };
  components = {};
  onLoad(options) {
    const res = wepy.getSystemInfoSync();
    this.navHeight = 44 + res.statusBarHeight + 'px';
    this.$apply();
  }
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    backTo() {
      wepy.navigateBack({
        delta: 1
      });
    },
    goHome(){
      wepy.reLaunch({
        url: '/pages/home/<USER>'
      });
    }
  };
}
</script>