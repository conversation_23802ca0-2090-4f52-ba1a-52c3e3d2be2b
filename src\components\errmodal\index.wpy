<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  export default class ErrModal extends wepy.component {
    props = {
      config: {
        type: Object,
        default: {
          show: false,
        },
        twoWay: true,
      }
    }

    data = {
    }

    events = {}

    methods = {
      bindTapOk(){
        this.config.show = false;
      },
      bindTouchMoveMask(e){
        return false;
      }
    }
  }
</script>
