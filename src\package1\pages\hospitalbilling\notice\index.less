@import '../../../../resources/style/mixins';

.p-page {
  width: 100%;
  height: 100%;

  .title {
    font-weight: 600;
    font-size: 48rpx;
    color: #000000;
    margin-top: 58rpx;
    text-align: center;
  }

  .content {
    margin: 40rpx 40rpx 54rpx;
    font-size: 36rpx;
    color: rgba(0, 0, 0, 0.7);
    line-height: 1.5em;
    font-weight: 400;
  }

  .tip {
    padding: 0 44rpx;
    color: rgba(0, 0, 0, 0.4);
    font-size: 30rpx;
    margin-top: 200rpx;
  }

  .btn {
    position: fixed;
    bottom: 48rpx;
    width: 100%;

    .btn-disable {
      background: rgba(0, 0, 0, 0.04) !important;
      color: rgba(0, 0, 0, 0.7) !important;
    }

    >view {
      margin: 0 32rpx;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      border-radius: 16rpx;
      font-weight: 600;
      font-size: 34rpx;

      &:first-child {
        background-color: #3eceb6;
        color: #fff;
        margin-bottom: 24rpx;
      }

      &:last-child {
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.7);
      }
    }
  }
}