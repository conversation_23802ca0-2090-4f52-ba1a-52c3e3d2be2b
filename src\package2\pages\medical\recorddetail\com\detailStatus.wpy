<style lang="less">
@import "../../../../../resources/style/mixins";

.wgt-detailstatus {
  padding: 25rpx 25rpx 30rpx 25rpx;

  &.wgt-detailstatus-success {
    background: @hc-color-primary;
  }
  &.wgt-detailstatus-fail {
    background: @hc-color-error;
  }
  &.wgt-detailstatus-abnormal {
    background: @hc-color-warn;
  }
  &.wgt-detailstatus-lock {
    background: @hc-color-primary;
  }
  &.wgt-detailstatus-cancel {
    background: #989898;
  }
  &.wgt-detailstatus-wait {
    background: #ffb040;
  }
}
.wgt-detailstatus-bd {
  display: flex;
  align-items: center;
}
.wgt-detailstatus-bd-icon {
  width: 60rpx;
  height: 60rpx;
  image {
    vertical-align: top;
    width: 100%;
    height: 100%;
  }
}
.wgt-detailstatus-bd-tit {
  margin-left: 15rpx;
  font-size: 32rpx;
  color: #fff;
}
.wgt-detailstatus-bd-label {
  margin-left: 15rpx;
  display: inline-block;
  border: 2rpx solid #fff;
  color: #fff;
  font-size: 24rpx;
  border-radius: 4rpx;
  text-align: center;
  padding: 3rpx 5rpx;
  vertical-align: middle;
}
.wgt-detailstatus-bd-timer {
  flex: 1;
  color: #fff;
  font-size: 50rpx;
  text-align: right;
}
.wgt-detailstatus-ft {
  font-size: 30rpx;
  color: #fff;
  margin-top: 30rpx;
}
</style>
<template lang="wxml">
<view class="wgt-detailstatus wgt-detailstatus-{{statusClassName}}">
  <view class="wgt-detailstatus-bd">
    <view class="wgt-detailstatus-bd-icon">
      <block wx:if="{{statusClassName}}">
        <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/detail-{{statusIcon}}.png"></image>
      </block>
    </view>
    <view class="wgt-detailstatus-bd-tit">
      <slot name="title"></slot>
    </view>
    <block wx:if="{{config.hasRefund}}">
      <view class="wgt-detailstatus-bd-label">有退款</view>
    </block>
    <view class="wgt-detailstatus-bd-timer" wx:if="{{config.status === 'L'}}">{{leftTime}}</view>
  </view>
  <view class="wgt-detailstatus-ft">
    <slot name="text"></slot>
    <view wx:if="{{config.hasRefund}}">退款金额已原路返回您的支付账户，请注意查收。</view>
  </view>
</view>
</template>

<script>
import wepy from "wepy"

const STATUS_MAP = {
  1: {
    name: "success",
    icon: "success",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#3ECDB5"
    }
  },
  2: {
    name: "success",
    icon: "success",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#3ECDB5"
    }
  },
  3: {
    name: "cancel",
    icon: "cancel",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#989898"
    }
  },
  4: {
    name: "cancel",
    icon: "cancel",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#989898"
    }
  },

  5: {
    name: "success",
    icon: "success",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#3ECDB5"
    }
  },
  6: {
    name: "success",
    icon: "success",
    navigationBarColor: {
      frontColor: "#ffffff",
      backgroundColor: "#3ECDB5"
    }
  }
}

export default class DetailStatus extends wepy.component {
  props = {
    config: {
      type: Object,
      default: {
        status: ""
      },
      twoWay: true
    }
  }

  watch = {
    config(newValue) {
      this.setStatus(newValue)
    }
  }

  data = {
    statusClassName: "",
    statusIcon: ""
  }

  methods = {}

  setStatus(config = {}) {
    console.log('---config---',config);
    const { status = "" } = config
    const statusObj = STATUS_MAP[status] || {}
    this.$emit("set-navigationbar-color", statusObj.navigationBarColor) // 根据订单状态设置导航颜色
    this.statusClassName = statusObj.name || ""
    this.statusIcon = statusObj.icon || ""
    this.$apply()
  }
}
</script>
