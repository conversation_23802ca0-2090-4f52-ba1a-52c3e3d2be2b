<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import Empty from '@/components/empty/index';
import * as api from './api';

export default class RiskArea extends wepy.page {
  config = {
    navigationBarTitleText: '中高风险疫情地区',
    navigationBarBackgroundColor: '#3eceb6',
  };

  components = {
    empty: Empty,
  };

  onShow() {
    this.getRiskArea();
  }

  data = {
    emptyConfig: {
      show: true,
    },
    riskArea: {},
  };

  // 获取风险区域
  async getRiskArea() {
    this.emptyConfig.show = true;
    this.riskArea = {};

    const { code, data = {} } = await api.getRiskArea();
    if (code == 0) {
      this.riskArea = data;

      if (data && data.questend_update_timeionList) {
        this.emptyConfig.show = false;
      }

      this.$apply();
    }
  }
}
</script>
