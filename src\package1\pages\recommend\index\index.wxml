<view class="p-page {{recommendedList.length > 0 ? 'bg-color' : ''}}">
  <!-- <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient> -->
  <view class="card">
    <view class="card-item">
      <view class="item-tit">{{patientInfo.patientName}}</view>
      <view class="item-content">就诊卡号：{{patientInfo.patCardNo}}</view>
    </view>
  </view>
  <view class="m-date-box"  wx:if="{{recommendedList.length > 0}}">
    <view class="m-date-range">
      <view class="m-date-item" wx:for="{{dateTypeList}}" wx:for-index="idx" wx:key="idx">{{item.name}}</view>
    </view>
    <view class="m-tab">
      <block >
        <view class="m-tab-list" wx:for="{{recommendedList}}" wx:key="index">
          <view class="list-item">{{item.patName}}</view>
          <view class="list-item">{{item.patMobile}}</view>
          <view class="list-item">{{item.medicalCode || '-'}}</view>
          <view class="list-item">{{item.createTime}}</view>
        </view>
      </block>
    </view>
  </view>
  <view wx:else>
    <empty :config.sync="emptyConfig">
      <block slot="text">
        <view class="empty-info">暂无推荐记录，欢迎推荐病友哦~</view>
      </block>
    </empty>
  </view>

  <view class="page-btn">
    <view class="torecommend" @tap="toRecommend()">我要推荐</view>
  </view>
</view>
