{"description": "家辉医院微信小程序", "setting": {"urlCheck": false, "es6": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "ignoreUploadUnusedFiles": true, "ignoreDevUnusedFiles": false, "compileWorklet": false, "uglifyFileName": false, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "appid": "wx4e983431083e4674", "uatappid": "wx4e983431083e4674", "version": "1.4.2", "projectname": "p242", "miniprogramRoot": "dist/", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "libVersion": "3.8.5", "condition": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}