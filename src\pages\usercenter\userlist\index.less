@import "../../../resources/style/mixins";

page {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  background: @hc-color-bg;
}

.p-page {
  height: 100%;
}

.m-card {
  margin: 24rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 32rpx;
  position: relative;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);

  // &:after {
  //   content: " ";
  //   position: absolute;
  //   right: 30rpx;
  //   top: 50%;
  //   width: 17rpx;
  //   height: 17rpx;
  //   border-right: 5rpx solid #c7c7cc;
  //   border-bottom: 5rpx solid #c7c7cc;
  //   transform: translate(-8rpx, -50%) rotate(-45deg);
  // }
  .info-main {
    display: flex;
    align-items: flex-start;
    padding-left: 2rpx;
  }
  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .name {
    font-weight: 600;
    font-size: 36rpx;
    line-height: 5r4px;
    color: rgba(0, 0, 0, 0.9);
  }
  .status {
    background: rgba(45, 102, 111, 0.10);
    border-radius: 4rpx;
    margin-left: 16rpx;
    font-weight: 600;
    font-size: 20rpx;
    color: #2D666F;
    padding: 4rpx 8rpx;
  }
  .info-extra {
    margin-top: 8rpx;
    color: rgba(0, 0, 0, 0.4);
    font-size: 28rpx;
  }
}

.m-adduser {
  padding: 49rpx 37rpx 48rpx 154rpx;
  background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/common/icon-add.png")
    no-repeat 30rpx center;
  background-size: 98rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;

  &:after {
    content: " ";
    position: absolute;
    right: 30rpx;
    top: 50%;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translate(-8rpx, -50%) rotate(-45deg);
  }

  .add-title {
    font-size: 34rpx;
  }
  .add-text {
    font-size: 28rpx;
    color: @hc-color-text;
  }
}

.afterscan-operbtnbox {
  position: absolute;
  bottom: 64rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
  .binduser-btn_line {
    height: 100rpx;
    line-height: 100rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    color: #fff;
    border-radius: 76rpx;
    text-align: center;
    font-size: 34rpx;
  }
}
button::after {
  border: none;
}
