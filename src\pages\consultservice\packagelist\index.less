.package-content {
  padding: 60rpx 48rpx 48rpx 48rpx;

  .package-content-top {

    .package-name {
      font-size: 64rpx;
      font-weight: 700;
    }

    .package-number {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #808080
    }
  }

  .package-detail {
    margin-top: 40rpx;
    box-shadow: #808080;

    .package-detail-tips {
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
    }

    .package-introuce-btn {
      margin-top: 40rpx;
      text-align: center;
      padding: 16rpx 20rpx 16rpx 20rpx;
      background: rgba(62, 206, 182, 0.1);
      border-radius: 14rpx;
      color: #3ECEB6;
      font-weight: 600;
    }
  }

  .package-listcontain {
    margin-top: 40rpx;

    .package-list {
      display: flex;
      align-items: center;
      padding: 48rpx 40rpx 48rpx 40rpx;
      border-radius: 24rpx;
      color: #fff;
    }

    .mange1 {
      background: #3ECE9B;
    }

    .mange2 {
      background: #3986FF;
    }

    .package-list-name {
      font-size: 36rpx;
      margin-bottom: 10rpx;
    }
  }

  .isPay-listcontain {
    margin-top: 40rpx;

    .isPaylist {
      display: flex;
      align-items: center;
      padding: 48rpx 40rpx 48rpx 40rpx;
      border-radius: 24rpx;
      background-color: #fff;
    }

    .isPay-name {
      font-size: 34rpx;
      font-weight: 700;
      flex: 1;
    }

    .isPay-right {
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.4);

      .isPay-time {
        margin-bottom: 6rpx;
      }

      .isPay-read {
        text-align: right;
      }
    }

  }

  .package-record {
    margin-top: 80rpx;
    text-align: center;
    color: #3986FF;
    font-size: 32rpx;
    font-weight: 400;
  }

}