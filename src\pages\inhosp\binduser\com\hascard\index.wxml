<view class="m-list" wx:if="{{patientData.cardList.length > 0}}">
  <view class="list-tit">通过就诊人添加</view>
  <view class="list">
    <block wx:for="{{patientData.cardList}}" wx:key="index">
      <view class="list-item" @tap="bindChangeUser({{item}})">
        <view class="item-bd">
          <view class="bd-tit">{{item.patientName}}</view>
          <view class="bd-txt">就诊卡：{{item.patCardNo}}</view>
        </view>
        <view class="item-ft">
          <block wx:if="{{item.checked}}">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checked-circle-on.png"></image>
          </block>
          <block wx:else>
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checked-circle-off.png"></image>
          </block>
        </view>
      </view>
    </block>
  </view>
</view>
<view class="m-form">
  <view class="form-tit">通过住院号快速添加</view>
  <view class="form">
    <view class="form-item">
      <view class="item-label">住院人名</view>
      <view class="item-input">
        <input
          type="text"
          placeholder="请在此处填写住院人姓名"
          value="{{inputData.patientName}}"
          @input="bindInputName"
        />
      </view>
    </view>
    <view class="form-item">
      <view class="item-label">住院号</view>
      <view class="item-input">
        <input
          type="text"
          placeholder="请在此处填写住院号"
          value="{{inputData.admissionNum}}"
          @input="bindInputNo"
        />
      </view>
    </view>
  </view>
</view>
<view class="m-btn">
  <view class="btn" @tap="bindAddUser">确认添加</view>
</view>