<view class="title">{{liveDetail.name}}</view>
<view class="describe" wx:if="{{remainTime > 0}}">直播{{remainTime}}分钟后开始，请等待...</view>
<view class="describe" wx:else>主播正在准备中，请等待</view>
<view class="card">
  <view class="card-title">主讲人介绍</view>
  <doctor-info :info.sync="doctorInfo" />
  <view class="card-content">{{liveDetail.introduction}}</view>
</view>
<image class="close" src="REPLACE_EHOSPITAL_DOMAIN/delete.png" @tap="back"></image>