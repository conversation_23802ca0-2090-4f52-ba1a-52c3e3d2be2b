import { REQUEST_QUERY } from "@/config/constant";
import { post } from "@/utils/request";

export const dateScheduleList = (param) =>
  post(
    `/api/register/dateschedulelist?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const scheduleDoctorList = (param) =>
  post(
    `/api/register/scheduledoctorlist?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const doctorList = (param) => post("/api/register/doctorlist", param);

export const getNoteProfile = (param) =>
  post("/api/register/getNoteProfile", param);

export const registerConfirm = (param) =>
  post("/api/register/registerconfirm", param);

export const getRelationList = (param) =>
  post(
    `/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const getPatientList = (param) => post("/api/user/patientslist", param);
