<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import TopTip from '@/components/toptip/index';
  
  import { CURSOR_SPACING, CHILD_MAX_AGE } from '@/config/constant';
  import { validator } from '@/utils/utils';
  import * as Api from '../../api';

  export default class BindUser extends wepy.component {

    components = {
      toptip: TopTip,
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      CURSOR_SPACING,

      placeholderColor: '',
      errorColor: 'red',

      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',


      patientName: '',
      idType: '1',
      idNo: '',
      admissionNum: '',
    };

    async pushData(){ // 绑卡
      wepy.showLoading({ title: '正在绑卡', mask: true });
      const value = this.getFormData();
      const { code, data, msg } = await Api.addUser(value);
      if(code == 0){
        wepy.hideLoading();
        wepy.showToast({
          title: '绑定成功',
          icon: 'success',
        });
        const timer = setTimeout(() => {
          clearTimeout(timer);
          wepy.navigateBack();
        }, 2000);
      }
    }

    validator(id){
      const validate = {
        patientName: {
          regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
          errTip: '请输入2-8位合法姓名',
        },
        idNo: {
          regexp: (v) => validator.idCard(v.idNo),
          errTip: '身份证格式校验错误',
        },
        admissionNum: {
          regexp: (val) => {
            return { ret: val.admissionNum.length > 0 };
          },
          errTip: '请输入住院号',
        },
      };
      const value = this.getFormData();

      let hasErr = false;
      for(let o in value){
        const obj = validate[o];
        if(obj && obj.regexp){
          let thisErr = false;
          if(typeof obj.regexp === 'function'){
            const retObj = obj.regexp(value);
            if(!retObj.ret){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          } else {
            if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
              hasErr = true;
              thisErr = true;
              if(id && id == o){
                this.errorElement[id] = true;
              }
            }
          }
          if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
            this.errorElement[obj.errTarget || o] = true;
            this.toptip = obj.errTip || '';
            const errTimer = setTimeout(() => {
              this.toptip = '';
              this.$apply();
              clearTimeout(errTimer);
            }, 2000);
            break;
          }
        }
      }

      return hasErr;
    }

    getFormData(){
      const { patientName, idNo, admissionNum, idType } = this;

      return {
        patientName,
        idNo,
        admissionNum,
        idType,
      };
    }


    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      resetThisError(e){
        const { id } = e.currentTarget;
        this.errorElement[id] = false;
      },
      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        this[id] = value;
      },

     // 获取订阅消息授权
      async getAuth(){
        if(this.hasErr){
          return false;
        }
        const that = this;
        if (wepy.$instance.globalData.isSDKVersion){
          const app = this.$root.$parent;  
          let templateIds =  []; 
          app.getTemplateId().then((templateId) => {     
            templateIds.push(templateId);
            if(templateId){
              wx.requestSubscribeMessage({
                tmplIds: templateIds,
                success(res) {
                  if (res[templateId] == 'accept'){
                    const subscribeList = [{
                      'status': res[templateId],
                      'templateId': templateId, 
                    }];
                    const param = {
                      subscribeList: JSON.stringify(subscribeList),
                    }
                    app.save(param);
                  } 
                },
                fail(res) {
                  console.log('fail', res);
                },
                complete(){
                  that.pushData();
                }  
              });    
            } else {
              that.pushData();
            }
          })
        } else {
          that.pushData();
        }
      },

      formSubmit(e){
        this.hasErr = this.validator();
        if(this.hasErr){
          return false;
        }
      },
    };

  }
</script>
