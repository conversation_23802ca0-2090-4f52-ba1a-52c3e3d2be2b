@import "../../../../resources/style/mixins";

.p-page{
  height: 100vh;
  overflow: hidden;
  .page-content{
    margin: 24rpx;
    padding: 32rpx;
    background-color: #fff;
    border-radius: 8rpx;
  }
  .content-box{
    margin-bottom: 32rpx;
    &:last-child{
      margin: 0;
    }
    .title{
      margin-bottom: 8rpx;
      color: rgba(0, 0, 0, 0.70);
      font-size: 28rpx;
      font-weight:600;
      &.require::before{
        content: "*";
        display: inline-block;
        color: red;
      }
    }
    input{
      padding: 16rpx 24rpx;
      font-size: 32rpx;
      border-radius: 8rpx;
      border: 2rpx solid rgba(0, 0, 0, 0.20);
    }
    .box-content{
      color: rgba(0, 0, 0, 0.70);
      font-size: 32rpx;
    }
    &.box-border{
      padding-bottom: 32rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
    }
  }
  .img-content{
    margin: 24rpx;
    overflow-y: auto;
    &.edit-box{
      max-height: calc(100vh - 200rpx);
    }
    .time{
      margin-bottom: 24rpx;
      font-weight: bold;
    }
  }
  .content-view{
    margin-bottom: 16rpx;
    padding: 32rpx 32rpx 0;
    background: #FFF;
    border-radius: 8rpx;
    box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  }
  .img-box-content{
    display: flex;
    flex-wrap: wrap;
  }
  .img-box{
    position: relative;
    margin-bottom: 24rpx;
    margin-right: 18rpx;
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    &:nth-child(4n){
      margin: 0;
    }
    .img{
      width: 100%;
      height: 100%;
    }
    .delete-img{
      position: absolute;
      width: 32rpx;
      height: 32rpx;
      right: 8rpx;
      top: 8rpx;
    }
  }
  .textarea-box{
    padding: 16rpx 24rpx;
    width: auto;
    height: 480rpx;
    font-size: 32rpx;
    border-radius: 8rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.20);
    
    &.no-border{
      padding: 0;
      border: none;
      color: rgba(0, 0, 0, 0.70);
      overflow-y: auto;
    }
    .box-tip{
      margin-bottom: 16rpx;
      color: rgba(0, 0, 0, 0.90);
      font-size: 28rpx;
      font-weight: 600;
    }
  }
  .btn-box{
    padding: 64rpx 24rpx;
    
    .cancel-btn{
      color: rgba(0, 0, 0, 0.70);
      background:rgba(0, 0, 0, 0.04);
    }
  }
  .btn{
    margin-bottom: 24rpx;
    height: 100rpx;
    text-align: center;
    line-height: 100rpx;
    font-size: 34rpx;
    font-weight: 600;
    color: #fff;
    border-radius: 76rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  }
  .update-btn{
    margin-top: 64rpx;
  }
  .bottom-btn{
    position: absolute;
    padding: 0 32rpx;
    bottom: 48rpx;
    width: 100%;
    margin: 0 -32rpx;
    display: flex;
    justify-content: space-between;
    .save{
      margin: 0 24rpx 0 32rpx;
    }
    
    .save, .cancel{
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      color: #3F969D;
      font-size: 34rpx;
      font-weight: 600;
      text-align: center;
      border-radius: 76rpx;
      border: 1rpx solid #3F969D;
    }
    .cancel{
      color: rgba(0, 0, 0, 0.70);
      margin-right: 32rpx;
      background:rgba(0, 0, 0, 0.04);
      border: 1rpx solid rgba(0, 0, 0, 0.04);
    }
  }
}