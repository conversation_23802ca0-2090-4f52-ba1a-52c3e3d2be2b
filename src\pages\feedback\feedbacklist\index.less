@import "../../../resources/style/mixins";

.p-page{
  height: 100vh;
}
.nav .capsule-box{
  font-size: 34rpx;
  font-weight: bold;
}
.list-container {
  .list-top{
    padding: 180rpx 52rpx 0 24rpx;
    display: flex;
    background-color: #3F969D;
    .list-top-title{
      position: relative;
      margin-top: 38rpx;
      color: #FFF;
      font-size: 48rpx;
      font-weight: 600;
      &::after{
        position: absolute;
        left: 0;
        top: 90rpx;
        content: '';
        display: inline-block;
        width: 41rpx;
        height: 7rpx;
        background: #FFF;
      }
    }
    image{
      width: 269rpx;
      height: 263.581rpx;
    }
  }

  .list-tips {
    padding-top: 16rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.70);
    font-size: 32rpx;
  }

  .hidden {
    // display: block !important;
  }

  .wgt-empty-box {
    text-align: center;
    // margin-top: -90vh;

    .wgt-empty-img {
      width: 300rpx;
      height: 224rpx;
      vertical-align: middle;
    }

    .wgt-empty-txt {
      padding: 40rpx 0;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
      .textBreak();

      .title {
        font-size: 34rpx;
        color: @hc-color-title;
        padding-bottom: 30rpx;
      }
    }

    .wgt-empty-action {
      display: none;
      padding: 50rpx 30rpx;
    }
  }

  .list-content {
    position: relative;
    padding: 32rpx;
    margin-top: -46rpx;
    border-radius: 8rpx 8rpx 0 0;
    z-index: 1;
    background-color: #f6f7f9;

    .m-list {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20rpx;
      background-color: #fff;
      padding: 32rpx;
      border-radius: 8rpx;
      box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);

      .m-list-type {
        margin-bottom: 8rpx;
        font-size: 36rpx;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.9);
      }

      .m-list-time {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.7);
      }

      .m-list-result {
        font-size: 28rpx;
        font-weight: 600;
        color: #CC8F24;
      }

      .m-list-re {
        font-weight: 600;
        font-size: 28rpx;
        color: #3F969D;
      }

      .m-list-jiantou {
        float: right;

        .union {
          width: 14rpx;
          height: 14rpx;
        }

      }
    }
  }

  .m-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;
    width: 100%;
    font-size: 36rpx;
    padding: 32rpx;
    box-sizing: border-box;

    view {
      width: 100%;
      border-radius: 76px;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-weight: 600;
      color: #fff;
    }
  }

  .m-bottom-fix {
    display: flex;
    align-items: center;
    padding: 0 40rpx;
    justify-content: center;
    position: fixed;
    left: 0;
    bottom: 24rpx;
    z-index: 9;
    width: 100%;
    color: #fff;
    font-size: 34rpx;
    box-sizing: border-box;

    view {
      width: 100%;
      line-height: 100rpx;
      text-align: center;
      border-radius: 76rpx;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    }
  }
}