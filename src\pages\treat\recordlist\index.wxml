<view class="p-page">
  <view class="top-msg">仅查询近6个月的缴费数据。</view>
  <view class="tab-container">
  <view 
    class="tab-item {{activeTab === 'success' ? 'active' : ''}}" 
    @tap="switchTab('success')"
  >缴费成功</view>
  <view 
    class="tab-item {{activeTab === 'abnormal' ? 'active' : ''}}" 
    @tap="switchTab('abnormal')"
  >缴费异常</view>
</view>
<block wx:if="{{currentList.length > 0}}">
  <view class="m-list">
    <block wx:for="{{currentList}}" wx:key="index">
        <view
          class="list-item list-item-{{WxsUtils.convertListStatus(item.status)}}"
          @tap="bindGoDetail({{item}})"
        >
          <view class="item-main">
            <view class="main-tit">
              <view class="item-icon">
                <image class="item-status" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
              </view>
              <text>{{item.statusName}}</text>
              <text class="unit-label" wx:if="{{item.refundStatus == 1 || item.refundStatus == 2 || item.refundStatus == 3}}">有退款</text>
            </view>
          </view>
          <view class="item-extra">
            <view class="main-txt">就诊人：{{item.patientName}}</view>
            <view class="main-txt">缴费时间：{{item.payedTime}}</view>
            <view class="extra-tit">支付金额：<text class="fee">￥{{WxsUtils.formatMoney(item.totalRealFee,1)}}</text></view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>