@import "../../../resources/style/mixins.less";

page {
}
.p-page {
  padding-bottom: 20rpx;
}
.survey-head {
  padding: 30rpx;
  background-color: #fff;
  word-wrap: break-word;
  word-break: break-all;
  .survey-title {
    text-align: center;
    font-size: 40rpx;
    color: #353535;
    font-weight: 600;
  }
  .survey-time {
    font-size: 26rpx;
    color: #888888;
    text-align: center;
    margin-top: 6rpx;
  }
  .survey-des {
    background-color: #fafafa;
    padding: 20rpx;
    margin: 36rpx 0 24rpx;
    font-size: 30rpx;
    color: #353535;
    .publish-unit {
      font-size: 26rpx;
      color: #888888;
      text-align: right;
      margin-top: 26rpx;
      &.text-link {
        color: #0ae;
      }
    }
  }
  .survey-target {
    display: flex;
    .dept,
    .doc {
      font-size: 28rpx;
      color: #888888;
      padding-left: 30rpx;
      margin-left: 98rpx;
      border-left: 1rpx solid @hc-color-border;
      &:first-child {
        border-left: none;
        margin-left: 0;
        padding-left: 0;
      }
      text {
        color: #353535;
      }
    }
  }
}

.survey-body {
  margin-top: 20rpx;
  .template-panle {
    padding: 30rpx 30rpx 0;
    border-bottom: 1rpx solid #e5e5e5;
    background-color: #fff;
    margin: 0 20rpx 10rpx;
    border-radius: 4rpx;
    box-shadow: 0 0 10 0 rgba(0, 0, 0, 0.06);
    font-size: 32rpx;
    line-height: 42rpx;
    color: #000000;
    word-wrap: break-word;
    word-break: break-all;
    &.single {
      padding-bottom: 30rpx;
    }
    .mast-select {
      height: 18rpx;
      width: 21rpx;
      margin-left: 10rpx;
    }
    
    /* 填空题标题行样式 */
    .placeholder-blank-title-row {
      display: flex;
      align-items: flex-start;
      
      text {
        white-space: nowrap;
        margin-right: 5rpx;
      }
      
      .placeholder-blank-text {
        flex: 1;
      }
    }
    
    /* 填空题输入区域样式 */
    .placeholder-blank-inputs {
      margin-top: 20rpx;
      padding: 0 20rpx;
      
      .placeholder-blank-input-item {
        display: flex;
        align-items: center;
        margin-bottom: 15rpx;
        
        .placeholder-blank-label {
          min-width: 40rpx;
          margin-right: 10rpx;
        }
        
        .placeholder-blank-input {
          flex: 1;
          border: 1px solid #e5e5e5;
          border-radius: 8rpx;
          padding: 8rpx 15rpx;
          margin-right: 10rpx;
          font-size: 28rpx;
        }
      }
    }
    
    /*单选*/
    .radio-group-item,
    .checkbox {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin: 40rpx 0;
      font-size: 30rpx;
      checkbox,
      radio {
        margin-right: 20rpx;
      }
      image {
        margin: 20rpx 80rpx;
        width: 120rpx;
      }
      input {
        width: 100px;
        margin-left: 20rpx;
        padding: 0 10rpx;
        border: 1rpx solid #ccc;
        border-radius: 10rpx;
      }
    }

    .checkbox {
      .checkbox-item {
        display: flex;
        align-items: center;
        width: 100%;
      }
    }

    .bold {
      font-weight: bold;
      padding-bottom: 30rpx;
    }
    // 单选拓展字段
    .radio-group-ext-panel {
      width: 100%;
      .radio-group-ext-input-item {
        display: flex;
        margin: 16rpx 0;
        flex-direction: column;
        label {
          margin-right: 24rpx;
        }
        input {
          flex: 1;
          margin-left: 0;
          width: 220px;
          margin-right: 30rpx;
          box-sizing: border-box;
        }
      }
    }
  }
  .listitem-head {
    height: 100rpx;
    display: flex;
    align-items: center;
    .textBreak();
    .list-title {
      flex: 1;
      font-size: 30rpx;
      color: @hc-color-title;
      padding-right: 12rpx;
      position: relative;
      line-height: 1;

      &.list-title_select:before {
        content: " ";
        position: absolute;
        right: 0;
        bottom: 0;
        box-sizing: border-box;
        border-bottom: 10rpx solid @hc-color-title;
        border-right: 10rpx solid @hc-color-title;
        border-top: 10rpx solid transparent;
        border-left: 10rpx solid transparent;
      }
    }
  }
  .anction-box {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    .anction-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      height: 0;
      background: rgba(0, 0, 0, 0.06);
    }
    .anction-content {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: @hc-color-bg;
      z-index: 99999;
      .top {
        margin-bottom: 20rpx;
        .content-item {
          padding: 30rpx 50rpx;
          display: flex;
          background: #fff;
        }
      }
      image {
        width: 120rpx;
      }
    }
  }
  /*输入框*/
  .list-box {
    box-sizing: border-box;
    padding: 30rpx;
    .list-title {
    }
    .list-items {
      margin-top: 30rpx;
      flex: 1;
      height: 68rpx;
      line-height: 68rpx;
      border: 1rpx solid #e5e5e5;
    }
  }
  /*多行*/
  .multiple-area {
    min-height: 250rpx;
    width: auto;
    margin-top: 30rpx;
  }
  /*单行*/
  .single-area {
    min-height: 60rpx;
    width: auto;
    margin-top: 30rpx;
  }
  /*星星评分*/
  .start-lev-txt,
  .slide-lev-txt {
    font-size: 28rpx;
    color: #f4b13c;
    padding-right: 20rpx;
  }
  .slide-lev-txt {
    color: #16d1b4;
    text-align: center;
  }
  /*图片上传*/
  .upload-imgcont {
    .upload-img-cont {
      margin-top: 20rpx;
      display: flex;
      align-items: center;
      flex-flow: row wrap;
      background-color: #dfdfdf;
      border: 1rpx solid #e5e5e5;
      border-radius: 5rpx;
      position: relative;
      flex: 1;
      max-width: 25%;
      min-width: 25%;
      width: 25%;
      height: 116rpx;
      padding: 0 15rpx;
      margin-bottom: 30rpx;
      box-sizing: border-box;
      .upload-img {
        display: block;
        height: 50%;
        width: 50%;
        margin: 0 auto;
      }
    }
  }
}

.survey-btn {
  border-radius: 76rpx;
  background: var(--linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
  margin: 40rpx 30rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #ffffff;
  height: 100rpx;
  line-height: 100rpx;
}

.container {
  position: relative;
  padding: 40rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.m-image-box {
  width: 100%;
  height: 220rpx;
  overflow: hidden;
}
.g-box {
  display: flex;
  flex-direction: column;
  background: #fff;
  margin-top: -80rpx;
  color: #000;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}
.patient-head-tips {
  font-size: 28rpx;
  color: #444;
  margin-bottom: 60rpx;
  border: none;
  padding-top: 40rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.patient-tips {
  display: block;
  text-indent: 2em;
}

/*下拉选择*/
/*日期选择*/
.sel-picker-item,
.dateScope-item {
  color: #bbb;
  font-size: 32rpx;
}
.data-scope-split {
  color: #bbb;
}
.date-select {
  padding: 40rpx 0;
}

.clear-img {
  position: absolute;
  bottom: -10rpx;
  right: -6rpx;
}
.padd-rl {
  padding: 0 30rpx;
}
.his-choose-item {
  padding-top: 50rpx;
  border: 1rpx #e5e5e5 solid;
}

.address-area {
  .picker-view {
    width: 100%;
    display: flex;
    z-index: 9999;
    background: #fff;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0rpx;
    height: 40vh;
  }
  .btn {
    width: 100%;
    height: 90rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    line-height: 90rpx;
    text-align: center;
    display: flex;
    background: rgba(255, 255, 255, 0.8);
    justify-content: space-between;
  }
  .cont {
    width: 100%;
    height: 389rpx;
  }
  .picker-item {
    line-height: 70rpx;
    margin-left: 5rpx;
    margin-right: 5rpx;
    text-align: center;
  }
  .address {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-bottom: 1rpx solid #f1f1f1;
  }
  .address-input {
    height: 60rpx;
  }
}

.link-item {
  display: inline-block;
  font-size: 32rpx;
  color: #3986ff;
}

// 弹窗样式
.desc-modal-mask {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;

  .desc-modal {
    width: 670rpx;
    border-radius: 8rpx;
    background-color: @hc-color-white;

    .desc-title {
      text-align: center;
      padding: 32rpx;
      font-size: 34rpx;
      line-height: 52rpx;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
    }

    .desc-content {
      width: 100%;
      max-height: 700rpx;
      min-height: 200rpx;
      box-sizing: border-box;
      padding: 0 32rpx 32rpx;
      color: @hc-color-text;
      font-size: 34rpx;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.4);
      .desc-content-title {
        color: @hc-color-title;
      }
    }
    .desc-footer {
      height: 116rpx;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      border-top: 1rpx solid @hc-color-border;

      > view {
        flex: 1;
        font-size: 34rpx;
        font-weight: 600;
        color: @hc-color-gray;
        line-height: 52rpx;
        text-align: center;
        padding: 32rpx 0;

        & + view {
          border-left: 1rpx solid @hc-color-border;
        }
      }

      .agree {
        color: @hc-color-primary;
      }
    }
  }
}

// 多值填空题样式
.question-item-fillblank {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 20rpx 30rpx;
}

.question-title-fillblank {
  margin: 0;
  padding-right: 4rpx;
  flex-shrink: 0;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.6;
  color: #333333;
}

.fill-blank-title {
  display: inline-flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
  line-height: 1.8;
  font-size: 28rpx;
  font-weight: normal;
  flex: 1;
  word-break: break-word;
}

.fill-blank-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  width: 100%;
}

.fill-blank-text {
  display: inline;
  word-break: break-word;
  font-size: 28rpx;
  line-height: 1.8;
  white-space: normal;
}

.fill-blank-input {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  border: none;
  border-bottom: 2rpx solid #3ECEB6;
  padding: 0 10rpx;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
  margin-left: 10rpx;
  
  &:focus {
    border-bottom-color: #2BA99A;
    outline: none;
  }
  
  &::placeholder {
    color: #BBBBBB;
    font-size: 26rpx;
  }
}

// 占位符填空题样式
.placeholder-blank-content {
  padding: 20rpx 0;
}

.placeholder-blank-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  
  .blank-underline {
    display: inline-block;
    min-width: 120rpx;
    border-bottom: 2rpx solid #3ECEB6;
    text-align: center;
    padding: 0 10rpx;
    color: #3ECEB6;
    font-weight: 500;
  }
}

.placeholder-blank-inputs {
  margin-top: 30rpx;
  
  .placeholder-blank-input-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .placeholder-blank-label {
      width: 40rpx;
      font-size: 28rpx;
      color: #666;
      margin-right: 20rpx;
    }
    
    .placeholder-blank-input {
      flex: 1;
      height: 70rpx;
      border: none;
      border-bottom: 2rpx solid #3ECEB6;
      font-size: 28rpx;
      padding: 0 10rpx;
      
      &:focus {
        border-bottom-color: #2BA99A;
      }
    }
  }
}

// 二级选项样式
.second-option-wrap {
  position: relative;
  margin-top: 15rpx;
  margin-left: 46rpx;
  margin-bottom: 5rpx;
  display: flex;
  align-items: flex-start;
  width: calc(100% - 46rpx);
  
  .second-option-dot {
    flex-shrink: 0;
    width: 10rpx;
    height: 10rpx;
    background-color: #3ECEB6;
    border-radius: 50%;
    margin-top: 16rpx;
    margin-right: 15rpx;
  }
}

.second-option-input {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  
  textarea.second-textarea {
    background-color: #f9f9f9;
    padding: 16rpx 20rpx;
    font-size: 28rpx;
    width: 100%;
    box-sizing: border-box;
    line-height: 1.6;
    min-height: 80rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 8rpx;
    transition: border-color 0.3s ease;
    margin-top: 10rpx;
    margin-bottom: 10rpx;
    
    &:focus {
      border: 1rpx solid #3ECEB6;
      background-color: #fff;
    }
  }
}

.second-option-checkboxes {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;
  
  .second-checkbox-item {
    display: flex;
    align-items: center;
    margin-right: 30rpx;
    margin-bottom: 20rpx;
    width: 45%; /* 每行显示两个选项 */
    float: left;
    padding: 8rpx 0;
    
    &:nth-child(2n) {
      margin-right: 0;
    }
    
    checkbox {
      transform: scale(0.85);
      margin-right: 10rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #555;
      flex: 1;
      word-break: break-all;
      line-height: 1.5;
    }
  }
}

// 二级多值填空样式
.second-option-fill-blank {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;

  .fill-blank-content {
    line-height: 1.8;
    font-size: 28rpx;
    color: #333;

    .fill-blank-text {
      display: inline;
      word-break: break-word;
    }

    .fill-blank-underline {
      display: inline;
      border-bottom: 2rpx solid #3ECEB6;
      min-width: 80rpx;
      padding: 0 8rpx 2rpx 8rpx;
      margin: 0 4rpx;
      color: #3ECEB6;
      font-weight: 500;
      text-align: center;

      &:empty::before {
        content: "____";
        color: #ccc;
      }
    }
  }
}

// 附件显示样式
.file-display-section {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;

  .file-display-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 15rpx;
  }

  // 网格布局
  .file-display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160rpx, 1fr));
    gap: 15rpx;
  }

  .file-display-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    // 缩略图容器
    .file-thumbnail {
      width: 120rpx;
      height: 120rpx;
      border-radius: 8rpx;
      overflow: hidden;
      position: relative;
      margin-bottom: 8rpx;
      border: 2rpx solid #e9ecef;

      &.image-thumbnail {
        background-color: #f8f9fa;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &.pdf-thumbnail {
        background-color: #ffebee;
        display: flex;
        align-items: center;
        justify-content: center;

        .pdf-icon-large {
          font-size: 32rpx;
          font-weight: 600;
          color: #d32f2f;
        }
      }

      // 文件类型标签
      .file-type-badge {
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 20rpx;
        padding: 2rpx 6rpx;
        border-radius: 4rpx;
        line-height: 1;
      }
    }

    // 文件名
    .file-name-grid {
      font-size: 24rpx;
      color: #495057;
      text-align: center;
      word-break: break-all;
      line-height: 1.3;
      max-width: 120rpx;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  // 兼容旧的列表布局（如果需要）
  .file-display-list {
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .file-display-item {
      flex-direction: row;
      align-items: center;
      padding: 15rpx;
      background-color: #fff;
      border-radius: 6rpx;
      border: 1rpx solid #dee2e6;

      .file-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 15rpx;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4rpx;
        font-size: 20rpx;
        font-weight: 500;

        &.file-icon-image {
          background-color: #e3f2fd;
          color: #1976d2;
        }

        &.file-icon-pdf {
          background-color: #ffebee;
          color: #d32f2f;
        }

        .file-icon-text {
          font-size: 20rpx;
          line-height: 1;
        }
      }

      .file-name {
        font-size: 26rpx;
        color: #495057;
        flex: 1;
        word-break: break-all;
        line-height: 1.4;
      }
    }
  }
}
