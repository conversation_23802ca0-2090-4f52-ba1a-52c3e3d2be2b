<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import { REQUEST_QUERY } from "@/config/constant";
  import TreatList from './com/treatList';
  import DateCheck from '@/components/datecheck/index';
  // import { getFormatDate } from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '门诊缴费',
      navigationBarBackgroundColor: '#fff'
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'treat-list': TreatList,
      'outpatient': Outpatient,
      'empty': Empty,
      'date-check': DateCheck,
    };

    async onLoad(options){
      const { patCardNo = '', iconkey = '', patientId = '', pid = '', gid = '', dateType = 1 } = options || {};

      // if (iconkey === 'hsjcyy') {
      //   const res = await wepy.showModal({
      //     title: '温馨提示', //提示的标题,
      //     content: '请选择核酸预约核酸检测日期', //提示的内容,
      //     showCancel: true, //是否显示取消按钮,
      //     cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
      //     cancelColor: '#000000', //取消按钮的文字颜色,
      //     confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
      //     confirmColor: '#3CC51F', //确定按钮的文字颜色,
      //     success: res => {},
      //   });
      //   console.log('res', res)
      //   if (!res.confirm) {
      //     wepy.navigateBack({
      //       delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
      //     });
      //   }
      // }
      this.patCardNo = patCardNo;
      this.type = iconkey;
      this.patientId = patientId;
      this.checkDateIndex = Number(dateType)-1;
      this.$apply();
    }
    //  https://wechatdev.jiahuiyiyuan.com/scancode?isScan=1&patCardType=21&patientName=孙磊测试&patCardNo=204892&patHisNo=20240313023
    onShow(options) {    
      const { q = '' } = options || this.$wxpage.options;
      let url = decodeURIComponent(q);
      console.log('url=', url);
      const querys = url.substring(url.indexOf('?') + 1).split('&');
      const result=[];
      for(let i = 0; i < querys.length; i++){
          let temp = querys[i].split('=');
          if(temp.length < 2){
            result[temp[0]]='';
          }else{
            result[temp[0]]=temp[1];
          }
      }
      console.log('result=', result);
      const isScan = result['isScan'];
      const patHisNo = result['patHisNo'];
      const patCardNo = result['patCardNo'];
      const patCardType = result['patCardType'] || '21';
      const patientName = result['patientName'] || result['patName'];

      this.isScan = isScan;
      this.patHisNo = patHisNo;
      this.patCardNo = patCardNo;
      this.patCardType = patCardType;
      this.patientName = patientName;

      this.$apply();

      console.log('isScan=', isScan);
      if (isScan == 1) {
        this.patientId = '-1';
        this.getList();
      } else {
        const { patientId = '' } = this.$wxpage.options;
        this.$broadcast('outpatient-get-patient', { patientId });
      }
    }

    data = {
      maxDate: this.getDateStr(),
      minDate: this.getTimeBegin(),
      selectedDate: '',
      patCardNo: '',
      type: '',
      // 时间段列表
      dateList: [
        {
          value: 1,
          text: '当天',
          day: 0,
        },
        {
          value: 2,
          text: '三个月内',
          day: -90,
        },
        {
          value: 3,
          text: '一年内',
          day: -365,
        }
      ],
      // 选中的时间段下标
      checkDateIndex: 0,
      // 当前就诊人的ID
      patientId: '',
      // 门诊待缴费数据
      treatList: {},
      patientConfig: {
        infoShow: true,
        show: false,
        initUser: {},
      },
      emptyConfig: {
        show: true,
      },
      patient: {},
      // 选中数量
      checkedLength: 0,
      // 合并金额
      totalFee: 0,
      // 扫码参数
      isScan:'', 
      patHisNo:'',
      patCardNo:'', 
      patCardType: '',
      patientName: '',
    };

    getTimeBegin(){
      let dd = new Date();
      dd.setDate(dd.getDate());//获取AddDayCount天后的日期
      let yyyy = dd.getFullYear(); 
      let mm = (dd.getMonth()+1)<10?"0"+(dd.getMonth()+1):(dd.getMonth()+1);//获取当前月份的日期，不足10补0
      let cc = dd.getDate()<10?"0"+dd.getDate():dd.getDate();//获取当前几号，不足10补0
      return yyyy+"-"+mm+"-"+cc;
    }

    getDateStr() { 
      let dd = new Date();
      dd.setDate(dd.getDate()+2);//获取AddDayCount天后的日期
      let yyyy = dd.getFullYear(); 
      let mm = (dd.getMonth()+1)<10?"0"+(dd.getMonth()+1):(dd.getMonth()+1);//获取当前月份的日期，不足10补0
      let cc = dd.getDate()<10?"0"+dd.getDate():dd.getDate();//获取当前几号，不足10补0
      return yyyy+"-"+mm+"-"+cc; 
    }

    methods = {
      bindChangeDate(e){
        const { value = '' } = e.detail;
        if (value == this.selectedDate) {
          return;
        }
        this.selectedDate = value;
        this.$apply();
      },
      orderProject(){
        if (!this.selectedDate) {
          wepy.showModal({
            title: '提示', //提示的标题,
            content: '请选择预约检测日期', //提示的内容,
            showCancel: false, //是否显示取消按钮,
            cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
            cancelColor: '#000000', //取消按钮的文字颜色,
            confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
            confirmColor: '#3CC51F', //确定按钮的文字颜色,
            success: res => {}
          });
          return;
        }
        this.bindOrderProject();
      }

    };

    async bindOrderProject(){
      const param = {
        patHisNo: this.patCardNo, 
        jhrq: this.selectedDate,
      }
      const { code, data = {}, msg } = await Api.orderProject(param);
      if(code == 0){
        const { resultCode = '', resultMessage = '' } = data;
        if (Number(resultCode) === 0) {
          wepy.showModal({
            title: '提示', //提示的标题,
            content: '申请成功', //提示的内容,
            showCancel: false, //是否显示取消按钮,
            cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
            cancelColor: '#000000', //取消按钮的文字颜色,
            confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
            confirmColor: '#3CC51F', //确定按钮的文字颜色,
            success: res => {},
            cancel: res => { console.log('111') }
          });
          this.getList();
        } else {
          wepy.showModal({
            title: '系统提示', //提示的标题,
            content: resultMessage, //提示的内容,
            showCancel: false, //是否显示取消按钮,
            cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
            cancelColor: '#000000', //取消按钮的文字颜色,
            confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
            confirmColor: '#3CC51F', //确定按钮的文字颜色,
            success: res => {}
          });
          
        }
      }   
    }

    events = {
      'outpatient-change-user': function (item = {}) {
        this.patientId = item.patientId || '';
        this.getList();
      },
      'date-change':function(){
        this.getList();
      }
    };
    /**
     * 获取待缴费列表
     */
    async getList() {
      this.emptyConfig.show = true;
      this.checkedLength = 0;
      this.totalFee = 0;
      this.treatList = [];

      const dateGap = this.dateList[this.checkDateIndex].day;
      const beginDate = Utils.getFormatDate(dateGap, '-');

      const {isScan='', patHisNo='', patCardNo='', patCardType = '', patientName = ''} = this;
      const ext = {
        isScan, patHisNo, patCardNo, patCardType, patientName
      }
      const extPropStr = JSON.stringify(ext);

      let params = {
        patientId: this.patientId,
        transParam: JSON.stringify({
          beginDate
        })
      }
      if(isScan == '1') {
        params = {
          extPropStr: extPropStr,
        };
      }
      const { code, data = {}, msg } = await Api.getList(params);
      if(code == 0){
        this.checkedLength = 0;
        this.totalFee = 0;

        if (data.waitOpList.length > 0) {
          this.emptyConfig.show = false;
        }
        this.treatList = data.waitOpList || [];
        this.$apply();
      }
    }
  }
</script>
