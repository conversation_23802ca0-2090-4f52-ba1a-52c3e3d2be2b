@import "../../../resources/style/mixins";

page {
}

.m-card {
  margin: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;

  .card-head {
    .take-status {
      color: @hc-color-primary;
      font-size: 34rpx;
    }
    .card-head-status {
      display: flex;
      align-items: center;
      color: @hc-color-title;
      font-weight: 600;
      font-size: 28rpx;
      padding-bottom: 20rpx;
      .card-head-icon {
        width: 32rpx;
        height: 32rpx;
        overflow: hidden;
        border-radius: 50%;
        margin-right: 16rpx;
        image {
          width: 32rpx;
          height: 32rpx;
          vertical-align: top;
        }
      }
    }
  }

  .card-body {
    padding-bottom: 24rpx;

    .takeinfo-item {
      padding-top: 8rpx;
      display: flex;
      font-size: 28rpx;

      .item-label {
        color: @hc-color-text;
        flex: 1;
      }
      .item-info {
        color: @hc-color-title;

        &.item-info_warn {
          color: @hc-color-assist;
        }
      }
    }
  }

  .card-foot {
    display: flex;

    .takeno-oper {
      text-align: center;
      border-top: 2rpx solid #e2e9e8;
      border-right: 2rpx solid #e2e9e8;
      flex: 1;
      padding-top: 24rpx;

      &:last-child {
        border-right: 0;
      }
    }
    .oper-primary {
      font-size: 28rpx;
      font-weight: 600;
      color: @hc-color-link;
    }
    .oper-text {
      font-size: 28rpx;
      color: @hc-color-text;
    }
  }
}
