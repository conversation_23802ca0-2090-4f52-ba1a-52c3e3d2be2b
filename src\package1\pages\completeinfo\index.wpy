<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from "wepy";
import TopTip from "@/components/toptip/index";
import * as WxmpRsa from '../../../utils/rsa/JSEncrypt.js';
import { CURSOR_SPACING, CHILD_MAX_AGE } from "@/config/constant";
import {
  validator,
  getAgeByBirthday,
  getBirthdayByIdCard,
  getSexByIdCard
} from "@/utils/utils";
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "完善信息",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    toptip: TopTip
  };

  onLoad(option) {
    this.isCode = option.isCode;
    this.isScan = option.isScan;
    this.idType = option.idType || 1;
    this.getHisConfig();
    this.getDictInfo(option);
    if (option.isNewCard == 1) {
      this.initNewCard(option);
    } else {
      this.initData(option);
      this.getPatientInfo(option);
    }
    this.getSecret();
  }

  async getSecret(){
    const { code, data, msg } = await Api.getSecret();
    if (code == 0 && data.secret) {
      if(data.resultCode == '0'){
        this.publicKey = data.secret;
      } else {
        wepy.showModal({
          title: '提示', //提示的标题,
          content: data.resultMessage || '未查询到公钥', //提示的内容,
          showCancel: false, //是否显示取消按钮,
          cancelText: '取消', //取消按钮的文字，默认为取消，最多 4 个字符,
          cancelColor: '#000000', //取消按钮的文字颜色,
          confirmText: '确定', //确定按钮的文字，默认为取消，最多 4 个字符,
          confirmColor: '#3CC51F', //确定按钮的文字颜色,
          success: res => {}
        });
      }
    } 
    
  }

  data = {
    publicKey: '',
    isCode: '',
    toptip: "",
    isOtherType: 0, // 是否是其它证件类型
    qryType: "", // 绑定类型 1本人 2他人
    dict: {}, // 字典信息
    userList: [], // 查询到的pid下的成员
    memberList: [], // 家庭成员列表
    CURSOR_SPACING,
    options: {},
    idType: "",
    idNo: "",
    pid: "",
    patientName: "",
    patientMobile: "",
    mobileDisabled: false, // 手机号是否不能编辑
    patSex: "",
    birthday: "",
    isNewCard: "0", // 是否新建卡
    isNoCard: '1', // 是否无卡
    idTypesIdx: 0,
    hisConfig: {
      relationTypes: [],
      patientTypes: [],
      idTypes: [
        {
          dictKey: "1",
          dictValue: "身份证",
          sortNo: 0,
          regexp: validator.idCard,
          errTip: "请输入18位身份证",
          inputType: "idcard"
        },
        { dictKey: "10", dictValue: "警官证", sortNo: 1 },
        { dictKey: "3", dictValue: "护照", sortNo: 3 },
        { dictKey: "4", dictValue: "军官证", sortNo: 4 },
        { dictKey: "8", dictValue: "其他法定有效证件", sortNo: 5 },
        { dictKey: "9", dictValue: "士兵证", sortNo: 6 }
      ],
      patCards: []
    },

    hisIdTypes: [
      {"value": "DB01", "text": "身份证"},
      {"value": "DB03", "text": "护照"},
      {"value": "DB04", "text": "军官证"},
      {"value": "DB08", "text": "其他法定有效证件"},
      {"value": "DB09", "text": "士兵证"},
      {"value": "DB10", "text": "警官证"},
      {"value": "DB11", "text": "学生证"},
      {"value": "DB13", "text": "港澳居民身份证"},
      {"value": "DB12", "text": "台湾居民身份证"},
    ],

    marryDes: "",
    marrList: [],
    marrArrList: [],
    indexZR: "",
    famList: [],
    famArrList: [],
    indexJT: "",
    natList: [],
    natArrList: [],
    indexAF: "",
    jobList: [],
    jobArrList: [],
    indexAG: "",
    nationList: [],
    nationArrList: [],
    indexAC: "",
    culList: [],
    culArrList: [],
    indexZC: "",

    marryCode: "",
    familyMemberCode: "",

    errorColor: "red",
    errorElement: {}, // 发生错误的元素
    hasErr: true, // 是否存在校验错误
    partdate: "",
    dataInfo: {},

    postalCode: "", //邮编
    idCardAddress: "", //身份证地址

    spouseInfo: { idTypesIdx: 0 },
    spouseName: "",
    spouseIdNo: "",
    spouseBirthday: "",
    spousePhone: "",

    isShowSpouseInfo: false,
  };

  initData(options) {
    const {
      patientName = "",
      idNo = "",
      pid = "",
      patientMobile = "",
      patSex = "",
      address = "",
      idCardPath = "",
      idCardToken = "",
      isNewCard = "0",
      isNoCard = "0",
      qryType = "2",
      isOtherType = 0,
      idTypesIdx = 0
    } = options;
    this.idNo = idNo;
    this.pid = pid;
    this.patientName = patientName;
    this.patientMobile = patientMobile;
    this.mobileDisabled = !!patientMobile;
    this.patSex = patSex;
    this.patientAddress = address;
    this.idCardPath = idCardPath;
    this.idCardToken = idCardToken;
    this.isNewCard = isNewCard;
    this.isNoCard = isNoCard;
    this.options = options;
    this.qryType = qryType;
    this.isOtherType = parseInt(isOtherType);
  }

  initNewCard(options) {
    // 初始化新建卡 不会查询用户信息
    const {
      patientName = "",
      idNo = "",
      idType = "1",
      pid = "",
      patientMobile = "",
      patSex = "",
      address = "",
      idCardPath = "",
      idCardToken = "",
      isNewCard = "1",
      isNoCard = "1",
      qryType = "2",
      isOtherType = 0,
      areaCode = "",
      areaName = "",
    } = options;
    this.idNo = idNo;
    this.pid = pid;
    this.patientName = patientName;
    this.patientMobile = patientMobile;
    this.patSex = patSex;
    this.patientAddress = address;
    this.idType = idType;
    this.idCardPath = idCardPath;
    this.idCardToken = idCardToken;
    this.isNewCard = isNewCard;
    this.isNoCard = isNoCard;
    this.options = options;
    this.qryType = qryType;
    this.isOtherType = parseInt(isOtherType);
    this.areaCode = areaCode ? areaCode.split('-') : "";
    this.areaName = areaName ? areaName.split('-') : "";
    this.dataInfo = {
      ...options,
      areaCode: areaCode ? areaCode.split('-') : "",
      areaName: areaName ? areaName.split('-') : "",
      telephone: options.patientMobile || "",
      idCardAddress: options.address || ""
    };
  }

  async getPatientInfo(option) {
    let idNo = '';
    let patName = '';
    let qryType = '';
    let pid = '';
    let idType = '';
    let patientName = '';
    if (this.isCode == 1) {
      idNo = option.idNo;
      patName = option.patName || option.patientName;
      qryType = option.qryType;
      pid = option.pid;
      idType = option.idType;
      patientName = option.patientName || option.patName;
    } else {
      idNo = this.options.idNo;
      patName = this.options.patName || this.options.patientName;
      qryType = this.options.qryType;
      pid = this.options.pid;
      idType = this.options.idType;
      patientName = this.options.patientName || this.options.patName;
    }
    // qryType为1的话查询自己，2为查询关系人
    const param = { idNo, patName, qryType: 2, patHisNo: pid, idType };
    const { code, data = {} } = await Api.getPatientInfo(param);
    if (
      code == 0 && data.resultCode == 0 &&
      data.itemList &&
      data.itemList[0] &&
      data.itemList[0].items &&
      data.itemList[0].items
    ) {
      let dataInfo;
      const userList = data.itemList[0].items;
      this.userList = userList;
      userList.forEach(item => {
        if (item.patName === patName) {
          dataInfo = item;
        }
      });
      if (!dataInfo) {
        return;
      }
      const areaCode = dataInfo.areaCode ? dataInfo.areaCode.split('-') : "";
      const areaName = dataInfo.areaName ? dataInfo.areaName.split('-') : "";
      this.dataInfo = dataInfo;
      this.dataInfo.areaCode = areaCode;
      this.dataInfo.areaName = areaName;
      this.patientName = dataInfo.patName || "";
      this.idNo = dataInfo.idNo || "";
      this.birthday = dataInfo.patBirth || "";
      if (!this.patientMobile) {
        this.patientMobile = dataInfo.telephone || "";
        this.mobileDisabled = !!dataInfo.telephone;
      }
      this.patSex = dataInfo.patSex || "";
      // 是否查询到了用户信息
      this.isNoCard = this.dataInfo.patName ? 0 : 1;
      this.isNewCard = this.dataInfo.patName ? 0 : 1;
      const { familyMemberCode, marryCode } = dataInfo;
      this.familyMemberCode = familyMemberCode;
      this.marryCode = marryCode;
      // 已婚且为女方或男方
      if (
        (familyMemberCode === "JT01" || familyMemberCode === "JT02") &&
        marryCode === "ZR01"
      ) {
        this.isShowSpouseInfo = true;
        this.$apply();
        // 判断是否存在配偶信息
        const spouseMemberCode = familyMemberCode === "JT01" ? "JT02" : "JT01";
        console.log("spouseInfo");
        userList.forEach(item => {
          if (item.familyMemberCode === spouseMemberCode && item.patName) {
            console.log('idType',item.idType)
            let idTypeIdx = this.hisIdTypes.findIndex((idType) => idType.value == item.idType);
            this.spouseInfo = { ...this.spouseInfo, ...item, idTypesIdx: idTypeIdx};
            this.spouseBirthday = item.patBirth;
            this.spouseName = item.patName || "";
            this.spouseIdNo = item.idNo || "";
            this.spousePhone = item.telephone || "";
          }
        });
      }
    } else {
      wepy.showModal({
        title: "温馨提示",
        content: data.resultMessage,
        showCancel: false
      });
    }
    this.$apply();
  }


  addMemberActionsheet() {
    const famArrList = this.famArrList;
    wepy.showActionSheet({
      itemList: famArrList,
      success: res => {
        cosnole.log("res", res);
      },
      fail: res => {
        console.log("fail", res);
      }
    });
  }

  addMember(param = {}) {
    // 添加家庭成员
    console.log("addMember");
    const { userList = [], memberList = [] } = this;
    // 已经存在家庭成员
    const memberCodeList = memberList.map(item => {
      return item.memberCode;
    });
    const userMemberCodeList = userList.map(item => {
      return item.familyMemberCode;
    });
    let memberInfo = {};
    memberInfo.index = memberList.length;
    memberInfo.idTypesIdx = 0;
    memberList.push(memberInfo);
    this.memberList = memberList;
    this.$apply();
  }

  validator(id) {
    let validate = {
      idNo: {
        regexp: (() => {
          const regexp = this.hisConfig.idTypes[this.idTypesIdx].regexp;
          if (typeof regexp === "function") {
            return val => regexp(val.idNo);
          } else {
            return /^\S+$/;
          }
        })(),
        errTip:
          this.hisConfig.idTypes[this.idTypesIdx].errTip ||
          `${this.hisConfig.idTypes[this.idTypesIdx].dictValue}不能为空`
      },
      // patientMobile: {
      //   regexp: /^1\d{10}$/,
      //   errTip: '请输入正确的手机号码'
      // },
      address: {
        regexp: /^\S+$/,
        errTip: '请输入通讯详细地址'
      },
      areaCode: {
        regexp: /^\S+$/,
        errTip: '请选择所在省市区/县'
      }
    };

    const value = this.getFormData();
    console.log('getFormData=', value);

    let hasErr = false;
    for (let o in value) {
      const obj = validate[o];
      if (obj && obj.regexp) {
        let thisErr = false;
        if (typeof obj.regexp === "function") {
          const retObj = obj.regexp(value);
          if (!retObj.ret) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        } else {
          if (
            typeof obj.regexp.test === "function" &&
            !obj.regexp.test(value[o])
          ) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        }
        if (
          (!id && hasErr) ||
          (obj.errTarget && obj.errTarget == id && thisErr)
        ) {
          // 提交时弹框提示
          this.errorElement[obj.errTarget || o] = true;
          this.toptip = obj.errTip || "";
          const errTimer = setTimeout(() => {
            this.toptip = "";
            this.$apply();
            clearTimeout(errTimer);
          }, 2000);
          break;
        }
      }
    }

    return hasErr;
  }

  validateMember(list) {
    // 校验家庭成员信息填写
    const result = { ret: true, tip: "" };
    const validate = {
      patientName: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,20}$/,
        errTip: "2-20位合法姓名"
      },
      idNo: {
        regexp: val => validator.idCard(val.idNo),
        errTip: "正确的证件号"
      },
      // patientMobile: {
      //   regexp: /^1\d{10}$/,
      //   errTip: "正确的手机号"
      // },
      familyMemberCode: {
        regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
        errTip: "关系"
      }
    };
    // 验证配偶信息是否有空值
    if (
      this.indexZR == 1 &&
      ((!this.spouseIdNo && !this.spouseInfo.idNo) ||
        !this.spouseName ||
        (!this.spousePhone && !this.spouseInfo.telephone))
    ) {
      result.ret = false;
      result.tip = `请输入配偶信息`;
      return result;
    }
    console.log(list);
    list.forEach(value => {
      let hasErr = false;
      for (let o in value) {
        const obj = validate[o];
        if (obj && obj.regexp) {
          let thisErr = false;
          if (o === "idNo") {
            let retObj;
            if (value.idType !== "1") {
              retObj = {
                ret: /^[\u4e00-\u9fa5_a-zA-Z0-9]{1,20}$/.test(value.idNo)
              };
            } else {
              retObj = validator.idCard(value.idNo);
            }
            if (!retObj.ret) {
              hasErr = true;
              thisErr = true;
            }
          } else if (typeof obj.regexp === "function") {
            const retObj = obj.regexp(value);
            if (!retObj.ret) {
              hasErr = true;
              thisErr = true;
            }
          } else {
            if (
              typeof obj.regexp.test === "function" &&
              !obj.regexp.test(value[o])
            ) {
              hasErr = true;
              thisErr = true;
            }
          }
          if (thisErr && !result.tip) {
            result.ret = false;
            result.tip = `请输入${"成员"}${obj.errTip}`;
            break;
          }
        }
      }
      // 家庭成员出生日期、性别
      console.log("家庭成员出生日期、性别", value);
      if (value.idType !== "1") {
        hasErr = true;
        if (!/^[\d]{4}-[\d]{2}-[\d]{2}$/.test(value.birthday)) {
          result.ret = false;
          result.tip = `请选择${"成员"}出生日期`;
        }
        if (!value.patSex) {
          result.ret = false;
          result.tip = `请选择成员性别`;
        }
      }
      // 配偶性别是否与关系匹配
      // if (value.marryCode === 'ZR01') {
      //   const obj = {
      //     'JT01': '女',
      //     'JT02': '男',
      //   }
      //   if (obj[value.familyMemberCode] !== value.patSex) {
      //     result.ret = false;
      //     result.tip = value.idType !== '1' ? '请选择正确性别' : '请输入正确性别身份证';
      //   }
      // }
    });
    return result;
  }

  // 获取字典内容
  async getDictInfo() {
    const { code, data = {} } = await Api.getDict();
    const { list = [] } = data;
    const dict = {};
    list.forEach(item => {
      if (item.dictType && item.itemList && Number(item.resultCode) === 0) {
        dict[item.dictType] = item;
      }
    });
    this.dict = dict;
    const option = {};
    const nameObj = {
      ZR: "marr",
      JT: "fam",
      AF: "nat",
      AG: "job",
      AC: "nation",
      ZC: "cul"
    };
    for (let name in nameObj) {
      this.getListInfoByName(name);
    }
    this.initPatientForm(this.dataInfo);
    this.$apply();
  }

  getListInfoByName(name = "") {
    // 根据名称获取字典数组信息
    const nameObj = {
      ZR: "marr",
      JT: "fam",
      AF: "nat",
      AG: "job",
      AC: "nation",
      ZC: "cul"
    };
    const data = this.dict[name] || {};
    const { itemList = [] } = data;
    const { items = [] } = itemList[0] || {};
    const arrList = [];
    items.map(item => {
      arrList.push(item.dictName);
    });
    console.log(arrList);
    if (name === "JT") {
      console.log(arrList);
    }
    this[`${nameObj[name]}ArrList`] = arrList;
  }

  initPatientForm(patientInfo = {}) {
    // 初始化就诊人表单内容
    const nameObj = {
      ZR: "marr",
      JT: "fam",
      AF: "nat",
      AG: "job",
      AC: "nation",
      ZC: "cul"
    };
    const valueList = ["address", "idCardAddress", "postalCode"];
    const pickerValueObj = {
      educationCode: { abbr: "ZC", arrListName: "cul" },
      familyMemberCode: { abbr: "JT", arrListName: "fam" },
      jobCode: { abbr: "AG", arrListName: "job" },
      marryCode: { abbr: "ZR", arrListName: "marr" },
      nationCode: { abbr: "AC", arrListName: "nation" },
      nativeCode: { abbr: "AF", arrListName: "nat" }
    };
    valueList.forEach(id => {
      // 判断是否存在这些值 直接赋值
      const value = patientInfo[id];
      if (value) {
        this[id] = value;
      }
    });
    for (const key in pickerValueObj) {
      // 判断是否存在值 循环对应数组
      const value = patientInfo[key];
      const { abbr, arrListName } = pickerValueObj[key];
      if (value) {
        // const list = this[`${arrListName}ArrList`] || [];
        const dictList =
          this.dict[abbr].itemList[0].items.map(item => item.dictCode) || [];
        const index = dictList.indexOf(value) || 0;
        this[`index${abbr}`] = index;
      }
    }
    console.log("initPatientForm");
  }

  getListData(obj = {}) {
    // 获取民族、籍贯等字典中的内容
    const { index, name = "" } = obj;
    if (!index && typeof index != "number") {
      return "";
    }
    const { dict = {} } = this;
    const { itemList = [] } = dict[name];
    const { items } = itemList[0];
    if (items.length <= 0) {
      return "";
    }
    return (items[index] || {}).dictCode;
  }

  getFormData() {
    const marryCode =
      this.getListData({ index: this.indexZR, name: "ZR" }) ||
      this.dataInfo.marryCode; //是否婚姻
    const familyMemberCode =
      this.getListData({ index: this.indexJT, name: "JT" }) ||
      this.dataInfo.familyMemberCode; //家庭成员关系
    const nativeCode =
      this.getListData({ index: this.indexAF, name: "AF" }) ||
      this.dataInfo.nativeCode; //户籍
    const jobCode =
      this.getListData({ index: this.indexAG, name: "AG" }) ||
      this.dataInfo.jobCode; //职业
    const nationCode =
      this.getListData({ index: this.indexAC, name: "AC" }) ||
      this.dataInfo.nationCode; //民族
    const educationCode =
      this.getListData({ index: this.indexZC, name: "ZC" }) ||
      this.dataInfo.educationCode; //文化程度

    // const { postalCode, idCardAddress, address } = this;
    const postalCode = this.postalCode || this.dataInfo.postalCode;
    const idCardAddress = this.idCardAddress || this.dataInfo.idCardAddress;
    const address = this.address || this.dataInfo.address;
    const areaCode = this.areaCode || this.dataInfo.areaCode;
    const areaName = this.areaName || this.dataInfo.areaName;
    //  patientAddress, patCardNo,
    let {
      patientName,
      idNo,
      birthday,
      patientMobile,
      isNewCard,
      isNoCard,
      idTypesIdx,
      patCardsIdx,
      patSex,
      isOtherType,
    } = this;
    const idType = this.idType;
    const patCardType =
      this.isNewCard == 1 ? "21" : this.hisConfig.patCards[0].dictKey;
    if(!isOtherType){
      patSex = getSexByIdCard(idNo || "");
    }
    const grid = this.dataInfo.grid; // 卡号

    return {
      patientName,
      idNo,
      grid,
      patSex,
      idType,
      birthday,
      patientMobile,
      relationType: "5",
      patientType: "0",
      patCardType,
      isNewCard,
      isNoCard,

      marryCode,
      familyMemberCode,
      nativeCode,
      jobCode,
      nationCode,
      educationCode,

      postalCode,
      idCardAddress,
      address,
      areaCode: areaCode ? areaCode.join('-') : '',
      areaName: areaName ? areaName.join('-') : '',
    };
  }
  async getHisConfig() {
    console.log("test, getHisConfig");
    const { code, data = {}, msg } = await Api.getHisConfig();
    // data.idTypes = this.hisConfig.idTypes;
    // this.idTypesIdx = data.idTypes.findIndex(item=>item.dictKey == this.idType) || 0;
    this.hisConfig = data;
    console.log('hisConfig',data)
    // if(code == 0){
    //   if(data.idTypes) {
    //     data.idTypes = data.idTypes.slice(0, 6); //最多6项
    //     data.idTypes = data.idTypes.map((v) => {
    //       if(v.dictKey == 1){
    //         v.regexp = validator.idCard,
    //         v.errTip = "请输入18位身份证";
    //         v.inputType = 'idcard';
    //       }
    //       return v;
    //     });
    //   }
    //   this.hisConfig = data;
    //   // console.log('hisConfig', this.hisConfig.idType)
    this.$apply();
    // }
  }

  getMemberInfoList() {
    // 获取家庭成员信息
    const { memberList = [], hisConfig } = this;
    if (memberList.length == 0) {
      return [];
    }
    console.log("getMemberInfoList");
    const memberInfoList = memberList.map(item => {
      const {
        name = "",
        idNo = "",
        patientMobile = "",
        memberName = "",
        familyMemberCode = "",
        idTypesIdx = 0,
        patSex = "",
        birthday = ""
      } = item;
      return {
        patientName: name,
        patName: name,
        idNo,
        patientMobile,
        familyMemberName: memberName,
        familyMemberCode: familyMemberCode,
        patSex: idTypesIdx === 0 ? getSexByIdCard(idNo || "") : patSex,
        birthday:
          idTypesIdx === 0 ? getBirthdayByIdCard(idNo || "", "-") : birthday,
        idType: hisConfig.idTypes[idTypesIdx].dictKey
      };
    });
    return memberInfoList;
  }
  getSpouseInfo() {
    // 获取配偶信息
    const {
      spouseName,
      spouseIdNo,
      spouseBirthday,
      spousePhone,
      marryCode,
      familyMemberCode,
      hisConfig
    } = this;
    const { idTypesIdx = 0 } = this.spouseInfo || {};
    return {
      // ...this.spouseInfo,
      patientName: spouseName,
      patName: spouseName,
      idNo: spouseIdNo,
      patientMobile: spousePhone,
      marryCode: "ZR01",
      familyMemberCode: familyMemberCode === "JT01" ? "JT02" : "JT01",
      patSex:
        idTypesIdx === 0
          ? getSexByIdCard(spouseIdNo || "")
          : familyMemberCode === "JT01"
            ? "男"
            : "女",
      birthday:
        idTypesIdx === 0
          ? getBirthdayByIdCard(spouseIdNo || "", "-")
          : spouseBirthday,
      idType: hisConfig.idTypes[idTypesIdx].dictKey
    };
  }

  computed = {};

  methods = {
    bindRegionChange(e) {
      console.log(e.detail)  
      // code=["110000", "110100", "110105"],value=["北京市", "北京市", "朝阳区"]
      if (e.detail.value) {
        this.dataInfo.areaCode = e.detail.code;
        this.dataInfo.areaName = e.detail.value;
      }
    },
    actionSheetType(type, e) {
      if (type === "spouse" && this.spouseInfo.idNo) {
        // 不允许切换配偶身份证类型
        wepy.showModal({
          title: "温馨提示",
          content: "不能切换身份证类型",
          showCancel: false
        });
        return false;
      }
      console.log("type", type);
      const { prop = "" } = e.target.dataset || {};
      if (prop) {
        const listData = this.hisConfig[prop] || [];
        wepy
          .showActionSheet({
            itemList: listData.map(v => v.dictValue)
          })
          .then(res => {
            const idxKey = `${prop}Idx`;
            if (prop === "idTypes") {
              if (type === "spouse") {
                this.spouseInfo[idxKey] = res.tapIndex;
              } else {
                this.memberList[type][idxKey] = res.tapIndex;
              }
            } else {
              this[idxKey] = res.tapIndex;
            }
            this.$apply();
          });
      }
    },
    changeIdType(type, e){
      if (type === "spouse" && this.spouseInfo.idNo) {
        // 不允许切换配偶身份证类型
        wepy.showModal({
          title: "温馨提示",
          content: "不能切换身份证类型",
          showCancel: false
        });
        return false;
      }
      console.log('e',e)
      const { prop = "" } = e.target.dataset || {};
      if (prop) {
        const idxKey = `${prop}Idx`;
        const { value = '' } = e.detail;
        if (prop === "idTypes") {
          if (type === "spouse") {
            this.spouseInfo[idxKey] = value;
          } else {
            this.memberList[type][idxKey] = value;
          }
        } else {
          this[idxKey] = value;
        }
      }
    },
    inputTrigger(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      console.log("inputTrigger", id, value);
      this[id] = value;
      this.dataInfo[id] = value;
      this.$apply();
    },
    pickerChange(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      console.log("pickerChange", id, value);
      this[`index${id}`] = value;
      if (id === "ZR") {
        this.marryCode = this.getListData({
          index: e.detail.value,
          name: "ZR"
        });
      } else if (id === "JT") {
        this.familyMemberCode = this.getListData({
          index: e.detail.value,
          name: "JT"
        });
      }
      this.$apply();
    },
    inputTriggerMember(e) {
      const { id, idx = 0 } = e.target.dataset || {};
      const { value } = e.detail;
      console.log("value", value);
      this.memberList[idx][id] = value;
      this.$apply();
    },
    bindDateChange: function(e) {
      this.partdate = e.detail.value;
    },
    removeMemberItem(index = 0) {
      // 去除家庭成员
      let { memberList = [] } = this;
      memberList.splice(index, 1);
      this.memberList = memberList;
      console.log(memberList);
      this.$apply();
    },
    bindMemberCode(item = {}, e) {
      const { index = 0 } = item;
      const familyMemberCodeIndex = e.detail.value || 0;
      const { memberList = [], dict = {} } = this;
      const { JT: { itemList: [items = []] = [] } = {} } = dict;
      const { dictCode: familyMemberCode, dictName: familyMemberName, pyCode } =
        items.items[Number(familyMemberCodeIndex)] || {};
      if (memberList[index]) {
        memberList[index].familyMemberCodeIndex = familyMemberCodeIndex;
        memberList[index].familyMemberName = familyMemberName;
        memberList[index].familyMemberCode = familyMemberCode;
        memberList[index].memberName = familyMemberName;
      }
      this.memberList = memberList;
      this.$apply();
    },
    bindMemberName(item = {}, e) {
      const { index = 0 } = item;
      const name = e.detail.value;
      const { memberList = [] } = this;
      if (memberList[index]) {
        memberList[index].name = name;
      }
      this.memberList = memberList;
      this.$apply();
    },
    bindMemberIdNo(item = {}, e) {
      const { index = 0 } = item;
      const idNo = e.detail.value;
      const { memberList = [] } = this;
      if (memberList[index]) {
        memberList[index].idNo = idNo;
      }
      this.memberList = memberList;
      this.$apply();
    },
    bindMemberPhoneNo(item = {}, e) {
      const { index = 0 } = item;
      const phoneNo = e.detail.value;
      const { memberList = [] } = this;
      if (memberList[index]) {
        memberList[index].patientMobile = phoneNo;
      }
      this.memberList = memberList;
      this.$apply();
      console.log("memberList", memberList);
    },
    async formSubmit() {
      const value = this.getFormData();
      console.log("getFormData=", value);
      this.hasErr = this.validator();
      if(this.hasErr){
        return false;
      }

      const memberList = this.getMemberInfoList() || [];
      const spouseInfo = this.getSpouseInfo();
      const { JSEncrypt } = WxmpRsa;
      // this.publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDMT8oFAAZI6Z1Gsxir0DyGsjNC0q+s6oV6RGKr+ooZWMMkrXafb3Cvpo35f13S8eXkrdzui44iVoXKY3OiUJPlalwSW3/FmCEZQSZM4FqQbRXtKFnmrygxA8zUiewOuAbjUrLXAPr4BmDcEcc3snyluuL67jIS8eWioXgL9x7g0wIDAQAB';

      WxmpRsa.JSEncrypt.prototype.setPublicKey(this.publicKey);
      spouseInfo.patientMobile = WxmpRsa.JSEncrypt.prototype.encrypt(spouseInfo.patientMobile);
      value.patientMobile = WxmpRsa.JSEncrypt.prototype.encrypt(value.patientMobile);
      
      if ((this.indexJT == 0 || this.indexJT == 1) && this.indexZR == 1) {
        // 校验配偶身份信息
        memberList.unshift(spouseInfo);
      }
      let validateData = { ret: true };
      console.log(value);
      if (!value.familyMemberCode || !value.marryCode) {
        validateData.ret = false;
        validateData.tip = "请选择家庭成员关系,婚姻状况";
      }
      if (validateData.ret && memberList.length > 0) {
        validateData = this.validateMember(memberList);
      }
      if (!validateData.ret) {
        // 校验不通过
        console.log("validateData", validateData);
        this.toptip = validateData.tip || "";
        const errTimer = setTimeout(() => {
          this.toptip = "";
          this.$apply();
          clearTimeout(errTimer);
        }, 2000);
        return;
      }
      const list = {
        row: [value, ...memberList]
      };
      const relationType = this.qryType == 1 ? 1 : 5;

      const param = {
        info: JSON.stringify({ list, isScan: this.isScan }),
        relationType,
        isNewCard: this.isNewCard,
        isNoCard: this.isNoCard,
        patientType: 0,
        patientName: value.patientName || "",
        patCardNo: this.dataInfo.grid || "",
        patientMobile: value.patientMobile || "",
        patCardType: 21,
        idType: this.idType,
        idNo: value.idNo,
        patHisNo: this.pid || "",
        patientAddress: this.idCardAddress || "",
        patSex:this.patSex || "",
      };
      console.log('param', param);
      const { code, data = {}, msg } = await Api.bindPatient(param);
      console.log("test", code);
      if (code == 0) {
        if (data.resultCode == '0') {
          wx.showToast({
            title: "完善信息成功",
            icon: "success",
            duration: 2000,
            success: () => {
              wepy.navigateBack({
                delta: 1
              });
              // const { query } = this.$wxpage.options;
              // wepy.navigateTo({
              //   url: `/pages/register/docinfo/index?${query}`
              // });
            }
          });
        } else {
          wepy.showModal({
            title: '温馨提示',
            content: data.resultMessage || '完善信息失败',
            showCancel: false,
            confirmText: '确定',
            confirmColor: '#3eceb6',
          });
        }
        
      } else {
        wepy.showModal({
          title: '温馨提示',
          content: msg || '完善信息失败',
          showCancel: false,
          confirmText: '确定',
          confirmColor: '#3eceb6',
        });
      }
    }
  };

  events = {};
}
</script>
