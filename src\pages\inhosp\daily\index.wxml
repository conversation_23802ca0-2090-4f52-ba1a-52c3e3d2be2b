<view class="ask">
  <!-- <watermark /> -->
  <view class="ask-tabs">
    <view class="tab-item {{ activeIndex == index ? 'active' : '' }}" wx:for="{{tabs}}" wx:key="{{index}}" @tap="checkThis({{index}})">
      <view class="title">
        <!-- <text class="badge">{{item.num}}</text> -->
        {{item.text}}
      </view>
      <!-- <text class="active-line" wx:if="{{activeIndex == index}}"></text> -->
    </view>
  </view>
  <view class="content" wx:if="{{activeIndex == 0}}">
    <view class="filter">
      <picker mode="date" value="{{beginDate}}" end="{{pickerEndDate}}" bindchange="beginDateChange">
        <view class="item date">
          <!-- <text>日期</text> -->
          <view class="rt">
            <text>{{beginDate}}</text>
            <text class="arrow"></text>
          </view>
        </view>
      </picker>
      <view class="item money">
        <text>总计：</text>
        <text class="val">{{WxsUtils.formatMoney(total.totalDay,100)}}元</text>
      </view>
    </view>
    <view class="tips">左右滑动表格查看更多信息</view>
    <view class="detail-list">
      <view class="table">
        <view class="th">
          <text class="td name">项目名称</text>
          <text class="td price">单价</text>
          <text class="td num">数量</text>
          <text class="td money">金额</text>
          <text class="td type">费用类型</text>
        </view>
        <block wx:for="{{dayList}}" wx:key="{{index}}">
          <view class="tr">
            <text class="td name">{{item.itemName}}</text>
            <text class="td price">{{WxsUtils.formatMoney(item.itemPrice,100)}}</text>
            <text class="td num">{{item.itemNumber+item.itemUnit}}</text>
            <text class="td money">{{WxsUtils.formatMoney(item.itemTotalFee,100)}}</text>
            <text class="td type">{{item.expenseType}}</text>
          </view>
        </block>
      </view>
      <view wx:if="{{dayList.length == 0}}">
        <empty>
          <text slot="text">暂未查询到相关信息</text>
        </empty>
      </view>
    </view>
    <view class="tips">清单仅供参考,如有疑问请与护士站联系,谢谢！</view>
  </view>
  
  <view class="content" wx:if="{{activeIndex == 1}}">
    <view class="filter center">
      <view class="item money">
        <text>总计：</text>
        <text class="val">{{WxsUtils.formatMoney(total.totalAll,100)}}元</text>
      </view>
    </view>
    <view class="tips">左右滑动表格查看更多信息</view>
    <view class="detail-list">
      <view class="table">
        <view class="th">
          <text class="td name">项目名称</text>
          <text class="td price">单价</text>
          <text class="td num">数量</text>
          <text class="td money">金额</text>
          <text class="td type">费用类型</text>
        </view>
        <block wx:for="{{totalList}}" wx:key="{{index}}">
          <view class="tr">
            <text class="td name">{{item.itemName}}</text>
            <text class="td price">{{WxsUtils.formatMoney(item.itemPrice,100)}}</text>
            <text class="td num">{{item.itemNumber+item.itemUnit}}</text>
            <text class="td money">{{WxsUtils.formatMoney(item.itemTotalFee,100)}}</text>
            <text class="td type">{{item.expenseType}}</text>
          </view>
        </block>
      </view>
      <view wx:if="{{totalList.length == 0}}">
        <empty>
          <text slot="text">暂未查询到相关信息</text>
        </empty>
      </view>
    </view>
    <view class="tips">清单仅供参考,如有疑问请与护士站联系,谢谢！</view>
  </view>
</view>