<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '选择院区',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      deptType: '',
      areaList: [
        {
          areaName: '麓谷总院（桐梓坡西路567号）',
          areaCode: 2,
        },
        {
          areaName: '开福区分院（湘雅路88号）',
          areaCode: 1,
        }
      ],
    };

    props = {};

    onLoad(options) {
      this.deptType = options.deptType;
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到科室列表页
       * @param item
       */
      bindGoDept(item){
        const { areaCode = '' } = item;
        wepy.navigateTo({
          url: `/pages/register/deptlist/index?deptType=${this.deptType}&areaCode=${item.areaCode}`,
        });
      },
    };
  }
</script>
