export const apiEnv = "REPLACE_API_ENV"; //编译配置自动替换

export const nodeEnv = "NODE_ENV";

export const IS_PRODUCTION = nodeEnv === 'production';

export const DOMAIN =
  apiEnv == "u" || apiEnv == "s"
    ? "https://wechatdev.jiahuiyiyuan.com"
    : "https://wechat.jiahuiyiyuan.com";
    // ? "https://wechat.jiahuiyiyuan.com"
    // : "https://wechat.jiahuiyiyuan.com";
    // https://wechat.jiahuiyiyuan.com 正式域名
/**
 * 支付域名
 * @type {string}
 */
// export const PAY_DOMAIN = 'https://devhlwyy.zxxyyy.cn'; // 测试域名
export const PAY_DOMAIN =
  apiEnv == "u" || apiEnv == "s"
    ? "https://wechatdev.jiahuiyiyuan.com"
    : "https://wechat.jiahuiyiyuan.com";
    // ? "https://wechat.jiahuiyiyuan.com"
    // : "https://wechat.jiahuiyiyuan.com";

/**
 * 平台id
 * @type {{platformId: number}}
 */
export const REQUEST_QUERY = {
  hisId: 242,     // 医院id
  platformId: 242,//平台ID
  platformSource: 3,  // 渠道来源
  subSource: 1,      // 子渠道
};

/**
 * 重发配置
 * @type {{YYGH: boolean, DBGH: boolean, ZYYJBJ: boolean, MZJF: boolean, YFKCZ: boolean, YYQH: boolean}}
 */
export const RETRY_MAP = {
  YYGH: true, // 预约挂号
  DBGH: true, // 当班挂号
  ZYYJBJ: true, // 住院押金补缴
  SCYJJN: true, // 住院押金补缴
  MZJF: true, // 门诊缴费
  YFKCZ: true, // 预付卡充值
  YYQH: true, // 预约取号
  YPJS: true, // 药品寄送
  DJJF: true, //冻精缴费
  BLFY: true, //冻精缴费
  SMKD: true, //扫码开单
};

/**
 * 详情页电话号码
 * @type {string}
 */
export const PHONE_NUMBER = "************";

/**
 * 前后端业务类型映射关系
 * @type {{DBGH: string, YYGH: string, YYQH: string, MZJF: string, YFKCZ: string, ZYYJBJ: string}}
 */
export const TYPE_MAP = {
  DBGH: "current_register",
  YYGH: "appointment_register",
  YYQH: "take_register",
  MZJF: "outpatient",
  YFKCZ: "patcard_recharge",
  ZYYJBJ: "inpatient",
  SCYJJN: "inpatient",
  YPJS: "medical_send",
  SZZXJF: "reproductive_center",
  DJJF: "frozen_sperm",
  BLFY: "medical_copy",
  ZZKD: "selfhelp_order",
  VIP: 'pay_consult',
  SMKD: 'dna_gene_pay',
};

/**
 * 是否有卡住院
 * @type {boolean}
 */
export const INPATIENT_HAS_CARD = false;

/**
 * 门诊缴费订单详情是否多级药品
 * @type {boolean}
 */
export const IS_SEC_ITEM = true;

/**
 * 是否支持门诊合并支付
 * @type {boolean}
 */
export const TREAT_MERGE_PAY = false;

/**
 * 是否分院模式
 * @type {boolean}
 */
export const IS_SUB_HIS = false;

/**
 * 预约挂号是否付费
 * @type {boolean}
 */
export const APPOINTMENT_REG_PAY = true;

/**
 * 当班挂号是否付费
 * @type {boolean}
 */
export const TODAY_REG_PAY = true;

/**
 * 一维码类型  code39 || code128
 * @type {string}
 */
export const CODE_TYPE = 'code128';


/**
 * 前后端时间代码对应表
 * @type {string}
 */
export const TIME_MAP = {
  0: null,
  1: "上午",
  2: "下午",
  3: "晚上",
  4: "白天",
  5: "全天",
};

/**
 * 是否显示结束时间
 * @type {string}
 */
export const IS_NEED_ENDTIME = true;

export const PRIMARY_COLOR = "#2D666F";

/**
 * 输入框聚焦时边框与键盘的距离
 * @type {string}
 */
export const CURSOR_SPACING = "100";

/**
 * 儿童与成人的分界年龄(岁)
 * @type {string}
 */
export const CHILD_MAX_AGE = 15;

/**
 * 是否支持添加卡券
 * @type {boolean}
 */
export const ADD_CARD = true;

//前置机接口的合作密钥
export const PARTNERKEY = "front242";

/**
 * 问卷ID常量
 * @type {string}
 */
export const ADULT_SURVEY_ID = "82"; // 成人版问卷ID
export const CHILD_SURVEY_ID = "82"; // 儿童版问卷ID，目前与成人版相同，可根据需要修改
