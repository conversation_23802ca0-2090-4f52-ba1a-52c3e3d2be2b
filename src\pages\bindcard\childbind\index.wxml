<message message="本院实行实名制就诊，请如实填写就诊人信息，如因信息错误产生的一切后果自行负责。" />
<form bindsubmit="formSubmit" report-submit='true'>
  <view class="bindcard-list">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">儿童姓名</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.patientName ? 'o-error' : ''}}" placeholder="请输入儿童姓名" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.patientName ? errorColor : placeholderColor}}" maxlength="8" id="patientName"
          value="{{patientName}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <view class="list-title">儿童身份证</view>
      </view>
      <view class="listitem-body">
        <view class="m-content">
          <radio-group @change="changeProp" data-prop="hasIdCard">
            <label class="binduser-radio">
              <radio value="1" checked class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">有</text>
            </label>
            <label class="binduser-radio">
              <radio value="0" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">无</text>
            </label>
          </radio-group>
        </view>
      </view>
    </view>
    <view class="bindcard-listitem" wx:if="{{hasIdCard == 1}}">
      <view class="listitem-head">
        <text class="list-title"></text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.idNo ? 'o-error' : ''}}" type="idcard" placeholder="请输入儿童身份证号" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.idNo ? errorColor : placeholderColor}}" maxlength="18" id="idNo"
          value="{{idNo}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem {{hasIdCard == 0 ? '' : 'bindcard-listitem_none'}}">
      <view class="listitem-head">
        <view class="list-title">儿童性别</view>
      </view>
      <view class="listitem-body">
        <view class="m-content">
          <radio-group @change="changeProp" data-prop="sex">
            <label class="binduser-radio">
              <radio value="M" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">男</text>
            </label>
            <label class="binduser-radio">
              <radio value="F" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">女</text>
            </label>
          </radio-group>
        </view>
      </view>
    </view>
    <view class="bindcard-listitem listitem_accest {{hasIdCard == 0 ? '' : 'bindcard-listitem_none'}}">
      <view class="listitem-head">
        <view class="list-title">儿童出生日期</view>
      </view>
      <picker
        class="listitem-body" mode="date"
        @change="changeProp" data-prop="birthday"
      >
        <input
          class="m-content {{errorElement.birthday ? 'o-error' : ''}}" placeholder="请选择出生日期" disabled
          placeholder-style="color:{{errorElement.birthday ? errorColor : placeholderColor}}" value="{{birthday}}"
        />
      </picker>
    </view>
  </view>
  <view class="bindcard-list m-mt-20">
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">监护人姓名</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.parentName ? 'o-error' : ''}}" placeholder="请输入监护人姓名" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.parentName ? errorColor : placeholderColor}}" maxlength="8" id="parentName"
          value="{{parentName}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <view class="list-title {{hisConfig.idTypes.length > 1 ? 'list-title_select' : ''}}"
          @tap="actionSheetType" data-prop="{{hisConfig.idTypes.length > 1 ? 'idTypes' : ''}}"
        >监护人{{hisConfig.idTypes[idTypesIdx].dictValue}}</view>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.parentIdNo ? 'o-error' : ''}}"
          type="{{hisConfig.idTypes[idTypesIdx].inputType || 'text'}}" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder="请输入监护人{{hisConfig.idTypes[idTypesIdx].dictValue}}号" placeholder-style="color:{{errorElement.parentIdNo ? errorColor : placeholderColor}}"
          id="parentIdNo" maxlength="18" value="{{parentIdNo}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">监护人住址</text>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.patientAddress ? 'o-error' : ''}}" type="text" placeholder="请输入住址" maxlength="30" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.patientAddress ? errorColor : placeholderColor}}" id="patientAddress" cursor="60"
          value="{{patientAddress}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
  </view>
  <view class="bindcard-list m-mt-20">
    <view class="bindcard-listitem" wx:if="{{isNewCard == 0}}">
      <view class="listitem-head">
        <view class="list-title">
          儿童就诊卡号
        </view>
      </view>
      <view class="listitem-body">
        <input
          class="m-content {{errorElement.patCardNo ? 'o-error' : ''}}" id="patCardNo" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder="请输入儿童就诊卡号" placeholder-style="color:{{errorElement.patCardNo ? errorColor : placeholderColor}}"
          value="{{patCardNo}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
    <view class="bindcard-listitem">
      <view class="listitem-head">
        <text class="list-title">监护人手机号</text>
      </view>
      <view class="listitem-body">
        <input class="m-content {{errorElement.patientMobile ? 'o-error' : ''}}" type="number" placeholder="请输入手机号码" cursor-spacing="{{CURSOR_SPACING}}"
          placeholder-style="color:{{errorElement.patientMobile ? errorColor : placeholderColor}}" id="patientMobile" maxlength="11"
          value="{{patientMobile}}" @input="inputTrigger" @focus="resetThisError"
        />
      </view>
    </view>
  </view>

  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">立即绑定</button>
  </view>

</form>
<toptip :toptip.sync="toptip" />
