<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '住院缴费记录',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 记录列表
      orderList: []
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'empty': Empty
    };

    props = {};

    onShow(options) {
      this.getOrderList();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到挂号详情页
       * @param item
       */
      bindGoDetail(item){
        const { orderId = '' } = item;
        wepy.navigateTo({
          url: `/pages/inhosp/recorddetail/index?orderId=${orderId}`,
        });
      },
    };

    async getOrderList(word) {
      const { code, data = {} } = await Api.orderList();
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      this.orderList = data.inpatientList || [];
      this.$apply();
    }
  }
</script>
