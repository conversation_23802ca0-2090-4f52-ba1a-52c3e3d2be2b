<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <view class="list-tit">病友信息</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.patientName}}">
        <view class="item-label">姓名</view>
        <view class="item-value">{{detailData.patientName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.pid}}">
        <view class="item-label">PID</view>
        <view class="item-value">{{detailData.pid}}</view>
      </view>
    </view>
  </view>
</template>

<script>
import wepy from "wepy"
import * as Utils from "@/utils/utils"

export default class BasicDetail extends wepy.component {
  data = {
    medicalCopyTypeList: {
      1: "住院病历",
      2: "试管病历",
      3: "人工授精病历"
    }
  }

  components = {}

  props = {
    detailData: {
      type: Object,
      default: {}
    }
  }

  onLoad(options) {}

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {}
}
</script>
