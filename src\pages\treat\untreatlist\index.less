@import "../../../resources/style/mixins";

page {
  height: 100%;
}

.p-page {
  height: 100%;
  padding-bottom: 130rpx;
}

.tips {
  position: relative;
  margin: 40rpx 32rpx;
  font-size: 24rpx;
  color: @hc-color-info;
}

.empty-container {
  // background: #ffffff;
  border-radius: 24rpx;
  margin: 0 32rpx;
}

.m-date {
  position: relative;
  z-index: 3;
  padding: 20rpx 0;
  text-align: center;
  // background-color: #fff;
  // box-shadow: 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.03);
  .date {
    display: inline-block;
    vertical-align: top;
    position: relative;
    padding: 0 32rpx;
    font-size: 32rpx;
    color: @hc-color-title;
    font-weight: 600;
  }
  .arrow {
    position: absolute;
    right: 0;
    top: 50%;
    width: 15rpx;
    height: 15rpx;
    border-right: 4rpx solid #c7c7cc;
    border-bottom: 4rpx solid #c7c7cc;
    transform: translate(0, -75%) rotate(45deg);
  }
}

.m-button {
  width: 100%;
  box-sizing: border-box;
  bottom: 48rpx;
  padding: 0 32rpx;
  margin-bottom: 40rpx;
  &.is-ab {
    position: absolute;
  }

  .pay-btn {
    background-color: @hc-color-primary;
    font-size: 34rpx;
    font-weight: 600;
    text-align: center;
    line-height: 100rpx;
    height: 100rpx;
    border-radius: 16rpx;
    color: @hc-color-white;
  }
}

.date-check {
  // margin-bottom: 20rpx;
}
.info-text {
  padding: 32rpx 24rpx;
  color: #BE8014;
  font-size: 24rpx;
  text-align: center;
  background: #FFFAF1;
}
.wait-tips {
  width: 100%;
  box-sizing: border-box;
  padding: 15rpx 30rpx;
  height: 70rpx;
  // margin: 20rpx 0;
  font-size: 28rpx;
  color: @hc-color-text;
  // background-color: @hc-color-white;
  .tips-icon {
    width: 40rpx;
    height: 40rpx;
    vertical-align: middle;
    box-sizing: border-box;
    margin-right: 25rpx;
  }
  .num {
    color: @hc-color-assist;
  }
}
.m-list {
  margin: 24rpx;
  &.merge {
    margin: 40rpx 32rpx;
  }
  .list-item {
    padding: 32rpx;
    margin-bottom: 12rpx; 
    background-color: @hc-color-white;
    border-radius: 24rpx;
    border-radius: 8rpx;
    box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  }
  .item-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: @hc-color-title;
    font-weight: 600;
    font-size: 36rpx;
    .item-icon {
      width: 32rpx;
      height: 32rpx;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 16rpx;
      image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
      }
    }
  }
  .item-hd {
    position: absolute;
    right: 64rpx;
    &.icon-txt {
      font-size: 28rpx;
      color: @hc-color-title;
      font-weight: 600;
      margin-left: 16rpx;
    }
    image {
      width: 50rpx;
      height: 50rpx;
      vertical-align: top;
    }
  }
  .item-bd {
    flex-direction: column;
    width: 100%;
  }
  .bd-main {
    flex: 1;
  }
  .main-tit {
    word-break: break-all;
    word-wrap: break-word;
    font-size: 36rpx;
    color: @hc-color-title;
    font-weight: 600;
  }
  .main-txt {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }
  .bd-extra {
    font-size: 28rpx;
    font-weight: 600;
    color: #CC8F24;
  }
  .item-ft {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }
}

.m-pay {
  display: flex;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9;
  width: 100%;
  // height: 112rpx;
  align-items: center;
  background: #ffffff;
  box-shadow: 0px -3rpx 24rpx rgba(10, 83, 61, 0.0741803);
  border-radius: 32rpx 32rpx 0px 0px;
  padding: 32rpx;
  box-sizing: border-box;

  .pay-main {
    display: flex;
    align-items: center;
    flex: 1;
    background: #fdf8ea;
    border-radius: 44rpx;
    padding: 0 32rpx;
    height: 88rpx;
    .all-check {
      padding-right: 20rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      font-weight: 600;
      image {
        width: 30rpx;
        height: 30rpx;
        margin-right: 16rpx;
      }
    }
  }

  .main-txt {
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
  }

  .main-num {
    color: @hc-color-assist;
    font-size: 28rpx;
  }

  .pay-btn {
    line-height: 88rpx;
    padding: 0 80rpx;
    position: absolute;
    z-index: 9;
    width: 88rpx;
    height: 88rpx;
    border-radius: 0px 44rpx 44rpx 0px;
    background-color: transparent;
    background-image: linear-gradient(
      105deg,
      transparent 0%,
      transparent 10%,
      @hc-color-primary 10%,
      @hc-color-primary 100%
    );
    border: none;
    right: 32rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: @hc-color-white;

    &.active {
      // background-color: @hc-color-primary;
    }

    .split-line {
      position: absolute;
      z-index: 11;
    }
  }
}
