@import "../../resources/style/mixins";

.wgt-user-box {
  position: relative;
  z-index: 1;
  padding: 30rpx;
  background-color: #fff;
  // box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.05);
}

.kdbtn {
  color: #3ECEB6;
  border: 1px solid #3ECEB6;
  padding: 5px;
  font-size: 14px;
  border-radius: 5px;
  margin: 20px 10px 0px 10px;
}

.wgt-user-main {
  display: flex;
  align-items: center;
}

.wgt-user-main-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.wgt-user-main-info-tit {
  // font-size: 37rpx;
  margin-right: 16rpx;
  font-weight: 600;
  font-size: 40rpx;
  line-height: 96rpx;
  color: @hc-color-title;
}
.wgt-change-btn{
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  border: 2rpx solid #2D666F;
  color: #2D666F;
  font-size: 24rpx;
  font-weight: 500;
}

.wgt-user-main-info-label {
  margin-left: 20rpx;
  flex: 1;
  color: @hc-color-text;
  font-size: 28rpx;
}

.wgt-user-main-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 40rpx;
  color: rgba(0, 0, 0, 0.7);
  font-size: 28rpx;
  padding: 12rpx 16rpx;

  .wgt-user-pop-title-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 16rpx;
  }
}

.registerStyle {
  margin: 20px  20px 0px 0px  !important;
}

.wgt-user-extra {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.40);
  .textBreak();
}

//就诊人切换弹窗，需要抽离组件出去
.wgt-user-pop-box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -999;
  visibility: hidden;
  transform: translateY(-1000%);
}

.wgt-user-pop-mask {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background 0.3s;
}

.wgt-user-pop {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  max-height: 90%;
  overflow-y: auto;
  background-color: #fff;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  transform: translateY(100%);
  transition: transform 0.3s 0.1s;
  border-radius: 8rpx 8rpx 0 0;
}

&.active {
  z-index: 999;
  visibility: visible;
  transform: translateY(0);

  .wgt-user-pop-mask {
    background-color: rgba(0, 0, 0, 0.6);
  }

  .wgt-user-pop {
    transform: translateY(0%);
  }
}

.wgt-user-pop-title {
  padding: 32rpx 24rpx;
  font-size: 34rpx;
  font-weight: 600;
  color: @hc-color-title;
  text-align: left;
}

.wgt-user-pop-list {
  margin: 24rpx;
}

.wgt-user-pop-list-item {
  position: relative;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;

  &.active {
    border: none;
    background: var(--linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
  }
}

.wgt-user-pop-list-item+.wgt-user-pop-list-item {
  margin-top: 32rpx;
}

.wgt-user-pop-list-item-main {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.wgt-user-pop-list-item-name {
  font-size: 36rpx;
  font-weight: 600;
  color: @hc-color-title;

  &.active {
    color: #fff;
  }
}

.wgt-user-pop-list-item-label {
  margin-left: 20rpx;
}

.wgt-user-pop-list-item-num {
  margin-top: 10rpx;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.70);
  .textBreak();

  &.active {
    color: #fff;
  }
}

.wgt-user-pop-list-item-ipt {
  display: none;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 38rpx;
  height: 27rpx;

  image {
    width: 100%;
    height: 100%;
    vertical-align: top;
  }

  &.active {
    display: block;
  }
}

.wgt-user-pop-opt {
  display: flex;
  // flex-direction: row;
  align-items: center;
  padding: 20rpx 32rpx;
  flex-direction: column;
  // border-top:2rpx solid @hc-color-border;
}

.wgt-user-pop-opt-item {
  font-size: 34rpx;
  text-align: center;
  flex: 1;
  // color:@hc-color-primary;
  // border-left:2rpx solid @hc-color-border;

  border-radius: 76rpx;
  background: var(--linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
  color: #fff;
  padding: 24rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
  margin: 16rpx;
  font-weight: 600;

  &:first-child {
    border-left: none;
  }
}

.wgt-user-pop-list-item {
  &.active {
    .wgt-user-pop-list-item-ipt {
      display: block;
    }
  }
}

.wgt-user-pop-close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 30rpx;
  width: 36rpx;
  height: 36rpx;
  background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/common/close.png") no-repeat 50% 50%;
  background-size: 36rpx 36rpx;
  z-index: 9;
}