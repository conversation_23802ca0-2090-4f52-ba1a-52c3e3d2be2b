<block wx:if="{{config.show}}">
  <view class="wgt-errmodal-ct">
    <view class="wgt-errmodal">
      <view class="wgt-errmodal-bd">
        <view class="wgt-errmodal-icon">
          <image
            mode="widthFix"
            src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/fail.png" alt=""
          ></image>
        </view>
        <view class="wgt-errmodal-title">
          <slot name="title">出错啦~</slot>
        </view>
        <view class="wgt-errmodal-text">
          <view class="wgt-errmodal-text-item">
            <slot name="text"></slot>
          </view>
        </view>
      </view>
      <view class="wgt-errmodal-ft">
        <view
          class="wgt-errmodal-btn"
          @tap="bindTapOk"
        >确定
        </view>
      </view>
    </view>
    <view
      class="wgt-errmodal-mark"
      @touchmove.stop="bindTouchMoveMask"
    />
  </view>
</block>


<!--import errModal from '@/components/errmodal/index'-->

<!--// 错误弹窗配置-->
<!--errModalConfig: {-->
<!--show: false,-->
<!--}-->

<!--调用示例-->
<!--<err-modal :config.sync="errModalConfig">-->
  <!--<block slot="title">系统错误</block>-->
  <!--<block slot="text">我也不知道怎么就不行了~</block>-->
<!--</err-modal>-->
