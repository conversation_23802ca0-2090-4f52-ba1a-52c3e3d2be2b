<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Api from "./api";
export default class Agreement extends wepy.page {
  config = {
    navigationBarTitleText: "",
    navigationBarBackgroundColor: '#fff',
  };
  data = {
    projectInfoData: {},
    isChecked: false,
    pid: "",
    name: "",
    title: "",
    projectId: ""
  };

  onLoad(options) {
    wx.setNavigationBarTitle({
      title: options.title
    });
    this.title = options.title;
    this.pid = options.pid;
    this.name = options.name;
    this.projectId = options.id;
  }
  onShow() {
    wepy.removeStorageSync("ouContent");
    const { id = "" } = this.$wxpage.options;
    this.getProjectInfo(id);
  }
  methods = {
    checkboxChange(e) {
      this.isChecked = !this.isChecked;
    },
    onApply() {
      const { otherIntroduce } = this.projectInfoData;
      if (!this.isChecked) {
        return;
      }
      if (this.projectInfoData.ouContent) {
        wepy.setStorageSync("ouContent", this.projectInfoData.ouContent);
      }
      wx.navigateTo({
        url: `/pages/pregnancy/material/index?title=${this.title}&pid=${
          this.pid
        }&name=${this.name}&otherIntroduce=${
          this.projectInfoData.otherIntroduce
        }&projectId=${this.projectId}`
      });
    }
  };

  async getProjectInfo(id) {
    const { code, data } = await Api.getInfoproject({ id });
    if (code === 0) {
      this.projectInfoData = data;
      this.$apply();
    }
  }
}
</script>


