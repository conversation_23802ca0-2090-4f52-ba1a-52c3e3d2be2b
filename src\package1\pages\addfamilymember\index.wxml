<view class="container">
  <view class="patInfo-card">
    <view class="patInfo-card-top">
      <view class="patInfo-card-info">
        <view class="info-name">{{ bindPatientName }}</view>
      </view>
      <view class="patInfo-card-other">
        {{ dataInfo.patSex }}<text class="birthday-box" wx:if="{{ dataInfo.birthday }}">{{ dataInfo.birthday }}</text>证件号:{{ dataInfo.idNo || idNo }}
      </view>
      <!-- <view class="patInfo-card-other">手机号码：{{ patientMobile }}</view> -->
    </view>
  </view>
  <form bindsubmit="formSubmit" report-submit='true'>
   
    <view class="form-container">
      <block wx:for="{{memberList}}" wx:for-item="itm" wx:for-index="idx" wx:key="idx">
        <view class="patInfo-part">
          <text class="list-title">家庭成员信息</text>
        </view>
        <view class="patInfo-list">
          <view class="patInfo-listitem">
            <view class="listitem-head">
              <text class="list-title require">成员关系</text>
            </view>
            <view class="listitem-body">
              <picker bindchange="bindMemberCode" value="{{memberIndex}}" range="{{famArrList}}" range-key="familyTypeName">
                <view class="picker">
                  <input class="picker-info" type="text" disabled value="{{famArrList[memberIndex].familyTypeName}}"
                    placeholder-style="{{ itm.familyMemberName ? '': ''}}"
                    placeholder="请选择成员关系" />
                  <view class="item-arrow"></view>
                </view>
              </picker>
            </view>
          </view>
          <view class="patInfo-listitem">
            <view class="listitem-head">
              <text class="list-title require">成员姓名</text>
            </view>
            <view class="listitem-body">
              <input @input="inputTriggerMember" data-id="name" data-index="{{idx}}" type="text" placeholder-style="" placeholder="请输入成员姓名" maxlength="20" />
            </view>
          </view>
          <view class="patInfo-listitem">
            <block wx:if="{{isOtherType == '1'}}">
              <picker class="listitem-head" range="{{hisConfig.idTypes}}" range-key="dictValue" bindchange="changeIdType('{{idx}}')" style="display: block;" data-prop="{{hisConfig.idTypes.length > 1 ? 'idTypes' : ''}}">
                <!-- <text class="list-title">身份证号码</text> -->
                <!-- 身份证 -->
                <view class="list-title require {{hisConfig.idTypes.length > 1 ? 'list-title_select' : ''}}">{{hisConfig.idTypes[itm.idTypesIdx || 0].dictValue}}</view>
              </picker>
            </block>
            <block wx:if="{{isOtherType == '0'}}">
              <view class="listitem-head">
                <text class="list-title require">成员证件号码</text>
              </view>
            </block>
            <view class="listitem-body">
              <input  @input="inputTriggerMember" data-id="idNo" maxlength="{{hisConfig.idTypes[itm.idTypesIdx].dictKey == 1 ? 18 : -1 }}" data-index="{{idx}}" placeholder-style="" type="idcard" placeholder="请输入成员证件号码" />
            </view>
          </view>
          <block wx:if="{{itm.idTypesIdx > 0}}">
            <view class="patInfo-listitem">
              <view class="listitem-head">
                <view class="list-title require">成员性别</view>
              </view>
              <view class="listitem-body">
                <view class="m-content">
                  <radio-group  @change="inputTriggerMember" data-id="patSex" data-index="{{idx}}">
                    <label class="binduser-radio">
                      <radio value="男" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">男</text>
                    </label>
                    <label class="binduser-radio">
                      <radio value="女" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">女</text>
                    </label>
                  </radio-group>
                </view>
              </view>
            </view>
            <view class="patInfo-listitem">
              <view class="listitem-head">
                <view class="list-title require">成员出生日期</view>
              </view>
              <picker
                class="listitem-body" mode="date"
                @change="inputTriggerMember" data-id="birthday" data-index="{{idx}}"
              >
                <input
                  class="m-content {{errorElement.birthday ? 'o-error' : ''}}" placeholder="请选择出生日期" disabled
                  placeholder-style="color:{{errorElement.birthday ? errorColor : placeholderColor}}" value="{{itm.birthday}}"
                />
              </picker>
            </view>
          </block>
          <view class="patInfo-listitem no-after">
            <view class="listitem-head">
              <text class="list-title require">手机号码</text>
            </view>
            <view class="listitem-body">
              <input  @input="inputTriggerMember" data-id="patientMobile" type="number" placeholder="请输入成员手机号码" placeholder-style=""
                maxlength="11" />
            </view>
          </view>
          <view class="patInfo-listitem no-after">
            <view class="listitem-head">
              <text class="list-title require">成员地址</text>
            </view>
            <picker mode="region" bindchange="bindRegionChange" value="{{areaCode || []}}" class="area-box">
              <view class="listitem-body" style="color:{{areaCode && areaCode.length > 0?'':'#bbb'}}">
                <block wx:if="{{areaCode && areaCode.length > 0}}">
                  <view class="picker-info">{{areaName[0]}}{{areaName[1]}}{{areaName[2]}}</view>
                </block>
                <block wx:else>
                  <view class="picker-info placeholder-text">请选择所在省市区/县</view>
                </block>
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
          <view class="patInfo-listitem no-after">
            <view class="listitem-head">
              <text class="list-title require">成员详细地址</text>
            </view>
            <view class="listitem-body">
              <input  @input="inputTriggerMember" data-id="patientAddress" placeholder="请输入成员详细地址" placeholder-style=""
              />
            </view>
          </view>
        </view>
      </block>
    </view>

    <view class="patInfo-btn">
      <button class="binduser-btn_line" formType="submit">立即绑定</button>
    </view>
    <view class="patInfo-btn">
      <button class="cancel-btn_line" formType="cancel" @tap="formCancel">取消</button>
    </view>
  </form>

</view>

<toptip :toptip.sync="toptip" />