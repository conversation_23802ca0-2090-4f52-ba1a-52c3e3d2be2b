<!-- <view class="scancard-tips">
  <view class="scancard-tips-content">
    本院实行实名制就诊，请确保您填写的信息与医院登记的信息一致以便完成绑定，病历号（PID号）为您病历本上的号码；新建档时系统也将自动绑定就诊人。
  </view>
  <image src="{{('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/hc-baby-tip.png')}}" mode="aspectFit"
    class="scan-tips-img" />
</view> -->
<message message="本院实行实名制就诊，请确保您填写的信息与医院登记的信息一致以便完成绑定，病历号（PID号）为您病历本上的号码；新建档时系统也将自动绑定就诊人。" />
<view class="scancard-block">
  <view class="scancard-imgbox" @tap="bindSelIdCardPic">
    <image src="{{idCardPath || ('REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/scan-card.png')}}" mode="aspectFit"
      class="scan-img" @load="setImgSize" style="width:{{imgWidth}}rpx;height:{{imgHeight}}rpx;" />
    <view class="re-upload" style="width:{{imgWidth}}rpx;" wx:if="{{idCardPath}}">重新上传</view>
  </view>
  <view class="scan-require">
    <view class="scan-require-title">拍摄要求</view>
    <view class="scan-require-item">1.垂直拍摄，证件边框完整</view>
    <view class="scan-require-item">2.避免证件反光，模糊</view>
  </view>
</view>

<view class="scancard-btnbox">
  <button class="binduser-btn_line" @tap="uploadCardImg">确认上传</button>
  <button class="binduser-btn_line nocard"
    @tap="navigateTo('/pages/bindcard/queryuserinfo/index?isNewCard={{isNewCard}}&isNoCard={{isNoCard}}&qryType={{qryType}}&isScan={{isScan}}')">没带身份证，手输信息</button>
  <view class="other-types" >
    <text @tap="navigateTo('/pages/bindcard/queryothertype/index?isNewCard={{isNewCard}}&isNoCard={{isNoCard}}&qryType={{qryType}}')">非身份证注册登录绑卡入口</text>
  </view>
</view>
