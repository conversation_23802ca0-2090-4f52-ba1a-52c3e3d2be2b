@import "../../resources/style/mixins";

.wgt-navtab {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: @hc-color-white;
  display: flex;
  border-top: 2rpx solid @hc-color-border;
  padding-bottom: env(safe-area-inset-bottom);
}

.wgt-navtab-item {
  flex: 1;
  text-align: center;
  align-items: center;
  height: 110rpx;
  padding-top: 16rpx;
  box-sizing: border-box;
  color: @hc-color-text;
  &.active {
    color: #3F969D;
  }
}
.wgt-navtab-item-icon {
  width: 50rpx;
  height: 50rpx;
  line-height: 0;
  margin: 0 auto;
  image {
    width: 100%;
    height: 100%;
  }
}
.wgt-navtab-item-name {
  font-size: 20rpx;
}
