<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import * as Api from './api';

  export default class Examine extends wepy.page {
    config = {
      navigationBarTitleText: '检验报告详情',
    };

    components = {
    };

    onLoad(options) {
      this.getInfo({reportId: options.reportId, patientId: options.patientId, inspectId: options.reportId, extFields: options.extFields});
    }

    data = {
      info: {},
      items: [],
    };

    methods = {
    };

    events = {
    };
    /**
     * 获取详情
     */
    async getInfo(param) {
      const { code, data = {}, msg } = await Api.getInfo(param);
      this.info = data || {};
      this.items = this.info.items || [];
      this.$apply();
    }
  }
</script>
