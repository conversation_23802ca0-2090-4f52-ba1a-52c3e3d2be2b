<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  export default class Info extends wepy.page {
    config = {
      navigationBarTitleText: '新闻详情',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    onLoad(options) {
      this.videoUrl = options.videoUrl;
      console.log('videoUrl', this.videoUrl);
    }

    /* async getData() {
      const { code, data = {} } = await Api.getData({ articleId: this.articleId });
      if (code == 0) {
        this.artic = data;
        this.$apply();
      }
    } */

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      videoUrl: '',
    };

    loadSuccess(){
      console.log(123);
    };

    loadError(){
      console.log('fail')
    }
  }
</script>
