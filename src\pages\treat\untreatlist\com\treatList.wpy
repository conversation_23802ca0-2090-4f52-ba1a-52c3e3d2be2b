<template lang="wxml">
  <view class="m-list {{mergePay ? 'merge' : ''}}">
    <block
      wx:for="{{treatList}}"
      wx:key="index"
    >
      <view class="list-item">
        <block wx:if="{{mergePay}}">
          <view class="item-hd" @tap="bindToggleCheck({{item}})">
            <block wx:if="{{item.checked}}">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-on.png"></image>
            </block>
            <block wx:else>
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-off.png"></image>
            </block>
          </view>
        </block>
        <block wx:else>
          <view class="item-status" @tap="bindGoDetail({{item}})">
            <!-- <view class="item-icon">
              <image mode="widthFix" src="https://hlwyy.zxxyyy.cn/hospital/his-miniapp/icon/new/others/wait.png"></image>
            </view> -->
            <view>
              <view>{{item.deptName}} | {{item.doctorName}}</view>
              <view class="main-txt">{{item.date}}</view>
            </view>
            <view class="bd-extra">￥{{WxsUtils.formatMoney(item.totalFee,100)}}</view>
          </view>
        </block>
      </view>
    </block>
  </view>
  <view class="m-pay" wx:if="{{mergePay}}">
    <view class="pay-main">
      <view class="all-check" @tap="bindToggleCheckAll">
        <image mode="widthFix" src="REPLACE_STATIC_DOMAIN/his-miniapp/icon/common/{{treatList.length === checkedLength ? 'checked.png' : 'no-checked.png' }}" />
        <text>全选</text>
      </view>
      <view class="main-txt">
        <view>共计：<text class="main-num">{{WxsUtils.formatMoney(totalFee,100)}}</text></view>
      </view>
    </view>
    <view class="split-line"></view>
    <view
      class="pay-btn {{(checkedLength !== 0 && !isSubmit) ? 'active' : ''}}"
      @tap="bindCreateOrder"
    >去缴费
    </view>
  </view>

</template>

<script>
import wepy from "wepy";
import WxsUtils from "../../../../wxs/utils.wxs";
import { TREAT_MERGE_PAY } from "@/config/constant";
import * as Api from "../api";

export default class TreatList extends wepy.component {
  props = {
    treatList: {
      type: Array,
      default: [],
      twoWay: true
    },
    patient: {
      type: Object,
      default: {}
    },
    // 选中数量
    checkedLength: {
      type: Number,
      default: 0,
      twoWay: true
    },
    // 合并金额
    totalFee: {
      type: Number,
      default: 0,
      twoWay: true
    }
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  data = {
    // 合并支付
    mergePay: TREAT_MERGE_PAY,
    isSubmit: false
  };

  onLoad() {
    console.log(this.treatList, '======104')
  }

  methods = {
    bindGoDetail(item = {}) {
      this.goDetail(item);
    },
    bindToggleCheck(item = {}) {
      this.toggleCheck(item);
    },
    bindToggleCheckAll() {
      this.toggleCheckAll();
    },
    bindCreateOrder() {
      this.createOrder();
    }
  };

  /**
   * 跳转至详情页
   * @param item
   */
  goDetail(item = {}) {
    // const { activePatient = {} } = this.patient;
    // const { patientId = '' } = activePatient;
    const { hisOrderNo = "", patientId = "" } = item;
    const {
      isScan = "0",
      patHisNo = "",
      patCardNo = "",
      patCardType = "",
      patientName = "",
    } = this.$parent.$data;
  
    wepy.navigateTo({
      url: `/pages/treat/untreatdetail/index?hisOrderNo=${hisOrderNo}&patientId=${patientId}&isScan=${isScan}&patHisNo=${patHisNo}&patCardNo=${patCardNo}&patCardType=${patCardType}&patientName=${patientName}`
    });
  }

  /**
   * 切换单个选中
   * @param data
   */
  toggleCheck(data = {}) {
    const { treatList = [] } = this;
    let checkedCount = 0;
    let totalFee = 0;
    this.treatList = treatList.map(item => {
      if (data.hisOrderNo === item.hisOrderNo) {
        item.checked = !item.checked;
      }
      if (item.checked) {
        ++checkedCount;
        totalFee += item.totalFee;
      }
      return item;
    });
    this.totalFee = totalFee;
    this.checkedLength = checkedCount;
  }

  /**
   * 切换选中全部
   */
  toggleCheckAll() {
    const { treatList = [], checkedLength } = this;
    const listLength = treatList.length;
    let totalFee = 0;
    this.treatList = treatList.map(item => {
      item.checked = checkedLength !== listLength;
      if (item.checked) {
        totalFee += item.totalFee;
      }
      return item;
    });
    this.totalFee = totalFee;
    this.checkedLength = checkedLength === listLength ? 0 : listLength;
  }

  /**
   * 创建合并订单
   */
  async createOrder() {
    const { treatList = [], checkedLength } = this;
    if (checkedLength <= 0) {
      return false;
    }
    const { activePatient = {} } = this.patient;
    const { patientId = "" } = activePatient;
    let hisOrderNo = [];
    treatList.map(item => {
      if (item.checked) {
        hisOrderNo.push(item.hisOrderNo);
      }
      return item;
    });
    hisOrderNo = hisOrderNo.join(",");
    const param = { patientId, hisOrderNo };

    this.isSubmit = true; // 禁用提交按钮

    const { code, data = {} } = await Api.createOrder(param);
    this.isSubmit = false; // 恢复提交按钮

    if (code !== 0) {
      return;
    }
    const { orderId = "" } = data;
    this.registerPayOrder({ orderId });
  }

  /**
   * 创建支付订单
   * @param item
   */
  async registerPayOrder(item = {}) {
    let bizContent;
    const { orderId = "" } = item;
    try {
      bizContent = JSON.stringify(this.getBizContent() || []);
    } catch (e) {
      bizContent = "[]";
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent
    });
    if (code !== 0) {
      return;
    }
    const { payOrderId = "" } = data;
    wepy.navigateTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=MZJF`
    });
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent() {
    const { activePatient = {} } = this.patient;
    const { patientName = "", patCardNo = "" } = activePatient;
    return [
      { key: "费用类型", value: "门诊缴费" },
      { key: "就诊人", value: patientName },
      { key: "就诊卡号", value: patCardNo }
    ];
  }
}
</script>
