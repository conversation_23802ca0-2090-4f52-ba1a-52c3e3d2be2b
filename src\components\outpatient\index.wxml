<block wx:if="{{config.infoShow}}">
	<view class="wgt-user-box">
		<view class="wgt-user-main">
			<view class="wgt-user-main-info">
				<view class="wgt-user-main-info-tit">{{patient.activePatient.patientName}}</view>
				<view class="wgt-change-btn" @tap="bindShow" wx:if="{{(!config.notShowChangeBtn && change)||config.registerShow}}">切换就诊人</view>
				<view class="wgt-user-main-info-label" />
			</view>
			<!--<view class="wgt-user-main-btn {{config.registerShow? 'registerStyle' : ''}}" @tap="bindShow" wx:if="{{(!config.notShowChangeBtn && change)||config.registerShow}}">
				<image class="wgt-user-pop-title-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/change.png" />
				切换
			</view>-->
			<view class="kdbtn" @tap="getRecordList" wx:if="{{config.notShowChangeBtn}}">
				查询开单记录
			</view>
		</view>
		<view class="wgt-user-extra">就诊卡号：{{patient.activePatient.idNo}}</view>
	</view>
</block>

<view class="wgt-user-pop-box {{config.show ? 'active' : ''}}">
	<view class="wgt-user-pop-mask" @tap="bindClose" />
	<view class="wgt-user-pop">
		<view class="wgt-user-pop-title">切换就诊人</view>
		<view class="wgt-user-pop-list">

			<block wx:for="{{patient.cardList || []}}" wx:key="index">
				<view class="wgt-user-pop-list-item {{item.patientId === patient.activePatient.patientId ? 'active' : ''}}" @tap="bindChangeUser({{item}})">
					<view class="wgt-user-pop-list-item-main">
						<view class="wgt-user-pop-list-item-name {{item.patientId === patient.activePatient.patientId ? 'active' : ''}}">{{item.patientName}}</view>
						<view class="wgt-user-pop-list-item-label" />
					</view>
					<view class="wgt-user-pop-list-item-num {{item.patientId === patient.activePatient.patientId ? 'active' : ''}}">就诊卡号：{{item.idNo}}</view>
					<!-- <view class="wgt-user-pop-list-item-ipt {{item.patientId === patient.activePatient.patientId ? 'active' : ''}}">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/user-icon-checked.png"></image>
          </view> -->
				</view>
			</block>

		</view>
		<view class="wgt-user-pop-opt">
			<block wx:if="{{patient.leftBindNum > 0}}">
				<view class="wgt-user-pop-opt-item" @tap="bindGoBindUser">添加就诊人</view>
			</block>
			<view class="wgt-user-pop-opt-item" @tap="bindGoUserList">管理就诊人</view>
		</view>
		<view class="wgt-user-pop-close" @tap="bindClose" />
	</view>
</view>

