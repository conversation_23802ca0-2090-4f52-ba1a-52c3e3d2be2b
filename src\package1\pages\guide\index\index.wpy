<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";
import DateCheck from "@/components/datecheck/index";
import Outpatient from "@/components/outpatient/index";
import empty from "@/components/empty/index";
export default class GuideIndex extends wepy.page {
  config = {
    navigationBarTitleText: "院内导诊",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    outpatient: Outpatient,
    datecheck: DateCheck,
    empty
  };
  data = {
    options: {},
    // 时间段列表
    dateList: [
      {
        text: "当天",
        day: 0
      },
      {
        text: "三个月内",
        day: -90
      },
      {
        text: "一年内",
        day: -365
      }
    ],
    // 选中的时间段下标
    checkDateIndex: 0,
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 汇总数据
    guideData: []
  };
  onLoad(options) {
    const { patientId = "" } = options;
    this.options = options;
    this.$broadcast("outpatient-get-patient", { patientId });
    // this.getGuideInfo();
  }
  async getGuideInfo() {
    const { patHisNo = "", patCardNo = "" } = this.options;
    if(!patHisNo || !patCardNo){
      return;
    }
    const date = this.dateList[this.checkDateIndex].day;
    const beginDate = Utils.getFormatDate(date, "-");
    const endDate = Utils.getFormatDate(0, "-");
    const { code, data = [], msg } = await Api.getGuideInfo({
      pId: patHisNo,
      grId: patCardNo,
      beginDate: beginDate,
      endDate: endDate
    });
    if (code !== 0) {
      return;
    }
    this.guideData[this.checkDateIndex] = data;
    this.$apply();
  }
  events = {
    "date-change": function() {
      const data = this.guideData[this.checkDateIndex];
      if (!this.guideData[this.checkDateIndex]) {
        this.guideData[this.checkDateIndex] = [];
      }
      this.getGuideInfo();
    },
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient;
        this.outpatientConfig.infoShow = true;
        this.$apply();
      }
      this.getGuideInfo();
    }
  };
  methods = {};
}
</script>



