<view class="p-page">
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{WxsUtils.convertListStatus(item.status)}}"
          @tap="bindGoDetail({{item}})"
        >
          <view class="item-icon">
            <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
          </view>
          <view class="item-main">
            <view class="main-tit">
              <text>{{item.statusName}}</text>
              <text class="unit-label" wx:if="{{item.refundStatus == 1 || item.refundStatus == 2 || item.refundStatus == 3}}">有退款</text>
            </view>
            <view class="main-txt">{{item.patientName}}</view>
          </view>
          <view class="item-extra">
            <view class="extra-tit">￥{{WxsUtils.formatMoney(item.totalFee,100)}}</view>
            <view class="extra-txt">{{item.orderTime}}</view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>