import { post } from '@/utils/request';
import { REQUEST_QUERY } from "@/config/constant";

export const orderList = (param) => post('/api/user/myoutpatientrecords', param);

export const orderListPay = (param) =>
  post(
  `/api/customize/getPayInfo?_route=h${REQUEST_QUERY.platformId}
  &cardNo=${param.cardNo}
  &patName=${param.patName}
  &parterId=${param.parterId}
  &passwd=${param.passwd}`,
  param
);



