<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';

  export default class DeptList extends wepy.component {
    props = {
      deptList: {
        type: Object,
        default: {},
        twoWay: true,
      },
      favoriteList: {
        type: Array,
        default: [],
        twoWay: true,
      },
      hisDoctorList: {
        type: Array,
        default: [],
        twoWay: true,
      },
      activeIdx: {
        type: Number,
        default: '',
        twoWay: true,
      },
      showHistory: {
        type: Boolean,
        default: false,
      }
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      tagopen: false,
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      bindChangeIndex(index, item){
        this.activeIdx = index;
        // if(index == -1){
        // }else{
        //   this.$emit('deptlist-tap-dept', item);
        // }
      },
      bindTapDoctor(item){
        this.$emit('deptlist-tap-doctor', item);
      },
      bindTapDept(item){
        this.$emit('deptlist-tap-dept', item);
      },
      tagOpen(){
        this.tagopen = !this.tagopen;
      }
    };
  }
</script>
