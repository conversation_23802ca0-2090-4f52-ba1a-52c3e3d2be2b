@import "../../../resources/style/mixins";

page {
  height: 100vh;
  background: #fff;
}

.p-page {
  height: 100%;
}

.m-btn {
  flex: 1;
  margin: 0 30rpx;
  font-size: 36rpx;
  .tips-title {
    color: rgba(0, 0, 0, 0.90);
    font-size: 40rpx;
    margin-bottom: 8rpx;
    text-align: center;
  }
  .tips {
    font-size: 30rpx;
    color: rgba(0, 0, 0, 0.70);
    margin-bottom: 144rpx;
    text-align: center;
  }
  .img-box {
    padding: 180rpx 0 40rpx;
    display: flex;
    justify-content: center;
    .btn-img {
      width: 160rpx;
      height: 160rpx;
    }
  }
  .btn {
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    color: #fff;
    font-size: 34rpx;
    font-weight: 600;
    border-radius: 76rpx;
    &::after {
      border: none;
    }
  }
  .cancel-btn {
    font-size: 34rpx;
    margin-top: 24rpx;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.7);
    border-radius: 76rpx;
    &::after {
      border: none;
    }
  }
}
