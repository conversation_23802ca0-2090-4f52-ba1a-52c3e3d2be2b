@import "../../../resources/style/mixins.less";
page {
  background-color: #f6f7f9;
  min-height: 100vh;
}

.guide-service-page {
  height: 100%;

  .guide-service-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 32rpx 40rpx;

    .guide-service-card-head {
      display: flex;
      align-items: center;
      width: 100%;
      height: 156rpx;
      font-weight: 600;
      font-size: 48rpx;
      line-height: 72rpx;
      color: #ffffff;
      text-indent: 57rpx;
      image {
        width: 100%;
        height: 156rpx;
        background: #3986ff;
        border-radius: 24rpx 24rpx 0 0;
      }
      .text {
        position: absolute;
        width: 70%;
        height: 156rpx;
        border-radius: 24rpx 0 0 0;
        line-height: 156rpx;
        &.bg1 {
          background: linear-gradient(
            90deg,
            #3986ff 23.42%,
            rgba(57, 134, 255, 0) 88.34%
          );
        }
        &.bg2 {
          background: linear-gradient(
            90deg,
            #3eceb6 23.42%,
            rgba(62, 206, 182, 0) 88.34%
          );
        }
      }
    }

    .guide-service-card-list {
      padding: 60rpx 40rpx 20rpx;

      .guide-service-card-list-item {
        margin-bottom: 40rpx;
        border-radius: 24rpx;
        text-align: center;
        color: rgba(0, 0, 0, 0.9);
        padding: 24rpx 32rpx;
        &.bg1 {
          background: rgba(57, 134, 255, 0.1);
        }
        &.bg2 {
          background: rgba(62, 206, 182, 0.1);
        }
      }
    }
  }
}
