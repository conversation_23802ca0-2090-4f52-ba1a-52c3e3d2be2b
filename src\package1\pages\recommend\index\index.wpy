<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import * as Api from "./api";

export default class ReportList extends wepy.page {
  config = {
    navigationBarTitleText: "我要推荐",
    navigationBarBackgroundColor: '#fff',
  };

  components = {
    outpatient: Outpatient,
    empty: Empty
  };

  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
    this.getPatientInfo(patientId);
  }

  data = {
    patientInfo: {},
    patientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    emptyConfig: {
      show: true
    },
    patient: {},
    pid: "",
    grid: "",
    reportList: [],
    dateTypeValue: 2,
    dateTypeList: [
      { value: 1, name: "病友姓名" },
      { value: 2, name: "手机号码" },
      { value: 3, name: "身份证号" },
      { value: 4, name: "推荐日期" }
    ],
    login: false,
    recommendedList: []
  };

  methods = {
    toRecommend() {
      const {
        patientName = "",
        patHisNo = "",
        patientId = "",
        patCardNo = ""
      } = this.patientInfo;
      wepy.navigateTo({
        url: `/package1/pages/recommend/add/index?pid=${this.grid}&grid=${
          this.grid
        }&patientName=${patientName}&patHisNo=${patHisNo}&patientId=${patientId}&patCardNo=${patCardNo}`
      });
    },
    navigateTo() {
      const url = encodeURIComponent(
        "https://mp.weixin.qq.com/s/akGAWysoWTCqSfAPXYqVoQ"
      );
      wepy.navigateTo({
        url: `/pages/webview/index?weburl=${url}`
      });
    }
  };

  events = {
    "outpatient-change-user": function(item = {}) {
      if (item) {
        this.patientConfig.infoShow = true;
        this.changeUser(item);
      }
    }
  };

  async changeUser(item = {}) {
    this.recommendedList = [];
    this.getList(item);
  }

  /**
   * 获取已推荐列表
   */
  async getList(item = {}, dateTypeValue) {
    const { idNo = "", patCardNo = "", patHisNo = "" } = item;
    this.grid = idNo.includes('*') ? patCardNo : idNo;
    this.pid = patHisNo;
    this.recommendedList = [];
    const { code, data = {} } = await Api.getList({ grid: this.grid });
    if (code == 0 && Array.isArray(data)) {
      this.recommendedList = data || [];
    } else {
      this.recommendedList = [];
      this.emptyConfig.show = true;
    }
    this.$apply();
  }

  /**
   * 获取已推荐列表
   */
  async getPatientInfo(patientId) {
    const { code, data = {} } = await Api.getPatientInfo({ patientId });
    if (code == 0) {
      this.patientInfo = data || [];
    }
    this.$apply();
  }
}
</script>