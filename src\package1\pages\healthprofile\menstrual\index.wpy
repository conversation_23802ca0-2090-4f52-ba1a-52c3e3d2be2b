<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Moment from 'moment';
  import TopTip from '@/components/toptip/index';
  import { validator } from '@/utils/utils';
  import * as Api from './api';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '月经史',
    };

    components = {
      toptip: TopTip,
    };
    
    onLoad(options) {
      const { sex = 'F', pid = '', healthId = '', type = '' } = options || {};
      this.sex = sex;
      this.pid = pid;
      this.healthId = healthId;
      this.type = type;
    }

    onShow() {
      this.cardList = [];

      this.getMaritalInfo();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      options: {},
      placeholderColor: '',
      errorColor: 'red',
      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      type: '',
      patientName: '',
      idNo: '',
      pid: '',
      sex: 'F',
      healthId: '',
      isUpdateInfo: false,
      yjzq: '',
      yjzqMin: '', // 月经周期最小
      yjzqMax: '', // 月经周期最大
      mcyjsj: '', // 末次月经时间
      yjts: '', // 月经天数
      jlqk: '', // 经量情况
      ccnl	: '', // 初潮年龄
      yjzqList: (() => { let n = 1;const array =  Array.from({ length: 60 }, () => `${n++}天`); return [array, array]})(), // 月经周期
      yjzqIndx: 0,
      yjtsList: (() => { let n = 0;return Array.from({ length: 11 }, () => `${n++}天`);})(), // 月经天数
      yjtsIndex: 0,
      ccnlList: (() => { let n = 1;return Array.from({ length: 29 }, () => `${n++}岁`);})(), // 初潮年龄
      ccnlIndex: 0,
      jlqkList: ['正常', '偏多', '偏少'], // 经量情况
      jlqkIndex: 0,
      minDate: Moment().subtract(3, 'years').format('YYYY-MM-DD'),
      maxDate: Moment(new Date().getTime()).format('YYYY-MM-DD'),
    };

    async getMaritalInfo(){
      let patientInfo = {};
      if (this.sex === 'F') {
        patientInfo = this.$parent.femalInfo || {};
      } else {
        patientInfo = this.$parent.manInfo || {};
      }
      console.log('patientInfo', patientInfo)
      const { basicInfo = {}, periodRecord: infoObj = {}, pid = '', healthId = '' } = patientInfo;
      this.periodRecord = infoObj;
      // 设置值
      if (basicInfo.name && basicInfo.idNo) {
        this.name = basicInfo.name || '';
        this.idNo	= basicInfo.idNo || '';
      }
      console.log('设置picker内容')
      if (infoObj.yjzq) {
        this.yjzq = infoObj.yjzq || '';
        this.yjts	= infoObj.yjts || '';
        this.ccnl	= infoObj.ccnl || '';
        this.jlqk = infoObj.jlqk || '';
        this.mcyjsj = infoObj.mcyjsj || '';
        this.healthId = healthId || '';

        // 设置picker内容
        const processList = ['yjzq', 'yjts', 'ccnl', 'jlqk'];
        processList.forEach((name) => {
          const value = infoObj[name];
          const list = this[`${name}List`];
          if (value && list && list.length > 0) {
            list.forEach((item, index) => {
              if (item === value) {
                this[`${name}Index`] = index;
              }
            });
          }
        });
      }
      this.$apply();
    }

    getFormData(){
      const { name = '', idNo = '', yjzq, mcyjsj, yjts, jlqk, ccnl, pid = '', sex = '', healthId = '' } = this;
      return {
        name,
        idNo,
        yjzq,
        mcyjsj,
        yjts,
        jlqk,
        ccnl,
        pid,
        patHisNo: pid,
        sex,
        healthId,
      };
    }

    validator(id){
        console.log('开始调用validator', id)
        const validate = {
          patientName: {
            regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
            errTip: '请输入2-8位合法姓名',
          },
          idNo: {
            regexp: (() => {
              const regexp = validator.idCard;
              if(typeof regexp === 'function'){
                return (val) => regexp(val.idNo);
              } else {
                return /^\S+$/;
              }
            })(),
            errTip: '请输入18位身份证',
          },
          // patientAddress: {
          //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          //   errTip: '请输入有效的住址',
          // },
          patientAddress: {
            regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
            errTip: '请输入有效的住址',
          },
          patientMobile: {
            regexp: /^1\d{10}$/,
            errTip: '请输入正确的手机号',
          },
          pid: {
            regexp: /^\d{3,8}/,
            errTip: '请输入正确pid号'
          }
        };


        const value = this.getFormData();

        let hasErr = false;
        for(let o in value){
          const obj = validate[o];
          if(obj && obj.regexp){
            let thisErr = false;
            if(typeof obj.regexp === 'function'){
              const retObj = obj.regexp(value);
              console.log('retObj', retObj)
              if(!retObj.ret){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            } else {
              if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            }
            if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
              this.errorElement[obj.errTarget || o] = true;
              this.toptip = obj.errTip || '';
              const errTimer = setTimeout(() => {
                this.toptip = '';
                this.$apply();
                clearTimeout(errTimer);
              }, 2000);
              break;
            }
          }
        }
        return hasErr;
      }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(url) {
        let { qryType = 2 } = this;
        // 跳转带参数 绑定本人或他人
        const jumpUrl = `${url}?qryType=${this.cardList.length > 0 ? 2 : 1}`
        wepy.navigateTo({ url: jumpUrl });
      },

      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        this[id] = value;
      },
      pickerMultiChange(name, e) {
        // 多列选择
        let { value = [] } = e.detail || {};
        console.log('value', value)
        if (!(value[0] >= 0 && value[1] >= 0)) {
          return false;
        }
        value = value.sort();
        this[`${name}Min`] = value[0];
        this[`${name}Max`] = value[1];
        const list = this[`${name}List`][0];
        this[name] = `${list[value[0]]}~${list[value[1]]}`;
        this.$apply();
      },
      pickerChange(name, e) {
        console.log(name, e, e.detail.value);
        // 需要处理的内容
        const processList = ['yjzq', 'yjts', 'ccnl', 'jlqk'];
        if (processList.indexOf(name) >= 0) {
          const list = this[`${name}List`] || [];
          const index = e.detail.value || 0;
          this[name] = list[index];
          this[`${name}Index`] = Number(index);
          this.$apply();
          return;
        }
        this[name] = e.detail.value || '';
        this.$apply();
      }
    };

    async formSubmit(e){
      let value = this.getFormData();
      console.log('formData', value)
      this.hasErr = this.validator();
      if(this.hasErr){
        return false;
      }
      let param = param = {
        pid: value.pid || '',
        sex: value.sex || '',
        healthId: value.healthId || '',
        periodRecord: value,
      };
      const { code, data = {}, msg } = await (this.type === 'update' ? Api.updateHealthRecord : Api.addHealthRecord)(param);
      // const { idTypes = [], isNewCard, patCards = [], patientTypes = [], relationTypes = [] } = this.hisConfig
      if (code == 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    }
  }
</script>
