<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import { desensitizeIDNumber } from '@/utils/utils';

  const InitState = {
    userInfo: {},
    cardList: [],
    leftBindNum: 0,
  }

  export default class UserList extends wepy.page {
    config = {
      navigationBarTitleText: '我的就诊人',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
    };

    onLoad(options) {
      this.patientName = options.patientName;
      this.patCardNo = options.patCardNo;
      this.idNo = options.idNo;
      this.patientId = options.patientId;
    }

    onShow(){
      this.userInfo = {};
      this.cardList = [];
      this.leftBindNum = 0;
      this.getRelationList({idNo: this.idNo, patName: this.patientName, patHisNo: this.idNo, qryType: 3, patientId: this.patientId});
      this.getCurrentPIDInfo({idNo: this.idNo, patName: this.patientName, patHisNo: this.idNo, qryType: 1,  patientId: this.patientId});
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      userInfo: {},
      cardList: [],
      leftBindNum: 0,
      patientName: '',
      patHisNo: '',
      idNo: '',
      patCardNo: '',
      patientId: ''
    };

    async getRelationList(param){
      const { data = {} } = await Api.getRelationList(param);
      const { items = [], resultCode = '' } = data;
      if (items.length > 0 && resultCode == 0) {
        items.map(item => {
          item.idNumber = desensitizeIDNumber(item.idNo);
          this.cardList.push(item);
        });
      }
      this.$apply();
    }

    async getCurrentPIDInfo(param) {
      const { data = {} } = await Api.getRelationList(param);
      const { items = [], resultCode = '' } = data;
      if (items.length > 0 && items[0] && resultCode == 0) {
        this.userInfo = items[0];
      }
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      toCode(card) {
        console.log(card, '=====75')
        if(card.bandFlg === '1') return
        const hisIdTypes = [
          {"value": "DB01", "text": "身份证", "dictKey": "1"},
          {"value": "DB03", "text": "护照", "dictKey": "3"},
          {"value": "DB04", "text": "军官证", "dictKey": "4"},
          {"value": "DB08", "text": "其他法定有效证件", "dictKey": "8"},
          {"value": "DB09", "text": "士兵证", "dictKey": "9"},
          {"value": "DB10", "text": "警官证", "dictKey": "10"},
          {"value": "DB11", "text": "学生证", "dictKey": "11"},
          {"value": "DB13", "text": "港澳居民身份证", "dictKey": "13"},
          {"value": "DB12", "text": "台湾居民身份证", "dictKey": "12"},
        ];
        hisIdTypes.map(item => {
          if (item.value == card.idType) {
            card.idType = item.dictKey;
          }
        });
        wepy.navigateTo({ 
          url: `/pages/usercenter/newphone/index?telephone=${card.telephone}&relationType=${card.relationType}&patName=${card.patName}&patSex=${card.patSex}&patBirth=${card.patBirth}&idNo=${card.idNo}&familyMemberName=${card.familyMemberName}&marryDes=${card.marryDes}&idType=${card.idType}&pid=${card.pid}&grid=${card.grid}&patAddress=${card.patAddress}&familyTypeId=${card.familyTypeId}`,
        });
      },
      toAdd() {
        const { idNo, patName, pid, address } = this.userInfo;
        wepy.navigateTo({
          url: `/package1/pages/addfamilymember/index?isNewCard=0&idNo=${this.idNo}&idType=1&patientName=${this.patientName}&qryType=&pid=${pid}&address${address}=&isScan=0&patientId=${this.patientId}&patCardNo=${this.patCardNo}`
        })
      },
    };

    events = {
    };

  }
</script>
