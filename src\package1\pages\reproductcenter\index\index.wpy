<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Outpatient from "@/components/outpatient/index";
import Empty from '@/components/empty/index';
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "生殖中心缴费清单",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    outpatient: Outpatient,
    empty:Empty
  };
  data = {
    // url参数
    options: {},
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 就诊人列表
    outpatient: {},
    // 住院信息列表
    inHospitalList: [],
    // 当前就诊人信息
    currentPatient: {}
  };
  onLoad() {}
  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.options = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
    this.getPatientInfo();
  }
  // 获取住院人信息
  async getPatientInfo() {
    const { patientId } = this.options;
    if(!patientId){
      return;
    }
    const { code, data = {}, msg } = await Api.getPatientInfo({ patientId });
    if (code !== 0) {
      return;
    }
    this.currentPatient = data;
    this.$apply();
    this.getInhospitalList();
  }
  // 获取住院信息列表
  async getInhospitalList() {
    const { patHisNo = "" } = this.currentPatient;
    const { code, data = {}, msg } = await Api.getInHospitalInfoList({
      patHisNo
    });
    if (code !== 0) {
      return;
    }
    const { voList = [] } = data;
    this.inHospitalList = voList;
    this.$apply();
  }
  events = {
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient;
        this.outpatientConfig.infoShow = true;
        this.$apply();
      }
      this.getPatientInfo();
    }
  };
  methods = {
    toDaily(item = {}) {
      const { admissionNum = "", inhospitalNo = "" } = item;
      const { patHisNo = "" } = this.currentPatient;
      wepy.navigateTo({
        url: `/pages/inhosp/daily/index?inhospitalNo=${inhospitalNo}&admissionNum=${admissionNum}&patHisNo=${patHisNo}`
      });
    }
  };
}
</script>


