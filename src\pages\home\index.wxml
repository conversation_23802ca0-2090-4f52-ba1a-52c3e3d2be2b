
<scroll-view
  scroll-y="true"
  class="p-page {{searchFocus ? 'unscroll' : ''}}"
  bindscroll="bindScroll"
  >
  <!-- 自定义导航 -->
  <nav-bar1 isBack="0" :bgColor.sync="navFlag">

    <image wx:if="{{navFlag !== '#fff'}}" class="nav-img" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/nv-title.png" />
    <text wx:else>湖南家辉遗传专科医院</text>
  </nav-bar1>
  <view class="home-page-box">
    <image class="home-bg" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/bg.png" />
    <!-- 用户信息（区分登录状态） -->
    <block>
      <view class="user-info">
        <block wx:if="{{activePatient.patHisNo}}">
          <view class="user-info-top">
            <view class="left">
              <view>
                <view class="title">{{activePatient.patientName}}</view>
                <view class="desc">就诊卡号：{{activePatient.patCardNo}}</view>
              </view>
            </view>
            <view class="right">
              <view class="btn-default" @tap="toManagerOutpatient">管理就诊人</view>
            </view>
          </view>
          <view class="user-info-bottom">
            <view class="bottom-btn" @tap="navigateTo" data-url="/pages/register/recordlist/index">
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/register.png" />
              <text>挂号记录</text>
            </view>
            <view class="line" />
            <view class="bottom-btn" @tap="navigateTo" data-url="/pages/treat/recordlist/index">
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/pay.png" />
              <text>缴费记录</text>
            </view>
            <view class="line" />
            <view class="bottom-btn" @tap="onToggleBarcode">
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/code.png" />
              <text>就诊码</text>
            </view>
          </view>
        </block>
        <block wx:else>
          <view class="left">
            <view class="avatar">
              <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/jiahui-logo.png" />
            </view>
            <view class="title-tips">
              <navigator url="/pages/bindcard/queryuserinfo/index?qryType=1">
                <text class="title-tips-text">登录/注册</text>
                <image
                class="trangle"
                mode="widthFix"
                src="REPLACE_IMG_DOMAIN/his-miniapp/images/trangle.png"
                />
              </navigator>
              <view class="title-tips-info">初次使用请办卡/绑卡</view>
            </view>
          </view>
        </block>
      </view>
    </block>

    <block>

      <!-- 快速操作（预约挂号、门诊缴费、报告查询） -->
      <view class="quick-menu">
        <view
        class="menu-item"
        wx:for="{{quickOperateFuncList}}"
        wx:key="index"
        @tap="onDealQuickNav({{index}})"
        >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/{{item.image}}' />
            <view wx:if="{{index === 2 && msgCount}}" class="menu-unread">{{msgCount}}</view>
          </view>
          <view class="mt-5">{{item.text}}</view>
          <view class="min-tips">{{item.minText}}</view>
        </view>
      </view>
    </block>

    <!--常用功能-->
    <view class="quick-menu function-menu">
      <view class="quick-menu-title">
        <text class="title">常用功能</text>
      </view>
      <view class="function-menu-list">
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/pages/report/reportlist/index"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/bill-day.png' />
            <!--<view wx:if="{{index === 1 && msgCount}}" class="menu-unread">{{msgCount}}</view>-->
          </view>
          <view class="mt-5">报告查询</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/package2/pages/samplereport/query/index"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/bill-day.png' />
            <!--<view wx:if="{{index === 1 && msgCount}}" class="menu-unread">{{msgCount}}</view>-->
          </view>
          <view class="mt-5">样本报告查询</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/package2/pages/healthrecord/home/<USER>"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/files.png' />
            <!--<view wx:if="{{index === 1 && msgCount}}" class="menu-unread">{{msgCount}}</view>-->
          </view>
          <view class="mt-5">健康档案</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/package1/pages/recommend/index/index"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/recommend.png' />
            <!--<view wx:if="{{index === 1 && msgCount}}" class="menu-unread">{{msgCount}}</view>-->
          </view>
          <view class="mt-5">我要推荐</view>
        </view>
        
      </view>
      <view class="function-menu-list list-pt">
        <view
          class="menu-item"
          @tap="ocjxt"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/jxt.png' />
          </view>
          <view class="mt-5">家系图工具</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/pages/dynamic/index/index?position=1"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/inheritance.png' />
            <!--<view wx:if="{{index === 1 && msgCount}}" class="menu-unread">{{msgCount}}</view>-->
          </view>
          <view class="mt-5">遗传科普</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/package2/pages/scancode/recordlist/index"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/scancode-home.png' />
          </view>
          <view class="mt-5">扫码缴费记录</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/pages/feedback/feedbacklist/index"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/address.png' />
          </view>
          <view class="mt-5">投诉建议</view>
        </view>
      </view>
      <view class="function-menu-list list-pt">
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/pages/register/doclist/index?deptName=医学影像科&deptId=61&areaCode="
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/bcyy.png' />
          </view>
          <view class="mt-5">B超预约</view>
        </view>
        <view
          class="menu-item"
          @tap="goFunctionPage"
          data-url="/package2/pages/scancode/home/<USER>"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/satisfaction.png' />
          </view>
          <view class="mt-5">项目缴费</view>
        </view>
        <view
          class="menu-item"
          @tap="goHlwMiniProgram"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/hlwyy.png' />
          </view>
          <view class="mt-5">互联网医院</view>
        </view>
        <view
          class="menu-item"
          @tap="handleSampleSurvey"
          >
          <view class="large-icon center pos-relative">
            <image src='REPLACE_IMG_DOMAIN/his-miniapp/images/files.png' />
          </view>
          <view class="mt-5">样本采集</view>
        </view>
      </view>
    </view>
    <!--知名专家-->
    <view class="quick-menu function-menu doctor-list">
      <view class="quick-menu-title">
        <text class="title">知名专家</text>
      </view>
      <view class="function-menu-list">
        <view
          class="menu-item"
          wx:for="{{doctorList}}"
          wx:key="index"
          @tap="toDoctorIntro({{item}})"
          >
          <view class="img-bg-box">
            <view class="large-icon center pos-relative">
              <image mode="aspectFill" src="{{item.img || (item.sex === 'M' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' )}}" />
            </view>
            <view class="extra">专家</view>
          </view>
          <view class="mt-5">{{item.name}}</view>
        </view>
      </view>
    </view>
    <!--遗传科普-->
    <view class="quick-menu function-menu files-menu">
      <view class="quick-menu-title">
        <text class="title">遗传科普</text>
        <text 
          class="more" 
          @tap="goFunctionPage"
          data-url="/pages/dynamic/index/index?position=1"
        >更多</text>
      </view>
      <view class="function-menu-list">
        <view class="file-list"
          wx:for="{{filesList}}"
          wx:key="index"
          @tap="navigateToDynamic({{item}})"
        >
          <view class="content-box">
            <view class="content">{{item.title}}</view>
            <view class="read-num">
              <text>{{item.createTime}}</text>
            </view>
          </view>
          <view class="large-icon pos-relative">
            <image mode="widthFix" src='{{item.picUrl}}' />
          </view>
        </view>
      </view>
    </view>

    <!-- 通知-notice {} 待办-todoMsgs []-->
    <!--<block wx:if="{{todoMsgs &&  todoMsgs.length>0}}">
      <swiper
      autoplay="{{false}}"
      interval="{{2000}}"
      duration="{{500}}"
      vertical="{{true}}"
      class="swiper card"
      @tap="bindTapNotice"
      >
        <block wx:for="{{todoMsgs}}" wx:key="*this">
          <swiper-item class="swiper-item">
            <view class="xsmall-icon">
              <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/home/<USER>' />
            </view>
            <view class="msg">{{item.title}}</view>
            <view class="unread-dot" />
          </swiper-item>
        </block>
      </swiper>
    </block>-->

    <!-- 查询 -->
    <!--<block>
      <view class="card card-3">
        <view class="title">查询</view>
        <view class="menu {{isSelectExpand ? 'is-expand' : ''}}">
          <block wx:for="{{queryFunc}}" wx:key="index">
            <view
            class="menu-item"
            wx:if="{{ (item.type === 'more' && isSelectExpand === false) || item.type !== 'more' }}"
            @tap="bindGoPage({{item}})"
            >
              <view class="middle-icon">
                <view wx:if="{{item.status == '0'}}" class="will-open">即将开放</view>
                <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/home/<USER>' />
              </view>
              <view class="menu-title">{{item.name}}</view>
              <view class="unread-radius" wx:if="{{item.name === '知情告知书' && isShowUnreadRadius}}" />
            </view>
          </block>
        </view>
        <view
        class="link"
        wx:if="{{isSelectExpand}}"
        @tap="onPackUp('isSelectExpand')"
        >收起
        </view>
      </view>
    </block>-->

    <!-- 服务 -->
    <!--<block>
      <view class="card card-3">
        <view class="title">服务</view>
        <view class="menu-service {{isServiceExpand ? 'is-expand' : ''}}">
          <block wx:for="{{serviceFunc}}" wx:key="index">
            <view
            class="menu-item"
            wx:if="{{ (item.type === 'more' && isServiceExpand === false) || item.type !== 'more' }}"
            @tap="bindGoPage({{item}})"
            >
              <view class="middle-icon">
                <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/home/<USER>' />
              </view>
              <view class="menu-title">{{item.name}}</view>
            </view>
          </block>
        </view>
        <view
        class="link"
        wx:if="{{isServiceExpand}}"
        @tap="onPackUp('isServiceExpand')"
        >收起
        </view>
      </view>
    </block>-->

    <!-- 其他功能 -->
    <!--<block>
      <view class="card card-4">
        <block wx:for="{{otherFunctionList}}" wx:key="index">
          <view class="card-item flex-1" @tap="bindGoPage({{item}})">
            <view class="small-icon">
              <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/icon/new/home/<USER>' />
            </view>
            <view class="title">{{item.name}}</view>
          </view>
        </block>
      </view>
    </block>-->

    <!-- 条形码弹窗 -->
    <block wx:if="{{showBarcode}}">
      <view class="desc-modal-mask">
        <view class="desc-modal-box">
          <view class="desc-modal">
            <view class="desc-content barcode">
              <view class="code-name">{{activePatient.patientName}}</view>
              <image
                wx:if="{{!!activePatient.idNo}}"
                mode="widthFix"
                src="https://wechat.jiahuiyiyuan.com/barcode?msg={{activePatient.idNo}}&type=code128&mw=.60"
              />
            </view>
            <view class="code-tips">在就医缴费的时候请将此卡出示给相关医师</view>
          </view>
          <view class="desc-footer">
            <view class="refuse" @tap="onToggleBarcode">确认</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 挂号弹窗提示 -->
    <block wx:if="{{modal.isShow}}">
      <view class="desc-modal-mask">
        <view class="desc-modal">
          <view class="desc-title">挂号须知</view>
          <scroll-view class="desc-content" scroll-y>
            <text decode="{{true}}" space="{{true}}">{{modal.content}}</text>
          </scroll-view>
          <view class="desc-footer">
            <view class="refuse" @tap="agree(0)">拒绝</view>
            <view class="agree" @tap="agree(1)">同意</view>
          </view>
        </view>
      </view>
    </block>
    <!-- 家系图弹窗提示 -->
    <view class="desc-modal-mask" wx:if="{{jxtisShow}}">
      <view class="desc-modal-new">
        <view class="desc-title">家系图绘制工具</view>
        <scroll-view class="desc-content" scroll-y>
          <text decode="{{true}}" space="{{true}}">您可以直接点击打开体验，也可以点击复制获取链接在电脑浏览器上打开完整使用。复制的链接有时效性，失效后请重新获取.</text>
          <view class="fzlj" @tap="copyjxt">复制链接</view>
        </scroll-view>
        <view class="desc-footer">
          <view class="refuse" @tap="ocjxt">取消</view>
          <view class="agree" @tap="jxtjump">打开</view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>


<!-- 底部tab菜单导航 -->
<nav-tab :type="navTabType" />

<!-- 就诊人弹层 -->
<outpatient
 :config.sync="outpatientConfig"
 :patient.sync="outpatient"
 :emptyNotice.sync="emptyNotice"
 :isHome.sync="isHome"
/>
<!--  首页弹窗提示语 -->
<!--<block wx:if="{{extra.isShow}}">
	<view class="desc-modal-mask">
		<view class="desc-modal">
			<view class="desc-title" style="color: red; font-weight: 600">111{{promotTitle}}</view>
			<scroll-view class="desc-content" scroll-y>
				<text decode="{{true}}" space="{{true}}">{{extra.content}}</text>
			</scroll-view>
			<view class="desc-footer">
				<view class="agree" @tap="changeExtra">确定</view>
			</view>
		</view>
	</view>
</block>-->
<block wx:if="{{extra1.isShow}}">
	<view class="desc-modal-mask">
		<view class="desc-modal">
			<view class="desc-title">各位病友</view>
			<scroll-view class="desc-content" scroll-y>
				<text decode="{{true}}" space="{{true}}">{{extra1.content}}</text>
			</scroll-view>
			<view class="desc-footer">
				<view class="agree" @tap="changeExtra1">确定</view>
			</view>
		</view>
	</view>
</block>
<pop :config.sync="popConfig">
	<block slot="content">
		<view class="m-pop-code">
			<view class="pop-hd">
				<view class="hd-tit">{{outpatient.activePatient.patientName}}</view>
				<view class="hd-txt">{{outpatient.activePatient.patHisNo}}</view>
			</view>
			<view class="pop-bd">
				<block wx:if="{{outpatient.activePatient.patHisNo}}">
					<image mode="widthFix" src="https://wechat.jiahuiyiyuan.com/barcode?msg={{outpatient.activePatient.patHisNo}}&type={{codeType}}&mw=.60&hrp=none" />
				</block>
			</view>
		</view>
	</block>
</pop>

