{"libVersion": "2.28.1", "projectname": "p242", "condition": {"miniprogram": {"list": [{"name": "package2/pages/scancode/home/<USER>", "pathName": "package2/pages/scancode/home/<USER>", "query": "acount=liyanlai&allianceId=12", "scene": null, "launchMode": "default", "partialCompile": {"enabled": true, "pages": []}}]}}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": false, "bigPackageSizeSupport": true}}