<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import Empty from "@/components/empty/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import { PRIMARY_COLOR } from '@/config/constant';
import md5 from "md5";
import * as Utils from "@/utils/utils";

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '报告查询',
	}

  components = {
    empty: Empty,
  };

  wxs = {
    WxsUtils: WxsUtils,
  };

  data = {
    keyName: '',
    caseid: ''
  }

  onShow(options) {}
  
  onLoad(options) {}

  async getBasicInfo() {
    const { code, data = {} } = await Api.getBasicInfo();
    if(code === 0){
      this.basicInfo = data;
      this.$apply();
    }
  };
  methods = {
    
    getKeyName(e) {
      this.keyName = e.detail.value;
      this.$apply();
    },
    getCaseid(e) {
      this.caseid = e.detail.value;
      this.$apply();
    },
    handleTurn() {
      wepy.navigateTo({
        url: '/pages/report/reportlist/index',
      });
    },
    async handleClick(){
      if (!this.keyName) {
        wepy.showModal({
          title: "温馨提示",
          content: "请输入证件号/手机号",
          showCancel: false
        });
        return;
      }
      if (!this.caseid) {
        wepy.showModal({
          title: "温馨提示",
          content: "请输入检测编号",
          showCancel: false
        });
        return;
      }
      const param = {
        keyName: this.keyName,
        caseid: this.caseid,
        msgKey: md5(this.caseid+'n2023beiri')
      }
      const { code, data = {} } = await Api.getList(param);
      if (data.resultCode === '0') {
        if (!data.list) {
          wx.showToast({
            title: "暂无数据",
            icon: "none"
          }, 2000);
          return
        }
        wepy.navigateTo({
          url: `/package2/pages/samplereport/list/index?${Utils.jsonToQueryString(param)}`
        });
      } else {
        wx.showToast({
          title: data.resultMessage,
          icon: "none"
        });
      }
    }
  }
}
</script>