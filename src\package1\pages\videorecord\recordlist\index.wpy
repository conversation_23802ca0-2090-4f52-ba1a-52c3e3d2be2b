<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from "@/components/outpatient/index";
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '推荐视频',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 挂号记录列表
      orderList: [],
      grid: '',
      pid: '',
      patientConfig: {
        infoShow: false,
        show: false,
        initUser: {},
      },
      login: false
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'outpatient': Outpatient,
      'empty': Empty
    };

    events = {
      'outpatient-change-user': function (item = {}) {
        if(item){
          this.patientConfig.infoShow = true;
          this.changeUser(item);
        }
      }
    };



    props = {};

    onShow(options) {
      // this.getPatientsList();
      const { patientId = '' } = this.$wxpage.options;
      this.$broadcast('outpatient-get-patient', { patientId });
    }

    onLoad(options) {
      const { grid = '', pid = '' } = options || {};
      this.grid = grid;  // 模板消息及个人中心两个入口，优先取模板消息的grid，若没取到就取登录人grid
      this.pid = pid;
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转视频链接
       * @param item
       */
      bindGoDetail(item){
        const { id = '', videoUrl = '' } = item;
        this.readVideo(id, videoUrl);
      },
    };

    // async getPatientsList() {
    //   const { grid = '', pid = '' } = this;
    //   const { code, data = {} } = await Api.getPatientsList({ isLogin: '1' });
    //   if( code == 0 ){
    //     const { cardList = '' } = data;
    //     const card = cardList && cardList.length > 0 && cardList[0] || {};
    //     this.grid = this.grid || card.patCardNo || '';
    //     this.pid = this.pid || card.patHisNo || '';
    //     this.getOrderList({
    //       grid: card.patCardNo || '',
    //       pid: card.patHisNo || '',
    //     });
    //   }
    // }

    async changeUser(item = {}) {
      this.getOrderList({
        grid: item.patCardNo || '',
        pid: item.patHisNo || '',
      });
      this.grid = item.patCardNo || '';
      this.pid = item.patHisNo || '';
      this.$apply()
    }

    async readVideo(id, videoUrl) {
      const { code, data = {} } = await Api.readVideo({ id, grid: this.grid, pid: this.pid });
      if(code == 0){
        wepy.navigateTo({
          url: `/package1/pages/videorecord/recorddetail/index?videoUrl=${videoUrl}`,
        });
      }
    }

    async getOrderList(param) {
      const { code, data = {} } = await Api.orderList(param);
      if (code == 0) {
        this.orderList = data;
      }
      this.$apply();
    } 
  }
</script>
