/**
 * 通用配置文件，以功能点作为切入点，便于不同医院的小程序项目实施
 */
export const _config = {
  name: '家辉医院互联网医院',         // 小程序名称，用于首页展示和分享
  rtcOrigin: 'tx',                          // 音视频方案    tx || ali
  // 健康小视频配置项
  healthVideo: {
    use: false,  
    tagList: ['高血压', '糖尿病'],            // 视频搜索的标记，方便后期增加
  },                        
  home: {
    hospitalIntro: true,                    // 展示医院介绍
  },
  // 在线开药功能
  medicine: {
    use: false,
  },
  // 预约检查功能
  appointmentCheck: {
    use: false,
  },
  // 随访功能
  followUp: {
    use: false,
  },
  // 开具处方功能
  prescribe: {
    use: true
  },
  // 开具检查和检验报告功能
  inspect: {
    use: false
  },
  // 案例公开功能
  openCase: true,
  // 慢病管理功能
  CDM: {
    use: false,
  },
  // 直播功能
  live: {
    use: false,
    origin: 'tx',             // tx || ali
  },
  // 圈子功能
  moment: {
    use: true,
  },
  // 检查检验报告
  report: {
    use: true,    
  },
  // 病历查看
  medicalRecord: {
    use: true,    
  },
  // 医院是否支持修改手机号码
  modifyMobile: {
    use: false,    
  }
}