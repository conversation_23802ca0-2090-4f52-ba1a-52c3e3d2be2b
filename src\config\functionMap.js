// urlType: 1 // 普通小程序内链接
// urlType：2 // 其他小程序
// urlType：3 // 跳转h5

export default {
  301001: {
    name: "预约挂号",
    icon: "yygh.png",
    url: "/pages/register/deptlist/index",
  },
  301002: {
    name: "门诊缴费",
    icon: "mzjf.png",
    url: "/pages/treat/untreatlist/index",
  },
  301003: {
    name: "住院服务",
    icon: "zyfw.png",
    url: "/pages/inpatient/home/<USER>",
  },
  301004: {
    name: "就诊卡充值",
    icon: "jzkcz.png",
    url: "/pages/recharge/amount/index",
  },
  302001: {
    name: "添加就诊人",
    icon: "tjjzr.png",
    url: "/pages/usercenter/userlist/index",
  },
  302002: {
    name: "当日挂号",
    icon: "drgh.png",
    url: "/pages/register/deptlist/index",
  },
  302003: {
    name: "排队候诊",
    icon: "pd.png",
    url: "/pages/queue/queue/index",
  },
  302004: {
    name: "报告查询",
    icon: "bgcx.png",
    url: "/pages/report/reportlist/index",
  },
  // 302005: {
  //   name: "在线取号",
  //   icon: "zxqh.png",
  //   // url: "/pages/takeno/index/index",
  // },
  302006: {
    name: "住院日清单",
    icon: "zyrqd.png",
    url: "/pages/inpatient/userlist/index?keyword=zyrqd",
  },
  302007: {
    name: "住院缴费",
    icon: "zyjf.png",
    url: "/pages/inpatient/amount/index",
  },
  302008: {
    name: "住院人信息",
    icon: "zyrxx.png",
    url: "/pages/inpatient/userlist/index?keyword=zyrxx",
  },
  302009: {
    name: "问卷调查",
    icon: "wjdc.png",
    url: "/pages/survey/surveytools/surveylist/index",
  },
  302010: {
    name: "微网站",
    icon: "wwz.png",
    url: "/package1/pages/microsite/home/<USER>",
  },
  302011: {
    name: "就诊须知",
    icon: "jzxz.png",
    url: "/pages/auth/developing/index",
  },
  302012: {
    name: "智能客服",
    icon: "znkf.png",
    url: "",
    urlType: 2,
    appId: "wxa89db5e57ee2a65e",
    envVersion: "trial",
  },
  302013: {
    name: "无号助手",
    icon: "whzs.png",
    url: "/pages/auth/developing/index",
  },
  302014: {
    name: "挂号记录",
    icon: "ghjl.png",
    url: "/pages/register/recordlist/index",
  },
  302015: {
    name: "缴费记录",
    icon: "jfjl.png",
    url: "/pages/treat/recordlist/index",
  },
  303001: {
    name: "商保理赔",
    icon: "sblp.png",
    url: "",
    urlType: 2,
    appId: "wx3fa4464915791992",
  },
  303002: {
    name: "体检预约",
    icon: "tj.png",
    url: "",
    urlType: 2,
    appId: "wxafb9e3096f9810c3",
  },
  303003: {
    name: "停车缴费",
    icon: "tcjf.png",
    url: "",
    urlType: 2,
    appId: "wx6292cb8430e4b9de",
  },
  303004: {
    name: "住院点餐",
    icon: "zydc.png",
    url: "",
    urlType: 2,
    appId: "wxa7f456245317a772",
  },
  303005: {
    name: "信用租赁",
    icon: "xyzl.png",
    url: "/pages/auth/developing/index",
  },
  302016: {
    name: "门诊电子病历",
    icon: "blb.png",
    url: "/package1/pages/medicalrecord/recordlist/index",
  },
  "test-yfxx": {
    //临时演示数据
    name: "孕妇学校",
    icon: "yfxx.png",
    url: "https://wxauth.wuaitec.com/wxauth/c/haici/mini/1",
    urlType: 3,
    desc: "智能云端怀孕管理",
  },
  // 'test-hlwyy': { //临时演示数据
  //   name: '互联网医院',
  //   icon: 'hlwyy.png',
  //   url: '',
  //   urlType: 2,
  //   desc: '在线就诊，方便快捷',
  //   appId: 'wx13d9d2c1f21960ae',
  // },
  "test-yyjc": {
    //临时演示数据
    name: "预约检查",
    icon: "yyjc.png",
    url: "",
    urlType: 2,
    appId: "wx25ad95d5a37d5c9c",
    desc: "一站式预约检查流程",
  },
  "test-zndz": {
    //临时演示数据
    name: "智能导诊",
    icon: "zndz.png",
    url: "https://mp.med.gzhc365.com/views/p099/index.html#/guide/guidelist",
    urlType: 3,
    sceneType: 1,
  },
  "test-cccx": {
    //临时演示数据
    name: "产程查询",
    icon: "hlwyy.png",
    url: "",
    urlType: 2,
    appId: "wx4fe7970bd94d058d",
    desc: "产程全了解",
  },
  "test-bcyy": {
    //临时演示数据
    name: "B超预约",
    icon: "zndz.png",
    urlType: 3,
    sceneType: 1,
  },
  "test-jtzs": {
    //临时演示数据
    name: "交通住宿",
    icon: "bgcx.png",
    urlType: 3,
    sceneType: 1,
  },
  "test-lydj": {
    name: "来院登记",
    icon: "wjdc.png",
    url: "pages/survey/surveydetail/index?id=5",
    urlType: 3,
    sceneType: 1,
  },
  // UI调整后的菜单
  "test-ssyy": {
    name: "手术预约",
    icon: "ssyy.png",
    url: "/pages/operation/index",
    desc: "手术预约",
  },
  "test-bcxdtyy": {
    name: "B超心电图预约",
    icon: "bcxdtyy.png",
    url: "/pages/jump/index",
    desc: "B超心电图预约",
  },
  "test-hsjcyy": {
    name: "核酸检测预约",
    icon: "hsjcyy.png",
    url: "/pages/treat/untreatlist/index",
    desc: "核酸检测预约",
    type: "HS",
    sceneType: 1,
  },
  "test-fyqd": {
    name: "费用清单",
    icon: "fyqd.png",
    url: "/pages/inhosp/index/index",
    desc: "费用清单",
    sceneType: 1,
    id: "302006",
  },
  "test-mzbl": {
    name: "门诊病历",
    icon: "mzbl.png",
    url: "",
    desc: "门诊病历",
    sceneType: 1,
  },
  "test-mzkd": {
    name: "门诊开单",
    icon: "mzkd.png",
    url: "",
    desc: "门诊开单",
    sceneType: 1,
  },
  "test-ptyz": {
    name: "胚胎移植",
    icon: "ptyz.png",
    url: "",
    desc: "胚胎移植",
    sceneType: 1,
  },
  "test-djcx": {
    name: "冻精查询",
    icon: "djcx.png",
    url: "",
    desc: "冻精查询",
    sceneType: 1,
  },
  "test-bcjcqk": {
    name: "周期中B超监测",
    icon: "bcjcqk.png",
    url: "https://ssyy.zxxyyy.cn/#/guide/ultrasound",
    urlType: 3,
    desc: "周期中B超监测",
    sceneType: 1,
    pid: true,
  },
  "test-ptcx": {
    name: "胚胎查询",
    icon: "ptcx.png",
    url: "",
    desc: "胚胎查询",
    sceneType: 1,
  },
  "test-zyd": {
    name: "指引单",
    icon: "zyd.png",
    url: "/package1/pages/mergeguides/index",
    desc: "指引单",
    sceneType: 1,
  },
  "test-yndz": {
    name: "院内导诊",
    icon: "yndz.png",
    url: "/package1/pages/guide/index/index",
    desc: "院内导诊",
    sceneType: 1,
    id: "test-zndz",
  },
  "test-hyjl": {
    name: "好孕接力",
    icon: "hyjl.png",
    url: "/package1/pages/recommend/index/index",
    desc: "好孕接力",
    sceneType: 1,
  },
  "test-axzy": {
    name: "爱心助孕",
    icon: "axzy.png",
    url: "https://mp.weixin.qq.com/s/ysiM8fV-YrjCcEblEk4tQQ",
    urlType: 3,
    desc: "爱心助孕",
    sceneType: 1,
  },
  "test-vipzzfw": {
    name: "VIP中心",
    icon: "vipzzfw.png",
    url: "https://mp.weixin.qq.com/s/b4S8Z7PR2WmHZ3W7-P4qoQ",
    urlType: 3,
    desc: "VIP中心",
    sceneType: 1,
  },
  // "test-jzk": {
  //   //临时演示数据
  //   name: "精子库",
  //   icon: "znkf.png",
  //   url: "https://jzkwx.zxxyyy.cn/",
  //   urlType: 3,
  //   desc: "精子库",
  // },
  "test-jzk": {
    name: "精子库",
    icon: "jzk.png",
    desc: "精子库",
    id: "test-jzk",
    url: "",
    urlType: 2,
    appId: "wx47895ff7b18988d6",
  },
  "test-rlcj": {
    name: "人脸采集",
    icon: "rlcj.png",
    url: "/pages/operation/index?type=home",
    desc: "人脸采集",
    sceneType: 1,
    id: "302010",
  },
  "test-wjdc": {
    name: "问卷调查",
    icon: "wjdc.png",
    url: "/pages/survey/surveytools/surveylist/index",
    desc: "问卷调查",
    id: "302009",
    sceneType: 1,
  },
  "test-ynbgsc": {
    name: "院外报告上传",
    icon: "ynbgsc.png",
    url: "/pages/uploadreport/reportlist/index",
    desc: "院外报告上传",
    sceneType: 1,
  },
  "test-zqtys": {
    name: "知情同意书",
    icon: "zqtys.png",
    desc: "知情同意书",
    sceneType: 1,
    url: "/pages/webview/index",
    isWebUrl: true,
  },
  "test-zqgzs": {
    name: "知情告知书",
    icon: "zqgzs.png",
    desc: "知情告知书",
    sceneType: 1,
    url: "/pages/agreement/list/index",
  },
  "test-ypjs": {
    name: "药品寄送",
    icon: "ypjs.png",
    desc: "药品寄送",
    sceneType: 1,
    url: "/pages/sendmedicine/home/<USER>",
    id: "test-ypjs",
  },
  "test-bccx": {
    name: "班车查询",
    icon: "bccx.png",
    url: "https://hlwyy.zxxyyy.cn/images/bus.jpg",
    urlType: 3,
    desc: "班车查询",
    sceneType: 1,
  },
};
