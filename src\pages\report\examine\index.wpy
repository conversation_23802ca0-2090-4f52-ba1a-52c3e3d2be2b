<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import * as Api from './api';

  export default class Examine extends wepy.page {
    config = {
      navigationBarTitleText: '检查报告详情',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
    };

    onLoad(options) {
      this.getInfo({checkId: options.reportId, reportId: options.reportId, patientId: options.patientId, extFields: options.extFields});
    }

    data = {
      info: {},
    };

    methods = {
    };

    events = {
    };
    /**
     * 获取详情
     */
    async getInfo(param) {
      const { code, data = {}, msg } = await Api.getInfo(param);
      this.info = data || {};
      this.$apply();
    }
  }
</script>
