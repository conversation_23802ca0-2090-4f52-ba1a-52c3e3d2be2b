<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy"

import { TYPE_MAP, CODE_TYPE, TIME_MAP } from "@/config/constant"
import { jsonToQueryString } from "@/utils/utils"

import * as Utils from "@/utils/utils"

import DetailStatus from "./com/detailStatus"
import UserInfo from "./com/userInfo"
import CheckDetail from "./com/checkDetail"
import MedicalDetail from "./com/medicalDetail"
import * as Api from "./api"

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "病历申请详情"
  }

  data = {
    // 页面参数
    options: {},
    // 订单详情
    detailData: {},
    // 顶部状态配置
    statusConfig: {}
    // 缴费信息是否展开,默认S状态收起
  }

  components = {
    "detail-status": DetailStatus,
    "user-info": UserInfo,
    "check-detail": CheckDetail,
    "medical-detail": MedicalDetail
  }

  props = {}

  onLoad(options) {
    this.options = options
    // const { id = "" } = options
    // this.orderDetail(id)
  }

  onShow() {
    const { id = "" } = this.$wxpage.options
    this.orderDetail(id)
  }

  events = {
    "set-navigationbar-color": (param) => {
      wepy.setNavigationBarColor(param)
    }
  }

  methods = {
    /**
     * 取消订单
     */
    bindCancelOrder() {
      this.cancelOrder()
    },
    goToPay() {
      this.createOrder()
    },
    anewApply() {
      const {
        addresseeInfo = "",
        copyNum = "",
        copyPurpose = "",
        copyContent = "",
        copyObject = "",
        deptName = "",
        hospitalTimes = "",
        inHospitalDate = "",
        medicalCopyType = "",
        zyh = "",
        pid = "",
        receiveType = "",
        remark = "",
        grid = "",
        patientName = "",
        doctorName = "",
        cyclesNum = "",
        jzid = ""
      } = this.detailData
      const params = {
        addresseeInfo: addresseeInfo && JSON.stringify(addresseeInfo),
        copyNum,
        copyPurpose,
        copyContent,
        copyObject,
        medicalCopyType,
        receiveType,
        remark,
        pid,
        grid,
        patientName,
        hospitalTimes
      }
      //用户病历信息
      const userInfo = {
        ryks: deptName,
        ryrq: inHospitalDate,
        zyh,
        jzid,
        zljd: doctorName,
        zqxx: cyclesNum
      }
      //过滤掉对象中的空属性
      let newParams = {}
      for (let i in userInfo) {
        if (userInfo[i]) {
          newParams[i] = userInfo[i]
        }
      }

      wepy.setStorageSync("selectInfo", [newParams])
      console.log("---newParams---", newParams)
      const query = decodeURIComponent(jsonToQueryString(params))
      wepy.navigateTo({
        url: `/package2/pages/medical/medicalinfo/index?${query}`
      })
    }
  }

  async orderDetail(id) {
    const { code, data = {}, msg } = await Api.orderDetail({ id })
    if (code === 0) {
      this.detailData = data
      try {
        this.detailData.addresseeInfo = JSON.parse(
          this.detailData.addresseeInfo
        )
      } catch (error) {}
      this.statusConfig = this.getStatus()
      this.$apply()
    }
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    const { status } = this.detailData
    let stsObj = {}
    if (status == "1") {
      stsObj = {
        statusName: "待审核",
        text:
          "您已成功提交病历复印申请，申请审核通过后推送病历复印费用缴费通知给您，请注意及时进行缴费。只有缴费成功后方能进行快递配送或自取。"
      }
    } else if (status == "2") {
      stsObj = {
        statusName: "审核通过待缴费",
        text:
          "您的病历复印申请已审核通过，请及时在线缴费。只有缴费成功后方能进行快递配送或自取。"
      }
    } else if (status == "3") {
      stsObj = {
        statusName: "已驳回",
        text: `您的病历复印已驳回，具体原因可查看下方详情。核对无误后您可重新申请。`
      }
    } else if (status == "4") {
      stsObj = {
        statusName: "已取消",
        text: "您的病历复印已取消，可重新申请。"
      }
    } else if (status == "5") {
      stsObj = {
        statusName: "支付成功",
        text:
          "您的病历复印已审核并支付成功，工作人员将及时复印好病历，您可以根据您的领取方式来院自取或请及时查收快递，快递费到付。"
      }
    } else if (status == "6") {
      stsObj = {
        statusName: "已完成",
        text:
          "您的病历复印已审核并支付成功，工作人员将及时复印好病历，您可以根据您的领取方式来院自取或请及时查收快递，快递费到付。"
      }
    }

    return {
      ...stsObj,
      status
    }
  }

  async cancelOrder() {
    const showModalRes = await wepy.showModal({
      content: "确定取消病历复印申请？",
      cancelText: "取消",
      confirmText: "确定",
      cancelColor: "#989898",
      confirmColor: "#3ECDB5"
    })
    if (!showModalRes.confirm) {
      return false
    }
    const { id = "" } = this.detailData
    const { code, data = {}, msg } = await Api.cancelOrder({ id, status: 4 })
    if (code !== 0) {
      return
    }

    wx.showToast({
      title: "取消成功",
      icon: "success",
      duration: 1500,
      success: () => {
        setTimeout(() => {
          this.orderDetail(id)
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 300
          })
        }, 1500)
      }
    })
  }

  createOrder = async () => {
    const {
      pid = "",
      grid = "",
      patientName = "",
      id = "",
      hospitalTimes = ""
    } = this.detailData
    const { code, data = {} } = await Api.createOrder({
      pid,
      patientId: hospitalTimes,
      patientName,
      bizType: "medical_copy",
      mcId: id,
      patCardNo: grid
    })
    if (code !== 0) {
      return
    }
    const { orderId = "" } = data
    this.registerPayOrder(orderId)
  }

  /**
   * 创建支付订单
   * @param item
   */
  async registerPayOrder(orderId) {
    let bizContent
    try {
      bizContent = JSON.stringify(this.getBizContent() || [])
    } catch (e) {
      bizContent = "[]"
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent
    })
    if (code !== 0) {
      return
    }
    const { payOrderId = "" } = data
    wepy.redirectTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=BLFY&id=${this
        .detailData.id || ""}`
    })
  }

  /**
   * 获取订单展示信息
   * @returns {*[]}
   */
  getBizContent() {
    const { detailData = {} } = this || {}
    return [
      { key: "费用类型", value: "病历复印" },
      { key: "就诊人姓名", value: detailData.patientName || "" },
      { key: "病历号", value: detailData.pid || "" }
    ]
  }
}
</script>
