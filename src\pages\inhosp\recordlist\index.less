@import "../../../resources/style/mixins";

page{
  
}

.p-page{
  
}

.m-list{
  overflow: hidden;
  .list-item{
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    margin: 20rpx 0;
    background-color: #fff;
    
    &.list-item-S,&.list-item-L{
      
    }
    &.list-item-F{
    
    }
    &.list-item-C{
    
    }
  }
  .item-icon{
    width: 60rpx;
    height: 60rpx;
    overflow: hidden;
    border-radius: 50%;
    image{
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-main{
    flex: 1;
    margin-left: 30rpx;
  }
  .main-tit{
    font-size: 34rpx;
    color:@hc-color-title;
  }
  .main-txt{
    font-size: 30rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
  }
  .item-extra{
    text-align: right;
  }
  .extra-tit{
    font-size: 34rpx;
    color:@hc-color-text;
  }
  .extra-txt{
    font-size: 30rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
  }
  
  .list-item{
    .item-icon{
      background-color: @hc-color-warn;
    }
    &.list-item-success,&.list-item-lock{
      .item-icon{
        background-color: @hc-color-primary;
      }
      .extra-tit{
        color:@hc-color-warn;
      }
    }
    &.list-item-fail{
      .item-icon{
        background-color: @hc-color-error;
      }
    }
    &.list-item-cancel{
      .item-icon{
        background-color: @hc-color-text;
      }
    }
    &.list-item-abnormal{
      .item-icon{
        background-color: @hc-color-warn;
      }
    }
  }
  .unit-label{
    margin-left: 5px;
    display: inline-block;
    font-size: 24rpx;
    padding: 0 6rpx;
    color:#fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 4rpx;
  }
}
