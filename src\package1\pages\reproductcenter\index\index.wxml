<view class="inhosp">
  <outpatient :config.sync="outpatientConfig" :patient.sync="outpatient" :emptyNotice.sync="emptyNotice"></outpatient>
  <view class="inhosp-container">
    <view class="info-tips" wx:if="{{outpatientConfig.infoShow}}">
      住院记录信息，共<text>{{inHospitalList.length}}</text>条
    </view>
    <view class="info-list" wx:if="{{inHospitalList.length}}">
      <block wx:for="{{inHospitalList}}" wx:key="{{index}}">
        <view class="info-item" @tap="toDaily({{item}})">
          <view class="item-left">
            <!-- <view class="dept">科室：<text class="attr-val">{{item.deptName}}</text></view>
            <view class="date">时间：<text class="attr-val">{{item.inDate + '~' + item.outDate}}</text></view> -->
            <view class="item-left-title">{{item.inDate ||  '-' + '~' + item.outDate || '-'}}  {{item.deptName || item.doctorName}}</view>
          </view>
          <view class="rt-arrow"></view>
        </view>
      </block>
    </view>
  </view>
  <empty wx:if="{{!outpatientConfig.infoShow}}">
    <text slot="text">你还没有任何住院信息</text>
  </empty>
</view>