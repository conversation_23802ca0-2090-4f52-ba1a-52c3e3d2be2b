@import "../../../resources/style/mixins";

page {
  height: 100%;
  overflow: hidden;
}

.p-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

//医生列表
.m-list {
  margin-bottom: 20rpx;
  // background-color: #fff;
  // &.list-all-box{
  //   padding-top: 0;
  //   border-top: 2rpx solid @hc-color-border;
  // }
  .list-item {
    margin: 24rpx 32rpx;
    border-radius: 8rpx;
    background: #fff;
    padding: 32rpx;
  }
  .item-box {
    display: flex;
    align-items: center;
  }
  .item-other {
    margin-top: -10rpx;
    font-size: 28rpx;
    color: @hc-color-warn;
    padding-bottom: 20rpx;
  }
  .other-date {
    &:after {
      display: inline;
      content: "、";
    }
    &:last-child {
      &:after {
        content: "";
      }
    }
  }
  .list-item {
  }
  .item-hd {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 119rpx;
    height: 133rpx;
    margin-right: 24rpx;
    border-radius: 12rpx;
    overflow: hidden;
    text-align: center;
    image {
      vertical-align: top;
    }
  }
  .item-bd {
    flex: 1;
    overflow: hidden;
  }
  .bd-info {
    display: flex;
  }
  .info-lt {
    flex: 1;
  }
  .lt-title {
    color: @hc-color-title;
    font-size: 32rpx;
    font-weight: 600;
  }
  .lt-address {
    .base-1 {
    }
    .base-2 {
    }
  }
  .lt-text {
    margin-left: 16rpx;
    color: @hc-color-text;
    font-size: 28rpx;
    font-weight: normal;
  }
  .info-rt {
    margin-left: 20rpx;
  }
  .rt-num {
    font-size: 28rpx;
    color: @hc-color-title;
    margin-right: 10rpx;
  }
  .unit-label {
    display: inline-block;
    background-color: @hc-color-warn;
    border-radius: 100rpx;
    line-height: 40rpx;
    white-space: nowrap;
    padding: 0 12rpx;
    color: #fff;
    font-size: 28rpx;
    .label-disabled {
    }
  }
  .bd-extra {
    padding-top: 5rpx;
    color: rgba(0, 0, 0, .4);
    font-size: 28rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .list-item.no-extra {
    .lt-text {
      margin-top: 20rpx;
    }
    .bd-info {
      align-items: center;
    }
  }
}
