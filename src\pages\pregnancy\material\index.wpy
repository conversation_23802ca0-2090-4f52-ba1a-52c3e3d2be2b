<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { uploadFile } from "@/utils/request";
import { tuoMing } from "@/utils/utils";
import * as Api from "./api";
export default class Material extends wepy.page {
  config = {
    navigationBarTitleText: "111",
    navigationBarBackgroundColor: '#fff',
  };
  data = {
    pid: "",
    name: "",
    personData: {},
    title: "",
    otherIntroduce: "",
    projectId: "",
    ouContent: "",
    imageObj: {
      1: [],
      2: [],
      3: []
    },
    showIdCardImgAdd: true,
    showMarryImgAdd: true,
    tuoMingmanIdNo: "",
    tuoMingWonmentIdNo: "",
    ouContentInfo: ""
  };

  onLoad(options) {
    wx.setNavigationBarTitle({
      title: options.title
    });
    this.pid = options.pid;
    this.name = options.name;
    this.ouContent = wepy.getStorageSync("ouContent") || "";
    this.otherIntroduce = options.otherIntroduce;
    this.projectId = options.projectId;
    this.title = options.title;
    if (options.idCardImg) {
      console.log(options.idCardImg, "options.idCardImg");
      this.imageObj[1] = options.idCardImg.split(",");
    }
    if (options.marriageCertificateImg) {
      this.imageObj[2] = options.marriageCertificateImg.split(",");
    }
    if (options.otherIntroduceImg) {
      this.imageObj[3] = options.otherIntroduceImg.split(",");
    }
    this.getPersonInfo(options.pid);
  }
  onShow() {
    const { pid = "" } = this.$wxpage.options;
    this.getPersonInfo(pid);
  }
  methods = {
    onOuContentInfoInputValue(e) {
      this.ouContentInfo = e.detail.value || "";
      this.$apply();
    },
    actionSheetTap(index = 0, phototype = "idCard") {
      wx.showActionSheet({
        itemList: ["删除"],
        success: res => {
          this.removeItem({ phototype, index });
        },
        fail: res => {
          console.log(res.errMsg);
        }
      });
    },
    previewImage(item, phototype) {
      wepy.previewImage({
        urls: this.imageObj[phototype] || [],
        current: item
      });
    },
    async chooseImage(type) {
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 1,
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"]
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths[0];
        wepy.showLoading({ title: "发送中...", mask: true });
        const { code, data } = await uploadFile(
          "/api/files/uploadpic",
          tempFilePath
        );
        if (code === 0) {
          this.imageObj[type] = this.imageObj[type].concat(data.url);
          this.$apply();
        } else {
          wx.showToast({
            title: "图片上传失败",
            icon: "none",
            duration: 1400
          });
        }
      }
    },
    confirm() {
      this.confirmApply();
    },
    delete(url, type) {
      const urls = this.imageObj[type].filter(i => i !== url);
      this.imageObj[type] = urls;
      this.$apply();
    }
  };

  async getPersonInfo(pid) {
    const { code, data = {} } = await Api.getPersonMaterial({ pid: pid });
    if (code === 0) {
      if (data.idCardImg) {
        const idImg = data.idCardImg.split(",");
        this.imageObj[1] = idImg;
      }
      if (data.marriageCertificateImg) {
        const marryImg = data.marriageCertificateImg.split(",");
        this.imageObj[2] = marryImg;
      }
      this.personData = data;
      this.tuoMingmanIdNo = tuoMing(data.manIdNo, 4, -4);
      this.tuoMingWonmentIdNo = tuoMing(data.womanIdNo, 4, -4);
      // if (idImg.length > 0) {
      //   this.showIdCardImgAdd = false;
      // }
      // if (marryImg.length > 0) {
      //   this.showMarryImgAdd = false;
      // }
      this.$apply();
    }
  }

  async confirmApply() {
    const {
      manGrid,
      manName,
      manAge,
      manIdNo,
      manMobile,
      womanGrid,
      womanName,
      womanAge,
      womanIdNo,
      womanMobile,
      address,
      ivfNo,
      doctorName
    } = this.personData;
    // const imgIdCard = this.imageObj[1].map(item => {
    //   return item.filePath;
    // });
    // const imgMarry = this.imageObj[2].map(item => {
    //   return item.filePath;
    // });
    // const imgOther = this.imageObj[3].map(item => {
    //   return item.filePath;
    // });
    const idCardImgsArr = this.imageObj[1].join(",");
    const marryImgArr = this.imageObj[2].join(",");
    const otherImgArr = this.imageObj[3].join(",");
    if (!idCardImgsArr) {
      wepy.showModal({
        title: "请上传女方身份证正反面照片",
        showCancel: false,
        success: () => {}
      });
      return;
    }
    if (!marryImgArr) {
      wepy.showModal({
        title: "请上传结婚证照片",
        showCancel: false,
        success: () => {}
      });
      return;
    }
    if (!otherImgArr) {
      wepy.showModal({
        title: `请上传${this.otherIntroduce}`,
        showCancel: false,
        success: () => {}
      });
      return;
    }
    if (this.ouContent && !this.ouContentInfo) {
      wepy.showModal({
        title: "请输入您的回答",
        showCancel: false,
        success: () => {}
      });
      return;
    }
    const params = {
      projectName: this.title,
      projectId: this.projectId,
      pid: this.pid,
      manGrid: manGrid ? manGrid : "",
      manName: manName ? manName : "",
      manAge: manAge ? manAge : "",
      manIdNo: manIdNo ? manIdNo : "",
      manMobile: manMobile ? manMobile : "",
      womanGrid: womanGrid ? womanGrid : "",
      womanName: womanName ? womanName : "",
      womanAge: womanAge ? womanAge : "",
      womanIdNo: womanIdNo ? womanIdNo : "",
      womanMobile: womanMobile ? womanMobile : "",
      address: address ? address : "",
      ivfNo: ivfNo ? ivfNo : "",
      doctorName: doctorName ? doctorName : "",
      otherIntroduceName: this.otherIntroduce,
      idCardImg: idCardImgsArr || "",
      marriageCertificateImg: marryImgArr || "",
      otherIntroduceImg: otherImgArr || "",
      applicantName: this.name
    };
    if (this.ouContent) {
      params.ouContentInfo = this.ouContentInfo || "";
      params.ouContentName = this.ouContent || "";
    }
    const { code, data } = await Api.saveMaterial(params);
    if (code === 0) {
      wepy.showToast({
        title: "申请成功",
        icon: "success"
      });
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/pregnancy/recorddetail/index?id=${data.id}`
        });
      }, 1000);
    }
  }
}
</script>


