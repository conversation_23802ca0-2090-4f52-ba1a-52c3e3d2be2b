@import "../../../resources/style/mixins";

page{
  
}

.p-page{
  padding-bottom: 30rpx;
}

.m-lefttime{
  .lefttime-zw{
    width: 100%;
    height: 70rpx;
  }
  .lefttime{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    line-height: 70rpx;
    height: 70rpx;
    background-color: @hc-color-warn;
    color:#fff;
    text-align: center;
    z-index: 9;
  }
}
.m-code {
  display: none;
  background-color: #fff;
  padding: 30rpx;
  &.active {
    display: block;
  }
  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }
  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;
    
    p {
      margin: 20rpx 0;
    }
  }
  .code-img {
    margin-top: 20rpx;
    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry{
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;
  
  .retry-btn{
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color:@hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border:2rpx solid @hc-color-primary;
  }
}

.m-list {
  padding: 30rpx 0;
  margin-top: 20rpx;
  background-color: #fff;
  .list-tit {
    padding: 0 30rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .list {
    padding-top: 17rpx;
  }
  .list-item {
    padding: 17rpx 30rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    
  }
  .item-label {
    color: @hc-color-text;
    min-width: 5em;
  }
  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    text-align: right;
    overflow: hidden;
    margin-left: 20rpx;
  }
  .unit-price {
    font-size: 48rpx;
  }
}
.m-pay{
  margin-top: 30rpx;
  padding: 0 30rpx;
  
  .pay-btn{
    background-color: @hc-color-primary;
    font-size: 34rpx;
    text-align: center;
    line-height: 88rpx;
    height: 88rpx;
    border-radius: 8rpx;
    color:#fff;
  }
}
.m-cancel{
  margin-top: 30rpx;
  padding: 0 30rpx;
  .cancel-btn{
    background-color: #fff;
    font-size: 34rpx;
    text-align: center;
    line-height: 88rpx;
    height: 88rpx;
    border-radius: 8rpx;
    color:@hc-color-title;
  }
}