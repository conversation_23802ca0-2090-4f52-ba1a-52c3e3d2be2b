@import "../../../resources/style/mixins";

page{
  min-height: 100%;
  background-color: #fefefe;
}

.p-page{
  padding-top: 80rpx;
}

.m-gif{
  text-align: center;
}
.wait-img{
  display: block;
  width: 150rpx;
  height: 150rpx;
  margin: 200rpx auto 150rpx auto;
}
.m-text{
  font-size: 36rpx;
  text-align: center;
  color: @hc-color-title;
  padding: 0 30rpx;
  margin-top: 20rpx;
}
.m-time{
  margin-top: 20rpx;
  font-size: 34rrpx;
  text-align: center;
  color: @hc-color-warn;
  padding: 0 30rpx;
}
.m-tips{
  opacity: 0.8;
  font-size: 30rrpx;
  color: @hc-color-primary;
  text-align: center;
  padding: 0 30rpx;
  margin-top: 300rpx;
}


.pacman {
  width: 100rpx;
  position: relative;
  transform: scale(1);
  margin: 200rpx auto 150rpx auto;
}
.pacman > view.item-2 {
  -webkit-animation: pacman-balls 1s -0.99s infinite linear;
  animation: pacman-balls 1s -0.99s infinite linear;
}
.pacman > view.item-3 {
  -webkit-animation: pacman-balls 1s -0.66s infinite linear;
  animation: pacman-balls 1s -0.66s infinite linear;
}
.pacman > view.item-4 {
  -webkit-animation: pacman-balls 1s -0.33s infinite linear;
  animation: pacman-balls 1s -0.33s infinite linear;
}
.pacman > view.item-5 {
  -webkit-animation: pacman-balls 1s 0s infinite linear;
  animation: pacman-balls 1s 0s infinite linear;
}
.pacman > view.item-1 {
  width: 0rpx;
  height: 0rpx;
  border-right: 25rpx solid transparent;
  border-top: 25rpx solid #3ECEB6;
  border-left: 25rpx solid #3ECEB6;
  border-bottom: 25rpx solid #3ECEB6;
  border-radius: 25rpx;
  -webkit-animation: rotate_pacman_half_up 0.5s 0s infinite;
  animation: rotate_pacman_half_up 0.5s 0s infinite;
  position: relative;
  left: -30rpx;
}
.pacman > view:nth-child(2) {
  width: 0rpx;
  height: 0rpx;
  border-right: 25rpx solid transparent;
  border-top: 25rpx solid #3ECEB6;
  border-left: 25rpx solid #3ECEB6;
  border-bottom: 25rpx solid #3ECEB6;
  border-radius: 25rpx;
  -webkit-animation: rotate_pacman_half_down 0.5s 0s infinite;
  animation: rotate_pacman_half_down 0.5s 0s infinite;
  margin-top: -50rpx;
  position: relative;
  left: -30rpx;
}
.pacman > view:nth-child(3), .pacman > view:nth-child(4), .pacman > view:nth-child(5), .pacman > view:nth-child(6) {
  background-color: #3ECEB6;
  width: 15rpx;
  height: 15rpx;
  border-radius: 100%;
  margin: 2rpx;
  width: 10rpx;
  height: 10rpx;
  position: absolute;
  -webkit-transform: translate(0, -6.25rpx);
  transform: translate(0, -6.25rpx);
  top: 25rpx;
  left: 70rpx;
}

@-webkit-keyframes rotate_pacman_half_up {
  0% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
  
  50% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  
  100% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
}

@keyframes rotate_pacman_half_up {
  0% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
  
  50% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  
  100% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
}

@-webkit-keyframes rotate_pacman_half_down {
  0% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  
  50% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@keyframes rotate_pacman_half_down {
  0% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  
  50% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@-webkit-keyframes pacman-balls {
  75% {
    opacity: 0.7;
  }
  
  100% {
    -webkit-transform: translate(-100rpx, -6.25rpx);
    transform: translate(-100rpx, -6.25rpx);
  }
}

@keyframes pacman-balls {
  75% {
    opacity: 0.7;
  }
  
  100% {
    -webkit-transform: translate(-100rpx, -6.25rpx);
    transform: translate(-100rpx, -6.25rpx);
  }
}