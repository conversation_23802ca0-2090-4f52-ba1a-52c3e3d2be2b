<!--  -->
<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveDetailMixin from '@/mixins/live/detailMixin';
import Empty from '@/components/empty/index';
import * as Api from './api';
export default class Waiting extends wepy.page {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom'
  };

  // 在mixin中处理了对直播详情的请求
  mixins = [LiveDetailMixin];

  data = {
    recommendedLiveDetail: {},
    liveingCount: 0,
  };

  components = {
    empty: Empty,
  };

  methods = {
    onBack() {
      wepy.navigateBack({
        delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
      });
    },
    onRefresh() {
      this.reqRecommendedLive();
    },
    onCardTab() {
      wepy.redirectTo({ url: `/package1/pages/live/play/index?id=${this.recommendedLiveDetail.id}` });
    }
  };

  events = {};

  watch = {
    // liveDetail(newVal) {
    //   if (newVal && newVal.isScreencap == 1) {
    //     wepy.redirectTo({ url: `/package1/pages/live/video/index?id=${newVal.id}` });
    //   }
    // }
  };

  computed = {};

  onLoad() {
    this.reqRecommendedLive();
  }

  onShow() {}
  async reqRecommendedLive() {
    const {code, data} = await Api.recommendedLive();
    if (code == 0 && data) {
      this.liveingCount = data.counts;
      this.recommendedLiveDetail = data.liveing;
      this.$apply();
    }
  }
}
</script>
