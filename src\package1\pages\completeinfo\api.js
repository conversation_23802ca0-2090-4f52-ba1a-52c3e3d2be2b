import { REQUEST_QUERY } from "@/config/constant";
import { post } from "@/utils/request";

export const getCardInfo = (param) =>
  post("/api/customize/register/queryRelationPatients", param);
export const getPatientInfo = (param) =>
  post(
    `/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`,
    param
  );
export const getDict = (param) =>
  post(
    `/api/customize/register/getDict?_route=h${REQUEST_QUERY.platformId}`,
    param
  );
export const getHisConfig = (param) =>
  post("/api/user/getbindcardprofile", param);
// export const bindPatient = (param) => post(`/api/user/bindpatients?_route=h${REQUEST_QUERY.platformId}`, param);
// export const bindPatient = (param) => post("/api/user/bindpatients", param);
export const bindPatient = (param) =>
  post(
    `/api/customize/updatePatientsInfoInfo?_route=h${REQUEST_QUERY.platformId}`,
    param
  );

export const getSecret = (param) =>
  post(`/api/customize/getSecret?_route=h${REQUEST_QUERY.platformId}`, param);
