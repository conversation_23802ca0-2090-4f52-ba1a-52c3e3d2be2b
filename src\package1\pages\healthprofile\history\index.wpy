<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Moment from 'moment';
  import TopTip from '@/components/toptip/index';
  import { validator } from '@/utils/utils';
  import * as Api from './api';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '既往病史',
    };

    components = {
      toptip: TopTip,
    };
    
    onLoad(options) {
      console.log("history信息", options);
      const { sex = 'F', pid = '', healthId = '', type = '' } = options || {};
      this.sex = sex;
      this.pid = pid;
      this.healthId = healthId;
      this.type = type;
    }

    onShow() {
      this.cardList = [];
      this.getMaritalInfo();
      this.queryDiseaseList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // historyList: ['肝炎', '结合', '肾脏疾病', '心血管疾病', '尿路系感染', '性传播疾病史', '阑尾炎', '盆腔炎'],
      currentSelectTripType: 'pinche',
      historyDisease: [],
      historyFaker: [],
      anamnesisRel: [],
      anamnesisRecord: '',

      options: {},
      placeholderColor: '',
      errorColor: 'red',
      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      type: '',
      patientName: '',
      idNo: '',
      pid: '',
      sex: 'F',
      healthId: '',
      isUpdateInfo: false,
    };

    // 查询历史疾病
    async queryDiseaseList(){
      
      console.log('history', this)
      const param = {
        type: "anamnesis_record",
      }
      const { code, data } = await Api.queryDiseaseList(param);
      data.forEach((item, index) => {
        return item.isChecked = false;
      })
      this.historyDisease = data;
      const { historyDisease } =this;
      console.log("data", historyDisease);
      this.getChecked();
      this.$apply();
    }
    getChecked() {
      const { sex = '' } = this;
      // if( sex == 'F'){
      //   let { historyDisease = [] } = this;
      // } else {
      //   let { historyDisease = [] } = this;
      // }
      let  { anamnesisRel = [], historyDisease } = this;
      console.log("anamnesisRelchecked", anamnesisRel)
      console.log('-------------', historyDisease);
      console.log('-------------', anamnesisRel);
      console.log('-------------', anamnesisRel.length)
      if(anamnesisRel.length != 0) {
        console.log("cehsi1")
        anamnesisRel = anamnesisRel.split(',');
        console.log('转为数组', anamnesisRel)
        for (let i = 0; i < historyDisease.length; i++) {
          for(let j = 0; j < anamnesisRel.length; j++) {
            if(historyDisease[i].id == anamnesisRel[j]){
              historyDisease[i].isChecked = true ;
            }
          }
          this.historyDisease = historyDisease;
          console.log("this.historyDisease", this.historyDisease)
        }
      }
      
      this.$apply();
    }

    //上传并筛选出id
    getDiseasse() {
      const { sex = '', historyDisease = [] } = this;
      let { historyFaker = [], anamnesisRel = [] } = this;
        // questionFaker = questionFaker.split(',');
      for(let i = 0; i < historyDisease.length; i++) {
        if( historyDisease[i].isChecked ){
          let ques = historyDisease[i].id;
          console.log("ques", ques);
          historyFaker.push(ques);
          this.historyFaker = historyFaker;
          console.log("this.historyFaker", this.historyFaker)
        }
      }
      this.$apply();
    }

    async getMaritalInfo(){
      let patientInfo = {};
      if (this.sex === 'F') {
        patientInfo = this.$parent.femalInfo || {};
      } else {
        patientInfo = this.$parent.manInfo || {};
      }
      console.log('patientInfo', patientInfo)
      const { basicInfo = {}, periodRecord: infoObj = {}, pid = '', healthId = '', anamnesisRecord = '' } = patientInfo;
      this.anamnesisRecord = anamnesisRecord;
      console.log("anamnesisRecord", anamnesisRecord)
      let { anamnesisRel = [] } = patientInfo;
      this.periodRecord = infoObj;
      if(anamnesisRel == "null"){
        console.log('11111111')
        anamnesisRel = new Array();
        anamnesisRel = [];
        console.log('anamnesisRel', anamnesisRel)
      }
      this.anamnesisRel = anamnesisRel;

      console.log("getMaritalInfo", this.anamnesisRel)
      this.periodRecord = infoObj;
      // 设置值
      if (basicInfo.name && basicInfo.idNo) {
        this.name = basicInfo.name || '';
        this.idNo	= basicInfo.idNo || '';
      }
      console.log('设置picker内容')
      if (infoObj.yjzq) {
        this.yjzq = infoObj.yjzq || '';
        this.yjts	= infoObj.yjts || '';
        this.ccnl	= infoObj.ccnl || '';
        this.jlqk = infoObj.jlqk || '';
        this.mcyjsj = infoObj.mcyjsj || '';
        this.healthId = healthId || '';

        // 设置picker内容
        const processList = ['yjzq', 'yjts', 'ccnl', 'jlqk'];
        processList.forEach((name) => {
          const value = infoObj[name];
          const list = this[`${name}List`];
          if (value && list && list.length > 0) {
            list.forEach((item, index) => {
              if (item === value) {
                this[`${name}Index`] = index;
              }
            });
          }
        });
      }
      this.$apply();
    }

    getFormData(){
      const { name = '', idNo = '', pid = '', sex = '', healthId = '', anamnesisRecord = '' } = this;
      let { historyFaker = [] } = this;
      console.log("historyFaker.length", historyFaker.length)
      if(historyFaker.length > 0){
        // 去重
        historyFaker = [...new Set(historyFaker)];
        historyFaker = historyFaker.join(",")
      } else {
        historyFaker = 'null';
      }      
      return {
        name,
        idNo,
        pid,
        patHisNo: pid,
        sex,
        healthId,
        anamnesisRel: historyFaker,
        anamnesisRecord: anamnesisRecord,
      };
    }

    validator(id){
        console.log('开始调用validator', id)
        const validate = {
          patientName: {
            regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
            errTip: '请输入2-8位合法姓名',
          },
          idNo: {
            regexp: (() => {
              const regexp = validator.idCard;
              if(typeof regexp === 'function'){
                return (val) => regexp(val.idNo);
              } else {
                return /^\S+$/;
              }
            })(),
            errTip: '请输入18位身份证',
          },
          // patientAddress: {
          //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          //   errTip: '请输入有效的住址',
          // },
          patientAddress: {
            regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
            errTip: '请输入有效的住址',
          },
          patientMobile: {
            regexp: /^1\d{10}$/,
            errTip: '请输入正确的手机号',
          },
          pid: {
            regexp: /^\d{3,8}/,
            errTip: '请输入正确pid号'
          }
        };


        const value = this.getFormData();

        let hasErr = false;
        for(let o in value){
          const obj = validate[o];
          if(obj && obj.regexp){
            let thisErr = false;
            if(typeof obj.regexp === 'function'){
              const retObj = obj.regexp(value);
              console.log('retObj', retObj)
              if(!retObj.ret){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            } else {
              if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            }
            if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
              this.errorElement[obj.errTarget || o] = true;
              this.toptip = obj.errTip || '';
              const errTimer = setTimeout(() => {
                this.toptip = '';
                this.$apply();
                clearTimeout(errTimer);
              }, 2000);
              break;
            }
          }
        }
        return hasErr;
      }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      selectedIsTrue(idx) {
        const { historyDisease = [] } = this;
        historyDisease[idx].isChecked = true;
        this.historyDisease = historyDisease;
        console.log("e", idx)
      },
      selectedIsFalse(idx) {
        const { historyDisease } = this;
        historyDisease[idx].isChecked = false;
        this.historyDisease = historyDisease;
        console.log("e", idx)
      },

      navigateTo(url) {
        let { qryType = 2 } = this;
        // 跳转带参数 绑定本人或他人
        const jumpUrl = `${url}?qryType=${this.cardList.length > 0 ? 2 : 1}`
        wepy.navigateTo({ url: jumpUrl });
      },

    };

    async formSubmit(e){
      console.log('formSubmit e', e)
      const { textarea = '' } = e.detail.value;
      this.anamnesisRecord = textarea;
      this.getDiseasse();
      let value = this.getFormData();
      console.log('formData', value)
      this.hasErr = this.validator();
      if(this.hasErr){
        return false;
      }
      let param = param = {
        pid: value.pid || '',
        sex: value.sex || '',
        healthId: value.healthId || '',
        // periodRecord: value,
        anamnesisRel: value.anamnesisRel || '',
        anamnesisRecord: textarea,
      };
      const { code, data = {}, msg } = await (this.type === 'update' ? Api.updateHealthRecord : Api.addHealthRecord)(param);
      // const { idTypes = [], isNewCard, patCards = [], patientTypes = [], relationTypes = [] } = this.hisConfig
      if (code == 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    }
  }
</script>
