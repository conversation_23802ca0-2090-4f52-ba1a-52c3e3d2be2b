@import "../../../resources/style/mixins";

page{
  
}

.p-page{
  padding-bottom: 30rpx;
  &.scroll-page{
    height: calc(100vh - 148rpx);
    overflow: auto;
  }
  .wgt-folding-tit{
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
  }
  .p-page-main{
    margin: 0 24rpx;
    padding: 32rpx;
    border-radius: 8rpx;
    background-color: #fff;
  }
}

.m-lefttime{
  .lefttime-zw{
    width: 100%;
    height: 70rpx;
  }
  .lefttime{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    line-height: 70rpx;
    height: 70rpx;
    background-color: @hc-color-warn;
    color:#fff;
    text-align: center;
    z-index: 9;
  }
}
.m-code {
  display: none;
  margin-bottom: 32rpx;
  padding-bottom: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
  &.active {
    display: block;
  }
  .code-tit {
    font-size: 28rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 32rpx;
  }
  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;
    
    p {
      margin: 20rpx 0;
    }
  }
  .code-img {
    margin-top: 20rpx;
    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry{
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;
  
  .retry-btn{
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color:@hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border:2rpx solid @hc-color-primary;
  }
}

.m-list {
  margin-bottom: 32rpx;
  padding-bottom: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
  &:first-child{
    margin-top: 0;
  }
  &:last-child{
    margin: 0;
    padding: 0;
    border: none;
  }
  .list-tit {
    font-weight: 600;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
  }
  .list {
    padding-top: 17rpx;
  }
  .list-item {
    padding: 17rpx 0;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    
  }
  .item-label {
    color: rgba(0, 0, 0, 0.40);
    min-width: 5em;
    font-size: 32rpx;
  }
  .item-value {
    color: rgba(0, 0, 0, 0.90);
    flex: 1;
    // word-wrap: break-word;
    // white-space: pre-wrap;
    font-size: 32rpx;
    // white-space: nowrap;
    word-break: break-all;
    overflow: hidden;
    margin-left: 20rpx;
  }
  .unit-price {
    // font-size: 48rpx;
  }
  .wgt-folding-tit{
    padding: 0;
  }
}
.m-pay{
  margin-top: 30rpx;
  padding: 0 30rpx;
  
  .pay-btn{
    border-radius: 76rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    font-size: 34rpx;
    text-align: center;
    line-height: 88rpx;
    height: 88rpx;
    color:#fff;
    font-weight: 600;
  }
}
.m-cancel{
  margin-top: 30rpx;
  padding: 0 30rpx;
  .cancel-btn{
    border-radius: 76rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    font-size: 34rpx;
    text-align: center;
    line-height: 88rpx;
    height: 88rpx;
    color: @hc-color-white;
    font-weight: 600;
  }
}
.ad-register{
  margin: 20rpx;
  .list-item{
    display: flex;
    align-items: center;
    padding: 25rpx 30rpx;
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 4rpx;
  }
  .item-hd{
    padding-right: 30rpx;
    image{
      width: 100rpx;
      height: 100rpx;
      vertical-align: top;
    }
  }
  .item-bd{
    flex: 1;
    display: flex;
    align-items: center;
    padding-right: 30rpx;
  }
  .bd-main{
    flex: 1;
  }
  .main-tit{
    font-size: 34rpx;
    color:@hc-color-title;
  }
  .main-txt{
    margin-top: 5rpx;
    font-size: 26rpx;
    color:@hc-color-text;
  }
}

.btn-box{
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0px -2rpx 8rpx 0px rgba(0, 0, 0, 0.04);
  .pay-money{
    padding-left: 24rpx;
    color: #000;
    font-size: 28rpx;
    font-weight: 600;
    text:last-child{
      color: #D2962B;
    }
  }
  .btn{
    display: flex;
    padding-right: 24rpx;
    .m-btn{
      width: 212rpx;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      font-weight: 600;
      .pay-btn{
        color: #FFF;
        border-radius: 0 76rpx 76rpx 0;
        background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      }
      .cancel-btn{
        border-radius: 76rpx 0 0 76rpx;
        background: #E6EDEE;
        
      }
    }
  }
}
