<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '候补记录',
      navigationBarBackgroundColor: '#fff',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 候补记录列表
      orderList: [],
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'empty': Empty,
    };

    props = {};

    onShow(options) {
      this.getOrderList();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到候补详情页
       * @param item
       */
      bindGoDetail(item){
        const { pid } = this.$wxpage.options;
        const { waitId = '' } = item;
        wepy.navigateTo({
          url: `/pages/register/alternatedetail/index?waitId=${waitId}&patHisNo=${pid}`,
        });
      },
    };

    async getOrderList() {
      const { pid } = this.$wxpage.options;
      const { code, data = {} } = await Api.orderList({patHisNo:pid});
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      const { resultCode, waitQueueDetails = [] } = data;
      this.orderList = waitQueueDetails || [];
      this.$apply();
    }
  }
</script>
