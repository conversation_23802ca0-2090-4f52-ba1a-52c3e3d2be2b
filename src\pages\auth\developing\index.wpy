<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '即将开放',
    };

    onHide() {
      this.closePage();
    }

    onUnload() {
      wepy.HAS_ERROR_998 = false;
    }

    data = {};

    methods = {};

    events = {};

    async closePage() {
      wepy.HAS_ERROR_998 = false;

      const { url = '' } = this.$wxpage.options || {};
      if (!url) {
        return false;
      }
      await wepy.redirectTo({
        url: `${decodeURIComponent(url)}`,
      });
    }
  }
</script>
