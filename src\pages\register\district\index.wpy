<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  const resData = {
    deptList: [
      { deptName: '心内科1', },
      { deptName: '心内科2', },
      { deptName: '心内科2', },
      { deptName: '心内科2', },
      { deptName: '心内科2', },
      { deptName: '心内科2', },
    ],
    doctorList: [
      {
        deptName: '心内科1',
        doctorName: '王小一',
        doctorTitle: '医师1',
        skill: '医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1医师1'
      },
      { deptName: '心内科2', doctorName: '王小二', doctorTitle: '医师2' },
      { deptName: '心内科2', doctorName: '王小二', doctorTitle: '医师2' },
      { deptName: '心内科2', doctorName: '王小二', doctorTitle: '医师2' },
      { deptName: '心内科2', doctorName: '王小二', doctorTitle: '医师2' },
    ],
  };
  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '首页',
    };

    components = {
    }

    onLoad(options) {
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      searchFocus: false,
      searchResult: resData,
      searchValue: '',
      showAllFun: false,
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      bindSearchFocus(){
        this.searchFocus = true;

        return '';
      },
      bindSearchBlur(){
        this.searchFocus = false;

        //        失去焦点，清空数据
        this.searchResult = {};
        this.searchValue = '';
        return '';
      },
      bindSearchInput(e){
        let { value } = e.detail;
        this.searchValue = value;

        //        获取搜索结果
        this.searchResult = resData;

        return value;
      },
      toggleAllFun(flag){
        this.showAllFun = flag;
      },
    };
  }
</script>
