@import "../../../resources/style/mixins";

page {}

.p-page {}

.top-msg {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #BE8014;
  background: #FFFAF1;
  text-align: center;
  
  // 数值居中
  
}

.m-list {
  padding: 24rpx;
  overflow: hidden;

  .list-item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0px 1rpx 2rpx 0px rgba(0, 0, 0, 0.08);

    &.list-item-S,
    &.list-item-L {}

    &.list-item-F {}

    &.list-item-C {}
  }

  .item-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    overflow: hidden;
    border-radius: 50%;

    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }

  .item-status {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }

  .item-main {
    flex: 1;
  }

  .main-tit {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
  }

  .main-txt {
    font-size: 28rpx;
    color: @hc-color-text;
    margin-top: 8rpx;
  }

  .item-extra {
    margin-top: 24rpx;
  }

  .extra-tit {
    margin-top: 8rpx;
    color: @hc-color-text;

    .fee {
      font-weight: 600;
      color: #FF5500; // 橙色字体
      font-weight: bold;
    }
  }

  .extra-txt {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .list-item {
    .item-icon {
      background-color: @hc-color-warn;
    }

    &.list-item-success,
    &.list-item-lock {
      .item-icon {
        background-color: @hc-color-primary;
      }
    }

    &.list-item-fail {
      .item-icon {
        background-color: @hc-color-error;
      }
    }

    &.list-item-cancel {
      .item-icon {
        background-color: @hc-color-text;
      }
    }

    &.list-item-abnormal {
      .item-icon {
        background-color: @hc-color-warn;
      }
    }
  }

  .unit-label {
    margin-left: 16rpx;
    display: inline-block;
    font-size: 22rpx;
    padding: 4rpx 16rpx;
    color: #fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 60rpx;
  }
}

.tab-container {
  display: flex;
  padding: 20rpx 0;
  background: #fff;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    color: #666;
    position: relative; 

    &.active {
      color: @hc-color-primary; 

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 20%; // 设置为文字宽度的一半
        height: 4rpx;
        background: @hc-color-primary; 
        border-radius: 2rpx;
      }
     
    }

  }
}