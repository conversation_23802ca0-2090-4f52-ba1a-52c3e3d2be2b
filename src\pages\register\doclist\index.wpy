<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Empty from "@/components/empty/index";
import WxsUtils from "../../../wxs/utils.wxs";
import BottomLogo from '@/components/bottomlogo/index';
import * as Utils from "@/utils/utils";
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "医生列表",
    navigationBarBackgroundColor: '#fff'
  };

  components = {
    empty: Empty,
    "bottom-logo": BottomLogo
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  onLoad(options) {
    this.options = options;
    const {
      subHisId = "",
      deptId = "",
      doctorStrNo = "",
      profileId = "",
      note = "",
      skill = ""
    } = options;
    if (skill) {
      this.modal = {
        title: "温馨提示",
        contentType: "string",
        content: skill,
        okText: "确认"
      };
      this.showDescModal = true;
      this.$apply();
    }
    if (this.tipsContent[deptId] && !skill) {
      this.modal = {
        title: "温馨提示",
        contentType: typeof this.tipsContent[deptId],
        content: this.tipsContent[deptId],
        okText: "确认"
      };
      this.showDescModal = true;
      this.$apply();
    }
    if (doctorStrNo) {
      this.doctorStrNo = JSON.parse(doctorStrNo);
      this.$apply();
    }
    if (profileId) {
      this.profileId = profileId;
      this.$apply();
    }
    this.getScheduleList({ subHisId, deptId });
    this.note = note;
    // this.getNoteProfile();
    if (options.deptName) {
      wepy.setNavigationBarTitle({ title: options.deptName });
    }
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    scheduleListFirstWeek: [],
    scheduleListSecondWeek: [],
    options: {},
    // 排班日期
    scheduleList: [],
    // 按日期获取医生列表
    scheduleDoctorList: {},
    // 获取全部医生列表，初始状态为false，代表没有初始化
    doctorList: false,
    // 日期补空位数组
    nullDateArr: [],
    // 选中日期
    selectedDay: "",
    // 日期展开状态
    expandDate: false,
    // 顶部tab，0：按日期，1：按医生
    tabIndex: 0,
    // 弹窗
    showDescModal: false,
    modal: {},
    //遗传科室分类
    doctorStrNo: "",
    profileId: "",

    tipsContent: {
      "0101":
        "您好，欢迎预约生殖健康体检门诊，请按预约时间来我院河东区（长沙市开福区湘雅路84号）二楼护士站，我们将安排专人接待您，体检完成后出具体检报告及健康指导建议，并可根据您的需求为您预约生殖健康专家进行详细生育咨询与指导。",
      "0042": [
        "试管婴儿门诊看诊攻略\n  为满足广大病友的看诊需求，我院现针对试管婴儿主管医生门诊推出预约锁号模式，病友通过以下任一预约模式均可锁定未来两周内的IVF主管医生号。\n1、现场预约锁号：二楼病友服务中心、三楼小厅前台； \n2、APP小程序线上预约锁号：进入中信湘雅APP小程序——点击在线咨询——联系线上护士预约锁号；\n3、电话预约锁号：预约电话",
        { type: "phone", phone: "0731-84372980" },
        "\n  特别提醒：锁号成功后，请您务必于1小时内在我院APP小程序“在线取号”缴费（未及时缴费系统将自动取消已锁号源）。"
      ],
      "0103":
        "为方便与照顾湖北疫区病友的就诊需求，特开放疫区病友的线上门诊特别通道，此通道仅为预约通道，非正式看诊时间。我院接收到预约信息后，会安排专人提前电话联系您，再根据您的具体情况安排合适的医生视频问诊。",
      "0105":
        "为给广大病友在疫情防控期间提供就诊便利，特开通此专病互联网门诊预约通道，此通道为预约通道，非正式问诊时间，接收到您的预约信息后，会有专人电话联系询问您的基本情况，再帮您安排合适的医生和会诊时间。\n请注意：如果您是我院已经接收过试管助孕的老病友，该通道不能预约，请您直接前往我院微信小程序的在线咨询平台进行问题咨询，会有主管医生和专科护士共同为您提供咨询服务。",
      "0106":
        "为给广大病友在疫情防控期间提供就诊便利，特开通互联网门诊预约通道，此通道为预约通道，非正式问诊时间，接收到您的预约信息后，会有专人电话联系询问您的基本情况，再帮您安排合适的问诊时间。",
      "0109":
        "为方便您后续治疗，建议上海周边的病友选择上海国际医学中心生殖中心（中信湘雅分院）",
      "0110":
        "在疫情期间为了有序恢复现场诊疗工作，特开放此通道用于检查及进周预约，如您需要来院做抽血、取白带、排精等检查，请在此界面选择检查预约，预约您的来院时间。如果您已达到进周要求，请根据您的主管医生和实际情况选择河东或河西预约进周。如当天已满，可以预约后面的日期或直接在小程序上咨询您的专科护士。",
      "0114":
        "您好，欢迎选择我院母亲节生育咨询套餐。购买后请到河东院区（长沙市开福区湘雅路84号）二楼护士站凭购买记录找工作人员打印指引单，套餐条款：\n1）该套餐优惠限初诊病人使用。\n2)套餐中未使用的服务项目不做退款，亦不可更换为套餐内其他服务项目。\n3)选择加项未例入套餐价格，需另外支付 ，不享受套餐同等折扣。\n4)本套餐项目仅限购买之日至2020年12 月31日内使用，过期未使用不予退费。\n5)该活动解释权归中信湘雅生殖与遗传专科医院所有。",
      "0091":
        "参与流程：关注我院官方微信小程序→“常用功能”→“生殖健康关怀”→携带教师资格证前往我院二楼护士站“生殖健康”接待处。",
      "0092":
        "参与流程：关注我院官方微信小程序→“常用功能”→“宝贝定制计划”→ 携带教师资格证前往我院二楼护士站“生殖健康”接待处。\n1）该套餐优惠限初诊病人使用。\n要求：活动时间内完成试管助孕前检查，符合试管婴儿助孕技术指征，并于2020年8月31前顺利办理签约手续。\n 现场接待地点：我院二楼护士站“试管宝宝定制计划”接待处，请携带教师资格证前往（夫妻双方一人为教师即可）。",
      "0116":
        "遗传性妇女肿瘤综合防治项目是长沙市卫健委为降低妇女遗传性乳腺癌/卵巢癌发病率，提高妇女生殖健康，提供的免费“筛查-评估-管理”综合防治服务的民生项目。\n项目参与流程：\n1.请先关注微信小程序-注册信息。\n 2.将患者身份证、户口本、病历资料上传在微信小程序中”院外报告上传”栏。\n3.因遗传咨询需要，请您提前了解家族三级亲属的疾病情况，包括您的父母、兄弟姐妹、子女、爷爷、奶奶、外公、外婆、叔、伯、姑、姨、舅。\n4.来院时请携带身份证、户口本和病历资料原件。"
    },
    guideContent: {
      "0042": [
        "1、微信锁号：通过您在三楼前台扫码绑定的主管医生微信留言预约。留言格式：预约医生+预约日期+就诊目的；\n2、电话锁号：助孕失败需复诊的病人请致电",
        { type: "phone", phone: "17375774497" },
        "预约锁号。\n经以上任一预约途径锁号成功后，请您根据预约时间提前至挂号窗口或微信线上取号。"
      ]
    },

    //顶部提示语
    note: "",
    //挂号地点确认弹窗
    areaShowConfirm: false,
    queryParams: null,
    // 进入医生详情页所需参数，用于探测确认页面跳转
    toInfoQuery: null,
    // 搜索
    searchFocus: false,
    searchValue: "",
    allScheduleDoctorList: [],
    allDocList: [],
    // 显示确认挂号弹窗
    showConfirm: false,
    // 确认挂号页面弹窗信息
    confirmData: {},
    // 是否时互联网门诊
    isInternet: false,
    // 选中就诊人信息
    activePatient: {}
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    stop() { },
    // 打电话
    phoneCall(phone) {
      wepy.makePhoneCall({
        phoneNumber: phone
      });
    },
    showGuide(item) {
      const { doctorLevel } = item;
      if (doctorLevel == 0) {
        const content = this.guideContent[this.options.deptId];
        if (!content) {
          return;
        }
        this.modal = {
          title: "预约指南",
          contentType: typeof content,
          content: content,
          okText: "我知道了"
        };
        this.showDescModal = true;
      } else if (doctorLevel == 1) {
        const profileKey = "getAlertNoteProfile_fullNotice_1";
        this.getProfile(profileKey);
      } else if (doctorLevel == 2) {
        const profileKey = "getAlertNoteProfile_fullNotice_2";
        this.getProfile(profileKey);
      } else if (doctorLevel == 3) {
        const profileKey = "getAlertNoteProfile_fullNotice_3";
        this.getProfile(profileKey);
      } else if (doctorLevel == 9) {
        this.$wxpage.showAlternateDialog(item);
      }

      this.$apply();
    },
    hidden() {
      this.showDescModal = false;
      this.$apply();
    },
    /**
     * 改变选中日期
     * @param item
     */
    bindChangeDate(item) {
      this.changeScheduleDoctorList(item);
    },
    /**
     * 改变日期展开状态
     */
    bindChangeExpandDate(nextStatus) {
      this.expandDate = nextStatus;
    },
    /**
     * 修改tab状态（按医生、按日期）
     */
    bindChangeTabIndex(index) {
      if (this.tabIndex === index) {
        return;
      }
      if (this.doctorList === false && index === 1) {
        // 首次点击按医生挂号时，获取全部医生数据
        this.getAllDoctor();
      }
      this.tabIndex = index;
    },
    /**
     * 带日期跳转至医生详情页
     * @param item
     */
    bindGoDocInfoWithDate(item = {}) {
      if (item.leftSource <= 0) {
        this.showGuide(item)
        return false;
      }
      const { doctorId = "", doctorRemark = "", positName = "" } = item;
      const { subHisId = "", deptId = "" } = this.options;
      const query = Utils.jsonToQueryString({
        subHisId,
        deptId,
        doctorId,
        scheduleDate: this.selectedDay,
        doctorRemark,
        positName,
        areaCode: this.options.areaCode || ""
      });
      this.toInfoQuery = query;
      wepy.navigateTo({
        url: `/pages/register/docinfo/index?${query}`
      });
      // 现场挂号需要弹窗确认，互联网挂号不需要（按日期才需要，按医生挂号没有院区字段）
      // if (this.options && this.options.deptType === '1' && this.tabIndex == 0) {
      //   this.areaShowConfirm = true;
      //   this.queryParams = item;
      // } else {
      //   wepy.navigateTo({
      //     url: `/pages/register/docinfo/index?${query}`
      //   });
      // }
    },
    /**
     * 跳转至医生详情页
     * @param item
     */
    bindGoDocInfo(item) {
      const { doctorId = "", doctorRemark = "", positName = "" } = item;
      const { subHisId = "", deptId = "" } = this.options;
      const query = Utils.jsonToQueryString({
        subHisId,
        deptId,
        doctorId,
        scheduleDate: this.selectedDay,
        doctorRemark,
        positName,
        areaCode: this.options.areaCode || ""
      });
      this.toInfoQuery = query;
      // 现场挂号需要弹窗确认，互联网挂号不需要（按日期才需要，按医生挂号没有院区字段）
      if (this.options && this.options.deptType === "1" && this.tabIndex == 0) {
        // this.areaShowConfirm = true;
        this.queryParams = item;
      } else {
        wepy.navigateTo({
          url: `/pages/register/docinfo/index?${query}`
        });
      }
    },
    // 挂号确认
    agree(flag) {
      this.areaShowConfirm = !this.areaShowConfirm;
      if (flag && this.toInfoQuery) {
        wepy.navigateTo({
          url: `/pages/register/docinfo/index?${this.toInfoQuery}`
        });
      }
    },
    // 开启搜索状态
    bindSearchFocus() {
      this.searchFocus = true;
      if (this.tabIndex === 0) {
        this.allScheduleDoctorList = [...this.scheduleDoctorList.doctorList];
      } else {
        this.allDocList = [...this.doctorList];
      }
    },
    // 关闭搜索状态
    bindSearchBlur() {
      this.searchFocus = false;
      // 取消搜索，重置数据
      this.searchValue = "";
      if (this.tabIndex === 0) {
        this.scheduleDoctorList.doctorList = [...this.allScheduleDoctorList];
      } else {
        this.doctorList = [...this.allDocList];
      }
    },
    // 输入搜索内容
    bindSearchInput(e) {
      let { value } = e.detail;
      value = value.trim();
      this.searchValue = value;
      console.log(value);

      if (!value) {
        if (this.tabIndex === 0) {
          this.scheduleDoctorList.doctorList = [...this.allScheduleDoctorList];
        } else {
          this.doctorList = [...this.allDocList];
        }
      } else {
        if (this.tabIndex === 0) {
          const _copyAllDocList = this.allScheduleDoctorList.filter(v =>
            v.doctorName.includes(value)
          );
          this.scheduleDoctorList.doctorList = [..._copyAllDocList];
        } else {
          const _copyAllDocList = this.allDocList.filter(v =>
            v.doctorName.includes(value)
          );
          this.doctorList = [..._copyAllDocList];
        }
      }
    },
    showAlternateDialog(item) {
      wepy
        .showModal({
          title: "温馨提示",
          content: "暂无号源，正在申请候补号源，确认是否继续？",
          cancelText: "取消",
          confirmText: "确认继续",
          confirmColor: "#3ecdb5"
        })
        .then(async res => {
          if (res.confirm) {
            // this.confirmData = {...item, leftBindNum: 0, patientList: []};
            // await this.getPatientList();
            // this.toggleConfirm(true);
            const { doctorId = "", doctorRemark = "", positName = "" } = item;
            const { subHisId = "", deptId = "" } = this.options;
            const query = Utils.jsonToQueryString({
              subHisId,
              deptId,
              doctorId,
              scheduleDate: this.selectedDay,
              doctorRemark,
              positName,
              areaCode: this.options.areaCode || ""
            });
            this.toInfoQuery = query;
            wepy.navigateTo({
              url: `/pages/register/docinfo/index?${query}`
            });
          }
        });
    },
    bindToggleConfirm(nextStatus) {
      this.toggleConfirm(nextStatus);
    },
    bindChangePatient(item) {
      this.changeActivePatient(item);
    },
    bindGoBindUser() {
      wepy.navigateTo({
        url: "/pages/bindcard/scancard/index"
      });
    },
    // 确认候补
    async bindRegisterConfirm() {
      // 走候补流程
      // 失败提示
      wepy.showModal({
        title: "候补失败",
        content: "错误文案",
        showCancel: false,
        confirmText: "确认",
        confirmColor: "#3ecdb5"
      });
      // 成功跳转
      wepy.navigateTo({
        url: "/pages/register/alternatedetail/index"
      });
    }
  };

  /**
   * 切换确认挂号弹窗状态
   * @param nextStatus
   */
  toggleConfirm(nextStatus) {
    this.showConfirm = nextStatus;
    this.$apply();
  }

  /**
   * 切换就诊人
   * @param item
   */
  changeActivePatient(item) {
    if (this.activePatient.patientId == item.patientId) {
      return;
    }
    this.activePatient = item;
  }

  async getPatientList() {
    const { code, data = {}, msg } = await Api.getPatientList({});
    const { cardList = [], leftBindNum = 0 } = data;
    cardList.forEach(item => {
      if (item.isDefault === 1) {
        this.activePatient = item;
      } else if (item.relationType === 1) {
        this.activePatient = item;
      } else {
        this.activePatient = cardList[0];
      }
    });
    this.confirmData.patientList = cardList;
    this.confirmData.leftBindNum = leftBindNum;
  }

  // 获取排班日期
  async getScheduleList(item = {}) {
    const { subHisId = "", deptId = "" } = item;
    const { code, data = {}, msg } = await Api.dateScheduleList({
      subHisId,
      deptId,
      transParam: this.doctorStrNo
        ? JSON.stringify({
          doctorStrNo: this.doctorStrNo,
          areaCode: this.options.areaCode || ""
        })
        : this.options.areaCode
          ? JSON.stringify({ areaCode: this.options.areaCode })
          : "",
      // transParam: this.doctorStrNo ? JSON.stringify({ doctorStrNo: this.doctorStrNo }) : '',
      profileId: this.profileId ? this.profileId : ""
    });
    if (code != 0) {
      return;
    }
    const { scheduleList = [] } = data;
    const weekMap = { 一: 0, 二: 1, 三: 2, 四: 3, 五: 4, 六: 5, 日: 6 };
    // 获取当前日期到周一有几天
    const nullDateCount =
      scheduleList[0] && scheduleList[0].weekDate
        ? weekMap[scheduleList[0].weekDate] || 0
        : 0;
    // 生成一个长度为差距天数的数组
    const nullDateArr = [1, 2, 3, 4, 5, 6, 7].slice(0, nullDateCount);
    this.nullDateArr = nullDateArr;
    this.scheduleList = scheduleList;

    this.scheduleListFirstWeek = scheduleList.slice(0, 7);
    this.scheduleListSecondWeek = scheduleList.slice(7, 14);

    // 获取初始选中日期，保存选中日期，并获取选中日期的排班列表
    for (let i = 0; i < scheduleList.length; i++) {
      const { selected, scheduleDate } = scheduleList[i];
      if (selected) {
        this.changeScheduleDoctorList({ subHisId, deptId, scheduleDate });
        break;
      }
    }

    this.$apply();
  }

  // 切换排班日期，获取排班医生列表
  async changeScheduleDoctorList(item = {}) {
    const { subHisId = "", deptId = "", scheduleDate: date = "" } = item;
    if (this.selectedDay === date) {
      return;
    }
    this.selectedDay = date;
    console.log(11111, this.options.areaCode);
    const { code, data = {}, msg } = await Api.scheduleDoctorList({
      subHisId,
      deptId,
      scheduleDate: date,
      transParam: this.doctorStrNo
        ? JSON.stringify({
          doctorStrNo: this.doctorStrNo,
          areaCode: this.options.areaCode || ""
        })
        : this.options.areaCode
          ? JSON.stringify({ areaCode: this.options.areaCode })
          : "",
      profileId: this.profileId ? this.profileId : ""
    });
    if (code != 0) {
      return;
    }
    data.doctorList.map(item => {
      if (item.doctorRemark.indexOf("|") != -1) {
        const query = item.doctorRemark.split("|");
        item.checkName = query[0];
        item.positName = query[1];
        const positName = query[1].split(":");
        item.areaName = positName[1];
      }
    });
    const {
      doctorList = [],
      scheduleDate = "",
      registerNum = "",
      visitNum = ""
    } = data;
    // 重新校验日期，日期不一致不录入数据
    if (this.selectedDay !== scheduleDate) {
      return;
    }
    doctorList.sort((a, b) => {
      let sortStatus = b.status - a.status;
      if (sortStatus == 0) {
        return b.leftSource - a.leftSource;
      }
      return sortStatus;
    });

    this.scheduleDoctorList = {
      doctorList: doctorList,
      scheduleDate,
      registerNum,
      visitNum
    };

    this.$apply();
  }

  /**
   * 获取全部医生列表
   */
  async getAllDoctor() {
    const { subHisId = "", deptId = "" } = this.options;
    const { code, data = {}, msg } = await Api.doctorList({ subHisId, deptId });
    if (code !== 0) {
      return;
    }
    const { doctorList = [] } = data;
    this.doctorList = doctorList;
    this.$apply();
  }

  //获取顶部提示语
  async getNoteProfile() {
    const { code, data = {}, msg } = await Api.getNoteProfile({
      profileKey: "getNoteProfile_selectRegSourceTop"
    });
    if (code == 0) {
      this.note = data.profileValue;
    }
  }

  // 获取弹框提示语
  async getProfile(profileKey) {
    const { code, data = {}, msg } = await Api.getNoteProfile({ profileKey });
    if (code == 0) {
      const contentText = data.profileValue;
      wepy
        .showModal({
          title: "提示",
          content: contentText
        })
        .then(res => {
          if (res.confirm) {
            if (profileKey == "getAlertNoteProfile_fullNotice_2") {
              wepy.navigateTo({
                url: "/pages/consult/choosetype/index"
              });
            }
          }
        });
    }
  }
}
</script>
