<view class="p-page">

  <!-- 自定义导航 -->
  <nav-bar bgColor="transparent" color="#fff">医院介绍</nav-bar>

  <!-- 顶部banner展示 -->
  <view class="m-banner" style="height:{{hisImgBannerHeight}}rpx;">
    <view class="banner" style="height:{{hisImgHeight}}rpx;">
      <image 
        class="banner-image" 
        src="{{hisInfo.imgUrl}}" 
        style="height:{{hisImgHeight}}rpx;" 
        mode="widthFix" 
        alt=""
        @load="setImgSize" />
    </view>
  </view>
  <view class="m-logo">
    <!-- <view class="clip-bg"></view> -->
    <view class="logo">
      <image src="{{hisInfo.logImgUrl}}" mode="widthFix" class="logo-img" alt="" />
    </view>
  </view>
  <view class="g-main">
    <view class="m-info">
      <view class="info-name">
        <view class="logo">
          <image src="/resources/images/logo.png" mode="widthFix" class="logo-img" alt="" />
        </view>
        <view>
          <view class="m-hisname">{{hisInfo.hisName}}</view>
          <view class="unit-tag">{{hisInfo.levelName}}</view>
        </view>
        <!-- <image class="icon-qrcode"
          src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/icon-qrcode.png" mode="widthFix"
          @tap="preview('REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/juhuama.jpg')"
        /> -->
      </view>
      <view class="info-detail">
        <view class="detail-item" @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}">
          <view class="item-text" @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}">
            <text class="item-label">咨询电话：</text>{{hisInfo.telNo || '暂未录入'}}</view>
          <view class="item-icon">
            <image class="icon-phone"
              src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/icon-phone.png" mode="aspectFit"
              @tap="makePhoneCall" data-phone="{{hisInfo.telNo}}"
            />
          </view>
        </view>
        <view class="detail-item" @tap="navigateTo" data-url="/package1/pages/microsite/map/index?area=1">
          <view class="item-text">
            <text class="item-label">麓谷总院：</text>湖南省长沙市岳麓区桐梓坡西路567号</view>
          <view class="item-icon">
            <image class="icon-address" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/icon-address.png" mode="aspectFit" />
          </view>
        </view>
        <view class="detail-item" @tap="navigateTo" data-url="/package1/pages/microsite/map/index">
          <view class="item-text">
            <text class="item-label">开福分院：</text>{{hisInfo.address}}</view>
          <view class="item-icon">
            <image class="icon-address" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/icon-address.png" mode="aspectFit" />
          </view>
        </view>
        
        <!-- <view class="ad-microsite">
          <view class="ad-content">出行卡打车7折起</view>
          <view class="main-btn">去打车</view>
        </view> -->
      </view>
    </view>

    <view id="J_list" class="m-list">
      <view class="list-hd">常用功能</view>
      <view class="list-bd">
        <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlist/index?funType=dept">
          <view class="item-icon">
            <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ksjs.png" mode="aspectFit" /> -->
            <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/microsite/ksjs.png" mode="aspectFit" />
          </view>
          <view class="item-tit">科室介绍</view>
        </view>
        <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlist/index?funType=doctor">
          <view class="item-icon">
            <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ysjs.png" mode="aspectFit" /> -->
            <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/microsite/ysjs.png" mode="aspectFit" />
          </view>
          <view class="item-tit">医生介绍</view>
        </view>
        <view class="list-item" @tap="navigateTo" data-url="/pages/dynamic/index/index">
          <view class="item-icon">
            <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-wwz.png" mode="aspectFit" /> -->
            <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/microsite/kjxc.png" mode="aspectFit" />
          </view>
          <view class="item-tit">科教宣传</view>
        </view>
        <view class="list-item" @tap="navigateTo" data-url="/package1/pages/microsite/deptlayout/index">
          <view class="item-icon">
            <!-- <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/microsite/fn-ksfb.png" mode="aspectFit" /> -->
            <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/microsite/ksfb.png" mode="aspectFit" />
          </view>
          <view class="item-tit">科室分布</view>
        </view>
      </view>
    </view>

    <view id="Y_list" class="c-list">
      <view class="list-hd">特色业务</view>
      <view class="list-bd">
        <block wx:for="{{characterList}}" wx:key="index">
          <view class="list-item" @tap="navigateTo" data-type="1" data-url="{{item.url}}">
            <!-- <view class="item-icon">
              <image class="fn-icon" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/others/menu-bg.png" mode="aspectFit" />
            </view> -->
            <view class="item-tit">{{item.name}}</view>
          </view>
        </block>
      </view>
    </view>

     <view class="m-media-box">
      <view class="m-media" @tap="navigateTo" data-url="/package1/pages/microsite/hisinfo/index">
        <view class="media-hd">
          <text class="hd-tit">医院介绍</text>
          <text class="hd-extra">查看更多</text>
        </view>
        <view class="media-bd article">
          <block wx:if="{{hisInfo.introduction}}">
            <wxparser rich-text="{{hisInfo.introduction}}" />
          </block>
          <block wx:if="{{!hisInfo.introduction}}">暂无介绍</block>
        </view>
      </view>
    </view>

  </view>


</view>
