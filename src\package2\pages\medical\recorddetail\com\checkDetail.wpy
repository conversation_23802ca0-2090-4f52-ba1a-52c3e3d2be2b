<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <view class="list-tit">审核缴费信息</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.createTime}}">
        <view class="item-label">申请时间</view>
        <view class="item-value">{{detailData.createTime}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.operateTime}}">
        <view class="item-label">{{detailData.status == 4?'取消时间':'审核时间'}}</view>
        <view class="item-value">{{detailData.operateTime}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.payedTime}}">
        <view class="item-label">缴费时间</view>
        <view class="item-value">{{detailData.payedTime}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.finishTime}}">
        <view class="item-label">完成时间</view>
        <view class="item-value">{{detailData.finishTime}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.agtOrdNum}}">
        <view class="item-label">支付流水号</view>
        <view class="item-value">{{detailData.agtOrdNum	}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.expressNumber}}">
        <view class="item-label">快递单号</view>
        <view class="item-value">{{detailData.expressNumber}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.reason}}">
        <view class="item-label">驳回原因</view>
        <view class="item-value">{{detailData.reason}}</view>
      </view>
    </view>
  </view>
</template>

<script>
import wepy from "wepy"
import * as Utils from "@/utils/utils"

export default class BasicDetail extends wepy.component {
  data = {}

  components = {}

  props = {
    detailData: {
      type: Object,
      default: {}
    }
  }

  onLoad(options) {}

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {}
}
</script>
