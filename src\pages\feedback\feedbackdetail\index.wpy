<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

import { TYPE_MAP } from "@/config/constant";
import WxsUtils from "../../../wxs/utils.wxs";
import * as Utils from "@/utils/utils";
import NavBar from "@/components/navbar/index";

import * as Api from "./api";

const NORMAL_MAP = ["S", "F"];
export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: '反馈详情',
    navigationStyle: 'custom',
    usingComponents: {
      "wxparser": "plugin://wxparserPlugin/wxparser"
    }
  };

  data = {
    // 订单详情
    detailData: {},
    // 顶部状态配置
    statusConfig: {},
    imageObj: []
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  components = {
    "nav-bar": NavBar,
  };

  onLoad(options) {
    this.orderDetail(options.id);
  }

  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };

  methods = {
    actionSheetTap(index = 0, phototype = "") {
      wx.showActionSheet({
        itemList: ["删除"],
        success: res => {
          this.removeItem({ phototype, index });
        },
        fail: res => {
          console.log(res.errMsg);
        }
      });
    },
    previewImage(item) {
      wepy.previewImage({
        urls: this.imageObj || [],
        current: item
      });
    }
  };

  async orderDetail(id) {
    const { code, data = {}, msg } = await Api.orderDetail({ id });
    if (code !== 0) {
      return;
    }
    const { status = "" } = data;
    this.detailData = data;
    this.statusConfig = this.getStatus() || {};
    if (data.feedbackImg) {
      this.imageObj = data.feedbackImg.split(",");
    }
    this.$apply();
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    let newStatus;
    const { status } = this.detailData;
    if (status === "0") {
      newStatus = "100";
    }
    if (status === "1") {
      newStatus = "101";
    }
    let stsObj = {};

    // 需要转成map
    if (newStatus == "100") {
      stsObj = {
        statusName: "提交成功",
        text:
          "您的意见反馈已提交，工作人员将会进行核实处理，届时将根据实际情况，给予信息回复或致电联系。"
      };
    } else if (newStatus == "101") {
      stsObj = {
        statusName: "已回复",
        text:
          "您的意见反馈已收到，工作人员已进行了回复，您可在页面下方查看回复内容。"
      };
    } else {
      stsObj = {
        statusName: "异常",
        text: ``
      };
    }

    return {
      ...stsObj,
      status: newStatus
    };
  }
}
</script>
