<view class="p-page">
  <view class="m-info">
    <view class="item-tit">{{orderList[0].doctorName}} 医生</view>
    <view class="item-txt">{{orderList[0].deptName}}</view>
  </view>
  <!-- <view class="top-tips">选择需要缴费的单据，若要修改缴费项目，请联系医生修改医嘱。</view> -->
  <view class="order-list">
    <block wx:for="{{listDetail}}" wx:key="{{index}}">
      <view class="o-detail">
        <view class="table-container">
          <view class="t-r">
            <view class="t-hd project">项目名称</view>
            <view class="t-hd count-unit">包装价格（元）</view>
            <view class="t-hd count-unit">数量</view>
            <view class="t-hd count-unit">规格</view>
            <view class="t-hd money">金额（元）</view>
          </view>
          <view class="t-r">
            <view class="t-rd project">{{item.itemName}}</view>
            <view class="t-rd count-unit">{{WxsUtils.formatMoney(item.itemPrice)}}</view>
            <view class="t-rd count-unit">{{item.itemNum + ' ' + item.itemUnit}}</view>
            <view class="t-rd count-unit">{{item.itemSpces}}</view>
            <view class="t-rd money">{{WxsUtils.formatMoney(item.totalFee,100)}}</view>
          </view>
        </view>
      </view>
    </block>
  </view>
  <view class="m-pay">
    <view class="pay-main">
      <!--<view class="all-check" @tap="checkAll">
        <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/{{isAllChecked ? 'checked.png' : 'no-checked.png' }}" />
        <text>全选</text>
      </view>-->
      <view class="main-txt">
        <view>共计：<text class="main-num">¥{{WxsUtils.formatMoney(detailTotalFee,100)}}</text></view>
        <!-- 数值显示错误，需要修正 -->
        <!-- <view>总额:<text class="main-num">￥{{WxsUtils.formatMoney(total.totalFee,100)}}</text></view> -->
        <!-- <view class="check-info">共<text class="main-num">{{orderList.length}}</text>笔，选中<text class="main-num">{{total.count}}</text>笔</view> -->
      </view>
    </view>
    <view
      class="pay-btn {{!isSubmit && total.totalFee != 0 ? 'active' : ''}}"
      @tap="bindCreateOrder"
    >去缴费
    </view>
  </view>
</view>
