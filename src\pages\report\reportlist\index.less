@import "../../../resources/style/mixins";

page {
}

.m-report {
  margin: 24rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8rpx;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  .report{
    
    color: #3F969D;
    font-size: 28rpx;
    font-weight: 600;
  }
  .unreport{
    color: #CC8F24;
    font-size: 28rpx;
    font-weight: 600;
  }

  .report-item {
    width: 86%;
    // padding: 38rpx 160rpx 38rpx 0;

    // &.report {
    //   background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/published.png")
    //     no-repeat;
    //   background-size: 100rpx 100rpx;
    //   background-position: 100% 0;
    // }

    // &.unreport {
    //   background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/checking.png")
    //     no-repeat;
    //   background-size: 100rpx 100rpx;
    //   background-position: 100% 0;
    // }

    .report-title {
      color: @hc-color-title;
      font-size: 36rpx;
      font-weight: 600;
      .ellipsis();
    }
    .report-text {
      margin-top: 8rpx;
      color: @hc-color-text;
      font-size: 28rpx;
      font-weight: normal;
      .ellipsis();
    }
  }
}

.m-tips {
  padding: 32rpx 24rpx;
  color: #BE8014;
  font-size: 24rpx;
  text-align: center;
  background-color: #FFFAF1;
  // border-radius: 4rx;
  // box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.02);
}

.m-date-range {
  margin: 20rpx 32rpx 0 0;
  height: 68rpx;
  display: flex;
  .m-date-item {
    flex: 1;
    line-height: 68rpx;
    font-size: 24rpx;
    font-weight: 600;
    text-align: center;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 8rpx;
    color: @hc-color-text;
    margin-left: 32rpx;
    // border-right: 2rpx solid @hc-color-border;
    &.active {
      background-color: @hc-color-primary;
      color: #fff;
      z-index: 1;
      // border-right: none;
    }
  }
}

.m-tab {
  margin-top: 20rpx;
  // background-color: #fff;
  padding: 0 5%;
  .tab-li {
    &:after {
      width: 1.5em;
    }
  }
}
.unit-tab {
  position: relative;
  display: flex;
  // background-color: #fff;

  & > .unit-tab-li {
    position: relative;
    z-index: 1;
    display: block;
    flex: 1;
    width: 0%;
    font-size: 28rpx;
    text-align: center;
    color: @hc-color-text;
    height: 94rpx;
    line-height: 94rpx;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    user-select: none;
    & > a {
      display: block;
      width: 100%;
      height: 100%;
      color: @hc-color-title;
    }
    &:after {
      // content: " ";
      // position: absolute;
      // left: 50%;
      // bottom: 0;
      // display: block;
      // width: 2.5em;
      // height: 4rpx;
      // background-color: @hc-color-primary;
      // transform: translateX(-50%) scaleX(0);
      // transition: all ease-out 0.2s 0.1s;
    }
    &.active {
      color: @hc-color-primary;
      &:after {
        // transform: translateX(-50%) scale(1);
      }
      & > a {
        color: @hc-color-primary;
      }
    }
  }
  .arrow {
    position: absolute;
    right: 20rpx;
    top: 50%;
    width: 15rpx;
    height: 15rpx;
    border-right: 4rpx solid #c7c7cc;
    border-bottom: 4rpx solid #c7c7cc;
    transform: translate(0, -75%) rotate(45deg);
  }
}
.m-tab-tips {
  color: @hc-color-text;
  font-size: 28rpx;
  border-top: 2rpx solid @hc-color-border;
  margin: 0 30rpx;
  padding: 24rpx;
  text-align: center;
}

.m-warn {
  color: @hc-color-warn;
  padding: 0 10rpx;
}
