<view class="p-page">
  <empty :config.sync="emptyConfig">
    <block slot="text">未查询到任何文章</block>
  </empty>
  <block wx:if="{{artic.recordList && artic.recordList.length > 0}}">
    <view class="{{type == 'all'?'dyna-list':'dyna-noheadlist'}}">
      <block wx:for="{{artic.recordList}}" wx:key="index">
        <view class="dyna-item" @tap="navigateTo({{item}})">
          <view class="item-asside">
            <view class="dyna-title">{{item.title || item.typeName}}</view>
            <view class="dyna-info">
              <view class="info-lt" wx:if="{{item.deptName}}">{{item.deptName}}</view>
              <view class="info-rt">{{item.createTime}}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </block>
</view>
