// @import "../../../resources/style/mixins";
@import '../../../../resources/style/mixins.less';


.ask {
  min-height: calc(100vh - 96rpx);
  .ask-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row nowrap;
    height: 100rpx;
    background-color: @hc-color-white;

    .tab-item {
      flex: 1;
      height: 96rpx;
      line-height: 96rpx;
      text-align: center;
      &.active{
        color: @hc-color-primary;
      }
      .title{
        position: relative;
        display: inline;
        .badge {
          display: block;
          position: absolute;
          top: -18rpx;
          right: -18rpx;
          min-width: 16rpx;
          padding: 0 10rpx;
          height: 36rpx;
          border-radius: 18rpx;
          line-height: 36rpx;
          background-color: @hc-color-error;
          font-size: 26rpx;
          text-align: center;
          color: @hc-color-white;
        }
      }
      .active-line{
        display: block;
        background-color: @hc-color-primary;
        width: 50%;
        height: 4rpx;
        border-radius: 2rpx;
        margin-left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .content{
    width: 100%;
    height: 100%;
    .ask-list{
      padding: 0 20rpx;
      .ask-item{
        box-sizing: border-box;
        background-color: @hc-color-white;
        border-radius: 8rpx;
        margin-top: 20rpx;
        color: @hc-color-text;
        overflow: hidden;
        .item-header{
          padding: 10rpx 20rpx;
          border-bottom: 2rpx solid @hc-color-border;
        }
        .item-content{
          display: flex;
          align-items: center;
          flex-flow: row nowrap;
          box-sizing: border-box;
          padding: 20rpx;
          .con-img{
            width: 150rpx;
            height: 150rpx;
            padding-right: 20rpx;
            image{
              // border: 2rpx solid @hc-color-border;
              width: 100%;
              height: 100%;
            }
          }
          .con-right{
            padding-left: 20rpx;
            .r-row{
              display: flex;
              align-items: center;
              &+.r-row{
                margin-top: 20rpx;
              }
              .name{
                color: @hc-color-title;
                padding-right: 30rpx;
              }
              .doc-name{
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
  }
}