@import "../../../resources/style/mixins";

page {
  min-height: 100%;
  margin: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.m-card {

  .report-title {
    text-align: center;
    font-size: 48rpx;
    font-weight: 600;
    padding: 40rpx 0;
    background-color: #fff;
  }

  .report-info {
    position: relative;
    padding: 32rpx;
    margin: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;

    .info-block {
      display: flex;
      padding-top: 24rpx;
      &:first-child{
        padding: 0;
      }

      .info-item {
        flex: 1;
        display: flex;
        max-width: 50%;

        &.info-item_block {
          max-width: none;
        }

        &:first-child .item-text {
          padding-right: 20rpx;
          color: rgba(0, 0, 0, 0.90);
        }
      }

      .item-title {
        margin-right: 16rpx;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.40);
      }

      .item-text {
        font-size: 28rpx;
        flex: 1;
      }
    }
    .report-info-title{
      margin-bottom: 32rpx;
      color: rgba(0, 0, 0, 0.90);
      font-size: 28rpx;
      font-weight: 600;
    }
  }

  .tips {
    padding: 10rpx 20rpx;
    color: @hc-color-text;
    font-size: 24rpx;
  }

  scroll-view {
    background-color: @hc-color-white;
  }

  .report-list {
    background-color: #fff;
    border-radius: 8rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.08);

    .list-head {
      color: @hc-color-text;
      position: relative;
      font-size: 30rpx;

    }

    .list-item {
      color: @hc-color-title;
    }

    .list-tr {
      display: flex;
      align-items: stretch;
      
      & + .list-tr {
        border-top: 2rpx solid @hc-color-border;
      }
      & > view {
        word-break: break-all;
        word-wrap: break-word;
        & + view {
          border-left: 2rpx solid @hc-color-border;
        }
      }
      &.list-item{
        .td{
          color: rgba(0, 0, 0, 0.70);
          font-size: 24rpx;
        }
      }
    }

    .td {
      width: 35%;
      color: rgba(0, 0, 0, 0.90);
      text-align: center;
      font-size: 20rpx;
      padding: 8rpx 16rpx;
      font-weight: 500;
    }


    .result-2 {
      //偏高
      position: relative;
      padding-right: 35rpx;
      background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/report/result-height.png")
        no-repeat;
      background-position: 100% 50%;
      background-size: 24rpx;
    }

    .result-1 {
      //偏低
      position: relative;
      padding-right: 35rpx;
      background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/report/result-low.png")
        no-repeat;
      background-position: 100% 50%;
      background-size: 24rpx;
    }
  }
}
.bottom-tips {
  position: relative;
  margin-top: 20rpx;
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: @hc-color-text;
  background-color: #fff;
}

.ad-report {
  margin-top: 20rpx;

  image {
    width: 100%;
  }
}
