<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import { getFormatDate } from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class ReportList extends wepy.page {
    config = {
      navigationBarTitleText: '检验检查报告',
    };
    data = {
      city: [],
      provinceName: '',
      cityName: '',
      areaName: '',
      userName: '',
      mobile: '',
      addressDetail: '',
      address: '',
    };

    onShow() {
      // this.getCity();
    }
    methods = {
      changeCity(e) {
        const provinceName = e.detail.value[0] || '';
        const cityName = e.detail.value[1] || '';
        const areaName = e.detail.value[2] || '';
        this.provinceName = provinceName;
        this.cityName = cityName;
        this.areaName = areaName;
        this.address = `${provinceName}-${cityName}-${areaName}`
        this.$apply();
      },
      formSubmit(e){
        console.log()
      }
    };
    
  }
</script>
