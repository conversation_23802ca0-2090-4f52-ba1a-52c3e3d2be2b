// @import "../../resources/style/mixins";
@import '../../../resources/style/mixins.less';

page {
  position: relative;
  font-size: 28rpx;
  height: 100%;
  overflow: hidden;
  line-height: 1.5;
  color: #000000;
  background-color: #f4f9f9;
  font-family: PingFangSC-Regular, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}

.page-security{
  position: relative;
  overflow: hidden;
  background: #FFD7BF;
  height: 100vh;
  z-index: 1;
  .banner{
    width: 100%;
    image{
      width: 100%;
    }
    .img-mb{
      margin-top: -14rpx;
    }
  }
  .m-content{
    position: absolute;
    width: calc(100vw - 48rpx);
    top: 465rpx;
    margin: 0 24rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 5rpx 20rpx rgba(240, 185, 152, 0.45);
    border-radius: 24rpx;
    padding-bottom: 60rpx;
    
    // .title{
    //   background: url('./../../../static/icon/bj.png');
    // }
    .title{
      padding: 60rpx 60rpx 30rpx 60rpx;
      image{
        width: 100%;
      }
      .tit-text{
        position: absolute;

        width: calc(100vw - 48rpx);
        top: 65rpx;
        left: 0;
        color: #F16462;
        width: 100%;
        font-size: 36rpx;
        font-weight: bold;
        text-align: center;
      }
    }

    .m-list{
      display: flex;
      flex-direction: column;
    }

    .content-item{
      background-color: #F16462;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 40rpx;
      margin: 15rpx 60rpx;
      background: #F16462;
      box-shadow: 6rpx 12rpx 0rpx rgba(241, 100, 98, 0.29);
      border-radius: 24rpx;

      .item-icon{
        display: flex;
        flex-direction: column;
        width: 44rpx;
        image{
          width: 44rpx;
        }
      }
      .item-main{
        margin-left: 20rpx;
        flex: 1 1;
        min-width: 30%;
        color: #fff;
        font-size: 36rpx;
      }
      .item-ft {
        width: 20rpx;
        height: 20rpx;
        border-right: 4rpx solid #fff;
        border-bottom: 4rpx solid #fff;
        -webkit-transform: translateX(-8rpx) rotate(-45deg);
        transform: translateX(-8rpx) rotate(-45deg);
      }
    }

  }
}