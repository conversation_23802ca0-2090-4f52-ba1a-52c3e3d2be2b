<view
  class="m-suggest {{tagopen ? 'tagopen' : ''}}"
  wx:if="{{deptList.levelDept == 1 && (favoriteList.length + hisDoctorList.length) > 0}}"
>
  <view class="suggest-title" @tap="tagOpen">历史记录</view>
  <view class="m-tags">
    <view
      class="tag-item"
      wx:for="{{hisDoctorList}}"
      wx:key="index"
      @tap="bindTapDoctor({{item}})"
    >{{item.doctorName}}</view>
    <view
      class="tag-item"
      wx:for="{{favoriteList}}"
      wx:key="index"
      @tap="bindTapDoctor({{item}})"
    >{{item.doctorName}}</view>
  </view>
</view>
<view class="g-list">
  <view class="m-list">
    <view class="list-box">
      <view class="list-lt-box">
        <view class="list-lt">
          
          <!--多级科室，按照index选中tab-->
          <block wx:if="{{deptList.levelDept > 1}}">
            <block
              wx:for="{{deptList.deptList || []}}"
              wx:key="index"
            >
              <view
                class="lt-item {{activeIdx === index ? 'active' : ''}}"
                @tap="bindChangeIndex({{index}},{{item}})"
              >{{item.deptName}}
              </view>
            </block>
          </block>
          <!--一级科室，约定索引为0显示科室列表tab-->
          <!-- <view
            class="lt-item {{activeIdx == 0 ? 'active' : ''}}"
            @tap="bindChangeIndex({{0}})"
            wx:else
          >科室列表
          </view> -->
          <block wx:if="{{showHistory && deptList.levelDept > 1}}">
            <!--index为-1显示历史挂号-->
            <view
              class="lt-item {{activeIdx == -1 ? 'active' : ''}}"
              wx:if="{{hisDoctorList.length > 0 || favoriteList.length > 0}}"
              @tap="bindChangeIndex({{-1}})"
            >历史挂号
            </view>
          </block>
        </view>
      </view>
      <view class="list-rt-box">
        <view class="list-rt">
          <block wx:if="{{showHistory && deptList.levelDept > 1}}">
            <!--历史挂号-->
            <view class="rt-history-box {{activeIdx == -1 ? 'active' : ''}}">
              <block wx:if="{{hisDoctorList.length > 0}}">
                <view class="rt-history">
                  <view class="his-tit">预约过</view>
                  <view>
                    <block
                      wx:for="{{hisDoctorList}}"
                      wx:key="index"
                    >
                      <view
                        class="his-item"
                        @tap="bindTapDoctor({{item}})"
                      >
                        <view class="item-hd">
                          <image
                            mode="widthFix"
                            src="{{item.doctorImg || 'REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png'}}"
                            alt="" />
                        </view>
                        <view class="item-bd">
                          <view class="unit-color-title">{{item.doctorName}}</view>
                          <view class="unit-color-text">{{item.deptName}}</view>
                        </view>
                        <view class="unit-arrow"></view>
                      </view>
                    </block>
                  </view>
                </view>
              </block>
              <block wx:if="{{favoriteList.length > 0}}">
                <view class="rt-history">
                  <view class="his-tit">收藏的医生</view>
                  <view>
                    <block
                      wx:for="{{favoriteList}}"
                      wx:key="index"
                    >
                      <view
                        class="his-item"
                        @tap="bindTapDoctor({{item}})"
                      >
                        <view class="item-hd">
                          <image
                            mode="widthFix"
                            src="{{item.doctorImg || ('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/doc-header-man.png')}}"
                            alt="" />
                        </view>
                        <view class="item-bd">
                          <view class="unit-color-title">{{item.doctorName}}</view>
                          <view class="unit-color-text">{{item.deptName}}</view>
                        </view>
                        <view class="unit-arrow"></view>
                      </view>
                    </block>
                  </view>
                </view>
              </block>
            </view>
          </block>
          <!--只有一级科室-->
          <view
            wx:if="{{deptList.levelDept == 1}}"
            class="rt-sec active"
          >
            <block
              wx:for="{{deptList.deptList || []}}"
              wx:key="index"
            >
              <view
                class="sec-li"
                @tap="bindTapDept({{item}})"
              >
                <view class="sec-li-wrap">
                  <view class="sec-bd">{{item.deptName}}</view>
                  <view class="unit-arrow"></view>
                </view>
              </view>
            </block>
          </view>

          <!--多级科室-->
          <view
            class="rt-sec active"
            wx:if="{{deptList.levelDept > 1}}"
          >
            <!--多级科室，但该科室没有下级科室-->
            <block wx:if="{{deptList.deptList[activeIdx].hasChild == 1}}">
              <view
                class="sec-li"
                @tap="bindTapDept({{deptList.deptList[activeIdx]}})"
              >
                <view class="sec-li-wrap">
                  <view class="sec-bd">{{deptList.deptList[activeIdx].deptName}}</view>
                  <view class="unit-arrow"></view>
                </view>
              </view>
            </block>

            <!--多级科室，存在二级科室和三级科室-->
            <block wx:if="{{deptList.deptList[activeIdx].hasChild == 0}}">
              <!--二级科室-->
              <block
                wx:for="{{deptList.deptList[activeIdx].deptList}}"
                wx:key="index"
              >
                <view
                  class="sec-li"
                  @tap="bindTapDept({{item}})"
                >
                  <block wx:if="{{item.hasChild == 1}}">
                    <view class="sec-li-wrap">
                      <view class="sec-bd">{{item.deptName}}</view>
                      <view class="unit-arrow"></view>
                    </view>
                  </block>
                </view>
                <!--三级科室-->
                <block wx:if="{{item.hasChild == 0}}">
                  <view class="sec-li active">
                    <view class="sec-li-wrap">
                      <view class="sec-bd">{{item.deptName}}</view>
                      <view class="trd-arrow"></view>
                    </view>
                    <view class="trd-box">
                      <block wx:for="{{item.deptList}}" wx:for-item="itm" wx:for-index="idx" wx:key="idx">
                        <view
                          class="trd-li"
                          @tap="bindTapDept({{itm}})"
                        >
                          <view class="trd-bd">{{itm.deptName}}</view>
                          <view class="unit-arrow"></view>
                        </view>
                      </block>
                    </view>
                  </view>
                </block>
              </block>
            </block>

          </view>
        </view>
      </view>
    </view>
  </view>
</view>