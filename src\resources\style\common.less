// 暂时需要的样式：flex 布局、spacing 间距（padding & margin）、按钮 btn
// https://codechina.csdn.net/mirrors/wux-weapp/wux-weapp/-/tree/master/src

@prefix: hc;
@success-color: #4CD964;

// Alignment
.textAlign(@type) {
  .@{prefix}-text--@{type} {
    text-align: @type !important;
  }
}

.textAlign(left);
.textAlign(right);
.textAlign(center);
.textAlign(justify);
.textAlign(nowrap);

// Transformation
.textTransform(@type) {
  .@{prefix}-text--@{type} {
    text-transform: @type !important;
  }
}

.textTransform(lowercase);
.textTransform(uppercase);
.textTransform(capitalize);

// Colors
.color(@color) {
  .@{prefix}-@{color} {
    color: @color !important;

    &--bg {
      background-color: @color !important;
    }

    &--border {
      border-color: @color !important;
    }
  }
}

// Margin and padding
.spacing(@count)when(@count >= 0) {
  @margin: @count * 2rpx;

  .@{prefix}-m--@{count},
  .@{prefix}-margin--@{count} {
    margin: @margin !important;
  }

  .@{prefix}-mt--@{count},
  .@{prefix}-margin-top--@{count} {
    margin-top: @margin !important;
  }

  .@{prefix}-mr--@{count},
  .@{prefix}-margin-right--@{count} {
    margin-right: @margin !important;
  }

  .@{prefix}-mb--@{count},
  .@{prefix}-margin-bottom--@{count} {
    margin-bottom: @margin !important;
  }

  .@{prefix}-ml--@{count},
  .@{prefix}-margin-left--@{count} {
    margin-left: @margin !important;
  }

  .@{prefix}-mv--@{count},
  .@{prefix}-margin-vertical--@{count} {
    margin: @margin 0 !important;
  }

  .@{prefix}-mh--@{count},
  .@{prefix}-margin-horizontal--@{count} {
    margin: 0 @margin !important;
  }

  .@{prefix}-p--@{count},
  .@{prefix}-padding--@{count} {
    padding: @margin !important;
  }

  .@{prefix}-pt--@{count},
  .@{prefix}-padding-top--@{count} {
    padding-top: @margin !important;
  }

  .@{prefix}-pr--@{count},
  .@{prefix}-padding-right--@{count} {
    padding-right: @margin !important;
  }

  .@{prefix}-pb--@{count},
  .@{prefix}-padding-bottom--@{count} {
    padding-bottom: @margin !important;
  }

  .@{prefix}-pl--@{count},
  .@{prefix}-padding-left--@{count} {
    padding-left: @margin !important;
  }

  .@{prefix}-pv--@{count},
  .@{prefix}-padding-vertical--@{count} {
    padding: @margin 0 !important;
  }

  .@{prefix}-ph--@{count},
  .@{prefix}-padding-horizontal--@{count} {
    padding: 0 @margin !important;
  }

  .spacing((@count - 5));
}

.spacing(30);

// Floats
.@{prefix}-clearfix {
  .clearfix();
}

.@{prefix}-pull-right {
  float: right !important;
}

.@{prefix}-pull-left {
  float: left !important;
}

// Hairline
.@{prefix}-hairline {
  &,
  &--top,
  &--right,
  &--bottom,
  &--left,
  &--horizontal,
  &--vertical,
  &--surrounded {
    position: relative;

    &::after {
      // .hairline();
    }
  }

  &--top::after {
    border-top-width: 1px;
  }

  &--right::after {
    border-right-width: 1px;
  }

  &--bottom::after {
    border-bottom-width: 1px;
  }

  &--left::after {
    border-left-width: 1px;
  }

  &--horizontal::after {
    border-width: 1px 0;
  }

  &--vertical::after {
    border-width: 0 1px;
  }

  &--surrounded::after {
    border-width: 1px;
  }
}

// common
.clearfix() {
  &::before,
  &::after {
    display: table;
    content: " ";
  }

  &::after {
    clear: both;
  }
}

// button
.button-style(@bg-color, @border-color: transparent, @color: #fff) {
  border-color: @border-color !important;
  background-color: @bg-color !important;
  color: @color !important;
}

.button-hover(@active-bg-color, @active-color: #fff) {
  background-color: @active-bg-color !important;
  color: @active-color !important;
}

.button-clear(@color) {
  background-color: transparent !important;
  color: @color !important;
}

.button-outline(@color, @bg-color: transparent) {
  border-color: @color !important;
  background-color: @bg-color !important;
  color: @color !important;
}
