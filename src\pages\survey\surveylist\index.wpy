<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class SurveyList extends wepy.page {
    config = {
      navigationBarTitleText: '问卷调查列表',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      const { type } = options;
      this.type = type;
      
    }
    onShow() {
      if(this.type === '1'){
        // 问卷记录
        this.getAnswerList();
        wx.setNavigationBarTitle({
          title: '问卷调查记录'
        });
      } else {
        this.getList();
      }
    }

    onUnload(event){ //多层级跳转之后，监听左上角返回事件，直接退回到index
      if(this.type){
        this.type = '';
      }
		};
    data = {
      emptyConfig: {
        show: true,
      },
      surveyList: [],
      type: ''   // 为1表示问卷调查记录
    };

    methods = {
      navToDeatil(e) {
        const { id = '', orderId = '', questionUserId = '' } = e.currentTarget.dataset.item;
        console.log(e.currentTarget.dataset.item)
        wepy.navigateTo({ url: `/pages/survey/surveydetail/index?id=${id}&orderId=${orderId}&record=${this.type}&questionUserId=${questionUserId}`});
        console.log(`/pages/survey/surveydetail/index?id=${id}&orderId=${orderId}&record=${this.type}&questionUserId=${questionUserId}`)
      },
      goRecord(e) {
        const { url = '' } = e.currentTarget.dataset;
        wepy.navigateTo({ url: url});
      }
    };

    /**
     * 获取满意度调查列表
     */
    async getList() {
      this.emptyConfig.show = true;
      this.surveyList = [];
      const { code, data = {} } = await Api.getList();
      if(code == 0){
        this.surveyList = data.questionList || [];
        if(data && data.questionList && data.questionList.length > 0){
          this.emptyConfig.show = false;
        }
        this.$apply();
      }
    }
    async getAnswerList() {
      this.emptyConfig.show = true;
      this.surveyList = [];
      const { code, data } = await Api.getAnswerList();
      if(code == 0){
       this.surveyList = data || [];
        if(data && data.length > 0){
          this.emptyConfig.show = false;
        }
        this.$apply();
      }
    }
  }
</script>
