<style lang="less">
// from: src/resources/style/mixins.less
@hc-color-primary: #2D666F;
@hc-color-title: #000000;
@hc-color-text: #989898;
@hc-color-info: #3986ff;
@hc-color-warn: #ffa14e;
@hc-color-error: #f76260;

@hc-color-primary: #2D666F;
@hc-color-bg: #F2F4F4;
@hc-color-border: rgba(0, 0, 0, 0.08);
@hc-color-title: rgba(0, 0, 0, 0.9);
@hc-color-text: rgba(0, 0, 0, 0.7);
@hc-color-info: rgba(0, 0, 0, 0.4);
@hc-color-warn: #ff613b;
@hc-color-assist: #ffb040;
@hc-color-tip: #ffba00;
@hc-color-white: #ffffff;
@hc-color-black: #000000;
@hc-color-split: #e0e9e9;
@hc-color-link: #3986ff;
@hc-color-gray: #888888;
@hc-color-primary-bg: rgba(62, 206, 182, 0.1);

// 定义产品级别的颜色
@color-completed: #07c160;
@color-processing: #ff9d00;
@color-primary: #30A1A6;

// 定义动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes borderPulse {
  0% {
    border-left-width: 6rpx;
  }
  50% {
    border-left-width: 10rpx;
  }
  100% {
    border-left-width: 6rpx;
  }
}

@hc-page-gap: 32rpx;
@hc-border-radius: 24rpx;

// 矩形
.rect(@width, @height) {
  width: @width;
  height: @height;
}

// 正方形
.square(@size) {
  width: @size;
  height: @size;
}

// 圆形
.circle(@size) {
  width: @size;
  height: @size;
  border-radius: 50%;
}

// 填充
.fillAll(@position: absolute) {
  position: @position;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// 单行省略号
.ellipsis() {
  overflow: hidden;
  word-wrap: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行省略号
.ellipsisLn(@line) {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line;
}

// 文字换行
.textBreak() {
  word-wrap: break-word;
  word-break: break-all;
}

// 清空button默认样式
.clearButton() {
  width: auto;
  height: auto;
  margin: auto;
  padding: auto;
  box-sizing: content-box;
  text-align: left;
  border-radius: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  overflow: auto;
  border: none;
  box-shadow: none;

  &:before,
  &:after {
    display: none;
    width: 0;
    height: 0;
    overflow: hidden;
    margin: auto;
    padding: auto;
    box-sizing: content-box;
    text-align: left;
    font-size: inherit;
    line-height: inherit;
    background-color: transparent;
    border-radius: 0;
    border: none;
    box-shadow: none;
  }
}

// Page styles
.p-page {
  padding: 0 0 120rpx; // 增加底部padding，为固定位置的按钮留出空间
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #f7f8fa; // 使用更柔和的背景色
  position: relative;
}

.header {
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);

  &::after {
    content: '';
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 30rpx;
    height: 6rpx;
    background-color: @color-primary;
    border-radius: 3rpx;
  }

  .page-title {
    font-size: 38rpx;
    font-weight: bold;
    color: #262626;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 38rpx;
      background-color: @color-primary;
      border-radius: 4rpx;
    }
  }

  .page-subtitle {
    font-size: 26rpx;
    color: #8c8c8c;
    margin-top: 10rpx;
    padding-left: 20rpx;
  }
}

// 测试按钮区域样式
.test-buttons {
  margin: 20rpx 30rpx 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #ff9d00;
  
  .test-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx dashed rgba(0, 0, 0, 0.1);
    position: relative;
    
    &::before {
      content: '🧪';
      margin-right: 8rpx;
    }
  }
  
  .test-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .test-button {
      flex: 1;
      min-width: 200rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 26rpx;
      color: #fff;
      border-radius: 8rpx;
      margin: 0;
      padding: 0 16rpx;
      background: linear-gradient(135deg, #35B8BE, #1F7A82);
      box-shadow: 0 4rpx 8rpx rgba(31, 122, 130, 0.2);
      
      &.adult {
        background: linear-gradient(135deg, #35B8BE, #1F7A82);
      }
      
      &.child {
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
      }
      
      &.address {
        background: linear-gradient(135deg, #FF9800, #F57C00);
      }
      
      &:active {
        opacity: 0.85;
        transform: scale(0.98);
      }
    }
  }
}

.survey-list {
  position: relative;
  z-index: 1;
  padding: 0 30rpx;

  .survey-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    padding: 30rpx 30rpx 30rpx 34rpx; // 调整左侧内边距，为左边框留出空间
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.2s ease-in-out;
    overflow: hidden; // 确保三角形不会溢出
    border-left: 6rpx solid @color-primary; // 添加左侧彩色边框
    animation: fadeIn 0.5s ease-out forwards;
    
    // 为每个卡片添加不同的延迟，实现瀑布流动画效果
    &:nth-child(1) { animation-delay: 0.05s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.15s; }
    &:nth-child(4) { animation-delay: 0.2s; }
    &:nth-child(5) { animation-delay: 0.25s; }
    
    // 卡片悬停效果
    &:active {
      opacity: 0.85; // 增强点击效果的反馈
      transform: scale(0.98) translateX(4rpx); // 添加轻微的缩放和位移效果
      transition: all 0.1s; // 添加过渡效果
    }

    // 添加卡片特效
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 120rpx;
      background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 100%);
      opacity: 0;
      pointer-events: none; // 不影响点击事件
    }
    
    // 根据状态设置左侧边框颜色
    &.completed {
      border-left-color: @color-completed;
    }
    
    &.processing {
      border-left-color: @color-processing;
      animation: borderPulse 2s infinite ease-in-out;
    }

    // 左上角问卷类型标志
    .type-badge {
      position: absolute;
      top: 18rpx;
      left: 0;
      padding: 6rpx 16rpx 6rpx 32rpx;
      background-color: rgba(48, 161, 166, 0.85);
      border-radius: 0 30rpx 30rpx 0;
      z-index: 10;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
      
      text {
        font-size: 22rpx;
        color: #fff;
        font-weight: 500;
        letter-spacing: 1rpx;
      }
      
      // 根据问卷类型设置不同颜色
      &.child {
        background-color: rgba(76, 175, 80, 0.85); // 儿童版使用绿色
      }
    }

    // 右上角状态三角形标志
    .status-corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 120rpx;
      height: 120rpx;
      overflow: hidden;
      z-index: 10;
      pointer-events: none; // 让点击事件穿透，不影响卡片的点击
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 80rpx 80rpx 0;
        border-color: transparent @color-processing transparent transparent;
      }
      
      &.completed::before {
        border-color: transparent @color-completed transparent transparent;
      }
      
      text {
        position: absolute;
        top: 14rpx;
        right: 2rpx;
        transform: rotate(45deg);
        font-size: 20rpx;
        color: #fff;
        font-weight: 500;
        text-align: center;
        width: 60rpx;
      }
    }

    // 卡片内容
    .item-body {
      padding-top: 50rpx; // 增加上边距，让内容整体下移
      
      .info-row {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        line-height: 1.8;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 180rpx;
          color: #666;
          flex-shrink: 0;
          text-align: left;
          padding-right: 20rpx;
          position: relative;
          
          // 为标签添加小圆点装饰
          &::before {
            content: '';
            display: inline-block;
            width: 6rpx;
            height: 6rpx;
            background-color: rgba(48, 161, 166, 0.4);
            border-radius: 50%;
            margin-right: 8rpx;
            vertical-align: middle;
            position: relative;
            top: -2rpx;
          }
        }
        
        .value {
          color: #333;
          flex: 1;
          display: flex;
          align-items: center;

          &.name {
            font-weight: bold;
            color: #262626;
            font-size: 30rpx;
            // 为名称添加下划线装饰
            text-decoration: none;
            border-bottom: 1rpx dashed rgba(48, 161, 166, 0.2);
            display: inline-block;
            padding-bottom: 2rpx;
          }
          
          &.status {
            color: #ff5151;
            font-weight: 500;
            position: relative;
            display: inline-block;
            padding: 2rpx 10rpx;
            border-radius: 4rpx;
            background-color: rgba(255, 81, 81, 0.1);

            &.completed {
              color: @color-completed;
              background-color: rgba(7, 193, 96, 0.1);
            }
          }
          
          &.warning {
            color: #ff5151;
            font-weight: 500;
          }
        }
      }
    }

    // 卡片底部
    .item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 24rpx;
      margin-top: 24rpx;
      border-top: 1rpx solid rgba(0, 0, 0, 0.05);

      .footer-hint {
          font-size: 24rpx;
          color: #8c8c8c;
        display: flex;
        align-items: center;
        
        // 添加小提示图标
        &::before {
          content: '';
          display: inline-block;
          width: 24rpx;
          height: 24rpx;
          background-color: rgba(48, 161, 166, 0.1);
          border-radius: 50%;
          margin-right: 8rpx;
          position: relative;
        }
      }
      
      // 右下角查看按钮
      .view-detail {
        display: flex;
        align-items: center;
        color: @color-primary;
        font-size: 24rpx;
        z-index: 10;
        background-color: rgba(48, 161, 166, 0.08);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        transition: all 0.2s;
        position: relative;
        overflow: hidden;
        
        // 添加按钮点击波纹效果
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 5rpx;
          height: 5rpx;
          background: rgba(255, 255, 255, 0.5);
          opacity: 0;
          border-radius: 100%;
          transform: scale(1, 1) translate(-50%, -50%);
          transform-origin: 50% 50%;
        }
        
        &:active::after {
          animation: ripple 0.6s ease-out;
        }
        
        .view-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 6rpx;
        }
        
        &:active {
          background-color: rgba(48, 161, 166, 0.15);
        }
      }
    }
  }
}

// 波纹动画
@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

// loading和no-more样式
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  
  .loading-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    border: 4rpx solid rgba(48, 161, 166, 0.1);
    border-top: 4rpx solid @color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
    
    // 添加发光效果
    &::after {
      content: '';
      position: absolute;
      top: -8rpx;
      left: -8rpx;
      right: -8rpx;
      bottom: -8rpx;
      border-radius: 50%;
      background: radial-gradient(rgba(48, 161, 166, 0.15), transparent 70%);
      z-index: -1;
      animation: pulse 2s ease-in-out infinite;
    }
  }
  
  text {
    font-size: 26rpx;
    color: #999;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes pulse {
    0% { transform: scale(0.8); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.5; }
    100% { transform: scale(0.8); opacity: 0.3; }
  }
}

.no-more {
  text-align: center;
  padding: 40rpx 0 60rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .no-more-line {
    height: 1rpx;
    width: 180rpx;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.08));
    position: relative;
    
    // 添加动态小点效果
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      width: 6rpx;
      height: 6rpx;
      border-radius: 50%;
      background-color: rgba(48, 161, 166, 0.4);
      transform: translateY(-50%);
      animation: dotMove 3s ease-in-out infinite;
    }
    
    @keyframes dotMove {
      0% { right: 0; opacity: 0; }
      20% { opacity: 1; }
      100% { right: 180rpx; opacity: 0; }
    }
  }
  
  .no-more-dot {
    height: 1rpx;
    width: 180rpx;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.02));
    position: relative;
    
    // 添加动态小点效果
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 6rpx;
      height: 6rpx;
      border-radius: 50%;
      background-color: rgba(48, 161, 166, 0.4);
      transform: translateY(-50%);
      animation: dotMoveReverse 3s ease-in-out infinite;
      animation-delay: 1.5s;
    }
    
    @keyframes dotMoveReverse {
      0% { left: 0; opacity: 0; }
      20% { opacity: 1; }
      100% { left: 180rpx; opacity: 0; }
    }
  }
  
  text {
    font-size: 26rpx;
    color: #999;
    display: inline-block;
    position: relative;
    padding: 0 30rpx;
    background-color: #f7f8fa;
  }
  
  // 添加小图标装饰
  &::after {
    content: '';
    position: absolute;
    top: 10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: rgba(48, 161, 166, 0.2);
    box-shadow: 0 0 0 4rpx rgba(48, 161, 166, 0.05);
  }
  
  // 添加底部装饰
  &::before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 30rpx;
    width: 100rpx;
    height: 4rpx;
    border-radius: 2rpx;
    background: linear-gradient(90deg, rgba(48, 161, 166, 0.05), rgba(48, 161, 166, 0.2), rgba(48, 161, 166, 0.05));
  }
}

// 底部新增按钮
.add-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  padding: 16rpx 40rpx;
  padding-bottom: calc(16rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-top: 1rpx solid rgba(0, 0, 0, 0.03);

  .add-button {
    height: 88rpx;
    background: linear-gradient(135deg, #35B8BE, #1F7A82);
    color: #fff;
    border-radius: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    font-weight: 500;
    width: 100%;
    box-shadow: 0 6rpx 16rpx rgba(48, 161, 166, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    
    // 添加按钮内部装饰效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    }
    
    .add-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 12rpx;
      filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
    }
    
    &:active {
      transform: translateY(2rpx) scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(48, 161, 166, 0.15);
      opacity: 0.9;
    }
  }
}
</style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import Empty from '@/components/empty/index';
import * as Api from './api';

export default class SurveyList extends wepy.page {
  config = {
    navigationBarTitleText: '收样列表',
    enablePullDownRefresh: true
  };

  components = {
    'empty': Empty
  };

  data = {
    // 样本问卷列表
    surveyList: [],
    isLoading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    emptyConfig: {
      show: false,
      tip: '暂无样本问卷',
      btnText: '新增样本问卷',
      btnShow: true
    },
    hisId: '242', // 医院ID，默认值
    patientId: '',
    patHisNo: '',
    patCardNo: ''
  };

  onLoad(options) {
    // 保存传递的参数
    if (options) {
      this.patientId = options.userId || '';
      this.patHisNo = options.patHisNo || '';
      this.patCardNo = options.patCardNo || '';
    }
    
    // 获取未提交的样本问卷列表
    this.getSurveyList();
  }
  
  onPullDownRefresh() {
    // 下拉刷新
    this.page = 1;
    this.surveyList = [];
    this.getSurveyList();
    wx.stopPullDownRefresh();
  }
  
  onReachBottom() {
    // 上拉加载更多 - 由于接口不支持分页，我们只在第一次加载数据
    // 直接设置hasMore为false，阻止重复请求
    console.log('触发底部加载，当前hasMore状态:', this.hasMore);
    this.hasMore = false;
    this.$apply();
  }

  // 获取样本问卷列表
  async getSurveyList() {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.$apply();
    
    try {
      // 调用API获取健康样本订单列表
      const params = {
        hisId: this.hisId,
        userId: this.patientId,
        patHisNo: this.patHisNo,
        patCardNo: this.patCardNo
      };
      
      console.log('请求健康样本订单列表参数:', params);
      
      const res = await Api.getHealthOrderList(params);
      
      //console.log('健康样本订单列表接口返回数据:', JSON.stringify(res));
      
      if (res && res.code === 0) {
        // 检查数据格式
        if (res.data) {
          console.log('数据类型:', typeof res.data);
          console.log('是否为数组:', Array.isArray(res.data));
          
          if (Array.isArray(res.data)) {
            console.log('数组长度:', res.data.length);
            
            // 确保reportStatus是字符串类型，并添加CSS类名
            res.data = res.data.map(item => {
              if (item.reportStatus !== undefined) {
                item.reportStatus = String(item.reportStatus);
                item.statusClass = item.reportStatus === '1' ? 'completed' : 'processing';
              }
              return item;
            });
          } else if (typeof res.data === 'object') {
            console.log('对象键数量:', Object.keys(res.data).length);
          }
        } else {
          console.log('数据为null或undefined');
        }
        
        // 使用接口返回的数据替换列表
        this.surveyList = Array.isArray(res.data) ? res.data : [];
        
        // 由于接口不支持分页，第一次加载后就设置hasMore为false
        // 这样就不会在滚动到底部时重复请求数据
        this.hasMore = false;
        console.log('设置hasMore为false，阻止重复请求');
      } else {
        console.log('接口请求失败:', res);
        wepy.showToast({
          title: res.msg || '获取列表失败',
          icon: 'none'
        });
      }
      
      // 更新空状态显示
      this.emptyConfig.show = this.surveyList.length === 0;
      console.log('列表是否为空:', this.emptyConfig.show);
      this.$apply();
    } catch (error) {
      console.error('获取样本问卷列表失败', error);
      wepy.showToast({
        title: '获取列表失败，请稍后重试',
        icon: 'none'
      });
    } finally {
      this.isLoading = false;
      this.$apply();
    }
  }

  methods = {
    // 查看详情
    viewDetail(e) {
      const { id } = e.currentTarget.dataset;
      const survey = this.surveyList.find(item => item.id === id);
      if (survey) {
        // 直接跳转到步骤3（报告出具），不需要判断问卷状态
        let urlParams = `id=${id}&step=3`;
        
        // 跳转到报告出具页面（步骤3）
        wepy.navigateTo({
          url: `/pages/survey/surveytools/index?${urlParams}`
        });
      }
    },
    
    // 继续填写问卷
    continueSurvey(e) {
      const { id } = e.currentTarget.dataset;
      const survey = this.surveyList.find(item => item.id === id);
      if (survey) {
        // 直接跳转到步骤3（报告出具），不需要判断问卷状态
        let urlParams = `id=${id}&step=3`;
        
        console.log('跳转参数:', urlParams);
        
        // 跳转到报告出具页面（步骤3）
        wepy.navigateTo({
          url: `/pages/survey/surveytools/index?${urlParams}`
        });
      }
    },
    
    // 新增样本问卷
    addNewSurvey() {
      wepy.navigateTo({
        url: '/pages/survey/surveytools/index?from=add'
      });
    },
    
    // 测试获取配置信息
    async testGetProfileByKey(e) {
      const { key } = e.currentTarget.dataset;
      
      try {
        // 显示加载提示
        wepy.showLoading({
          title: '请求中...',
          mask: true
        });
        
        // 准备请求参数
        const params = {
          hisId: '242', // 医院ID
          platformId: '242', // 平台ID
          key: key // 根据按钮传入的key值请求不同的配置信息
        };
        
        console.log('请求getProfileByKey接口参数:', params);
        
        // 导入并调用getProfileByKey接口
        const Api = require('../api.js');
        const res = await Api.getProfileByKey(params);
        
        // 隐藏加载提示
        wepy.hideLoading();
        
        console.log('getProfileByKey接口返回:', res);
        
        // 如果接口调用成功，显示返回的配置信息
        if (res && res.code === 0) {
          // 显示配置信息
          wepy.showModal({
            title: '配置信息',
            content: `Key: ${key}\n\nValue: ${JSON.stringify(res.data || {})}`,
            showCancel: false
          });
        } else {
          // 接口调用失败，显示错误信息
          wepy.showToast({
            title: res && res.msg ? res.msg : '获取配置信息失败',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        wepy.hideLoading();
        console.error('测试获取配置信息失败:', error);
        wepy.showToast({
          title: '请求失败，请检查网络',
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    // 空状态按钮点击事件
    onEmptyBtnTap() {
      this.addNewSurvey();
    },
    
    // 删除问卷
    deleteSurvey(e) {
      const { id } = e.currentTarget.dataset;
      wx.showModal({
        title: '提示',
        content: '确定要删除此问卷吗？',
        success: res => {
          if (res.confirm) {
            // 调用删除API
            // Api.deleteSurvey({ id });
            
            // 从列表中移除
            this.surveyList = this.surveyList.filter(item => item.id !== id);
            
            // 更新空状态显示
            this.emptyConfig.show = this.surveyList.length === 0;
            this.$apply();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    }
  };
}
</script> 