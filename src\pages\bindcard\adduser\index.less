@import "../../../resources/style/mixins.less";

page {
  width: 100%;
  overflow-x: hidden;
  background: #fff;
}

.container {
  height: 100vh;
  min-height: 100vh;
  padding-bottom: 20rpx;
  overflow-y: auto;
  // background-color: #fff;
}

.form-container {
  background-color: #fff;
  input {
    height: 80rpx;
    line-height: 80rpx;
    color: rgba(0, 0, 0, 0.7);
    font-size: 32rpx;
    padding-left: 24rpx;
    border-radius: 16rpx;
    &:focus {
      border: 1px solid #3eceb6;
    }
    &.disabled{
      color: rgba(0, 0, 0, 0.3);
    }
  }
  .member-title{
    margin: 40rpx 32rpx 0;
    padding: 40rpx 0 7rpx;
    color: #000;
    font-size: 32rpx;
    font-weight: 500;
    border-top: 1rpx solid rgba(0, 0, 0, 0.10);
  }
}

.patInfo-card {
  position: relative;
  .patInfo-card-top {
    padding: 40rpx 32rpx;
  }
  .patInfo-card-info {
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 20rpx;
    .info-name {
      line-height: 48rpx;
      padding-right: 30rpx;
      font-size: 64rpx;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
    }
    .info-other {
      color: #2d2d2d;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 42rpx;
      padding-right: 30rpx;
    }
  }

  .patInfo-card-other {
    color: rgba(0, 0, 0, 0.7);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 42rpx;
    // padding-bottom: 10rpx;
  }
}

.patInfo-list {
  // background: #fff;
  .patInfo-listitem {
    padding: 32rpx 32rpx 0;
    display: flex;
    flex-direction: column;
    position: relative;

    &.patInfo-listitem_none {
      display: none;
    }

    // &:first-child:before {
    //   display: none;
    // }

    &.no-after:after {
      display: none;
    }

    .listitem-head {
      display: flex;
      align-items: center;
      padding-bottom: 24rpx;
      .textBreak();

      .list-title {
        flex: 1;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.70);
        font-weight: 400;
        position: relative;

        &.require {
          position: relative;
          &::before {
            content: "*";
            color: #f76260;
            font-size: 30rpx;
          }
        }

        &.list-title_select:before {
          content: "切换证件类型";
          position: absolute;
          right: 0;
          bottom: 0;
          box-sizing: border-box;
          font-size: 32rpx;
          color: @hc-color-primary;
          // border-bottom: 10rpx solid @hc-color-title;
          // border-right: 10rpx solid @hc-color-title;
          // border-top: 10rpx solid transparent;
          // border-left: 10rpx solid transparent;
        }
      }
    }
    .listitem-body {
      flex: 1;
      // padding-left: 30rpx;
      position: relative;
      border-radius: 8px;
      &.default-bg{
        background: #F6F7F9;
      }
      &.border-bg{
        border: 2rpx solid rgba(44, 44, 44, 0.20);
      }
      &.no-bg {
        background: transparent;
      }
      .picker {
        display: flex;
        align-items: center;
        .picker-info {
          flex: 1;
          font-size: 30rpx;
          color: #353535;
          font-weight: 400;
          line-height: 1;
        }
        .item-arrow {
          width: 17rpx;
          height: 17rpx;
          border-right: 5rpx solid #c7c7cc;
          border-bottom: 5rpx solid #c7c7cc;
          -webkit-transform: translateX(-32rpx) rotate(-45deg);
          transform: translateX(-32rpx) rotate(-45deg);
        }
      }

      .textBreak();
    }
    .patInfo-add {
      width: 100%;
      font-size: 30rpx;
      font-weight: 400;
      color: #3eceb6;
      line-height: 42rpx;
      text-align: center;
    }
  }

  .listitem_accest {
    color: red;
  }

  .listitem_accest .listitem-body:before {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-right: 2rpx solid @hc-color-text;
    border-bottom: 2rpx solid @hc-color-text;
    width: 16rpx;
    height: 16rpx;
    transform: translateY(-50%) rotate(-45deg);
  }
}

.patInfo-part {
  padding: 20rpx 30rpx;
  // background-color: #f5f5f5;

  .list-title {
    &.require {
      position: relative;
      &:after {
        content: "*";
        color: #f76260;
        font-size: 28rpx;
      }
    }
  }
}

.patInfo-tips {
  margin-top: 48rpx;
  padding: 0 24rpx;
  color: rgba(0, 0, 0, 0.60);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 36rpx;
}

.patInfo-btn {
  margin: 40rpx;
}
.binduser-btn_line {
  border-radius: 76rpx;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #fff;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  height: 100rpx;
  line-height: 100rpx;
}

.m-delete-member {
  // justify-content: center;
  align-items: flex-end;
  // border-bottom: 2rpx solid @hc-color-border;
  color: rgba(136, 47, 56, 1);
  width: 100rpx;
  font-size: 26rpx;
  font-weight: 600;
  height: 47rpx;
  line-height: 47rpx;
  text-align: center;
  border-radius: 44rpx;
  background-color: rgba(136, 47, 56, 0.2);
}

.binduser-radio {
  display: inline-block;
  width: 150rpx;
  margin-left: 10rpx;
  &:first-child {
    margin-left: 0;
  }
}
.binduser-radio_object {
  transform-origin: 0 30%;
  transform: scale(0.7);
}
.binduser-radio_text {
  font-size: 30rpx;
}
button::after {
  border: none;
}
.add-member{
  margin: 40rpx 0 40rpx;
  color: #2D666F;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}
.page-bottom-logo {
  &.add-user-bottom{
    margin-top: 40rpx;
    position: relative;
    bottom: 0;
  }
}

