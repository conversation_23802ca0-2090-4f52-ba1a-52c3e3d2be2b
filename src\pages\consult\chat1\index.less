@import "../../../resources/style/mixins";
@wx-color-primary: #98E165;

page {
  height: 100%;
}

.f-color-red {
  color: red;
}

.p-page {
  height: 100%;
  background: #F8F8F8;

  .header {
    display: flex;
    align-items: center;
    height: 88rpx;
    font-size: 34rpx;
    color: @hc-color-title;
    padding: 0 30rpx;
    background: @hc-color-white;
    box-shadow: 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.07);

    .tip-item {
      flex: 1;
      display: flex;
      align-items: center;

      .head-tip {
        flex: 1;
      }

      >text {
        display: inline-block;
        margin-left: 20rpx;
        border-radius: 6rpx;
        border: 1rpx solid @hc-color-primary;
        line-height: 60rpx;
        font-size: 28rpx;
        color: @hc-color-primary;
        padding: 0 12rpx;
      }
    }
  }
  .tips{
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    padding: 15rpx 30rpx;
    box-sizing: border-box;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FEFCEB;
    color: #F96A0E;
    .tips-btn{
      color: #586C94;
      display: inline-block;
      font-size: 24rpx;
      line-height: 2.5555;
      border-radius: 4rpx;
    }
  }

  .scroll-bottom {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
  }

  .operation-box {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 9;

    .operation-content {
      background: #f5f5f5;
    }

    .expression-block {
      max-height: 290rpx;
      background: #f5f5f5;
      position: relative;
      overflow-y: auto;
      padding: 20rpx 10rpx;
      text-align: left;

      .expression-item {
        display: inline-flex;
        justify-content: center;
        // align-items: center;
        width: 80rpx;
        height: 80rpx;
        padding: 12rpx;

        image {
          width: 60rpx;
          // max-width: 80rpx;
          max-height: 60rpx;
          transform-origin: 0 100%;
          // height: 80rpx;
        }
      }
    }

    .top {
      position: relative;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 16rpx 20rpx 16rpx 30rpx;
      border-bottom: 1px solid #D8D8D8;
      border-top: 1rpx solid #D8D8D8;

      .input {
        position: relative;
        flex: 1;
        height: 100%;
        min-height: 40rpx;
        max-height: 120rpx;
        border: 1rpx solid #d8d8d8;
        font-size: 30rpx;
        color: #505050;
        white-space: normal;
        padding: 10rpx 20rpx;
        border-radius: 4rpx;
        background: @hc-color-white;
      }

      image {
        width: 60rpx;
        height: 60rpx;
        margin-left: 30rpx;
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      height: 200rpx;
      box-sizing: border-box;
      padding: 27rpx 70rpx 0;
      background: #F5F5F5;
      color: #989898;

      >view,
      navigator {
        width: 110rpx;
        margin-bottom: 30rpx;
        font-size: 26rpx;

        &:not(:nth-child(4n+0)) {
          margin-right: 56rpx;
        }

        image {
          width: 110rpx;
          height: 110rpx;
        }

        >view {
          margin-top: 10rpx;
          text-align: center;
        }
      }
    }
  }

  .operation-btn-area {
    bottom: 20rpx;
    padding: 0 20rpx;
    .btn {
      background: @hc-color-primary;
      color: #fff;
      border-radius: 10rpx;
      text-align: center;
      font-size: 32rpx;
    }
  }

  .content {
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    padding: 16rpx 30rpx;

    .m-loading {
      height: 30rpx;
      line-height: 30rpx;
      padding: 16rpx;
      text-align: center;
      font-size: 28rpx;
      color: @hc-color-title;

      .m-loading-image {
        width: 30rpx;
        height: 30rpx;
        padding: 0 10rpx;
      }
    }

    >view {
      margin-bottom: 40rpx;

      &.msg {
        background: #DDDDDD;
        border-radius: 15rpx;
        padding: 8rpx 23rpx;
        font-size: 26rpx;
        color: @hc-color-white;
        text-align: center;
      }

      &.date {
        margin-bottom: 20rpx;
        text-align: center;
        font-size: 28rpx;
        color: #B2B2B2;
      }
    }

    .invoke-block {
      display: flex;
      position: relative;
      margin-bottom: 10rpx;
      align-items: center;
      justify-content: center;
      height: 40rpx;
      color: @hc-color-text;
      font-size: 28rpx;
    }

    .left,
    .right {
      position: relative;
      display: flex;
      align-items: flex-start;

      &.right {
        justify-content: flex-end;
      }

      .chat-content {
        position: relative;
        display: inline-flex;
        justify-content: flex-end;
      }

      .img {
        min-width: 80rpx;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 34rpx;
        overflow: hidden;
        background: @hc-color-primary;
        text-align: center;
        font-size: 30rpx;
        line-height: 80rpx;
        color: @hc-color-white;
      }

      .img image,
      .img>open-data {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .name {
        font-size: 22rpx;
        padding: 2rpx 0 6rpx 3rpx;
        color: @hc-color-text;
        margin-top: -10rpx;
      }

      .text {
        position: relative;
        background: @hc-color-white;
        border-radius: 16rpx;
        font-size: 30rpx;
        max-width: 480rpx;
        box-sizing: border-box;
        color: @hc-color-title;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
      }

      .image-block {
        position: relative;
        border-radius: 10rpx;
        font-size: 30rpx;
        color: #505050;
        width: 210rpx;
        border-radius: 16rpx;
        // height: 218rpx;

        image {
          // width: 100%;
          // height: 100%;
          width: 210rpx;
          height: 100rpx;
          border-radius: 16rpx;
          // height: 218rpx;
        }
      }

      .tip {
        position: absolute;
        top: -70rpx;
        right: 0;
        height: 60rpx;
        min-width: 292rpx;
        padding: 10rpx 0;
        box-sizing: border-box;
        background: #505050;
        border-radius: 8px;
        font-size: 28rpx;
        color: @hc-color-white;

        &.no {
          min-width: 105rpx;

          &.nocopy {
            min-width: 100rpx;
          }
        }

        text {
          display: inline-block;
          line-height: 40rpx;
          padding: 0 20rpx;

          &:not(:last-child) {
            border-right: 1rpx solid @hc-color-white;
          }
        }

        i {
          border: 20rpx solid transparent;
          border-top-color: #505050;
          position: absolute;
          top: 60rpx;
          right: 30rpx;
        }
      }

      .flex {
        flex: 1;
      }
    }

    .left {
      .text {
        box-shadow: 0 2rpx 12rpx 0 rgba(255, 255, 255, 0.05);

        &::before {
          content: '';
          border: 16rpx solid transparent;
          border-right-color: @hc-color-white;
          position: absolute;
          top: 16rpx;
          left: -32rpx;
        }

        .content-box {
          display: flex;
          padding: 25rpx;
          white-space: pre-wrap;
          >view {
            flex: 1;

            >view:last-child {
              font-size: 26rpx;
              color: #989898;
              // overflow: hidden;
              // display: -webkit-box;
              // text-overflow: ellipsis;
              // -webkit-box-orient: vertical;
              // -webkit-line-clamp: 2;
            }
          }
        }
      }

      .image-block {
        &::after {
          border-left-color: @hc-color-white;
        }

        .content-box {
          padding: 0;
        }
      }
    }

    .right {
      .img {
        margin-left: 36rpx;
        margin-right: 0;
      }

      .text {
        background: @wx-color-primary;
        color: @hc-color-white;
        box-shadow: 0 2rpx 12rpx 0 rgba(176, 227, 110, .4);

        &::after {
          content: '';
          border: 16rpx solid transparent;
          border-left-color: @wx-color-primary;
          position: absolute;
          top: 16rpx;
          right: -32rpx; // 32rpx
        }

        .content-box {
          padding: 25rpx;
        }
      }

      .image-block {
        &::after {
          border-left-color: @hc-color-white;
        }

        .content-box {
          padding: 0;
        }
      }

      .content-box {
        display: flex;

        >view {
          flex: 1;

          >view:last-child {
            font-size: 26rpx;
            color: @hc-color-white;
            // overflow: hidden;
            // display: -webkit-box;
            // text-overflow: ellipsis;
            // -webkit-box-orient: vertical;
            // -webkit-line-clamp: 2;
          }
        }
      }
    }
  }

  .expression {
    .content-box {
      .expression-image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .video-image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .list-view {
    // transform: rotate(180deg);

    .content {
      // transform: rotate(-180deg);
    }
  }

  .modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-body {
      width: 75%;
      box-sizing: border-box;
      border-radius: 8rpx;
      background: @hc-color-white;

      .modal-title {
        padding: 50rpx 0 16rpx;
        font-size: 36rpx;
        color: @hc-color-black;
        text-align: center;
      }

      .modal-content {
        padding: 0 26rpx 30rpx;
        overflow: auto;
        font-size: 30rpx;
        color: #888;
        line-height: 1.5;
        text-align: center;
      }

      .modal-footer {
        border-top: 1rpx solid #E5E5E5;
        display: flex;

        >text {
          flex: 1;
          box-sizing: border-box;
          text-align: center;
          color: @hc-color-black;
          font-size: 36rpx;
          height: 100rpx;
          line-height: 100rpx;
          width: auto;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:last-child {
            border-left: 1rpx solid #d2d2d2;
            color: #3ECEB6;
          }
        }
      }
    }
  }

  .footer-btn {
    position: fixed;
    bottom: 0;
    border-top: 1rpx solid @hc-color-border;
    background-color: @hc-color-white;
    display: flex;
    flex-direction: end;
    font-size: 36rpx;
    height: 94rpx;
    line-height: 94rpx;
    width: 100%;

    .inquiry-item {
      padding-left: 30rpx;
      flex: 1;
      text-align: center;
      color: @hc-color-title;
      border-right: 1rpx solid @hc-color-border;
    }

    .evaluate-btn {
      width: 50%;
      padding: 0;
      margin: 0;
      background-color: @hc-color-primary;
      color: @hc-color-white;
      text-align: center;
      border-radius: 0rpx;
      border: none;
    }
  }

  .m-robot-message-list {
    background-color: #fff;
    opacity: .9;

    .m-robot-message-item {
      padding-left: 30rpx;
      color: @hc-color-title;
      font-size: 30rpx;
      height: 100rpx;
      line-height: 100rpx;
      white-space: nowrap;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      &:before {
        content: ' ';
        position: absolute;
        left: 30rpx;
        right: 0;
        top: 0;
        border-top: 1rpx solid @hc-color-border;
      }

      &:first-child:before {
        display: none;
      }
      .light {
        color: @hc-color-primary;
      }
    }
  }
}