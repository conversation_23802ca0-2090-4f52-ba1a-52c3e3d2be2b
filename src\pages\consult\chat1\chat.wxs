module.exports = {
  isShow: function (id, showId) {
    if (id == showId) {
      return true;
    } else {
      return false;
    }
  },
  isRevoke: function (date) {
    // IOS不支持2019-01-01的日期格式
    var regx = getRegExp('-', 'g');
    if ((getDate().getTime() - getDate(date.replace(regx, '/')).getTime()) > 120000) {
      return false;
    } else {
      return true;
    }
  },
  getObj: function (str) {
    return JSON.parse(str);
  }

};