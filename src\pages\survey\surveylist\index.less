@import "../../../resources/style/mixins.less";

.p-page {
  height: 100vh;
  .p-page-content{
    height: calc(100vh - 160rpx);
    overflow-y: scroll;
  }
}

.m-survey {
  border-radius: 4rpx;
  margin: 20rpx;
  padding: 0 25rpx 0 30rpx;
  background-color: #fff;

  .survey-item {
    padding: 38rpx 160rpx 38rpx 0;

    &.unsurvey {
      background: url("REPLACE_STATIC_DOMAIN/icon/common/icon-unreply.png")
        no-repeat;
      background-size: 120rpx 92rpx;
      background-position: 100% 0;
    }

    .survey-title {
      color: @hc-color-title;
      font-size: 34rpx;
      font-weight: 600;
      .ellipsis();
    }
    .survey-text {
      margin-top: 10rpx;
      color: @hc-color-text;
      font-size: 30rpx;
      .ellipsis();
    }
  }
}
.page-button{
  position: absolute;
  bottom: 40rpx;
  width: 100%;
  text-align: center;
  .button{
    margin: 0 40rpx;
    height: 100rpx;
    color: #fff;
    line-height: 100rpx;
    font-size: 34rpx;
    font-weight: 600;
    border-radius: 76rpx;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  }
}
