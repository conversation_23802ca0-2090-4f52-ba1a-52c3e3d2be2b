@import "../../../resources/style/mixins.less";

page {
  background: @hc-color-bg;
}

.p-page {
  padding: 0 34rpx 130rpx;
}

.bg {
  margin-top: 20rpx;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  width: 100%;
  height: 650rpx;
  flex-direction: column;
  padding: 40rpx;
  background: linear-gradient(
    180deg,
    rgba(62, 206, 182, 0.69) 0%,
    rgba(62, 206, 182, 0) 100%
  );
  border-radius: 24rpx;
  .title {
    font-weight: 600;
    font-size: 36rpx;
  }
  .desc {
    font-size: 26rpx;
    line-height: 40rpx;
  }
  .bg-img {
    position: absolute;
    top: 0rpx;
    right: 34rpx;
    image {
      width: 235rpx;
      height: 183rpx;
    }
  }
}

.ml-20 {
  margin-left: 20rpx;
}

.m-card {
  margin: 24rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;
  top: -500rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  image {
    width: 48rpx;
    height: 48rpx;
  }
  .card-info {
    padding: 39rpx 0 36rpx;
  }
  .info-main {
    display: flex;
    align-items: center;
    padding-left: 2rpx;
  }
  .main-name {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: space-between;
  }
  .name {
    display: flex;
    align-items: center;
    font-size: 36rpx;
    font-weight: bold;
  }
  .info-extra {
    color: @hc-color-text;
    font-size: 26rpx;
    line-height: 40rpx;
    margin-top: 20rpx;
  }
}
