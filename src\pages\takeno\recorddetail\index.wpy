<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  import { TYPE_MAP, CODE_TYPE } from '@/config/constant';
  import DetailStatus from '@/components/detailstatus/index';
  import RefundList from '@/components/refundlist/index';
  import * as Utils from '@/utils/utils';

  import BasicDetail from './com/basicDetail';
  import PayDetail from './com/payDetail';
  import * as Api from './api';

  const NORMAL_MAP = ['S', 'F', 'L', 'C'];
  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '取号详情',
    };

    data = {
      // 页面参数
      options: {},
      // 订单详情
      detailData: {},
      // 顶部状态配置
      statusConfig: {},
      // 退款列表
      refundList: [],
      // 订单状态是否异常，用来确定是否需要重发
      isAbnormal: false,
      // 缴费信息是否展开,默认S状态收起
      payIsExpand: true,
      // 条形码格式
      codeType: '',
    };

    components = {
      'detail-status': DetailStatus,
      'refund-list': RefundList,
      'basic-detail': BasicDetail,
      'pay-detail': PayDetail,
    };

    props = {};

    onLoad(options) {
      this.options = options;
      this.codeType = CODE_TYPE;
      const { orderId = '' } = options;
      this.orderDetail({ orderId });
    }

    events = {
      'set-navigationbar-color': (param) => {
        wepy.setNavigationBarColor(param);
      }
    }

    methods = {
      /**
       * 重发订单状态查询
       */
      bindRetryOrder(){
        this.retryOrder();
      },
      /**
      * 取消订单
      */
      bindCancelOrder(){
        this.cancelOrder();
      },
    };

    async orderDetail(item = {}) {
      const { orderId = '' } = item;
      const { code, data = {}, msg } = await Api.orderDetail({ orderId });
      if (code !== 0) {
        return;
      }
      const { status = '' } = data;
      data.visitTime = Utils.getVisitTime(data);
      this.detailData = data;
      this.refundList = data.refundList || [];
      this.statusConfig =  this.getStatus() || {};
      if (NORMAL_MAP.indexOf(status) === -1) {
        this.isAbnormal = true;
      }
      if (status === 'S') {
        this.payIsExpand = false;
      } else {
        this.payIsExpand = true;
      }
      this.$apply();
    }
    
    async cancelOrder() {
      const showModalRes = await wepy.showModal({
        title: '取消取号提示',
        content: '取消取号您将无法进行就诊，确认是否取消取号，若您已支付，系统将为您自动退款，确认是否取消？',
        cancelText: '确定取消',
        confirmText: '保留号源',
        cancelColor: '#989898',
        confirmColor: '#3ECDB5',
      });
      if (showModalRes.confirm) {
        return false;
      }
      const { orderId = '' } = this.detailData;
      const { code, data = {}, msg } = await Api.cancelOrder({ orderId });
      if (code !== 0) {
        return;
      }

      wepy.showToast({
        title: '取消成功',
        icon: 'success',
      });

      this.leftTimer && clearTimeout(this.leftTimer);
      this.orderDetail({ orderId });
      wepy.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }

    /**
     * 获取订单描述文案
     */
    getStatus() {
      const { bizType, status, refundStatus } = this.detailData;
      let stsObj = {};

      // 当班挂号
      if (status == 'S') {
        stsObj = {
          statusName: '取号成功',
          text: '现场门诊请在预约当天至少提前15分钟前往医院挂号科室排队候诊。互联网门诊无需来院，请在预约当天注意留意微信小程序上的信息，互联网门诊具体就诊时间以诊室通知为准，谢谢您的理解！',
        };
      } else if (status == 'F') {
        stsObj = {
          statusName: '取号失败',
          text: '您的退款申请已受理。',
        };
      } else if (status == 'P') {
        stsObj = {
          statusName: '付款完成，调用医院支付接口中',
          text: '',
        };
      } else if (status == 'H' || status == 'Z') {
        stsObj = {
          statusName: '取号异常',
          text: `操作超时，请咨询医院窗口。`,
        };
      } else if (status == 'C') {
        stsObj = {
          statusName: '取消成功',
          text: '预约已取消，如需就诊请重新挂号。',
        };
      } else if (status == undefined) {
        stsObj = {
          statusClassName: '',
          statusName: '',
          text: '',
        };
      } else {
        stsObj = {
          statusName: '取号异常',
          text: `操作超时，请咨询医院窗口。`,
        };
      }

      return {
        ...stsObj,
        status,
        hasRefund: refundStatus == 1 || refundStatus == 2,
      };
    }
    /**
     * 重发订单状态查询
     */
    async retryOrder() {
      const { orderId = '' } = this.detailData;
      const { code, data = {}, msg } = await Api.manualNotify({
        orderId,
        bizType: TYPE_MAP['YYQH'] || 'default',
      });
      if (code !== 0) {
        return;
      }
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?orderId=${orderId}&type=YYQH&time=15&from=detail`
      });
    }

    /**
     * 获取支付页展示信息
     */
    getBizContent(item = {}) {
      return [
        { key: '费用类型', value: '预约取号' },
        { key: '就诊科室', value: item.deptName },
        { key: '医生名称', value: item.doctorName },
        { key: '就诊时段', value: Utils.getVisitTime(item) },
        { key: '就诊人', value: item.patientName },
        { key: '就诊卡号', value: item.patCardNo },
      ];
    }

    /**
     * 获取支付订单号
     */
    async registerPayOrder() {
      let bizContent;
      const { orderId = '', bizType = '', patientName = '', patCardNo = '' } = this.detailData;
      try {
        bizContent = JSON.stringify(this.getBizContent(this.detailData) || []);
      } catch (e) {
        bizContent = "[]";
      }

      const { code, data = {}, msg } = await Api.registerPayOrder({ orderId, bizContent });
      if (code !== 0) {
        return false;
      }
      const { payOrderId = '' } = data;
      wepy.redirectTo({
        url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=YYQH`
      });
    };
  }
</script>
