@import "../../../../resources/style/mixins";

.address-title {
  color: #989898;
  padding: 20rpx 0 10rpx;
}

.picker-li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto 12px;

  .rt {
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .arrow {
      margin-left: 20rpx;
      display: inline-block;
      width: 0;
      height: 0;
      border-left: 8rpx solid transparent;
      border-right: 8rpx solid transparent;
      border-bottom: 14rpx solid red;
      transform: translateY(-8rpx) rotate(60deg);
    }
  }
}

.medical {
  position: relative;

  .medical-container {

    .medical-info {
      padding: 30rpx;
      background-color: #fff;
      font-size: 32rpx;
      margin-bottom: 30rpx;

      .medical-title {
        color: @hc-color-primary;
        margin-bottom: 20rpx;
        font-size: 36rpx;
      }

      .info-li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .info-key {
          color: #989898;
        }

        picker {
          text-align: right;
        }

        .number-info {
          display: flex;
          align-items: center;

          .btn-li {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 36rpx;
            height: 36rpx;
            border-radius: 50%;
            border: 2rpx solid @hc-color-primary;
          }

          .number {
            margin: 0 24rpx;
          }

          .reduce-btn,
          .add-btn {
            display: inline-block;
            background: @hc-color-primary;
            height: 2rpx;
            position: relative;
            width: 20rpx;
          }

          .add-btn:after {
            background: @hc-color-primary;
            content: "";
            height: 2rpx;
            left: 0;
            position: absolute;
            top: 0;
            width: 20rpx;
            transform: rotateZ(90deg)
          }
        }
      }

      .other-li {
        display: flex;
        align-items: center;
        line-height: 1;

        textarea {
          flex: 1;
        }
      }

      .radio-title {
        display: flex;
        align-items: center;
        justify-items: flex-end;
        color: @hc-color-primary;
        margin-bottom: 10rpx;

        .radio-item {
          transform: scale(0.8);
        }
      }
    }


    .form-info {
      font-size: 32rpx;
      background: #ffffff;
      padding: 10rpx 30rpx;

      input,
      picker,
      textarea {
        text-align: right;
      }

      .form-item {
        display: flex;
        justify-content: space-between;
        padding: 24rpx 0;

        &:not(:last-child) {
          box-shadow: inset 0px -1px 0px rgba(152, 151, 151, 0.06);
        }

        .label {
          flex: 0 0 208rpx;
        }
      }

      .form-between {
        display: flex;
        justify-content: space-between;
      }
    }

    .btn {
      padding: 56rpx 32rpx 48rpx;

      >view {
        background-color: #3eceb6;
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        border-radius: 16rpx;
        color: #fff;
        font-weight: 600;
        font-size: 34rpx;
      }
    }
  }
}


.radio-group {
  display: flex;
  align-items: center;
  justify-items: flex-end;

  .radio-label {
    display: flex;
    align-items: center;
    justify-items: flex-end;

    .radio-item {
      // transform-origin: 0 30%;
      transform: scale(0.8);
    }

    &:last-child {
      margin-left: 20rpx;
    }
  }
}

.check-group {
  margin: 20rpx 0;

  .radio-label {
    display: flex;
    align-items: center;
    justify-items: flex-end;

    .radio-item {
      transform: scale(0.8);
    }
  }

}

.color-red {
  color: red;
  margin-right: 6rpx;
}