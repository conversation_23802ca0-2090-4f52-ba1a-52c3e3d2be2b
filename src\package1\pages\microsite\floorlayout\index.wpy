<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class FloorLayout extends wepy.page {
    config = {
      navigationBarTitleText: '楼群分布',
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      this.getFloorImg();
    }

    data = {
      emptyConfig: {
        show: true,
      },
      hisImgHeight: 0,
      floorImgUrl: '',
      emptyMsg: '未查询到楼群分布图',
    };

    methods = {
      setImgSize(e){
        wepy.hideLoading();
        const { width, height } = e.detail;
        const basScale = 750 / width;
        const imgHeight = height * basScale;
        this.hisImgHeight = imgHeight;
      },
      setImgNull(e){
        wepy.hideLoading();
        this.emptyConfig.show = true;
        this.floorImgUrl = '';
        this.emptyMsg = '图片加载失败';
      },
      preview(){
        wepy.previewImage({
          urls: [this.floorImgUrl],
        });
      }
    };
    /**
     * 获取医院信息
     */
    async getFloorImg(){
      const { data = {}, code } = await Api.getFloorImg();
      if(code == 0){
        const { floorImgUrl = '' } = data;
        this.floorImgUrl = floorImgUrl;
        if(floorImgUrl){
          this.emptyConfig.show = false;
        }
        this.$apply();

        wepy.showLoading({
          title: '图片加载中',
          mask: true,
        });
      }
    }
  }
</script>
