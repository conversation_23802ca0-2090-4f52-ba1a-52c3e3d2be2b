<view class="p-page">
  <view class="info-text">暂时不支持医保卡看病</view>
  <block wx:if="{{isScan == '1'}}">
    <view class="wgt-user-box">
      <view class="wgt-user-main">
        <view class="wgt-user-main-info">
          <view class="wgt-user-main-info-tit">{{patientName}}</view>
          <view class="wgt-user-main-info-label"></view>
        </view>
      </view>
      <view class="wgt-user-extra">就诊卡：{{patCardNo}}</view>
    </view>
  </block>
  <block wx:else>
    <outpatient :config.sync="patientConfig" :patient.sync="patient"></outpatient>
  </block>
  <block wx:if="{{type === 'hsjcyy'}}"> 
    <date-check :dateList.sync="dateList" :checkIndex.sync="checkDateIndex" />
  </block>
  <!-- <view class="wait-tips" wx:if="{{treatList.length > 0}}">
    待缴费信息，共<text class="num">{{treatList.length}}</text>条
  </view> -->

<view class="m-date" wx:if="{{type === 'hsjcyy'}}">
  <picker
    mode="date"
    start="{{minDate}}"
    end="{{maxDate}}"
    value="{{selectedDate}}"
    @change="bindChangeDate"
  >
    <view class="date">{{selectedDate || '请选择预约日期'}}<view class="arrow"></view></view>
  </picker>
</view>

  <treat-list
    :treatList.sync="treatList"
    :patient.sync="patient"
    :checkedLength.sync="checkedLength"
    :totalFee.sync="totalFee"
  >
  </treat-list>

  <empty class="empty-container" :config.sync="emptyConfig">
    <block slot="text">暂未查询到您的待缴费数据</block>
  </empty>
  <view wx:if="{{type === 'hsjcyy'}}" class="tips">
    <view>
      温馨提示: 
    </view>
    <view>
      采样地点：麓谷总院：门诊楼一楼；开福分院：一楼西头采样；
    </view>
    <view>采样时间：上午 9:30—11:30 点，下午 2:30 — 3:30</view></view>
  <view class="m-button {{treatList.length > 1 ? '' : 'is-ab'}}" wx:if="{{type === 'hsjcyy'}}">
    <view class="pay-btn" @tap="orderProject">点击申请核酸检测开单</view>
  </view>
</view>

