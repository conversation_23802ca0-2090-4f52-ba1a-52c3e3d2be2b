<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

import md5 from "md5";
import { DOMAIN, REQUEST_QUERY, TYPE_MAP, CODE_TYPE } from "@/config/constant";
import DetailStatus from "@/components/detailstatus/index";
import * as Utils from "@/utils/utils";

import PayDetail from "./com/payDetail";
import * as Api from "./api";
import WxsUtils from "../../../../wxs/utils.wxs";

const NORMAL_MAP = ["S", "F"];
export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "扫码开单详情"
  };
  wxs = {
    WxsUtils: WxsUtils
  };

  data = {
    // 订单详情
    detailData: {},
    // 导诊信息是否展开
    guideIsExpand: true,
    // 顶部状态配置
    statusConfig: {},
    // 退款列表
    refundList: [],
    // 订单状态是否异常，用来确定是否需要重发
    isAbnormal: false,
    // 缴费信息是否展开,默认S状态收起
    payIsExpand: true,
    // 条形码格式
    codeType: "",
    // 处方单是否展开
    medIsExpand: true,
    products: {},
    showModal: false,
    payCertificate: [],
    sampleNumber: '',
    extfiled1: '',
    dzfp: '2',
  };

  components = {
    "detail-status": DetailStatus,
    "pay-detail": PayDetail,
  };


  onShow(options) {
    let pages = getCurrentPages();
  // 数组中索引最大的页面--当前页面
    let currentPage = pages[pages.length-1];
      // 打印出当前页面中的 options
    console.log(currentPage.options)	
    console.log(options, '=====55')
    const { sampleNumber = '', extfiled1 = '' } = currentPage.options;
    this.extfiled1 = extfiled1;
    this.codeType = CODE_TYPE;
    this.orderDetail(currentPage.options);
    this.sampleNumber = sampleNumber || '';
    console.log(this.sampleNumber, '========59')
  }

  onLoad(options) {
    console.log('===65')
  }

  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };

  methods = {
    goPerfectMsg() {
      console.log(this.detailData, this.detailData.templateType, '======81')
      const productId = this.products.products[0].productId;
      if(this.extfiled1 && this.extfiled1 == '3') {
        wepy.navigateTo({
          url: `/package2/pages/scancode/perfectinformationdetail/index?orderId=${this.detailData.id}&productId=${productId}&deptId=${this.detailData.deptId}&sampleNumber=${this.detailData.sampleNumber || ''}&patientName=${this.detailData.patientName}&idNumber=${this.detailData.idNumber}&mobile=${this.detailData.mobile}`
        });
      } else {
        if(this.detailData.templateType && this.detailData.templateType === 'XB'){
          wepy.navigateTo({
            url: `/package2/pages/scancode/perfectinformation/index?orderId=${this.detailData.id}&productId=${productId}&deptId=${this.detailData.deptId}&sampleNumber=${this.detailData.sampleNumber || ''}&deptName=${this.detailData.deptName}&patientName=${this.detailData.patientName}&idNumber=${this.detailData.idNumber}&mobile=${this.detailData.mobile}&docname=${this.products.docname}`
          });
          return
        }
        wepy.showModal({
          title: "温馨提示",
          content: '暂不支持该类型产品的样本信息移动端录入。',
          showCancel: false
        });
        
      }
    },
    closeModal() {
      this.showModal = false;
      this.$apply();
    },
    /**
     * 重发订单状态查询
     */
    bindRetryOrder() {
      this.retryOrder();
    },
    // 开票、查看发票
    bindQueryEleck() {
      this.queryEleck();
    },

  };
  
  async orderDetail(item = {}) {
    const { orderId = "" } = item;
    const { code, data = {}, msg } = await Api.orderDetail({ orderId });
    if (code !== 0) {
      return;
    }
    const { status = "", refundStatus = "", itemList = [], extFieldsViews = "", payCertificate = "" } = data;
    this.products = JSON.parse(extFieldsViews);
    if (payCertificate) {
      this.payCertificate = JSON.parse(payCertificate);
    }
    if (this.products.regType == 1) {
      this.showModal = true;
      this.$apply();
    }
    itemList.forEach(item => {
      item.itemPrice = Number(item.itemPrice) * 100;
      item.totalFee = Number(item.totalFee) * 100;
    })
    this.detailData = data;
    this.refundList = data.refundList || [];
    this.statusConfig = this.getStatus() || {};
    if (NORMAL_MAP.indexOf(status) === -1) {
      this.isAbnormal = true;
    }
    if (status === 'S' && !(refundStatus == 1 || refundStatus == 2)) {
      this.queryEleckStatus(data.id);
    }
    this.$apply();
  }

  async queryEleckStatus(psOrdNum) {
    const { code, data = '2' } = await Api.queryEleckStatus({ psOrdNum });
    if (code !== 0) {
      return;
    }
    this.dzfp = data;
    this.$apply();
  }
  async queryEleck() {
    const { id = "" } = this.detailData;
    if (this.dzfp == '1') {
      wx.showLoading();
      //读取本地文件
      wx.downloadFile({
        // 示例 url，并非真实存在
        url: `${DOMAIN}/api/customize/queryEleck?_route=h${REQUEST_QUERY.platformId}&psOrdNum=${id}&msgKey=${md5(id+'2024pdfquandianpiao')}`,
        success: function (res) {
          const filePath = res.tempFilePath;
          wx.openDocument({
            filePath: filePath,
            showMenu: true,
            success: function (res) {
              wx.hideLoading();
            }
          })
        }
      });
    } else {
      const { code, data = {} } = await Api.dealEleck({
        psOrdNum: id,
        msgKey: md5(id+'2024quandianpiao')
      });
      if (code !== 0) {
        return;
      }
      if (data.resultCode != 0) {
        wepy.showModal({
          title: '提示',
          content: '功能更新中，请稍后再试。',
          showCancel: false,
          confirmColor: '#30A1A6',
        });
        return;
      }
      this.queryEleckStatus(id);
    }
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    const { status, refundStatus } = this.detailData;
    let stsObj = {};

    // 需要转成map
    if (status == "S") {
      stsObj = {
        statusName: "缴费成功",
        text:
          "扫码开单并缴费成功，请按医生要求进行相关检测。"
      };
    } else if (status == "F") {
      stsObj = {
        statusName: "缴费失败",
        text: "您的缴费提交失败，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。"
      };
    } else if (status == "P") {
      stsObj = {
        statusName: "付款完成，调用医院支付接口中",
        text: ""
      };
    } else if (status == "H" || status == "Z") {
      stsObj = {
        statusName: "缴费异常",
        text: `您的缴费提交异常，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。`
      };
    } else if (status == undefined) {
      stsObj = {
        statusName: "",
        text: ""
      };
    } else {
      stsObj = {
        statusName: "缴费异常",
        text: `您的缴费提交异常，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。`
      };
    }

    return {
      ...stsObj,
      status,
      hasRefund: refundStatus == 1 || refundStatus == 2
    };
  }

  /**
   * 重发订单状态查询
   */
  async retryOrder() {
    const { orderId = "" } = this.detailData;
    const type = "MZJF";
    const { code } = await Api.manualNotify({
      orderId,
      bizType: TYPE_MAP[type] || "default"
    });
    if (code !== 0) {
      return;
    }
    wepy.redirectTo({
      url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&time=15&from=detail`
    });
  }
}
</script>
