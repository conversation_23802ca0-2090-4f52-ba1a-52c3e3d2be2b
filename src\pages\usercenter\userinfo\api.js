import { post } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

export const getUserInfo = (param) => post('/api/user/patientinfo', param);

export const setDefault = (param) => post('/api/user/setdefaultpatient', param);

export const unBind = (param) => post('/api/user/unbindpatient', param);

export const getCardInfo = param => post('/api/membercard/getcardinfo', param);

export const getPatientInfo = (param) => post(`/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`, param);

export const getDict = (param) => post(`/api/customize/register/getDict?_route=h${REQUEST_QUERY.platformId}`, param);
