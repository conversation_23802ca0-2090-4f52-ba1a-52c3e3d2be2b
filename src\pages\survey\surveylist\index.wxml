<view class="p-page">
  <view class="p-page-content" wx:if="{{surveyList.length}}">
    <block wx:for="{{surveyList}}" wx:for-index="idx" wx:key="idx">
      <view class="m-survey" bindtap="navToDeatil" data-item="{{item}}">
        <view class="survey-item unsurvey">
          <view class="survey-title">{{item.examTitle}}</view>
          <view class="survey-text">{{item.createTime}}</view>
        </view>
      </view>
    </block>
  </view>
  <view class="page-button" wx:if="{{type !== '1'}}">
    <view class="button" bindtap="goRecord" data-url="/pages/survey/surveylist/index?type=1">问卷调查记录</view>
  </view>
  <empty :config.sync="emptyConfig">
    <block slot="text">{{type === '1' ? '暂无问卷调查记录' : '暂无可填写的问卷'}}</block>
  </empty>
</view>
