import { post } from '@/utils/request';

export const getSurveyDetail = (param = {}) => {
  return post('/api/questionphone/getquestionscopeforid', param);
};
export const getSurveyAnswer = (param = {}) => {
  return post('/api/questionphone/getquestiondetailbyid', param);
};

export const saveQuestion = (param) => post('/api/questionphone/savequestion', param);

// export const getQuestionDetail = (param) => post('/api/questionphone/getquestiondetailbyid', param);
