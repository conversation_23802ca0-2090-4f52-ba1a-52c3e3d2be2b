<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';

  const InitState = {
    userInfo: {},
    cardList: [],
    leftBindNum: 0,
  }

  export default class UserList extends wepy.page {
    config = {
      navigationBarTitleText: '我的就诊人',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
    };

    onLoad(options) {
    }

    onShow(){
      this.userInfo = {};
      this.cardList = [];
      this.leftBindNum = 0;
      
      this.getCardList();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      userInfo: {},
      cardList: [],
      leftBindNum: 0,
      patientName: '',
      patCardNo: '',
      patHisNo: '',
      idNo: '',
      patientId: ''
    };

    async getCardList(){
      const { data = {} } = await Api.getCardList();
      const { cardList = [], leftBindNum = 0 } = data;
      this.cardList = cardList;
      cardList.map(item => {
        if (item.relationType == 1) {
          this.patientName = item.patientName;
          this.patHisNo = item.patHisNo;
          this.idNo = item.idNo;
          this.patCardNo = item.patCardNo;
          this.patientId = item.patientId;
        }
      });
      this.leftBindNum = leftBindNum;
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      toAdd() {
        let url = '';
        if (this.cardList.length > 0) {
          // url = '/pages/bindcard/scancard/index';
          url = `/pages/usercenter/queryrelation/index?patientName=${this.patientName}&patCardNo=${this.patCardNo}&idNo=${this.idNo}&patientId=${this.patientId}`;
        } else {
          url = `/pages/bindcard/index/index?qryType=1`;
        }
        wepy.navigateTo({
          url: url,
        });
      },
      formSubmit(e){
        debugger
      },
    };

    events = {
    };

  }
</script>
