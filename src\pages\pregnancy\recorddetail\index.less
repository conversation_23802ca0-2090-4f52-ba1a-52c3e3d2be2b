@import "../../../resources/style/mixins";

page {}

.p-page {
  padding-bottom: 30rpx;
}

.m-list-button {
  padding: 30rpx;

  .button {
    background: rgba(0, 0, 0, 0.04);
    border-radius: 5px;
  }

  .button-primary {
    background: #3ECEB6;
    border-radius: 5px;
    color: #FFFFFF;
  }
}

.m-code {
  display: none;
  background-color: #fff;
  padding: 30rpx;

  &.active {
    display: block;
  }

  .code-tit {
    font-size: 34rpx;
    color: @hc-color-title;
    font-weight: 600;
    margin-bottom: 15rpx;
  }

  .code-text {
    font-size: 30rpx;
    color: @hc-color-text;

    p {
      margin: 20rpx 0;
    }
  }

  .code-img {
    margin-top: 20rpx;

    image {
      vertical-align: top;
      width: 100%;
    }
  }
}

.m-retry {
  background-color: #fff;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;

  .retry-btn {
    display: inline-block;
    vertical-align: top;
    font-size: 34rpx;
    color: @hc-color-primary;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 4rpx;
    border: 2rpx solid @hc-color-primary;
  }
}

.m-list {
  padding: 30rpx 0;
  margin-top: 20rpx;
  background-color: #fff;

  .list-tit {
    padding: 0 30rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .list {
    padding-top: 17rpx;
  }

  .list-item {
    padding: 17rpx 30rpx;
    font-size: 30rpx;
    display: flex;
    align-items: center;

  }

  .item-label {
    color: @hc-color-text;
    min-width: 5em;
  }

  .item-value {
    color: @hc-color-title;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    text-align: right;
    overflow: hidden;
    margin-left: 20rpx;
  }

  .unit-price {
    font-size: 48rpx;
  }

  .photo-image {
    padding: 32rpx 32rpx 0px 32rpx;

    .photo-image-title {
      color: rgba(0, 0, 0, 0.7);
      font-size: 15px;
    }

    .m-upload-list {
      display: flex;
      flex-wrap: wrap;
      margin-top: 10px;

      // padding: 10rpx 0 10rpx;
      .m-upload-item {
        position: relative;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin: 0 32rpx 18rpx 0rpx;
        width: 165rpx;
        height: 165rpx;
        background-color: #f5f5f5;
        overflow: hidden;
        text-align: center;
        border-radius: 24rpx;

        .m-upload-image {
          max-width: 100%;
          max-height: 100%;
        }

        .m-upload-tips-image {
          position: absolute;
          right: 6rpx;
          top: 6rpx;
          width: 26rpx;
          height: 26rpx;
          background-size: 100%;

          &.m-upload-sucess {
            background-position: top right;
            background-repeat: no-repeat;
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACHUlEQVRIS72WPUgjQRTH/2+DnGhycCgERQW9FKkOLiIWmtXuFMUyhY2dhRYWx1V+FBrtLUxhIVhosXBwHKennSRaibEN4gcq5BC95pLzrjA7Mks2bOLsjht0p33z3m/ee/+ZNwTZ0jRfNOTvBaASEGZAA3ch4DcDMgCSqdP8PmKxglMosjP2ZPYDvvvcFAObIKDJKQgDfhEoUagLLB+Ee3OivUJQ9HhnCExfJaBZlrDVzoAsSBlPfRzYqvR7AlKPfkwzRZknBsUNxNzLCDrp+lyyc3jR6l8G4hCQEq8G8MSH6TNWWAlklAv692ozqQTxzABlxCyjAeKNV+7/ZNz2RJY575le9zbMBWKA1PTPGYAtyBxl9rHge+QLD/h6d2nZSrPJyGCcoGk+NeS/hkTCMshkcxixYDvWsidYvzkrbefST53mWyma3u4jYE8WyMluQrSbC6xk+R0uXwzo56BZAuarBckgPC4D5khNb28AGBVpnknoz4EUQ2zyjHYI+GSNOd32AS219fhydmg0V7RcQHhGu0JQV6ARSx0RnP/L4bMA5gZSLN2usHTc2B1oRFwAcwuxls5WDJWwsWDIkLCduuxaaohBJm8T9rfwgHc1b1xDiqXrNy5sNOS/dpo5HLbQEcG32yskBPfESZylC/vcJ6iWfPjPHIeoDc98grx8VPlRPBkTZs6eDD4r7NVHuQnz5HNiwjz5bpXp9IU+kI8KPzAUBWAp/QAAAABJRU5ErkJggg==");
          }

          &.m-upload-queue {
            background-position: top right;
            background-repeat: no-repeat;
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAB8UlEQVRIS7WWMUtdQRCFv4OSzsaAJEXAJmBpIRGJxAjpAkLsg9gYEMQfoEYi5geIIGgjQdIaCAQsBCUoorwiZcAmYJEgaGMnhpEJ8/S+57vv3g2+Le/e3W/3zJmZFQXDzNqAQeAF0AM8jCVnwE/gO7An6W+zrZQ3aWYdwDQwCTwGroBfwGms6QK6gXbgN7ACLEm6aLRnQ5CZvQbWgEfAN2Ad2K7fJA7zChgHfM0fYEKSr6kZd0BmNgMsAD+Ad5IqRfL6vJn1AatAL/Be0sfsuhpQQBaBT3GyyzKQ6j9m9iCUGANms7AbUMj1FdhwKSRZI4iZTfl3Scs5876nS/0WGKnK+A8UWruDXOMBSbk3MbOdAA03MZLf7CBi3OOxrYJmgQ9Af1FMyoAyMTsE5iUtKvLkBKhIGimKSVlQwDwUbpInDhoCdoFRSV/uGfQG2AReOmjO7Qh05iVbFp54I0/6c08XB30Gnkl6WnSbkKPQDHUHOwaOHLQFdEh6ngDypPSEbjQ2s9Y3s33g4n9AnkejTQ6VC0qSrsyt86RLMkMKKArBjRmS7J0IqrG3N7bSCZsIuk3YsGzpElQWFG3jtgSlFtUyoGgXd4tqwLxDFraJIpCZ5beJTOPy7traxlcHa20rz8Ba/zjJwFr/3KorIffygLwGdCorROT1aU8AAAAASUVORK5CYII=");
          }
        }
      }

      .m-upload-icon {
        box-sizing: border-box;
        border: 1px dashed @hc-color-border;
        font-size: 80rpx;
        font-weight: 100;
        line-height: 135rpx;
        text-align: center;
        color: @hc-color-info;
      }
    }
  }
}

.m-table {
  background-color: @hc-color-bg;

  .table {
    background-color: #fff;
    margin-bottom: 20rpx;
    border-top: 4rpx solid @hc-color-primary;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tb-extra {
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom: 2rpx solid #E5E5E5;
  }

  .extra-tit {
    flex: 1;
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .extra-txt {
    font-size: 28rpx;
    color: @hc-color-text;
  }

  .extra-num {
    font-size: 36rpx;
    color: @hc-color-title;
    margin-left: 20rpx;
  }

  .tb-head,
  .tb-body {}

  .tb-head {}

  .tb-body {}

  .head-tr,
  .body-tr {
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid @hc-color-border;
    padding: 0 15rpx;
  }

  .empty-tr {
    display: block;
    text-align: center;
    padding: 20rpx 0
  }

  .head-td,
  .body-td {
    .textBreak();

    &.td-1 {
      flex-basis: 20%;
    }

    &.td-2 {
      flex-basis: 40%;
    }

    &.td-center {
      text-align: center;
    }

    &.td-right {
      text-align: right;
    }
  }

  .head-td {
    padding: 15rpx;
    color: @hc-color-text;
    font-size: 28rpx;
  }

  .body-td {
    padding: 20rpx 15rpx;
    color: @hc-color-title;
    font-size: 30rpx;
  }
}

.ad-treat {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
  background-color: #fff;
  overflow: hidden;

  .ad-content {
    float: left;
    margin-top: 5rpx;
    color: @hc-color-warn;
  }

  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}

.main {
  padding: 20rpx;

  .list {
    margin-top: 20rpx;

    .title-box {
      display: flex;
      margin-bottom: 20rpx;

      image {
        display: flex;
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
      }

      .list-title {
        font-size: 32rpx;
        line-height: 50rpx;
      }
    }

    .table {
      border-top: 2rpx solid #ccc;
      border-left: 2rpx solid #ccc;
      background: #fff;

      .tr {
        display: flex;
        text-align: center;
        width: 100%;

        .th {
          font-weight: bold;
          font-size: 24rpx;
          line-height: 32rpx;
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
        }

        .td {
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.afterscan-operbtnbox {
  margin: 42rpx 40rpx;

  .binduser-btn_line {
    background: @hc-color-primary;
    color: #fff;
    border-radius: 10rpx;
    text-align: center;
    font-size: 36rpx;
    //padding: 22rpx 0;
  }
}

.ouContent-info{
  font-size: 15px !important;
  padding: 32rpx 32rpx 0px 32rpx;
}