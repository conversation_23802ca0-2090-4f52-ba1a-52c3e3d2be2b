<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from 'wepy';
import * as Api from './api';
import * as Utils from '@/utils/utils';
import ConfigMixin from '@/pages/configMixin';
import rtcUtils from './webrtc';

let pushCtx = null;
let allowPush = false;
export default class OrderDetail extends wepy.page {
  config = {
    navigationBarTitleText: ''
  };
  mixins = [ConfigMixin];
  data = {
    orientation: 'horizontal', // 画面方向
    hideMsg: true,
    isFavorite: false,
    doctorId: '',
    deptId: '',
    list: [],
    msgText: null,
    isShow: false,
    groupId: '',
    playUrl: '',
    pushUrl: '',
    interval: 0,
    showOpts: false,
    intervalStatus: 0,
    playStatus: false,
    pushStatus: false,
    inquiryId: '',
    inquirys: {},
    interval1: 0,
    scroll: 0,
    docInfo: {},
    bottomItem: [],
    userInfo: {},
    rtcCount1019: 0, // 1019 退出房间的计数器
    type:'',
    pushMagnify: false, // 录像组件是否放大
  };
  onReady(res) {
    pushCtx = wx.createLivePusherContext();
  }
  addRtcUser(msg = { userlist: [] }) {
    const userList = msg.userlist;
    console.log('test')
    this.playUrl = userList.length > 0 ? userList[0].playurl : this.playUrl;
  }
  async pushstatechange(e) {
    console.log('live-push code:', e.detail.code, e.detail.message);
    const { code, message } = e.detail;
    // 使用allowPush，重启推流操作时关闭状态码监听，避免导致重复报错
    if (!allowPush) return false;

    if (this._config.rtcOrigin === 'tx') {
      switch (code) {
        case -1301:
          this.inRtcRoomAgain('打开摄像头失败，请退出页面重进');
          break;
        case -1302:
          this.inRtcRoomAgain('打开麦克风失败，请退出页面重进');
          break;
        case -1307:
          this.inRtcRoomAgain('网络断连，请退出页面重进');
          break;
        case 1101:
          wepy.showToast({
            title: '网络状况不佳', //提示的内容,
            icon: 'none', //图标,
            duration: 1000, //延迟时间,
            mask: true //显示透明蒙层，防止触摸穿透
          });
          break;
        case 1019:
          if (this.rtcCount1019 <= 3) {
            this.txRTC();
            this.rtcCount1019 += 1;
            this.$apply();
          } else {
            this.inRtcRoomAgain('意外退出视频房间，请退出页面重进');
            this.rtcCount1019 = 0;
          }
          break;
        case 1020: {
          this.addRtcUser(JSON.parse(message)); // 获取用户列表
          break;
        }
        case 3001: //RTMP -DNS解析失败
          this.inRtcRoomAgain('DNS解析失败，请退出页面重进');
          break;
        case 3004: // RTMP服务器主动断开，请检查推流地址的合法性或防盗链有效期
          this.inRtcRoomAgain('推流地址不合法或防盗链已失效');
          break;
        case 3005: // RTMP读/写失败，自动重连
          wx.showToast({
            title: '读写视频流失败，正在自动重连',
            icon: 'none'
          })
          
          this.txRTC();
          // this.inRtcRoomAgain('数据读写失败，请退出页面重进');
          break;
        case 5000: // 5000 就退房
          this.txRTC();
          //   this.inRtcRoomAgain();
          break;
      }
    } else {
      switch (code) {
        case 1002:
          this.pushStatus = true;
          //   if (this.playStatus) {
          //     const { data = {}, code } = await Api.startLive({
          //       groupId: this.groupId
          //     });
          //     this.getChat();
          //   }
          break;
      }
    }

    // if (e.detail.code == '1002') {
    //     this.pushStatus = true;
    //     //   if (this.playStatus) {
    //     //     const { data = {}, code } = await Api.startLive({
    //     //       groupId: this.groupId
    //     //     });
    //     //     this.getChat();
    //     //   }
    // }
  }
  async playstatechange(e) {
    console.log('live-play code:', e.detail.code, e.detail.message);
    if (e.detail.code == '2004') {
      this.playStatus = true;
      this.startLive();
    }
  }
  async getPushStream(groupId) {
    const { data = {}, code } = await Api.pushStream({ groupId });
    if (data != null) {
      this.pushUrl = `${data.pushStream}&auth_key=${data.authKey}`;
      this.$apply();
    }
  }
  async getStatus(groupId) {
    console.log('test')
    const { data, code } = await Api.getStatus({ groupId });
    this.docInfo.image = data.headimg || '';
    if ( code == 999 ) {
      clearInterval(this.intervalStatus);
    }
    if (
      data != null &&
      data.status == 'live' && 
      !this.playUrl &&
      this._config.rtcOrigin !== 'tx'
    ) {
      //live已进入，end已结束
      this.getPullStream(groupId);
    }
    if (data != null && data.status == 'end') {
      clearInterval(this.intervalStatus);
      this.endLive();
      return;
    }
    this.$apply();
  }
  async getPullStream(groupId) {
    const { data = {}, code } = await Api.pullStream({ groupId });
    if (data != null && code === 0) {
      this.playUrl = `${data.pullStream}?auth_key=${data.authKey}`;
      this.startLive();
      this.getChat();
      this.interval = setInterval(() => this.getChat(), 2000);
      this.$apply();
    }
  }
  async startLive() {
    const { data = {}, code } = await Api.startLive({ groupId: this.groupId });
  }
  async endLive() {
    const param = {
      groupId: this.groupId,
      streamType: this._config.rtcOrigin === 'tx' ? 'txrtc' : ''
    };
    const { data, code } = await Api.endLive(param);
    if (code === 0) {
      this.intervalStatus && clearInterval(this.intervalStatus);
      await wepy.showToast({
        title: '通话结束',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        // 修复多次跳转页面的问题
        wepy.navigateBack({delta:1})
        // wepy.redirectTo({
        //   url: `/pages/consult/choosetype/index?groupId=${
        //     this.groupId
        //   }&type=${this.type || 2}`
        // });
      }, 2000);
    }
  }
  async getChat() {
    const params = {
      groupId: this.groupId,
      operator: 'user',
    }
    if (this.type == '12') {
      params.doctorType = '2';
    }
    const { data = {}, code } = await Api.getChat(params);
    if (code == 0) {
      this.list = (data.items || []).reverse();
      this.inquirys = data.inquiry || '';
      this.initBottomItem();
      wx.createSelectorQuery()
        .select('#chat-box')
        .boundingClientRect(rect => {
          this.scroll = rect ? rect.height - 160 : 160;
        })
        .exec();
      this.$apply();
    }
  }
  async send(param) {
    wepy.showLoading({ title: '发送中...', mask: true });
    const { data, code } = await Api.sendMsg(param);
    wepy.hideLoading();
    if (code == 0) {
      this.getChat();
    }
  }
  async getDocDetail(doctorId, deptId) {
    const params = { doctorId, deptId };
    if (this.type == '12') {
      params.doctorType = '2';
    }
    const getDocDetailRes = await Api.getDocDetail(params);
    if (getDocDetailRes.code == 0 && getDocDetailRes.data != null) {
      this.docInfo = getDocDetailRes.data.doctor;
      this.isFavorite = getDocDetailRes.data.isFavorite;
      wepy.setNavigationBarTitle({
        title: `${getDocDetailRes.data.doctor.name}`
      });
      this.$apply();
    }
  }

  /**
   * 使用腾讯实时音视频，生成pushUrl
   * @returns {Promise<void>}
   */
  async txRTC() {
    wepy.showLoading({
      title: '连接中...', //提示的内容,
      mask: true //显示透明蒙层，防止触摸穿透
    });
    const resRtcKey = await rtcUtils.getRtcKey({
      expire: 7200,
      groupId: this.groupId
    });
    if (resRtcKey.code != 0) {
      this.inRtcRoomAgain('获取进入视频房间的签名失败，请重试');
      return false;
    }
    this.pushUrl = await rtcUtils.getPushUrl({...resRtcKey.data, record_id: this.inquiryId});
    // 开启chat
    if (!this.pushUrl) {
      this.inRtcRoomAgain();
      return false;
    }
    wepy.hideLoading();

    // 告知后台推流成功，患者已进入直播间
    Api.pushStream({ groupId: this.groupId, streamType: 'txrtc' });
    allowPush = true;
    // this.getDocDetail(this.doctorId, this.deptId);
    // this.getChat();
    // this.interval = setInterval(() => this.getChat(), 2000);
    this.$apply();
  }
  /**
   * rtc失败，提示重新进入房间， 失败原因： 1，后台获取userSig失败 2，pusher组件返回状态5000等
   */
  inRtcRoomAgain(reason = '') {
    wepy.hideLoading();
    allowPush = false;
    wx.showModal({
      title: '温馨提示', //提示的标题,
      content: reason || '视频连接失败，请退出页面重新连接！', //提示的内容,
      showCancel: false, //是否显示取消按钮,
      confirmText: '退出', //确定按钮的文字，默认为取消，最多 4 个字符,
      confirmColor: '#3eceb6', //确定按钮的文字颜色,
      success: res => {
        if (res.confirm) {
          wepy.navigateBack({
            delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
          });
        }
      }
    });
  }
  aliRTC() {
    this.getPushStream(this.groupId);
    // this.getDocDetail(this.doctorId, this.deptId);
  }

  methods = {
    screenRoate(){
      this.orientation = this.orientation == 'horizontal' ? 'vertical' : 'horizontal';
      this.$apply();
    },
    liveScale(){
      this.pushMagnify = !this.pushMagnify;
      this.$apply();
    },
    tapShowOpts() {
      this.showOpts = !this.showOpts;
    },
    hideMsg() {
      this.hideMsg = !this.hideMsg;
    },
    async switchCollect(isFavorite) {
      const { doctorId, deptId } = this;
      if (!isFavorite) {
        const addCollectRes = await Api.addCollect({ doctorId, deptId });
        if (addCollectRes.code == 0) {
          this.isFavorite = true;
          wepy.showToast({
            title: '收藏成功',
            icon: 'success'
          });
        }
      } else {
        const cancelCollectRes = await Api.cancelCollect({ doctorId, deptId });
        if (cancelCollectRes.code == 0) {
          this.isFavorite = false;
          wepy.showToast({
            title: '取消成功',
            icon: 'success'
          });
        }
      }
      this.$apply();
    },
    openModal() {
      this.isShow = true;
    },
    cancel() {
      this.isShow = false;
    },
    sure() {
      this.isShow = false;
      this.endLive();
    },

    sendMsg() {
      if (this.msgText) {
        this.send({
          inquiryId: this.inquiryId,
          operator: 'user',
          content: this.msgText
        });
        this.msgText = this.msgText == null ? '' : null;
        this.$apply();
      }
    },
    saveMsg(e) {
      this.msgText = e.detail.value;
    },
    async picture(e) {
      const chooseRes = await wepy.chooseImage({
        sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
        count: 1
      });
      if (chooseRes.errMsg == 'chooseImage:ok') {
        const tempFilePath = chooseRes.tempFilePaths[0];
        try {
          wepy.showLoading({ title: '发送中...', mask: true });
          const imagesPaths = await Api.uploadImages(tempFilePath, {authUser: this.inquirys.edoctorId});
          wepy.hideLoading();
          this.send({
            inquiryId: this.inquiryId,
            operator: 'user',
            url:imagesPaths
          });
        } catch (error) {
          console.log(error)
          if (error.code !=0) {
            await wepy.showToast({
              title: error.msg || '发送失败',
              icon: 'none',
              duration: 2000
            });
          }
        }
      }
    },
    previewImg(e) {
      const arr = [];
      this.list.map(item => {
        if (item.url) {
          arr.push(item.url);
        }
      });
      const imgUrl = e.currentTarget.dataset.preurl;
      wepy.previewImage({
        current: imgUrl, // 当前显示图片的http链接
        urls: arr // 需要预览的图片http链接列表
      });
    },
    jumpDet(content, type) {
      console.log(type);
      const hisOrderId = JSON.parse(content).groupId;
      if (type == 2) {
        wepy.navigateTo({
          url: `/pages/inquiry/detail/examine?hisOrderId=${hisOrderId}`
        });
      } else if (type == 3) {
        wepy.navigateTo({
          url: `/pages/inquiry/detail/inspect?hisOrderId=${hisOrderId}`
        });
      } else {
        wepy.navigateTo({
          url: `/pages/inquiry/detail/prescribe?hisOrderId=${hisOrderId}`
        });
      }
    },
    jumpSign(content) {
      const userId = JSON.parse(content)[0].userId;
      const userName = userId == this.inquirys.userId ? this.inquirys.userName : '';
      wepy.navigateTo({
        url: `/pages/inquiry/mysigns/index?userId=${userId}&name=${userName}`
      });
    }
  };
  onLoad(options) {
    console.log('onload')
    this.inquiryId = options.inquiryId;
    this.groupId = options.groupId;
    this.deptId = options.deptId;
    this.doctorId = options.doctorId;
    this.userInfo = wepy.getStorageSync('userInfo');
    // 选择腾讯rtc和阿里rtc
    this._config.rtcOrigin === 'tx' ? this.txRTC() : this.aliRTC();
    this.getStatus(this.groupId);
    this.intervalStatus = setInterval(() => this.getStatus(this.groupId), 2000);
    this.type = options.type;
    this.startLive(options.groupId);
  }
  onShow() {
  this.showOpts = false;
    this.initBottomItem();
    wepy.setKeepScreenOn({
      keepScreenOn: true
    });
  }
  initBottomItem() {
    this.bottomItem = [
      {
        name: '处方记录',
        url: `/pages/inquiry/detail/listhistory?patientId=${this.inquirys.patientId}&inquiryId=${this.inquiryId}&name=${this.inquirys.patientName}`,
        icon: 'REPLACE_IMG_DOMAIN/ih-miniapp/kcf.png',
        event: '',
        show: this._config.prescribe.use ? true : false
      },
      {
        name: '我的体征',
        url: `/pages/inquiry/myfamily/index?inquiryId=${this.inquiryId}&userName=${this.inquirys.userName}&userId=${this.inquirys.userId}`,
        icon: 'REPLACE_IMG_DOMAIN/ih-miniapp/my-signs.png',
        event: '',
        show: this._config.CDM.use ? true : false
      },
    ];
  }
  onHide() {}
  onUnload() {
    clearInterval(this.interval);
    clearInterval(this.intervalStatus);
    clearInterval(this.interval1);
  }
}
</script>
