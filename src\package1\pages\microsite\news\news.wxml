<view class="p-page page-news">
  <view class="m-tab">
    <view class="unit-tab">
      <block wx:for="{{tabList}}" wx:for-index="index" wx:for-item="item" wx:key="{{index}}">
        <view
          class="unit-tab-li tab-li {{tabIndex == item.id ? 'active' : ''}}"
          @tap="bindChangeTabIndex({{item.id}})"
        >{{item.name}}
        </view>
      </block>
    </view>
  </view>

  <block wx:if="{{articles.length > 0}}">
    <navigator wx:for="{{articles}}" wx:key="{{index}}" url="/pages/microweb/news/artical/news/index?id={{item.id}}">
      <view class="item-box">
        <!--<view class="image-box">
          <image src="{{item.doctor.image || 'REPLACE_IMG_DOMAIN/ih-miniapp/news1.png'}}" alt="医生头像" />
        </view>-->
        <view class="list-box">
          <view class='list-title'>{{item.title}}</view>
          <view class='list-time'><text>{{item.tagName}}</text>{{item.createTime}}</view>
        </view>
      </view>
    </navigator>
  </block>
  <block wx:else>
    <empty :config.sync="emptyConfig">
      <block slot="text">暂无新闻公告</block>
    </empty>
  </block>
</view>