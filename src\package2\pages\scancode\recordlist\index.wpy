<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class Search extends wepy.page {
    config = {
      navigationBarTitleText: '扫码开单缴费记录',
      navigationBarBackgroundColor: '#fff',
    };

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 挂号记录列表
      orderList: []
    };

    wxs = {
      WxsUtils: WxsUtils
    };

    components = {
      'empty': Empty
    };

    props = {};

    onShow(options) {
      this.getOrderList();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到挂号详情页
       * @param item
       */
      bindGoDetail(item){
        const { id = '', sampleNumber, extfiled1} = item;
        wepy.navigateTo({
          url: `/package2/pages/scancode/recorddetail/index?orderId=${id}&sampleNumber=${sampleNumber}&extfiled1=${extfiled1}`,
        });
      },
    };

    async getOrderList(word) {
      const { code, data = {} } = await Api.orderList();
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      
      // this.orderList = data.extorderList || [];
      this.revertList(data.extorderList || []);
      // this.$apply();
    };

    async revertList(extorderList) {
      wepy.showLoading({
        title: '加载中',
        mask: true,
      });

      // 存放所有异步操作的 Promise
      const promises = [];

      for(let el of extorderList) {
        if(el.sampleNumber){
          // const { code, data = {} } = await Api.getBySamplenumber({id: el.sampleNumber});
          // el.sampleReadyFlag = data.sampleReadyFlag;
          // el.sjzReadyFlag = data.sjzReadyFlag;
          // el.sampleNumber = data.sampleNumber;
          // el.extfiled1 = data.extfiled1;
          promises.push(
            Api.getBySamplenumber({ id: el.sampleNumber }).then(({ code, data = {} }) => {
              el.sampleReadyFlag = data.sampleReadyFlag;
              el.sjzReadyFlag = data.sjzReadyFlag;
              el.sampleNumber = data.sampleNumber;
              el.extfiled1 = data.extfiled1;
            })
          );
        }
      }
      
      // 使用 Promise.all() 等待所有异步操作完成
      await Promise.all(promises);

      wepy.hideLoading();
      this.orderList = extorderList;
      this.$apply();
    }
    
  }
</script>
