@import "../../resources/style/mixins";

.g-list {
  position: relative;
  height: 100%;
  flex: 1;
  -webkit-overflow-scrolling: touch;
}

.m-list {
  position: absolute;
  left: 0;
  right: 0;
  top: 1rpx;
  bottom: 0;

  &.active {
    display: block;
  }
  .list-box {
    display: flex;
    flex-direction: row;
    height: 100%;
  }
  .list-lt-box {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 30rpx;
    background-color: #fff;
  }
  .list-lt {
    position: relative;
    margin-bottom: 30rpx;
  }
  .lt-item {
    position: relative;
    max-width: 7em;
    min-width: 4em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 88rpx;
    line-height: 88rpx;
    padding: 0 32rpx;
    color: rgba(0, 0, 0, 0.7);
    font-weight: 600;
    font-size: 34rpx;

    &.active {
      color: @hc-color-primary;
      z-index: 2;
    }
    &.active::after {
      content: " ";
      position: absolute;
      right: 0;
      top: 16rpx;
      width: 8rpx;
      height: 56rpx;
      background: @hc-color-primary;
      border-radius: 28px 0 0 28px;
    }
  }
  .list-rt-box {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 30rpx;
    background-color: #F2F4F4;
  }
  .list-rt {
    position: relative;
    // margin-top: 40rpx;
    overflow-x: hidden;
    padding-bottom: 30rpx;
  }
  .rt-history-box {
    display: none;
    &.active {
      display: block;
    }
  }
  .rt-history {
    display: block;
    color: rgba(0, 0, 0, 0.9);
    margin: 24rpx 32rpx 24rpx 40rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 12rpx 32rpx;

    .his-tit {
      padding-top: 24rpx;
      font-weight: 500;
      font-size: 28rpx;
    }
    .his-item {
      display: flex;
      padding: 24rpx 30rpx 24rpx 0;
      border-bottom: 1rpx solid @hc-color-border;
      align-items: center;
      &:last-child {
        border-bottom: none;
      }
    }
    .item-hd {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 90rpx;
      border-radius: 6rpx;
      overflow: hidden;
      image {
        vertical-align: top;
      }
    }
    .item-bd {
      flex: 1;
      margin-left: 30rpx;

      .unit-color-title {
        color: @hc-color-title;
        font-size: 30rpx;
      }

      .unit-color-text {
        color: @hc-color-text;
        font-size: 28rpx;
        margin-top: 5rpx;
      }
    }
    .item-ft {
    }
  }
  .rt-sec {
    display: none;
    &.active {
      display: block;
    }
    .sec-li {
      display: block;
      color: @hc-color-title;
      margin: 24rpx 32rpx 24rpx 40rpx;
      background: #fff;
      border-radius: 24rpx;
      padding: 12rpx 32rpx;
      // margin: 24rpx;
      // background: #fff;
      // border-radius: 8rpx;
      // height: 168rpx;
      // line-height: 168rpx;
      // text-align: center;
      // background: url('REPLACE_IMG_DOMAIN/his-miniapp/images/deplist-bg.png') no-repeat;
      // background-position: center;
      // background-size: cover;
      // border: 1rpx solid rgba(211, 234, 235, 1);
    }
    .sec-li-wrap {
      display: flex;
      // flex-direction: column;
      align-items: center;
      color: @hc-color-title;
      // line-height: 168rpx;
      // .little-text{
      //   color: #2D666F;
      //   font-size: 16rpx;
      // }
    }
    .sec-bd {
      flex: 1;
      // font-weight: 600;
      line-height: 88rpx; 
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #2D666F;
      // font-size: 32rpx;
    }
    .trd-arrow {
      font-size: 34rpx;
      line-height: 1;
      color: @hc-color-primary;
      transition: transform 0.2s;
    }
    .trd-box {
      display: none;
      padding-left: 30rpx;
    }
    .trd-li {
      display: flex;
      align-items: center;
      line-height: 88rpx;
      padding-right: 30rpx;
      color: @hc-color-title;
      border-bottom: 1rpx solid @hc-color-border;
    }
    .trd-bd {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .sec-li.active {
      .trd-box {
        display: block;
      }
      .trd-arrow {
        transform: rotate(-180deg);
      }
    }
  }
}

.unit-arrow {
  display: none;
  width: 15rpx;
  height: 15rpx;
  border-right: 4rpx solid #c7c7cc;
  border-bottom: 4rpx solid #c7c7cc;
  transform: translateX(-8rpx) rotate(-45deg);
}

.m-suggest {
  background-color: #fff;
  padding: 20rpx 0;
  .suggest-title {
    color: @hc-color-title;
    font-size: 28rpx;
    margin: 0 30rpx 0;
    position: relative;

    &:after {
      content: " ";
      position: absolute;
      right: 0;
      top: 50%;
      transform-origin: center;
      width: 15rpx;
      height: 15rpx;
      border-right: 4rpx solid #c7c7cc;
      border-bottom: 4rpx solid #c7c7cc;
      transform: translateY(-50%) rotate(45deg);
    }
  }

  .m-tags {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 10000rpx;

    .tag-item {
      font-size: 30rpx;
      color: @hc-color-text;
      display: block;
      margin: 20rpx 0 0 30rpx;
      width: 150rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      overflow: hidden;
      border: 1rpx solid @hc-color-border;
      border-radius: 30px;
    }
  }

  &.tagopen {
    .suggest-title:after {
      transform: translateY(-50%) rotate(-135deg);
    }

    .m-tags {
      width: 750rpx;
      flex-wrap: wrap;
    }
  }
}
