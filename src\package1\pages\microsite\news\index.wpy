<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import Empty from '@/components/empty/index';
import ConfigMixin from '@/pages/configMixin';

  export default class Index extends wepy.page {
    config = {
      navigationBarTitleText: '科普宣教',
    };
    mixins = [ConfigMixin];

    components = {
    'empty': Empty,
  };


    data = {
      tabIndex: '',
      tabList: [],
      articleList: [],
      emptyConfig: {
        show: true
      },
    };

    async getArticleTags() {
      const { data, code } = await Api.getTags({contentType: 'health_education',pid:1 });
      this.tabList = data;
      if (data.length > 0) {
        this.tabIndex = data[0].typeId;
        this.getarticles(data[0].typeId);
      }
      this.$apply();
    }

    async getarticles(tagId) {
      const { data = {}, code } = await Api.getarticles({contentType: 'health_education', typeId: tagId});
      this.articleList = data.recordList || [];
      this.$apply();
    }

    methods = {
      bindChangeTabIndex(index){
        if (this.tabIndex === index) {
          return;
        }
        this.tabIndex = index;
        this.getarticles(index);
      }
    };
    onLoad(options){
      this.tabIndex = options.tabIndex;
      this.getArticleTags();
    }
  onShareAppMessage() {
    return {
      title: `${this._config.name}`,
      path: `/pages/microwsite/news/index?tabIndex=${this.tabIndex}`
    }
  }
  }
</script>
