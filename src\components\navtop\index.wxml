<view class="nav-top" style="padding-bottom: {{navHeight}}">
  <view class="nav-box" style="height:{{navHeight}}; background-color: {{bgColor}}; color: {{color}}">
    <view class="capsule" wx:if="{{isBack}}">
      <view class="nav-back" bindtap="backTo">
        <view class="back-confont"></view>
      </view>
      <view class="back-home" bindtap="goHome">
        <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/parking/home.png" />
      </view>
    </view>
    <view class="nav-title" style="padding-right: {{isBack ? '190rpx' : ''}}">
      <slot name="text"></slot>
    </view>
  </view>
</view>