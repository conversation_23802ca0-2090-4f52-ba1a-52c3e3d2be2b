<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import Table from "@/components/table/index";
import DetailItem from "@/components/detailitem/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import * as Utils from "@/utils/utils";
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "住院申请结算",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    outpatient: Outpatient,
    empty: Empty,
    table: Table,
    "detail-item": DetailItem
  };
  wxs = {
    WxsUtils: WxsUtils
  };
  data = {
    // url参数
    options: {},
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 就诊人列表
    outpatient: {},
    // 当前就诊人信息
    currentPatient: {},
    inHospitalBill: [],
    infoTitle: "住院信息",
    tableHeader: ["项目名称", "单价/数量", "金额"],
    columnKey: ["xmmc", "priceAndAcount", "je"],
    dataSource: [
      { label: "住院科室", value: "生殖中心" },
      { label: "住院科室", value: "生殖中心" },
      { label: "住院科室", value: "生殖中心" }
    ],
    canChangePatients: false,
    billInfo: {},
    jzid: "",
    patCardNo: ""
  };
  onLoad(options) {
    this.jzid = options.jzid;
    this.patCardNo = options.patCardNo;
  }
  onShow() {
    const { patientId = "", billInfo = "" } = this.$wxpage.options;
    const info = JSON.parse(billInfo);
    this.options = this.$wxpage.options;
    this.billInfo = info;
    this.dataSource = [
      { label: "住院科室", value: info.ryksmc },
      { label: "入院时间", value: info.rysj },
      {
        label: "住院预交总金额（元）",
        value: Utils.formatMoney(info.yjzje)
      },
      { label: "费用总金额（元）", value: Utils.formatMoney(info.zyzfy) },
      { label: "应退费金额（元）", value: Utils.formatMoney(info.yjjye) }
    ];
    this.$broadcast("outpatient-get-patient", { patientId });
    this.getPatientInfo();
  }
  // 获取住院人信息
  async getPatientInfo() {
    const { patientId } = this.options;
    if (!patientId) {
      return;
    }
    const { code, data = {}, msg } = await Api.getPatientInfo({ patientId });
    if (code !== 0) {
      return;
    }
    this.currentPatient = data;
    this.getWaitSettlementInfo();
    this.$apply();
  }
  events = {
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient;
        this.outpatientConfig.infoShow = true;
        this.$apply();
      }
      this.getPatientInfo();
    },
    getBillDetail(item = {}) {
      return;
    }
  };
  methods = {
    applyAccount() {
      const { idNo, patientName } = this.currentPatient;
      const billInfo = JSON.stringify({ ...this.billInfo, idNo, patientName });
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/notice/index?billInfo=${billInfo}&jzid=${
          this.jzid
        }&patCardNo=${this.patCardNo}`
      });
    }
  };
  async getWaitSettlementInfo() {
    const { patCardNo, patHisNo } = this.currentPatient;
    const { zyh } = this.billInfo;
    const { data, code } = await Api.getWaitSettlementInfo({
      pid: patHisNo,
      grid: patCardNo,
      zyh
    });
    if (code === 0) {
      this.inHospitalBill =
        data.map(i => {
          const newJe = Utils.formatMoney(i.je);
          return {
            ...i,
            je: newJe,
            priceAndAcount: `${Utils.formatMoney(i.dj)}/${i.sl}`
          };
        }) || [];
      this.$apply();
    }
  }
}
</script>
