@import "../../../../resources/style/mixins";

page{
  padding-bottom: 40rpx;
}

.m-tab {
  background-color: #fff;
  padding: 0 15%;
  .tab-li {
    &:after {
      width: 1.5em;
    }
  }
}

.unit-tab {
  position: relative;
  display: flex;
  background-color: #fff;
  
  & > .unit-tab-li {
    position: relative;
    z-index: 1;
    display: block;
    flex: 1;
    width: 0%;
    font-size: 28rpx;
    text-align: center;
    color: @hc-color-title;
    height: 94rpx;
    line-height: 94rpx;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    user-select: none;
    & > a {
      display: block;
      width: 100%;
      height: 100%;
      color: @hc-color-title;
    }
    &:after {
      content: " ";
      position: absolute;
      left: 50%;
      bottom: 0;
      display: block;
      width: 2.5em;
      height: 4rpx;
      background-color: @hc-color-primary;
      transform: translateX(-50%) scaleX(0);
      transition: all ease-out 0.2s 0.1s;
    }
    &.active {
      color: @hc-color-primary;
      &:after {
        transform: translateX(-50%) scale(1);
      }
      & > a {
        color: @hc-color-primary;
      }
    }
  }
}

.m-title-icon {
  display: inline-block;
  text-align: center;
  line-height: 36rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  color: #fff;
  margin-right: 10rpx;
  image {
    width: 26rpx;
    height: 26rpx;
  }
  &.women {
    background-color: #FF6FA1;
  }
  &.man {
    background-color: #259EFF;
  }
}

.m-profile-card {
  margin: 30rpx 30rpx 0;
  border-radius: 4rpx;
  // padding: 0 30rpx;
  background-color: #fff;
  .m-profile-title {
    text-align: center;
    padding: 30rpx 0 10rpx;
  }
  .m-title-content {
    display: inline;
    color: @hc-color-title;
    font-size: 36rpx;
    font-weight: 500;
  }
}

.item-arrow{
  width: 17rpx;
  height: 17rpx;
  border-right: 5rpx solid #C7C7CC;
  border-bottom: 5rpx solid #C7C7CC;
  transform: translateX(-8rpx) rotate(-45deg);
}

.m-list{
  padding-left: 30rpx;
  overflow: hidden;
  .list-item{
    position: relative;
    padding: 12rpx 20rpx 12rpx 0;
    display: flex;
    align-items: center;
    margin: 6rpx 0;
    background-color: #fff;
    &:before{
      content: ' ';
      position: absolute;
      left: 0rpx;
      right: 0;
      top: 0;
      border-top: 2rpx solid @hc-color-border;
    }
    &:first-child:before{
      display: none;
    }
  }
  .item-icon{
    width: 60rpx;
    height: 60rpx;
    overflow: hidden;
    border-radius: 50%;
    image{
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .item-main{
    flex: 1;
  }
  .main-tit{
    font-size: 30rpx;
    color:@hc-color-title;
  }
  .main-txt{
    font-size: 26rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
    word-break: break-all;
  }
  .item-extra{
    text-align: right;
  }
  .extra-tit{
    font-size: 34rpx;
    color:@hc-color-text;
  }
  .extra-txt{
    font-size: 30rpx;
    color:@hc-color-text;
    margin-top: 10rpx;
  }
  
  .list-item{
  }
  .unit-label{
    margin-left: 5px;
    display: inline-block;
    font-size: 24rpx;
    padding: 0 6rpx;
    color:#fff;
    line-height: 34rpx;
    vertical-align: middle;
    background-color: @hc-color-primary;
    border-radius: 4rpx;
  }
  .item-arrow{
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #C7C7CC;
    border-bottom: 5rpx solid #C7C7CC;
    -webkit-transform: translateX(-8rpx) rotate(-45deg);
    transform: translateX(-8rpx) rotate(-45deg);
  }
}