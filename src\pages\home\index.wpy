<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from "wepy";

import { IS_PRODUCTION, DOMAIN, ADULT_SURVEY_ID, CHILD_SURVEY_ID } from "@/config/constant";
import functionMap from "@/config/functionMap";

import Search from "@/components/search/index";
import Pop from "@/components/pop/index";
import Outpatient from "@/components/outpatient/index";
import NavTab from "@/components/navtab/index";
import NavBar from "@/components/navbar/index";
import { sleep } from "@/utils/utils";
import * as Utils from "@/utils/utils";
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "湖南家辉遗传专科医院",
    navigationBarBackgroundColor: "#fff",
    navigationStyle: "custom",
    usingComponents: {
      wxparser: "plugin://wxparserPlugin/wxparser",
    },
  };

  components = {
    search: Search,
    outpatient: Outpatient,
    "nav-tab": NavTab,
    "nav-bar1": NavBar,
    pop: Pop,
  };

  onShow() {
    this.isQueryChat = true;
    this.getFunctionList();
    this.getUnreadNotice();
    this.getUnreadMsgCount();
    this.getdocbyexpert();
    this.getFamilyrelations();
    this.$broadcast("outpatient-get-patient");
    this.time = this.$parent.globalData.time;
    this.$apply();
  }

  onLoad(options) {
    // this.getNoteProfile({
    // 	profileKey: 'getAlertNoteProfile_xcxHomePage_title',
    // 	hisId: 242,
    // })
    this.getNoteProfileTitle({
      profileKey: "getAlertNoteProfile_xcxHomePage",
      hisId: 242,
    });
    this.getNoteProfileRegister({
      profileKey: "getAlertNoteProfile_registertext",
      hisId: 242,
    });
    this.getarticles();
    if (options.jumpUrl) {
      wepy.navigateTo({
        url: decodeURIComponent(options.jumpUrl),
      });
    }
    this.domain = DOMAIN;
    console.log(DOMAIN, this.domain, "=====68");
  }

  onShareAppMessage() {
    return {
      title: "湖南家辉遗传专科医院",
      path: "/pages/home/<USER>",
    };
  }

  onHide() {
    this.isQueryChat = false;
  }
  onUnload() {
    this.isQueryChat = false;
    if (getCurrentPages().length != 0) {
      getCurrentPages()[getCurrentPages().length - 1].onLoad();
    }
    return;
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    domain: "",
    isShowUnreadRadius: false,
    isHome: true,
    promotTitle: "",
    // 未读聊天消息数量
    msgCount: 0,
    msgCountTimer: 0,
    // 是否查询聊天信息, setTimeout不稳定
    isQueryChat: true,
    isSelectExpand: false,
    isServiceExpand: false,
    // 挂号弹窗提示
    modal: {},
    // 挂号弹窗内容
    descContent: "",
    functionMap,
    // 空就诊人列表提醒
    emptyNotice: false,
    // 登录的就诊人
    activePatient: {},
    // 就诊人配置
    outpatientConfig: {
      show: false,
    },
    // 就诊人列表
    outpatient: {},
    // 搜索框状态
    searchFocus: false,
    // 显示全部功能
    showAllFun: false,
    // 快速办理-预约功能
    quickFunc: [],
    // 查询功能
    queryFunc: [],
    // 服务功能
    serviceFunc: [],
    // 未读重要通知
    notice: {},
    noticeList: [{}],
    // 未读待办列表
    todoMsgs: [],
    // 未读通知消息数量
    unreadCount: 0,
    // tab类型
    navTabType: "home",
    // 弹窗显示
    popConfig: {
      show: false,
    },
    // 弹窗码类型
    codeType: 1,
    // 是否显示关注组件
    officialAccountShow: "1",
    // 专家列表
    doctorList: [],
    //病毒提示
    extra: {
      isShow: true,
      content: ``,
    },

    extra1: {
      isShow: false,
      content: `因特殊原因，原定于今晚19:30在中信湘雅小程序直播的卢光琇教授"罕见病夫妇优生优育之路"转至其他平台，具体观看地址请参加我院公众号3月10号推出的直播预告，敬请谅解，欢迎收看。`,
    },

    // UI升级调整
    // 快速操作（互联网挂号、门诊挂号）
    functionList: [
      {
        url: "/pages/register/arealist/index", // /pages/register/deptlist/index
        image: "/hlwspkzgh.png",
        text: "互联网视频",
        desc: "看诊挂号",
      },
      {
        url: "/pages/register/deptlist/index",
        image: "/xcmzgh.png",
        text: "现场门诊",
        desc: "挂号",
      },
    ],
    // 快速操作（快速缴费、在线咨询、报告查询）
    quickOperateFuncList: [
      {
        url: "/pages/register/deptlist/index",
        image: "reservation.png",
        text: "预约挂号",
        minText: "在线实时挂号",
        urlType: 1,
        sceneType: 1,
        id: "",
      },
      {
        url: "/pages/treat/untreatlist/index",
        image: "outpatient-payment.png",
        text: "门诊缴费",
        minText: "线上快速缴费",
        id: "",
      },
      {
        url: "/pages/consult/choosetype/index",
        image: "consulting.png",
        text: "在线咨询",
        minText: "医生在线答疑",
        urlType: 1,
        sceneType: 1,
        id: "",
      },
      // {
      // 	url: '/pages/report/reportlist/index',
      // 	image: 'bill-day.png',
      // 	text: '报告查询',
      // 	minText: '检测报告快速查',
      // 	urlType: 1,
      // 	sceneType: 1,
      // 	id: '302004',
      // },
    ],
    // 底部其他功能
    otherFunctionList: [
      {
        name: "医院介绍",
        icon: "yyjs.png",
        url: "/pages/microsite/home/<USER>",
        urlType: 1,
        sceneType: 1,
        id: "",
      },
      {
        name: "导诊服务",
        icon: "dzfw.png",
        url: "/pages/guideservice/index",
        urlType: 1,
        sceneType: 1,
        id: "",
      },
      {
        name: "消息中心",
        icon: "xxzx.png",
        url: "/pages/todo/index",
        urlType: 1,
        sceneType: 1,
        id: "",
      },
    ],
    // 条形码/二维码弹窗
    showBarcode: false,
    // 切换导航条
    navFlag: "",
    // 遗传科普
    filesList: [],
    // 家系图弹框
    jxtisShow: false,
    jxturl: "https://yc.jiahuiyiyuan.com/familyTree/drawImage/index.jsp",
  };

  //获取首页弹框提示语
  // async getNoteProfile(param) {
  // 	wepy.showLoading({title: '加载中', mask: true})
  // 	const {code, data = {}, msg} = await Api.getNoteProfile(param)
  // 	wepy.hideLoading()
  // 	if (code == 0) {
  // 		this.extra.content = data.profileValue
  // 	}
  // }

  async getarticles() {
    const { code, data = {} } = await Api.getarticles();
    data.recordList = data.recordList.filter(
      (item) => item.position && item.position.indexOf("1") !== -1
    );
    if (code == 0) {
      this.filesList = data.recordList;
    }
  }

  async getFamilyrelations() {
    const { code, data = {} } = await Api.getFamilyrelations();
    if (code === 0 && data.resultCode == 0) {
      wepy.setStorageSync("familyrelations", data.item);
    }
  }

  async getNoteProfileTitle(param) {
    const { code, data = {}, msg } = await Api.getNoteProfile(param);
    if (code == 0) {
      this.promotTitle = data.profileValue;
    }
  }

  //获取挂号弹框提示语
  async getNoteProfileRegister(param) {
    const { code, data = {}, msg } = await Api.getNoteProfile(param);
    if (code == 0) {
      this.descContent = data.profileValue;
    }
  }

  // 获取知情同意书未读信息
  async getZQSUnreadNotice(parent) {
    const params = {
      pid: parent.patHisNo,
      grid: parent.patCardNo,
    };
    const { code, data = {}, msg } = await Api.getZQSUnreadNotice(params);
    if (code === 0) {
      const unReadList = data.filter((item) => item.ztbz === "0");
      this.isShowUnreadRadius = !!unReadList.length;
      this.$apply();
    }
  }

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    ocjxt() {
      this.jxtisShow = !this.jxtisShow;
      this.$apply();
    },
    copyjxt() {
      wepy.setClipboardData({
        data: `${this.jxturl}?${Date.now()}`,
        success() {
          wepy.showToast({
            title: "复制成功",
            icon: "success",
            duration: 2000,
          });
        },
      });
    },
    jxtjump() {
      this.jxtisShow = false;
      this.$apply();
      wepy.navigateTo({
        url: `/pages/webview/index?weburl=${this.jxturl}?${Date.now()}`,
      });
    },
    toManagerOutpatient() {
      wepy.navigateTo({ url: "/pages/usercenter/userlist/index" });
    },
    /**
     * 功能页面跳转
     * @param {string} url
     * @param {number} index
     */
    toFunc(url, index) {
      if ((index == 0 || index == 1) && this.descContent) {
        // 挂号须知
        this.modal = {
          index,
          isShow: true,
          contentType: typeof this.descContent,
          content: this.descContent,
        };
        this.$apply();
        return false;
      }
      wepy.navigateTo({ url: `${url}?deptType=${index}` });
    },
    // 打电话
    phoneCall(phone) {
      wepy.makePhoneCall({
        phoneNumber: phone,
      });
    },
    /**
     * 是否同意挂号须知，1为同意
     * @param {number} numStr
     */
    agree(numStr) {
      if (parseInt(numStr)) {
        if (this.modal.index == 0) {
          wepy.navigateTo({
            url: `/pages/register/deptlist/index?deptType=${this.modal.index}`,
          });
        } else {
          wepy.navigateTo({
            url: `/pages/register/deptlist/index?deptType=${this.modal.index}`,
          });
        }
      }
      this.modal.isShow = false;
      this.$apply();
    },
    changeExtra() {
      this.extra.isShow = false;
      this.$apply();
    },
    changeExtra1() {
      this.extra1.isShow = false;
      this.$apply();
    },
    bindShowOutpatient() {
      this.outpatientConfig.show = true;
    },
    /**
     * 显示全部功能切换
     * @param nextStatus
     */
    bindToggleAllFun(nextStatus) {
      this.showAllFun = nextStatus;
    },
    bindQrCode() {
      this.popConfig.show = true;
    },
    navigateToDynamic(item) {
      let url = "";
      if (item.articleId) {
        url = `/pages/dynamic/info/index?articleId=${item.articleId}&position=${this.position}`;
      } else {
        url = `/pages/dynamic/secondindex/index?typeId=${item.typeId}&title=${
          item.typeName || item.title
        }`;
      }
      wepy.navigateTo({ url });
    },
    /**
     * 点击提示功能
     */
    bindTapNotice() {
      wepy.navigateTo({
        url: "/pages/todo/index",
      });
    },
    bindGoPage(item = {}) {
      // 处理"查看更多"
      if (item.type === "more") {
        this[item.isExpandProp] = true;
        return false;
      }

      // 处理跳转h5外部页面
      if (item.isWebUrl) {
        const weburl = encodeURIComponent(
          `https://ssyy.zxxyyy.cn/#/instrument/main?pid=${this.activePatient.patHisNo}&grid=${this.activePatient.patCardNo}`
        );
        wepy.navigateTo({
          url: `${item.url}?weburl=${weburl}`,
        });
        return false;
      }

      if (item.status == 0) {
        // 下线处理
        const url = encodeURIComponent("/pages/home/<USER>");
        wepy.navigateTo({
          url: `/pages/auth/developing/index?url=${url}`,
        });
        return false;
      }
      if (item.urlType == 2) {
        // 打开其他小程序
        if (!item.appId) {
          return;
        }
        wepy.navigateToMiniProgram({
          appId: item.appId,
          path: item.url,
          envVersion: item.envVersion || "trial", // develop trial releaseconsole
        });
        return;
      } else if (item.urlType == 3) {
        if (item.pid) {
          if (!this.activePatient.patHisNo) {
            wepy.showModal({
              title: "提示", //提示的标题,
              content: "请先登录", //提示的内容,
              showCancel: false, //是否显示取消按钮,
              confirmText: "确定", //确定按钮的文字，默认为取消，最多 4 个字符,
            });
            return;
          }
          wepy.navigateTo({
            url: `/pages/webview/index?pid=${this.activePatient.patHisNo}&weburl=${item.url}`,
          });
          return false;
        }
        wepy.navigateTo({
          url: `/pages/webview/index?weburl=${item.url}`,
        });
        return false;
      }
      // 小程序内链接
      if (!item.url) {
        wepy.showModal({
          title: "提示",
          content: "功能开发中，敬请期待...",
          showCancel: false,
          confirmText: "确定",
        });
        return;
      }
      const { activePatient = {} } = this.outpatient;
      const { patientId = "", patHisNo = "", patCardNo = "" } = activePatient;
      wepy.navigateTo({
        url: `${item.url}?patientId=${patientId}&patHisNo=${patHisNo}&patCardNo=${patCardNo}&type=${item.type}&iconkey=${item.icon}`,
      });
    },

    //跳转互联网医院小程序
    goHlwMiniProgram() {
      wepy.navigateToMiniProgram({
        appId: "wx2f57a3cd11748440",
      });
    },

    // 常用功能
    goFunctionPage(e) {
      const { url, type } = e.currentTarget.dataset;
      // const {activePatient = {}} = this.outpatient
      const {
        patientId = "",
        patHisNo = "",
        patCardNo = "",
        patientName = "",
      } = this.activePatient;
      if (type) {
        wepy.navigateTo({
          url: url,
        });
        return;
      }
      wepy.navigateTo({
        url: `${url}?patientId=${patientId}&patHisNo=${patHisNo}&patCardNo=${patCardNo}&patientName=${patientName}`,
      });
    },

    navigateTo(e) {
      const { url } = e.currentTarget.dataset;
      const { activePatient = {} } = this.outpatient;
      const { patientId = "", patHisNo = "", patCardNo = "" } = activePatient;
      wepy.navigateTo({
        url: `${url}?patientId=${patientId}&pid=${patHisNo}&grid=${patCardNo}`,
      });
    },

    // 收起展开菜单
    onPackUp(prop) {
      this[prop] = false;
    },
    // 处理缴费、在线咨询、报告查询的跳转
    onDealQuickNav(index) {
      const item = this.quickOperateFuncList[index];
      if (!item.url) return;
      index === 1
        ? this.$wxpage.toFunc(2, item.url)
        : this.$wxpage.bindGoPage(item);
    },
    // 打开/关闭条形码弹窗
    onToggleBarcode() {
      this.showBarcode = !this.showBarcode;
    },
    onSwitchCodeType(v) {
      if (v) {
        this.codeType = v;
        this.$apply();
      }
    },
    // 页面滚动修切换航条颜色
    bindScroll(e) {
      if (e.detail.scrollTop > 50) {
        this.navFlag = "#fff";
      } else {
        this.navFlag = "transparent";
      }
      this.$apply();
    },
    /**
     * 跳转医生主页
     */
    toDoctorIntro(item = {}) {
      const { no = "", deptId = "", deptName = "", sex = "" } = item;
      const query = Utils.jsonToQueryString({
        deptId,
        doctorId: no,
        deptName: deptName || "",
        sex,
      });
      wepy.navigateTo({
        url: `/package1/pages/microsite/doctorinfo/index?${query}`,
      });
    },
    /**
     * 处理样本问卷点击事件
     */
    handleSampleSurvey(e) {
      const { activePatient = {} } = this;
      const { patientId = "", patHisNo = "", patCardNo = "" } = activePatient;
      
      console.log('样本问卷点击，患者信息:', { patientId, patHisNo, patCardNo });
      
      // 显示加载中
      wepy.showLoading({
        title: '加载中',
        mask: true
      });
      
      // 请求健康样本订单列表
      Api.getHealthOrderList({
        hisId: '242' // 医院ID，默认值
      }).then(res => {
        wepy.hideLoading();
        
        console.log('健康样本订单列表接口返回数据:', JSON.stringify(res));
        
        if (res && res.code === 0) {
          // 检查数据是否为空（考虑多种可能的数据结构）
          let isEmpty = true;
          
          if (res.data) {
            console.log('数据类型:', typeof res.data);
            console.log('是否为数组:', Array.isArray(res.data));
            
            if (Array.isArray(res.data)) {
              console.log('数组长度:', res.data.length);
              isEmpty = res.data.length === 0;
            } else if (typeof res.data === 'object') {
              console.log('对象键数量:', Object.keys(res.data).length);
              isEmpty = Object.keys(res.data).length === 0;
            }
          } else {
            console.log('数据为null或undefined');
          }
          
          console.log('判断数据是否为空:', isEmpty);
          
          // 确定问卷类型和问卷ID
          // 这里可以根据实际需求确定问卷类型，例如根据患者年龄或其他条件
          // 示例：根据患者ID的最后一位数字判断类型（仅作演示）
          const questionType = patientId && patientId.length > 0 ? 
                              (parseInt(patientId.charAt(patientId.length - 1)) % 2 === 0 ? '1' : '2') : 
                              '1'; // 默认使用成人版问卷
          
          const surveyId = questionType === '1' ? ADULT_SURVEY_ID : CHILD_SURVEY_ID;
          
          if (isEmpty) {
            console.log('数据为空，准备跳转到选择产品页面');
            // 如果数据为空，直接跳转到选择产品页面
            const url = `/pages/survey/surveytools/index?userId=${patientId}&patHisNo=${patHisNo}&patCardNo=${patCardNo}&questionType=${questionType}&surveyId=${surveyId}`;
            console.log('跳转URL:', url);
            
            // 使用setTimeout确保在微信小程序环境下正确执行跳转
            setTimeout(() => {
              wepy.navigateTo({
                url: url,
                success: () => {
                  console.log('跳转成功');
                },
                fail: (err) => {
                  console.error('跳转失败:', err);
                }
              });
            }, 100);
          } else {
            console.log('数据不为空，准备跳转到列表页');
            // 有数据，跳转到列表页
            const url = `/pages/survey/surveytools/surveylist/index?userId=${patientId}&patHisNo=${patHisNo}&patCardNo=${patCardNo}&questionType=${questionType}&surveyId=${surveyId}`;
            console.log('跳转URL:', url);
            
            // 使用setTimeout确保在微信小程序环境下正确执行跳转
            setTimeout(() => {
              wepy.navigateTo({
                url: url,
                success: () => {
                  console.log('跳转成功');
                },
                fail: (err) => {
                  console.error('跳转失败:', err);
                }
              });
            }, 100);
          }
        } else {
          console.log('接口请求失败:', res);
          wepy.showToast({
            title: res.msg || '获取列表失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('获取健康样本订单列表失败', err);
        wepy.hideLoading();
        wepy.showToast({
          title: '获取列表失败，请稍后重试',
          icon: 'none'
        });
      });
    },
  };

  events = {
    /**
     * 搜索框状态变化
     * @param nextStatus
     */
    "search-toggle-focus": function (nextStatus) {
      this.searchFocus = nextStatus;
    },
    "outpatient-change-user": function () {
      const {
        globalData: {
          outpatient: { cardList = [] },
        },
      } = this.$parent;
      if (cardList.length == 0) {
        this.activePatient = {};
      }
      cardList.forEach((item) => {
        if (item.relationType == 1) {
          this.activePatient = item;
          this.$apply();
        }
      });
      this.$apply();
    },
    "refresh-home": function (parent) {
      // this.getZQSUnreadNotice(parent)
    },
  };

  // 在线咨询未读提醒
  async getUnreadMsgCount() {
    const { data = [], code } = await Api.queryPatChat();
    let msgCount = 0;
    if (Array.isArray(data)) {
      data.forEach((item) => {
        msgCount += item.unreadNum;
      });
    }
    this.msgCount = msgCount > 99 ? "99+" : msgCount;
    this.$apply();
    if (this.isQueryChat) {
      await sleep(IS_PRODUCTION ? 1000 * 10 : 1000 * 60 * 5);
      await this.getUnreadMsgCount();
    }
  }

  /**
   * 删除通知
   */
  async readNotice(item = {}) {
    const { id = "" } = item;
    const param = { id, dataFlag: "20" };
    const { code, data = {} } = await Api.updateReadFlag(param);
    if (code != 0) {
      return;
    }
    this.notice = {};
    this.$apply();
  }

  /**
   * 获取未读重要通知、待办消息
   */
  async getUnreadNotice() {
    const { code, data = {}, msg } = await Api.getUnreadNotice();
    const { notice = {}, todoMsgs = [], unreadCount = 0 } = data;
    this.notice = notice;
    this.todoMsgs = todoMsgs;
    this.unreadCount = unreadCount;
    this.$apply();
    if (this.isQueryChat) {
      await sleep(IS_PRODUCTION ? 1000 * 5 : 1000 * 60 * 5);
      await this.getUnreadNotice();
    }
  }
  /**
   * 获取专家列表
   */
  async getdocbyexpert() {
    const { data = {} } = await Api.getdocbyexpert();
    this.doctorList = data.doctorlist;
  }

  /**
   * 获取功能列表
   */
  async getFunctionList() {
    // TODO 竟然已经是后端接口返回了，为什么前端还要单独做一份数据？
    const { code, data = {}, msg } = await Api.getFunctionList();
    const { quickFunc = [], queryFunc = [], serviceFunc = [] } = data;

    const filterQuickFunc = quickFunc.filter((item) => item.status !== 2);

    this.quickFunc = filterQuickFunc.map((item, index) => {
      return {
        ...(functionMap[`test-${item.icon}`] || {}),
        ...item,
        url: item.entryUrl,
      };
    });
    this.queryFunc = queryFunc.map((item, index) => {
      return {
        ...(functionMap[`test-${item.icon}`] || {}),
        ...item,
        url: item.entryUrl,
      };
    });
    serviceFunc.push();
    this.serviceFunc = serviceFunc.map((item, index) => {
      return {
        ...(functionMap[`test-${item.icon}`] || {}),
        ...item,
        url: item.entryUrl,
      };
    });

    // 处理更多
    if (this.queryFunc.length > 8) {
      this.queryFunc.splice(7, 0, {
        name: "更多功能",
        icon: "gdgn",
        type: "more",
        isExpandProp: "isSelectExpand",
      });
    }
    // if (this.serviceFunc.length > 12) {
    // 	this.serviceFunc.splice(11, 0, {
    // 		name: '更多功能',
    // 		icon: 'gdgn',
    // 		type: 'more',
    // 		isExpandProp: 'isServiceExpand',
    // 	})
    // }
    this.$apply();
  }
}
</script>
