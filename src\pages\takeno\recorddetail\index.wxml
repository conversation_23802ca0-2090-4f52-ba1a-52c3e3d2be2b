<view class="p-page">
  <detail-status :config.sync="statusConfig">
    <block slot="title">{{statusConfig.statusName}}</block>
    <block slot="text">
      <view>{{statusConfig.text}}</view>
    </block>
  </detail-status>
  <view class="m-code {{detailData.status === 'S' ? 'active' : ''}}">
    <view class="code-tit">就诊凭条</view>
    <view class="code-img">
      <block wx:if="{{detailData.patCardNo}}">
        <image
          mode="widthFix"
          src="https://wechat.jiahuiyiyuan.com/barcode?msg={{detailData.patCardNo}}&type=code128&mw=.60"
          alt=""
        ></image>
      </block>
    </view>
  </view>
  <!-- <block wx:if="{{isAbnormal}}">
    <view class="m-retry">
      <view class="retry-btn" @tap="bindRetryOrder">点击重试刷新</view>
    </view>
  </block> -->
  <refund-list :refundList.sync="refundList"></refund-list>
  <basic-detail :detailData.sync="detailData"></basic-detail>
  <pay-detail :detailData.sync="detailData" :isExpand.sync="payIsExpand"></pay-detail>
  <block wx:if="{{detailData.canCancelFlag == 1}}">
    <view class="m-cancel">
      <view class="cancel-btn" @tap="bindCancelOrder">取消取号</view>
    </view>
  </block>
</view>
