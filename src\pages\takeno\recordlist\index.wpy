<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import WxsUtils from '../../../wxs/utils.wxs';
  import * as Api from './api';

  export default class RecordList extends wepy.page {
    config = {
      navigationBarTitleText: '取号记录',
    };
    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 挂号记录列表
      orderList: [],
    };

    components = {
      'empty': Empty,
    };

    props = {};

    onLoad(options) {
      this.getOrderList();
    }

    wxs = {
      WxsUtils: WxsUtils
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      /**
       * 跳转到挂号详情页
       * @param item
       */
      bindGoDetail(item){
        const { orderId = '' } = item;
        wepy.navigateTo({
          url: `/pages/takeno/recorddetail/index?orderId=${orderId}`,
        });
      },
    };

    async getOrderList(word) {
      const { code, data = {} } = await Api.orderList();
      if (code !== 0) {
        this.orderList = [];
        this.$apply();
        return;
      }
      this.orderList = (data.takeList || []).map((item) => {
        item.regTimeFormat = Utils.getVisitTime(item);
        return item;
      });
      this.$apply();
    }
  }
</script>
