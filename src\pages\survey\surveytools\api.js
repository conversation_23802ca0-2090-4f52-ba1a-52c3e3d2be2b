import { request } from '../../../utils/request';
import { post } from '../../../utils/request';
import { uploadFile as uploadFileUtil } from '../../../utils/request';

/**
 * 获取问卷类型列表
 * @param {Object} params - 请求参数
 * @param {String} params.patientId - 患者ID
 * @param {String} params.patHisNo - 患者历史编号
 * @param {String} params.patCardNo - 患者卡号
 * @returns {Promise}
 */
export function getQuestionTypes(params = {}) {
  return request({
    url: '/api/survey/types',
    method: 'GET',
    data: params
  });
}

/**
 * 获取健康样本订单详情
 * @param {Object} params - 请求参数
 * @param {String} params.id - 订单ID
 * @param {Boolean} [showLoading=true] - 是否显示加载提示，默认为true
 * @returns {Promise}
 */
export function getHealthOrderById(params = {}, showLoading = true) {
  return post('/api/sample/getHealthOrderById', params, showLoading);
}

/**
 * 提交问卷基础信息
 * @param {Object} data - 基础信息数据
 * @returns {Promise}
 */
export function submitBasicInfo(data = {}) {
  return request({
    url: '/api/survey/basicInfo',
    method: 'POST',
    data
  });
}

/**
 * 提交问卷答案
 * @param {Object} data - 问卷答案数据
 * @returns {Promise}
 */
export function submitSurveyAnswers(data = {}) {
  return request({
    url: '/api/survey/answers',
    method: 'POST',
    data
  });
}

/**
 * 获取健康样本订单列表
 * @param {Object} params - 请求参数
 * @param {String} params.hisId - 医院ID
 * @returns {Promise}
 */
export function getHealthOrderList(params = {}) {
  return post('/api/sample/getHealthOrderList', params);
}

/**
 * 获取样本问卷列表
 * @param {Object} params - 请求参数
 * @param {String} params.userId - 用户ID，可选
 * @param {String} params.patientId - 患者ID，可选
 * @returns {Promise}
 */
export function getSampleSurveyList(params = {}) {
  return post('/api/sample/getSampleSurveyList', params);
}

/**
 * 获取问卷记录列表
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @returns {Promise}
 */
export function getAnswerList(params = {}) {
  return post('/api/sample/getAnswerList', params);
}

/**
 * 保存样本基础信息
 * @param {Object} data - 样本基础信息数据
 * @param {String} data.userId - 用户ID
 * @param {String} data.hisId - 医院ID，默认传242
 * @param {String} data.sjzName - 姓名
 * @param {String} data.sjzIdType - 证件类型
 * @param {String} data.sjzIdNum - 证件号码
 * @param {String} data.sjzAge - 年龄
 * @param {String} data.sjzSex - 性别
 * @param {String} data.sjzPhone - 电话
 * @param {String} data.sjzHeight - 身高
 * @param {String} data.sjzWeight - 体重
 * @param {String} data.sjzNation - 民族
 * @param {String} data.occupation - 职业
 * @param {String} data.education - 文化程度
 * @param {String} data.questionType - 问卷类型（1:成人版, 2:儿童版）
 * @param {String} data.jhrName - 监护人姓名
 * @param {String} data.jhrIdType - 监护人证件类型
 * @param {String} data.jhrIdNum - 监护人证件号码
 * @returns {Promise}
 */
export function saveSampleInfo(data = {}) {
  return post('/api/sample/saveSampleInfo', data);
}

/**
 * 获取问卷详情
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getSurveyDetail(params = {}) {
  return post('/api/questionphone/getquestionscopeforid', params);
}

/**
 * 获取问卷答案
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getSurveyAnswer(params = {}) {
  return post('/api/questionphone/getquestiondetailbyid', params);
}

/**
 * 保存问卷答案
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function saveQuestion(params) {
  return post('/api/questionphone/savequestion', params);
}

/**
 * 签署知情同意书
 * @param {Object} params - 请求参数
 * @param {String} params.id - saveSampleInfo接口返回的样本ID，而不是用户ID
 * @returns {Promise}
 */
export function signSampleFile(params = {}) {
  return post('/api/sample/signSampleFile', params);
}

/**
 * 根据Key获取配置信息
 * @param {Object} params - 请求参数
 * @param {String} params.hisId - 医院ID
 * @param {String} params.platformId - 平台ID
 * @param {String} params.key - 问卷ID（成人版或儿童版）
 * @returns {Promise}
 */
export function getProfileByKey(params = {}) {
  return post('/api/address/getProfileByKey', params);
}

/**
 * 上传文件
 * @param {String} filePath - 文件路径
 * @param {String} fileType - 文件类型，默认为'image'
 * @returns {Promise}
 */
export function uploadFile(filePath, fileType = 'image') {
  console.log('api.js中的uploadFile被调用:', filePath, fileType);
  
  // 确保文件路径有效
  if (!filePath) {
    console.error('文件路径无效');
    return Promise.reject(new Error('文件路径无效'));
  }
  
  // 使用utils/request.js中的uploadFile方法
  return uploadFileUtil('/api/files/uploadpic', filePath);
} 