<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from "wepy"
import * as Api from "./api"
import * as Utils from "@/utils/utils"
import empty from "@/components/empty/index"
import {REQUEST_QUERY} from "@/config/constant"
export default class GuideIndex extends wepy.page {
	config = {
		navigationBarTitleText: "导诊服务",
		navigationBarBackgroundColor: '#fff'
	}
	components = {
		empty,
	}
	data = {
		serviceList: [
			{
				typeName: "就诊流程",
				bgImage: "jzlc.png",
				itemList: [],
			},
			{
				typeName: "周边服务",
				bgImage: "zbfw.png",
				itemList: [],
			},
		],
	}
	onLoad() {
		this.getGuideInfo()
	}
	async getGuideInfo() {
		const {code, data = [], msg} = await Api.getGuideInfo()
		if (code !== 0) {
			return
		}
		const {treatmentProcess, peripheralService} = data
		this.serviceList[0].itemList = treatmentProcess
		try {
			const fisrtArr = peripheralService.slice(0, 3)
			const endArr = peripheralService.slice(3)
			this.serviceList[1].itemList = [...endArr, ...fisrtArr]
		} catch (error) {
			this.serviceList[1].itemList = []
		}
		this.$apply()
	}
	events = {}
	// 交互事件，必须放methods中，如tap触发的方法
	methods = {
		navigateTo(e) {
			const {url} = e.currentTarget.dataset
			if (url) {
				wepy.navigateTo({
					url: `/pages/webview/index?pid=&weburl=${url}`,
				})
				return false
			} else {
				wepy.showModal({
					title: "提示",
					content: "功能开发中，敬请期待...",
					showCancel: false,
					confirmText: "确定",
				})
			}
		},
	}
}
</script>



