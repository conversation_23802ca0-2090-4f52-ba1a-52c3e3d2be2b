@import "../../../../resources/style/mixins";

.p-page{
  padding-bottom: 24rpx;
  &.hiddenScroll{
    overflow: hidden;
  }
  .record{
    color: #3F969D;
    text-align: center;
  }
  .tips{
    padding: 32rpx 24rpx;
    font-size: 24rpx;
    background: #FFFAF1;
    color: #BE8014;
  }
  .header{
    padding: 24rpx;
    .header_title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.90);
      font-size: 28rpx;
      font-weight: 600;
      line-height: 1.5;
      .header_icon{
        width: 40rpx;
        height: 40rpx;
      }
    }
    .header_camera{
      margin-top: 24rpx;
      width: 100%;
      height: 220rpx;
      border-radius: 11rpx;
      border: 1rpx solid #308B91;
    }
  }
  .p-patient-info{
    padding: 0 24rpx;
    background: #fff;
    .info-item{
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.90);
      padding: 24rpx 0;
      box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
      .label{
        width: 228rpx;
        &.required::before{
          content: "*";
          display: inline-block;
          color: #FF613B;
          font-size: 32rpx;
        }
      }
    }
  }
  .p-content{
    margin: 24rpx 16rpx;
    padding: 0 32rpx 32rpx;
    border-radius: 8rpx;
    background: #FFF;
    .pic-content{
      border-top: 1rpx solid rgba(0, 0, 0, 0.06);
      .content-list{
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.4);
      }
    }
    .img-item{
      margin-right: 20rpx;
      flex: 0 0 196rpx;
      width: 196rpx;
      height: 196rpx;
    }
    &.pay-type-content{
      padding: 32rpx;
      .picker{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title{
          font-size: 32rpx;
        }
      }
      
      .pay-type{
        position: relative;
        display: flex;
        align-items: center;
        padding-right: 30rpx;
        &.empty{
          padding-right: 20rpx;
          color: rgba(0, 0, 0, 0.4);
        }
      }
      .pay-type-icon{
        margin-right: 16rpx;
        width: 40rpx;
        height: 40rpx;
      }
      .arrow-icon{
        position: absolute;
        margin-top: 6rpx;
        right: 0;
        top: 50%;
        width: 15rpx;
        height: 15rpx;
        border-right: 4rpx solid #c7c7cc;
        border-bottom: 4rpx solid #c7c7cc;
        transform: translate(0, -75%) rotate(-45deg);
      }
    }
    .content-title{
      margin-top: 24rpx;
      padding: 24rpx 0;
      color:rgba(0, 0, 0, 0.90);
      font-size: 32rpx;
      font-weight: 500;
      &::before{
        content: '*';
        color: red;
        font-size: 28rpx;
      }
      &:first-child{
        margin-top: 0;
      }
    }
    .content-group{
      .box-list{
        display: flex;
        padding: 24rpx;
        margin-bottom: 24rpx;
        border-radius: 8px;
        background:#F6F7F9;
        align-items: center;
        justify-content: space-between;
        &:last-child{
          margin: 0;
        }
        .group-left{
          display: flex;
          align-items: center;
          .product-content{
            margin: 0 16rpx;
            word-break: keep-all;
            font-size: 28rpx;
            color: #286AE8;
            text-decoration: underline;
          }
        }
        image{
          flex-shrink: 0;
          margin-right: 16rpx;
          width: 40rpx;
          height: 40rpx;
        }
        .left-title{
          color: rgba(0, 0, 0, 0.90);
          font-size: 32rpx;
          line-height: 40rpx;
          word-break: break-all;
        }
        .small-title{
          font-size: 28rpx;
        }
        .group-right{
          flex-shrink: 0;
          color: #CC8F24;
          font-size: 32rpx;
          font-style: normal;
          font-weight: 600;
        }
      }
    }
    .list-item{
      padding: 24rpx;
      font-size: 32rpx;
      border-radius: 8rpx;
      background:#F6F7F9;
    }
    .is-urgent{
      display: flex;
      align-items: center;
      .list-item{
        flex: 1;
        display: flex;
        align-items: center;
        &:first-child{
          margin-right: 10rpx;
        }
        image{
          margin-right: 16rpx;
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
    .msg{
      margin-top: 24rpx;
      color:rgba(0, 0, 0, 0.40);
      font-size: 26rpx;
    }
  }
  .p-btn-box{
    margin: 64rpx 40rpx;
    view{
      height: 100rpx;
      line-height: 100rpx;
      border-radius: 76rpx;
      font-size: 34rpx;
      font-weight: 600;
      color: #fff;
      text-align: center;
    }
    .confirm-btn{
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    }
    .cancel-btn{
      margin-top: 24rpx;
      color: rgba(0, 0, 0, 0.70);
      background:rgba(0, 0, 0, 0.04);
    }
  }
  .pic-content{
    
  }
  
  .pop-view{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -999;
    visibility: hidden;
    transform: translateY(-1000%);
    .pop-content{
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      max-height: 90%;
      overflow-y: auto;
      background-color: #fff;
      z-index: 1;
      -webkit-overflow-scrolling: touch;
      transform: translateY(100%);
      transition: transform 0.3s 0.1s;
      border-radius: 8rpx 8rpx 0 0;
    }
    &.active {
      z-index: 999;
      visibility: visible;
      transform: translateY(0);
    
      .wgt-user-pop-mask {
        background-color: rgba(0, 0, 0, 0.6);
      }
    
      .pop-content {
        transform: translateY(0%);
      }
    }
    .pop-top{
      padding: 32rpx 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .pop-title{
        font-size: 34rpx;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
      }
      .delete-icon{
        width: 32rpx;
        height: 32rpx;
      }
    }
    .wgt-user-pop-mask {
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0);
      transition: background 0.3s;
    }
    .pop-btn{
      margin-top: 40rpx;
      padding: 48rpx 24rpx;
      .btn{
        width: 100%;
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        font-weight: bold;
        font-size: 34rpx;
        color: rgba(255, 255, 255, 1);
        background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
        border-radius: 81rpx;
      }
    }
  }
  
}