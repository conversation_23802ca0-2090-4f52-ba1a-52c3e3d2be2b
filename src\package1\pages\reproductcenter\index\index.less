@import "../../../../resources/style/mixins";

.inhosp {
  width: 100%;
  height: 100%;
  .info-tips {
    width: 100%;
    box-sizing: border-box;
    padding: 32rpx;
    margin-top: 24rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: @hc-color-title;
    .tips-count {
      color: @hc-color-assist;
    }
  }
  .inhosp-container {
    margin: 0 32rpx;
    background: @hc-color-white;
    border-radius: 24rpx;
  }
  .info-list {
    width: 100%;
    box-sizing: border-box;
    padding-left: 32rpx;
    background-color: @hc-color-white;
    padding-bottom: 24rpx;
    .info-item {
      padding: 24rpx 32rpx 24rpx 0;
      color: @hc-color-title;
      font-size: 32rpx;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      & + .info-item {
        border-top: 2rpx solid @hc-color-border;
      }
      .item-left {
        .attr-val {
          color: @hc-color-primary;
        }
        .item-left-title {
          font-size: 32rpx;
          color: @hc-color-title;
        }
      }
      .rt-arrow {
        width: 17rpx;
        height: 17rpx;
        border-right: 4rpx solid #c7c7cc;
        border-bottom: 4rpx solid #c7c7cc;
        transform: translateX(-8rpx) rotate(-45deg);
      }
    }
  }
}
