<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import * as Api from './api';
  
  export default class Examine extends wepy.page {
    config = {
      navigationBarTitleText: '病历详情',
    };

    components = {
    };

    onLoad(options) {
      this.getInfo({visitRecordId: options.visitRecordId, patientId: options.patientId});
    }

    data = {
      baseInfo: {},
      visitDetail: {},
      doctorAdvice: {},
    };

    methods = {
    };

    events = {
    };
    /**
     * 获取详情
     */
    async getInfo(param) {
      const { code, data = {}, msg } = await Api.getInfo(param);
      this.info = data.repList || {};
      this.baseInfo = this.info.baseInfo || {};
      this.visitDetail = this.info.visitDetail || {};
      this.doctorAdvice = this.info.doctorAdvice || {};
      console.warn(this.baseInfo);
      this.$apply();
    }
  }
</script>
