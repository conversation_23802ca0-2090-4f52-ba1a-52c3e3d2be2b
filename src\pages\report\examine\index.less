@import "../../../resources/style/mixins";

page{
  min-height: 100%;
  margin: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.m-card{
  .report-title{
    text-align: center;
    font-size: 48rpx;
    font-weight: 600;
    padding: 40rpx 0;
    background-color: #fff;
  }

  .report-info{
    position: relative;
    padding: 32rpx;
    margin: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;

    .info-block {
      display: flex;
      padding-top: 24rpx;
      &:first-child{
        padding: 0;
      }

      .info-item{
        flex: 1;
        display: flex;
        max-width: 50%;

        &.info-item_block{
          max-width: none;
        }

        &:first-child {
          .item-text{
            padding-right: 20rpx;
            color: rgba(0, 0, 0, 0.90);
          }
        }
        
      }

      .item-title{
        margin-right: 16rpx;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.40);
      }

      .item-text{
        font-size: 28rpx;
        flex: 1;
      }

    }
  }

  .report-item{
    padding: 32rpx 0;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
    &:first-child{
      padding-top: 0;
    }
    &:last-child{
      padding-bottom: 0;
      border: none;
    }
    .item-title{
      margin-bottom: 8rpx;
      color: @hc-color-title;
      font-size: 28rpx;
      font-weight: 600;
    }

    .item-text{
      font-size: 32rpx;
      color: @hc-color-text;
      margin-top: 14rpx;
    }
  }

}
.ad-report{
  margin-top: 20rpx;
  image{
    width: 100%;
  }
}
