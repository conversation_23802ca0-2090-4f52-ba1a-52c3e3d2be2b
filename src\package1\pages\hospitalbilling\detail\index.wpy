<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import DetailItem from "@/components/detailitem/index";
import DetailStatus from "@/components/detailstatus/index";
import NavTop from "@/components/navtop/index";
import * as Utils from "@/utils/utils";
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "出院结算申请详情",
    navigationStyle: "custom",
    navigationBarBackgroundColor: '#fff'
  };
  components = {
    navtop: NavTop,
    "detail-item-examine": DetailItem,
    "detail-item-inhospital": DetailItem,
    "detail-item-apply": DetailItem,
    "detail-item-bankinfo": DetailItem,
    "detail-item-address": DetailItem,
    "detail-item-mailinfo": DetailItem,
    "detail-status": DetailStatus
  };
  events = {
    "set-navigationbar-color": param => {
      console.log("first", param);
      const isVersion = this.$parent.globalData.isVersion;
      this.isVersion = isVersion;
      if (!isVersion) {
        wepy.setNavigationBarColor(param);
      }
      this.bgColor = param.backgroundColor;
      this.color = param.frontColor;
      this.$apply();
    }
  };
  data = {
    examineInfo: [],
    hospitalInfo: [],
    applyInfo: [],
    bankCardInfo: [],
    hospitalDepositSlip: [],
    addressInfo: [],
    address: {},
    mailInfo: [],
    detailData: {},
    bgColor: "#3ECDB5",
    color: "white",
    isBack: true,
    navHeight: "52rpx",
    topHeight: null,
    isVersion: true,
    statusConfig: {},
    statusMap: {
      0: "WAIT_EXAMINE",
      1: "REJECTED",
      2: "WAIT_ACCOUNT",
      3: "ACCOUNTED",
      4: "CANCEL"
    },
    expressType: {
      1: "（顺丰特快）",
      2: "（顺丰标快）"
    },
    expressInfoTitle: "结算发票邮寄地址",
    patCardNo: "",
    patHisNo: "",
    jzid: "",
    grid: "",
    pid: ""
  };
  onLoad(options) {
    this.patCardNo = options.patCardNo;
    this.patHisNo = options.patHisNo;
    this.grid = options.grid;
    this.pid = options.pid;
    this.jzid = options.jzid;
    this.getHospitalBillInfo(options.id);
    const res = wepy.getSystemInfoSync();
    this.navHeight = 44 + res.statusBarHeight + "px";
    this.topHeight = 44 + res.statusBarHeight;
    this.searchHeight = 5 + res.statusBarHeight + "px";
  }
  onShow() {}

  computed = {
    canReApply() {
      const { statusMap = {}, detailData = {} } = this;
      return ["REJECTED", "CANCEL"].includes(statusMap[detailData.status]);
    },
    showExaminInfo() {
      const { statusMap = {}, detailData = {} } = this;
      return ["REJECTED", "WAIT_ACCOUNT", "ACCOUNTED"].includes(
        statusMap[detailData.status]
      );
    },
    canCancelApply() {
      const { statusMap = {}, detailData = {} } = this;
      return statusMap[detailData.status] === "WAIT_EXAMINE";
    }
  };

  methods = {
    previewImg(imgUrl) {
      wepy.previewImage({
        current: imgUrl, // 当前显示图片的http链接
        urls: this.hospitalDepositSlip // 需要预览的图片http链接列表
      });
    },
    checkProgress() {
      wepy.navigateToMiniProgram({
        appId: "wx6885acbedba59c14",
        path: `pages/result/result?nu=${
          this.detailData.expressNumber
        }&querysource=shouye_search`,
        envVersion: "release" // develop trial release
      });
    },
    reApply() {
      const newpatCardNo = this.patCardNo ? this.patCardNo : this.grid;
      const newpatCard = this.patHisNo ? this.patHisNo : this.pid;
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/apply/index?id=${
          this.detailData.id
        }&patCardNo=${newpatCardNo}&patHisNo=${newpatCard}&jzid=${
          this.detailData.jzid
        }`
      });
    }
  };
  async cancelRecord() {
    const { data, code } = await Api.cancelRecord({ id: this.detailData.id });
    if (code === 0) {
      wx.showToast({
        title: "取消成功",
        icon: "success"
      });
      this.getHospitalBillInfo(this.detailData.id);
    }
  }
  async getHospitalBillInfo(id) {
    const { data, code } = await Api.getHospitalBillInfo({ id });
    if (code === 0) {
      this.detailData = data;
      this.statusConfig = await this.getStatus();
      this.examineInfo = [
        { label: "审核时间", value: data.operateTime },
        { label: "结算时间", value: data.refundFinishTime },
				{ label: "驳回原因", value: data.reason }
      ];
      this.hospitalInfo = [
        { label: "住院人", value: data.patientName },
        { label: "身份证号码", value: data.idNo },
        { label: "住院科室", value: data.deptName },
        { label: "住院号", value: data.hospitalNo },
        { label: "PID", value: data.patHisNo }
      ];
      this.applyInfo = [
        {
          label: "退费金额（元）",
          value: Utils.formatMoney(data.refundTotalFee)
        },
        { label: "申请时间", value: data.createTime }
      ];
      this.bankCardInfo = [
        { label: "户名", value: data.bankAccountName },
        { label: "账号", value: data.bankCardNumber },
        { label: "开户行", value: data.bankName }
      ];
      this.hospitalDepositSlip = data.hospitalDepositSlip.split(",");
      const addressInfo = data.addresseeInfo
        ? JSON.parse(data.addresseeInfo)
        : {};
      this.expressInfoTitle = `结算发票邮寄地址${this.expressType[
        addressInfo.expressType
      ] || ""}`;
      this.addressInfo = addressInfo.userName
        ? [
            { label: "收件人姓名", value: addressInfo.userName },
            { label: "手机号码", value: addressInfo.mobile },
            {
              label: "所在地区",
              value: `${addressInfo.provinceName} ${addressInfo.cityName} ${
                addressInfo.areaName
              }`
            },
            { label: "详细地址", value: addressInfo.addressDetail }
          ]
        : [];
      this.mailInfo = [{ label: "快递单号", value: data.expressNumber }];
      this.$apply();
    }
  }
  /**
   * 获取订单描述文案
   */
  async getStatus() {
    const { statusMap } = this;
    const { status } = this.detailData;
    let STATUS_CONFIG = {};
    if (statusMap[status] === "WAIT_EXAMINE") {
      STATUS_CONFIG = {
        status: "W",
        statusName: "待审核",
        text:
          "您已成功提交出院结算申请，申请审核通过后会及时为您办理出院结算，1-7个工作日内处理完毕，请留意银行到账信息。"
      };
    }
    if (statusMap[status] === "REJECTED") {
      STATUS_CONFIG = {
        status: "F",
        statusName: "已驳回",
        text: "您的出院结算申请已驳回，请核对后重新提交申请。"
      };
    }
    if (statusMap[status] === "WAIT_ACCOUNT") {
      STATUS_CONFIG = {
        status: "W",
        statusName: "审核通过待结算",
        text:
          "您已成功提交出院结算申请，出院结算会在1-7个工作日内处理完毕，请留意银行到账信息。"
      };
    }
    if (statusMap[status] === "ACCOUNTED") {
      STATUS_CONFIG = {
        status: "S",
        statusName: "已结算",
        text:
          "您的出院结算已办理完成，将于1-7个工作日内退款到账，请留意到账信息。"
      };
    }
    if (statusMap[status] === "CANCEL") {
      STATUS_CONFIG = {
        status: "C",
        statusName: "已取消",
        text: "您的出院结算申请已取消，可重新申请。"
      };
    }
    return STATUS_CONFIG;
  }
}
</script>
