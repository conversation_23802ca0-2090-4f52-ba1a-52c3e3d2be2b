import { post } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

// export const getList = (param) => post('/api/report/getreportlist', param);

export const getList = (param) => post(`/api/report/getreportlist`, param);

export const getBeiriList = (param) => post(`/api/customize/getbeiriList?_route=h${REQUEST_QUERY.platformId}`, param);

export const getbeiripdf = (param) => post(`/api/customize/getbeiripdf?_route=h${REQUEST_QUERY.platformId}`, param);
