<view>
	<view class="u-img">
		<image src="../../resources/images/doctor.jpg" class="u-img"></image>
	</view>
	<view class="m-activeInfo">
		<view class="g-title">
			<p>中信湘雅"最美医生"评选活动</p>
			<!-- <p>活动介绍></p> -->
		</view>

		<view>
			<view class="g-textIcon">
				<image class="u-icon" src="../../resources/images/time.png" width="20px" height="20px" />
				<p>开始时间：2020-07-29 08:30 </p>
			</view>
			<view class="g-textIcon">
				<image class="u-icon" src="../../resources/images/time.png" width="20px" height="20px" />
				<p>结束时间：2020-08-05 17:30</p>
			</view>
			<view class="g-textIcon">
				<image class="u-icon" src="../../resources/images/warn.png" width="20px" height="20px" />
				<p>投票规则：生殖中心最多选7人，最少不限制；其他科室（影像科、检验科、男科、遗传）最多选三个，最少不限制。排名不分先后，按照临床、医技、姓氏排序。</p>
			</view>
		</view>
	</view>


	<repeat for="{{deptList}}" key="key" item="dept">
		<view class="u-listTitle">{{dept.voteDeptName+'（最多选'+dept.maxVote+'人）'}}</view>
		<repeat for="{{dept.voteDoctorList}}" key="index" item="doctorInfo" index="index">
			<!-- <doctorItem @selectData="selectData" :doctorInfo.sync="item" :dept="dept.voteDeptName">
</doctorItem> -->
			<view class="g-doctorInfo">
				<view style="float:left;clear:both;">
					<image src="{{doctorInfo.doctorImg}}" class="u-doctorPic"></image>
					<checkbox-group data-id="{{doctorInfo.voteDoctorId}}" bindchange="checkValue">
						<label class="g-check">
							<checkbox class="u-checkbox" value="{{doctorInfo.voteDoctorId+'/'+dept.voteDeptName}}" checked="{{doctorInfo.check}}" color="#3eceb5" />
							<view style="color:#37383B">{{doctorInfo.voteDoctorName}}</view>
						</label>
					</checkbox-group>
					<!-- <view class="g-vote">{{doctorInfo.voteCount+'票'}}</view> -->
				</view>
				<p wx:if="{{doctorInfo.introduce.length>145 && judge}}" class="g-ellipsisIntroduce">
					<view class="u-introTitle">介绍：</view>{{doctorInfo.introduce}}
				</p>
				<p wx:else class="g-introduce">
					<view class="u-introTitle">介绍：</view>{{doctorInfo.introduce}}
				</p>
				<view wx:if="{{doctorInfo.introduce.length>145 && judge}}" class="u-unfold" bindtap="unfoldEvent()">展开></view>
			</view>
		</repeat>
	</repeat>

	<view wx:if="{{judge}}" class="u-button" @tap="voteEvent">投票</view>
	<view wx:else class="u-unbutton">投票</view>

</view>