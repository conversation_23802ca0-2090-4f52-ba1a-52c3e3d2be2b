<view class="p-page">
  <view class="m-tips">我院采用到付的方式进行药品邮寄，请选择您需要邮寄的药品，提交成功后我院将尽快安排寄送。 </view>
  <!-- <view class="wgt-user-box">
    <view class="wgt-user-main">
      <view class="wgt-user-main-info">
        <view class="wgt-user-main-info-tit">{{patient.activePatient.patientName}}</view>
        <view class="wgt-user-main-info-label">病历号：{{patient.activePatient.patHisNo}}</view>
      </view>
    </view>
  </view> -->
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :change.sync="changeShow"></outpatient>

  <empty :config.sync="emptyConfig">
    <block slot="text">暂无可邮寄的项目</block>
  </empty>
  <block wx:if="{{extra.isShow}}">
    <view class="desc-modal-mask">
      <view class="desc-modal">
        <view class="desc-title" style="font-weight: 600">温馨提示</view>
        <scroll-view class="desc-content" scroll-y>
          <text decode="{{true}}" space="{{true}}">{{extra.content}}</text>
        </scroll-view>
        <view class="desc-footer">
          <view class="agree" bindtap="changeExtra" >同意</view>
        </view>
      </view>
    </view>
  </block>
  <view wx:if="{{list && list.length > 0}}" class="main">
    <view class="head">目前可寄送的药品如下，请选择需邮寄的项目：</view>
    <view class="list" wx:for="{{list}}" wx:key="index">
      <view class="title-box">
        <block wx:if="{{item.proIsDefault}}" bind:tap="onchangeChecked">
          <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-on.png"></image>
        </block>
        <block wx:if="{{!item.proIsDefault}}" bind:tap="onchangeChecked">
          <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-off.png"></image>
        </block>
        <view class="list-title">领药单{{index + 1}}</view>
      </view>
      <view class="table">
        <view class="tr">
          <view class="th">药品名称</view>
          <view class="th">数量·单位</view>
          <view class="th">金额（元）</view>
        </view>
        <view class="tr" wx:for="{{item.medicalSendProItem}}" wx:for-item="itm">
          <view class="td">{{itm.itemName}}</view>
          <view class="td">{{itm.itemSum}}{{itm.itemUnit}}</view>
          <view class="td">{{itm.itemFee}}</view>
        </view>
      </view>
    </view>
  </view>
  <view class="address-box">
    <view class="adress-list">
      <radio-group bindchange="radioChange">
        <view class="item-card" wx:if="{{addressList && addressList.length > 0}}" wx:for="{{addressList}}">
          <view class="card-content">
            <view class="content-head">
              <view class="head-name">{{item.name}}</view>
              <view class="head-phone">{{item.phone}}</view>
            </view>
            <view class="content-addr">{{item.addr}}</view>
          </view>
          <view class="card-bottom">
            <view class="check">
              <radio value="{{item.value}}" checked="{{item.isChecked}}"></radio>
            </view>
            <view class="btn">编辑</view>
            <view class="btn">删除</view>
          </view>
        </view>
      </radio-group>
    </view>
    <navigator url="/package1/pages/sendmedicine/edit/index" class="m-adduser">
      <view class="add-title">添加配送地址</view>
    </navigator>
  </view>
  <view class="btn-box">
    <view class="btn cancel">取消</view>
    <view class="btn sure">确定</view>
  </view>
</view>
