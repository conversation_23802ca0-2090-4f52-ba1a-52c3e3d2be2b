<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy'
import * as Api from './api'

export default class PidInfo extends wepy.page {
	config = {
		navigationBarTitleText: '条形码',
	}

	onLoad(options) {
		this.pid = options.pid || ''
		this.$apply()
	}

	// data中的数据，可以直接通过  this.xxx 获取，如：this.text
	data = {
		pid: '',
	}

	// 交互事件，必须放methods中，如tap触发的方法
	methods = {}
}
</script>
