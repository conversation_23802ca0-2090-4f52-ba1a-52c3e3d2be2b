<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import Empty from '@/components/empty/index'
  import TopTip from '@/components/toptip/index';
  import * as Api from './api';

  export default class Amount extends wepy.page {
    config = {
      navigationBarTitleText: '住院缴费',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      'toptip': TopTip,
      'outpatient': Outpatient,
      empty: Empty
    };
    
    onLoad(options) {
      let { 
        payAmout = '', 
        isScan = '', 
        patientId = '',
        extFields = '',
        admissionNum = '',
        inpatientId = '',
        patientName = '',
        patCardNo = '',
      } = this.$wxpage.options || options;
      this.$broadcast('outpatient-get-patient', { patientId });
      this.patientId = patientId;
      if (payAmout) {
        this.totalFee = Number.parseInt(payAmout);
      }
      this.$apply();
    }

    data = {
      toptip: '',
      patientConfig: {
        infoShow: false,
        show: false,
        initUser: {},
      },
      patient: {},
      user: {},

      isScan : '', 
      extFields: '',
      patientId: '',
      admissionNum: '',
      inpatientId: '',
      patientName: '',
      patCardNo: '',

      payList: [{}],
      login: false,
    };

    methods = {
      bindGoBindUser(){
        wepy.navigateTo({
          url: '/pages/inhosp/binduser/index',
        });
      },
      userIO(e){
        const { value } = e.detail;
        this.totalFee = value;
        this.activeAmount = -1;
      },
      async recharge(item = {}){
        if (!this.user.patientId && !this.isScan) {
          // this.showTopTip('请选择充值对象');
          return false;
        }
        const { price: totalFee, admissionNum, hisOrderNo, inhospitalNo, pid, patName } = item;
        const param = {
          price: totalFee,
          extFields: this.extFields,
          patientId : this.patientId || this.user.patientId,
          transParam: `{"type":"hcTransParam","plat":"gzhc365zhyy"}`,
          admissionNum, hisOrderNo, inhospitalNo, pid,
        }
        const { code, data = {}, msg } = await Api.recharge(param);

        if(code == 0){
          const { orderId = '' } = data;
          this.registerPayOrder(orderId,admissionNum,patName);
        }
      }
    };

    events = {
      'outpatient-change-user': function (item = {}) {
        if(JSON.stringify(item) != '{}'){
          this.patientConfig.infoShow = true;
          this.changeUser(item);
        }
      }
    };

    async changeUser(user = {}) {
      this.user = user;
      this.getPayList(user);
    }

    async getPayList() {
      const { code, data = [], msg } = await Api.getPayList({ pid: this.user.patHisNo });
      if(code == 0) {
        const { voList = [] } = data;
        this.payList = voList.map((item) => {
          item.priceStr = (item.price / 100).toFixed(2);
          return item;
        });
        this.$apply();
      }
    }

    async registerPayOrder(orderId = '', admissionNum = '', patName = ''){
      const bizContent = this.getBizContent({admissionNum, patName});
      const { code, data = {}, msg } = await Api.registerPayOrder({ orderId, bizContent: JSON.stringify(bizContent) });
      if(code == 0){
        const { payOrderId = '' } = data;
        wepy.navigateTo({
          url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=ZYYJBJ`
        });
      }
    }

    /**
     * 获取订单展示信息
     * @returns {*[]}
     */
    getBizContent({admissionNum, patName}) {
      return [
        { key: '费用类型', value: '住院缴费' },
        { key: '住院人', value: patName || '' },
        { key: '住院号', value: admissionNum || '' },
      ];
    }
  }
</script>
