<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import { PRIMARY_COLOR, ADD_CARD, DOMAIN } from '@/config/constant';
  import * as Api from './api';

  export default class UserInfo extends wepy.page {
    config = {
      navigationBarTitleText: '就诊人详情',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
    };

    onLoad(options) {
      this.relationType = options.relationType;
      this.getUserInfo({ patientId: options.patientId, idFullTransFlag: 1 });
      // this.getDictInfo();
      console.log(this.relationType)
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      userInfo: {},
      ADD_CARD: ADD_CARD,
      relationType: '',
      dataInfo: '',
      isShowEditInfo: false,
      typeList:[
        {dictKey: "1", dictValue: "身份证", sortNo: 0},
        {dictKey: "3", dictValue: "护照", sortNo: 1},
        {dictKey: "4", dictValue: "军官证", sortNo: 2},
        {dictKey: "8", dictValue: "其他法定有效证件", sortNo: 3},
        {dictKey: "9", dictValue: "士兵证", sortNo: 4},
        {dictKey: "10", dictValue: "警官证", sortNo: 5},
        {dictKey: "11", dictValue: "学生证", sortNo: 6},
        {dictKey: "13", dictValue: "港澳居民身份证", sortNo: 7},
        {dictKey: "12", dictValue: "台湾居民身份证", sortNo: 8}
      ],
      dict: '', // 字典信息
      marryDes: '',
      marrList: [],
      marrArrList: [],
      indexZR: '',
      famList: [],
      famArrList: [],
      indexJT: '',
      natList: [],
      natArrList: [],
      indexAF: '',
      jobList: [],
      jobArrList: [],
      indexAG: '',
      nationList: [],
      nationArrList: [],
      indexAC: '',
      culList: [],
      culArrList: [],
      indexZC: '',
    };

    async getUserInfo(param){
      const { data } = await Api.getUserInfo(param);
      this.userInfo = data;
      if (data.patientName) {
        // 获取基本信息
        const { patientName = '', patientFullIdNo: idNo = '', pid = '' } = data;
        console.log('data',data);
        const typeIndex = this.typeList.findIndex((current) => {return data.idTypeName == current.dictValue})
        console.log('typeIndex',typeIndex)
        // const idType = this.typeList[typeIndex].dictKey;
        this.getUserDetail({ patName: patientName, idNo, pid});
      }
      this.$apply();
    }

    async getUserDetail(param) {
      param.qryType = 1;
      const { code, data } = await Api.getPatientInfo(param);
      let dataInfo;
      if (code == 0 && data.itemList && data.itemList[0]&& data.itemList[0].items && data.itemList[0].items) {
        let dataInfo;
        const userList = data.itemList[0].items;
        userList.forEach((item) => {
          if (item.patName === param.patName) {
            dataInfo = item;
          }
        })
        this.dataInfo = dataInfo;
      }
      if (this.dict) {
        this.initPatientForm(this.dataInfo);
      }
      this.$apply();
    }

    async getDictInfo(){
      // 获取字典内容
      const { code, data = {} } = await Api.getDict();
      const { list = [] } = data;
      const dict = {};
      list.forEach((item) => {
        if (item.dictType && item.itemList && Number(item.resultCode) === 0) {
          dict[item.dictType] = item;
        }
      });
      this.dict = dict;
      const option = {};
      const nameObj = { ZR: 'marr', JT: 'fam', AF: 'nat', AG: 'job', AC: 'nation', ZC: 'cul' };
      for (let name in nameObj) {
        this.getListInfoByName(name);
      }
      if (this.dataInfo.patName) {
        this.initPatientForm(this.dataInfo);
      }
      this.$apply();
    }

    getListInfoByName(name = '') {
      // 根据名称获取字典数组信息
      const nameObj = { ZR: 'marr', JT: 'fam', AF: 'nat', AG: 'job', AC: 'nation', ZC: 'cul' };
      const data = this.dict[name] || {};
      const { itemList = [] } = data;
      const { items = []} = itemList[0] || {};
      const arrList = [];
      items.map((item)=>{
        arrList.push(item.dictName);
      });
      console.log(arrList)
      if (name === 'JT') {
        console.log(arrList)
      }
      this[`${nameObj[name]}ArrList`] = arrList;
    }

    initPatientForm(patientInfo = {}) {
      // 初始化就诊人表单内容
      const nameObj = { ZR: 'marr', JT: 'fam', AF: 'nat', AG: 'job', AC: 'nation', ZC: 'cul' };
      const valueList = ['address', 'idCardAddress', 'postalCode'];
      const pickerValueObj = {
        educationCode: { abbr: 'ZC', arrListName: 'cul' },
        familyMemberCode: { abbr: 'JT', arrListName: 'fam' },
        jobCode: { abbr: 'AG', arrListName: 'job' },
        marryCode: { abbr: 'ZR', arrListName: 'marr' },
        nationCode: { abbr: 'AC', arrListName: 'nation' },
        nativeCode: { abbr: 'AF', arrListName: 'nat' },
      }
      valueList.forEach((id) => {
        // 判断是否存在这些值 直接赋值
        const value = patientInfo[id];
        if (value) {
          this[id] = value;
        }
      });
      for (const key in pickerValueObj) {
        // 判断是否存在值 循环对应数组
        const value = patientInfo[key];
        const { abbr, arrListName } = pickerValueObj[key];
        if (value) {
          // const list = this[`${arrListName}ArrList`] || [];
          const dictList = this.dict[abbr].itemList[0].items.map((item) => item.dictCode) || [];
          const index = dictList.indexOf(value) || 0;
          this[`index${abbr}`] = index;
        }
      }
      console.log('initPatientForm');

    }

    getListData(obj = {}) {
      // 获取民族、籍贯等字典中的内容
      const { index, name = '' } = obj;
      if (!index && typeof index != 'number') {
        return '';
      }
      const { dict = {} } = this;
      const { itemList = [] } = dict[name];
      const { items } = itemList[0];
      if (items.length <= 0) {
        return '';
      }
      return (items[index] || {}).dictCode;
    }

    getFormData() {
      const dataInfo = this.dataInfo;
      // const marryCode = this.getListData({ index: this.indexZR, name: 'ZR' }) || this.dataInfo.marryCode; //是否婚姻
      // const familyMemberCode = this.getListData({ index: this.indexJT, name: 'JT' }) || this.dataInfo.familyMemberCode; //家庭成员关系
      const nativeCode = this.getListData({ index: this.indexAF, name: 'AF' }) || this.dataInfo.nativeCode;  //户籍
      const nativeName = this.natArrList[this.indexAF] ||  this.dataInfo.nativeName; 
      const jobCode = this.getListData({ index: this.indexAG, name: 'AG' }) || this.dataInfo.jobCode;  //职业
      const jobName = this.jobArrList[this.indexAG] ||  this.dataInfo.jobName;
      const nationCode = this.getListData({ index: this.indexAC, name: 'AC' }) || this.dataInfo.nationCode; //民族
      const nationName = this.nationArrList[this.indexAC] ||  this.dataInfo.nationName;
      const educationCode = this.getListData({ index: this.indexZC, name: 'ZC' }) || this.dataInfo.educationCode;    //文化程度
      const educationName = this.culArrList[this.indexZC] ||  this.dataInfo.educationName;
      const postalCode = this.postalCode || this.dataInfo.postalCode;
      const idCardAddress = this.idCardAddress || this.dataInfo.idCardAddress;
      const address = this.address || this.dataInfo.address;

      return {
        ...dataInfo,
        nativeCode,
        nativeName,
        jobCode,
        jobName,
        nationCode,
        nationName,
        educationCode,
        educationName,
        postalCode,
        idCardAddress,
        address,
      };
    }



    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      async formSubmit() {
        const value = this.getFormData();
        console.log('test', value);
      },
      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        console.log('inputTrigger', id, value)
        this[id] = value;
        this.$apply();
      },
      pickerChange(e) {
        return false;
        const { id } = e.currentTarget;
        const { value } = e.detail;
        console.log('pickerChange', id, value);
        this[`index${id}`] = value;
        this.$apply();
      },
      toggleEdit() {
        this.isShowEditInfo = !this.isShowEditInfo;
      },
      async setDefault(){
        if(this.userInfo.isDefault != 1){
          const { code, data } = await Api.setDefault({ patientId: this.userInfo.patientId });
          if(code == 0){
            await wepy.showToast({
              title: '设置成功',
              icon: 'success',
            });
            this.userInfo.isDefault = 1;
            this.$apply();
          }
        }
      },
      async unBind(){
        let showModalRes;
        const app = this.$root.$parent;
        const { globalData = {} } = app;
        if(this.relationType == 1){
          showModalRes = await wepy.showModal({
            title:'提示',
            content: '确定退出就诊人吗？',
            cancelText: '暂不退出',
            confirmText: '确定退出',
            cancelColor: '#989898',
            confirmColor: PRIMARY_COLOR,
          });
        }else{
          showModalRes = await wepy.showModal({
            title: '提示',
            content: '确定删除该就诊人吗？',
            cancelText: '暂不删除',
            confirmText: '确定删除',
            cancelColor: '#989898',
            confirmColor: PRIMARY_COLOR,
          });
        }
        if (showModalRes.cancel) {
          return;
        }
        const { code, data } = await Api.unBind({ patientId: this.userInfo.patientId });
        if(code == 0){
          delete globalData.loginPatient;
          await wepy.showToast({
            title: '删除成功',
            icon: 'success',
          });
          if (this.relationType == 1) {
            const timer = setTimeout(() => {
              clearTimeout(timer);
              wepy.redirectTo({
                url: '/pages/home/<USER>'
              });
            }, 2000);
          } else {
            const timer = setTimeout(() => {
              clearTimeout(timer);
              wepy.navigateBack();
            }, 2000);
          }
          
        }
      },
      async addCard() {
        const { patCardNo } = this.userInfo;
        const param = {
          memberCardCode: patCardNo
        };
        const { code, data } = await Api.getCardInfo(param);
        if(code === 0){
          try {
            if ( typeof wx.addCard === 'function' ) {
              wx.addCard({
                cardList: [{
                  cardId: data.card_id,
                  cardExt: '{"code":"'+data.code+'","openid":"","timestamp":"'+data.timestamp+'","signature":"'+data.signature+'","nonce_str":"'+data.nonce_str+'"}'
                }], // 需要添加的卡券列表
                success: function (res) {
                  const cardList = res.cardList; // 添加的卡券列表信息
                }
              });
            } else {
              wx.showModal({
                title: '提示',
                content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
              })
            }
          } catch (e) {
            console.log(res)
          }
        }
      }
    };

    events = {
    };

  }
</script>
