<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
import wepy from 'wepy';

export default class DetailItem extends wepy.component {
  props = {
    title: {
      type: String,
      default: '',
    },
    hasRadius: {
      type: String,
      default: '1',
    },
    isBetween: {
      type: String,
      default: '2',
    },
    dataSource: {
      type: Array,
      default: [],
    },
  };

  components = {};

  onLoad(options) {}

  data = {};

  methods = {};
}
</script>
