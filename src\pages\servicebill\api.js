import { REQUEST_QUERY } from '@/config/constant';
import { post } from '@/utils/request';

export const queryZzxmcx = (param) => post(`/api/customize/queryZzxmcx?_route=h${REQUEST_QUERY.platformId}`, param);

export const queryZzkd = (param) => post(`/api/ext/saveorder`, param);

export const registerPayOrder = (param) => post("/api/ext/extpayorder", param);
export const getNoteProfile = (param) => post('/api/register/getNoteProfile', param);

