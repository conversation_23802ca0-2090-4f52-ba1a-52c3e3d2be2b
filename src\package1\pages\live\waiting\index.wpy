<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveDetailMixin from '@/mixins/live/detailMixin';
import { loopRequest } from '@/utils/utils';
import * as Api from './api';
import DoctorInfo from '../comm/doctorInfo';
export default class Waiting extends wepy.page {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom'
  };
  // 在mixin中处理了对直播详情的请求
  mixins = [LiveDetailMixin];
  data = {};

  components = {
    'doctor-info': DoctorInfo
  };

  methods = {
    back() {
      console.log(1111);
      wepy.navigateBack({
        delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
      });
    }
  };

  events = {};
  watch = {
    liveDetail(newVal = {}) {
      const { liveStatus, id } = newVal;
      if (liveStatus != 0) {
        let url = '/package1/pages/live/';
        const paths = {
          1: `play/index?id=${id}`,
          2: `end/index?id=${id}`,
        };
        wepy.redirectTo({ url: `${url}${paths[liveStatus]}` });
      }
    }
  };
  computed = {
    remainTime() {
      const { liveTime } = this.liveDetail;
      const minutes = time => {
        return (time / 1000 / 60).toFixed(0);
      };
      return liveTime ? minutes(this.mixinTimeDiffWithNow(liveTime)) : 0;
    }
  };

  onLoad(options) {
    const { id } = options;
    console.log('11111')
    this.interval = loopRequest(() => this.mixinGetLiveDetail(id, false));
  }

  onShow() {}

  onUnload() {
    clearInterval(this.interval);
  }
}
</script>
