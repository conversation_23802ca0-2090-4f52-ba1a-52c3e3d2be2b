@import "../../../resources/style/mixins";

page {
  background-color: @hc-color-bg;
  min-height: 100vh;
}

.dyna-tab {
  padding: 40rpx 0 0 48rpx;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  background-color: #fff;  
  overflow-x: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .dyna-tabitem {

    .dyna-tabitem-content {
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.40);
      margin-right: 71rpx;
      word-break: keep-all;
      &.on{
        color: #3F969D;
        font-weight: 600;
        &::after{
          content: '';
          display: block;
          margin-top: 20rpx;
          width: 100%;
          height: 8rpx;
          background-color: #30A1A6;
        }
      }
    }
  }
}
.dyna-item{
  margin: 22rpx 24rpx 16rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
  .item-asside{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  image{
    width: 140rpx;
    height: auto;
    border-radius: 8rpx;
  }
  .dyna-title{
    color: @hc-color-title;
    font-size: 32rpx;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .info-rt{
    color: rgba(0, 0, 0, 0.40);
    font-size: 24rpx;
  }
}
