<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import * as Api from '../../api';

  export default class News extends wepy.page {
    config = {
      navigationBarTitleText: '宣教内容',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    data = {
      article: {},
    };

    methods = {
    };
    async getarticledetail(id){
      const { data = {}, code } = await Api.getarticledetail({articleId: id});
      if(code == 0){
        this.article = data;
        if (data.title) {
          wx.setNavigationBarTitle({
            title: data.title,
          });
        }
        this.$apply();
      }
    }
    onLoad(options){
      this.getarticledetail(options.id);
    }
  }
</script>
