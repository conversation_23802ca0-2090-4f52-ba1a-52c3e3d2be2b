<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import TopTip from "@/components/toptip/index";
import Empty from "@/components/empty/index";
import { SURVEY_ECHO } from "@/config/constant";
import { ADULT_SURVEY_ID, CHILD_SURVEY_ID } from "@/config/constant";
import { validator } from "@/utils/utils";
import * as Api from "./api";
import * as Utils from "@/utils/utils";

export default class SurveyDetail extends wepy.page {
  config = {
    navigationBarTitleText: "问卷调查"
  };
  components = {
    empty: Empty,
    toptip: TopTip
  };
  onShareAppMessage() {
      
  }

  onShow(options) {
    const animation = wx.createAnimation({
      duration: 500,
      timingFunction: "linear"
    });
    this.animation = animation;
  }

  onLoad(options) {
    this.id = options.id;
    this.orderId = options.orderId;
    this.questionUserId = options.questionUserId || '';
    this.urlAddress = options.address || "";
    this.type = options.type || "";
    this.pacsDeptName = options.pacsDeptName || "";
    this.pascDoctorName = options.pascDoctorName || "";
    this.doctorName = options.doctorName || "";
    this.advice = options.advice || "";
    this.record = options.record;
    
    // 判断是否为成人版或儿童版问卷
    if (this.id === ADULT_SURVEY_ID || this.id === CHILD_SURVEY_ID) {
      this.hideHeader = true;
      console.log('检测到成人版或儿童版问卷，隐藏问卷头部信息');
    }
    
    if (this.id == "13") {
      this.showModel = true;
    }
    this.getSurveyDetail();
    this.$apply();
  }

  data = {
    type: "",
    record: '',
    pacsDeptName: "",
    pascDoctorName: "",
    advice: "",
    doctorName: "",
    emptyConfig: {
      show: true
    },
    toptip: "",
    quesInfo: {},
    titleList: [],
    id: "",
    orderId: "",
    isOldPlatform: 0,
    jsonToAnswer: [],
    score: {},
    isDisabled: false,
    region: [],
    scrollView: "",
    scrollHeight: "",
    temJsonToAnswer: [],
    hideHeader: false, // 是否隐藏问卷头部信息
    // 问卷=13时，第二个段落选否的标志，选“否”才显示后面的问卷；选“是”直接填写确诊名称，不显示之后的问卷，默认不显示
    isSpecialDis: true,
    // 问卷=13时，需要折叠问题的开始下标
    startExpandIdx: 17,
    // 问卷=13时，url带入的地址，默认辖区
    urlAddress: "",
    showModel: false,
    modelContent:
      "亲爱的育龄朋友：\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 为了您将来的宝宝更健康，您的家庭更幸福，远离遗传性罕见病的困扰，从2021年11月底开始，长沙市人民政府实施长沙市健康民生项目—遗传性罕见病综合防控项目，为所有户籍在长沙市（或女方持有有效居住证）的计划怀孕或孕13周内的育龄夫妇家庭（女方年龄在49岁周岁以下），提供免费的遗传性罕见病综合防控服务。\n",
    modelTitle1:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 一、为什么要参加遗传性罕见病综合防控项目？\n",
    modelContent1:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 罕见病是一类严重威胁人类健康的疾病，目前已知约6000-7000种，多由遗传性因素引起，在全球已知的罕见病中，有80%是由于基因缺陷导致的遗传病。由于我国人口基数大，罕见病在我国实际并不罕见。根据统计报告，目前我国约有2000万罕见病患者。罕见病药物昂贵短缺、医治困难、误诊率高，不论是于社会还是于家庭，其带来的打击无疑是沉重的；对于这些患者家庭而言，面临的是由于无法负担造成的无药可治。在孕前或早孕时构筑遗传性罕见病防控体系，积极查找可能导致遗传性罕见病等不良妊娠结局的风险因素，预防遗传性罕见病胎儿的出生，是预防遗传性罕见病最有效的手段。\n",
    modelTitle2:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 二、何时参加遗传性罕见病综合防控项目？\n",
    modelContent2:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 只要夫妻一方具有长沙市户籍（或女方持有有效居住证）的计划怀孕或孕13周内的育龄夫妇家庭，在孕前或孕13周前的育龄夫妇都可参加遗传性罕见病综合防控项目。参加的最好时机是在计划受孕前3-6个月。由于怀孕、胎儿生长发育是一个复杂的生理过程，还会存在其他不确定因素，因此尽管检查结果正常，或者发现风险因素采取相关预防措施后，怀孕后仍需定期接受孕期检查和做好保健。\n",
    modelTitle3:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 三、遗传性罕见病综合防控项目包括哪些内容？\n",
    modelContent3:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 项目主要针对遗传性罕见病风险因素中较重要或较常见因素进行检查，包括优生健康教育、家系调查、遗传咨询、遗传学检测、风险评估、生育指导、产前诊断或植入前胚胎遗传学检测、追踪随访等综合服务。\n",
    modelTitle4:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 四、如何参加免费遗传性罕见病综合防控项目？\n",
    modelTitle41:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; （一）新婚夫妇及参与孕前优生健康检查的夫妇。\n",
    modelContent411:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 1.在全市各婚登中心登记结婚的新婚夫妇或有生育计划的夫妇，可直接在各区县（市）妇幼保健院（所）进行婚前医学检查时或孕前优生健康检查时进行免费的健康教育和遗传性罕见病家系调查。\n",
    modelContent412:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 2.经家系调查，怀疑或确认为遗传性罕见病风险对象，携带身份证（或有效居住证）及家族中遗传性罕见病患者相关资料，到项目服务机构中信湘雅生殖与遗传专科医院，或与项目服务机构签订服务协议的长沙市辖区产前诊断机构进行免费的遗传咨询和风险评估；经遗传咨询为高风险的遗传性罕见病家庭（夫妇及患儿）提供免费的遗传学检测；为有生育意愿的遗传学检测阳性对象提供生育指导，如后续进行产前诊断或植入前胚胎遗传学检测，提供部分费用补助。\n",
    modelTitle42:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; （二）早孕建册（孕13周前）的夫妇。",
    modelContent42:
      " 在各社区卫生服务中心（乡镇卫生院）早孕建册时进行免费的健康教育和遗传学罕见病家系调查。服务流程同一（但不参与植入前胚胎遗传学检测）。\n",
    modelTitle43: " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; （三）其它育龄夫妇。",
    modelContent43:
      " 通过扫描微信二维码或拨打电话预约参与（电话：84800171），或直接到实施机构参与项目。服务流程同一。 \n",
    modelTitle44:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; （以上三种形式可以参加任意一种，不重复参加）\n",
    modelTitle5:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 五、服务机构及服务网络\n",
    modelContent5:
      "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 中信湘雅生殖与遗传专科医院：84800171  15873180538岳麓区桐梓坡西路567号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;长沙市妇幼保健院  84196885（婚孕检咨询）  84196626（早孕咨询） 雨花区城南东路416号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;芙蓉区妇幼保健所 84885750 马王堆火炬中路8号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;天心区妇幼保健所  85539592（婚孕检咨询）新开铺莲花巷86号天心区妇幼保健所5楼；89906510（早孕咨询）新开铺莲花巷86号天心区妇幼保健所4楼妇保科\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;岳麓区妇幼保健所 85054046 望月湖小区月宫街99号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;开福区妇幼保健所 84482408 长沙市开福区新河路217号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;雨花区妇幼保健所 85882070（婚孕检咨询）香樟路592号雨花区政府机关二院1号楼二楼；85880957（早孕咨询）长沙市中意一路772号雨花区公共卫生大楼5楼妇保科\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;望城区妇幼保健院  88075707（婚孕检中心）望城区政务服务中心三楼； 88081707（早孕咨询）孕产保健科（高塘岭大道5号）\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;长沙县妇幼保健院  84012361（婚孕检咨询）长沙县文体中心一楼婚姻登记处旁（星沙街道望仙东路598号）；86912345长沙县妇幼保健院门诊楼4楼4075（星沙街道开元东路298号）\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;浏阳市妇幼保健院83688308（早孕咨询）83608299（婚前检查咨询）浏阳市婚检中心（浏阳市北正北路53号）；83600160（孕前检查咨询）浏阳市方竹路2号\n &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;宁乡市妇幼保健院  87633897（婚孕检咨询）宁乡市玉潭街道花明北路413号市民之家二楼H区；87841351（早孕咨询 ）宁乡市妇幼保健院保健部（白马大道324号）\n",
    modelTitle6:
      " &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 祝您拥有健康的宝宝，拥有幸福的家庭！\n\n"
  };

  methods = {
    scroll() {
      this.scrollView = "";
    },
    bindRegionChange: function(e) {
      const { titleId, questionsType } = e.currentTarget.dataset;
      this.titleList.map(item => {
        if (item.titleId == titleId) {
          item.region = e.detail.value;
        }
      });
    },
    // 改变单选多选附加值输入
    bindAnswerRemarkChange(e) {
      this.onRemarkChange(e);
    },
    bindExtAnswerRemarkChange(e, echo) {
      const { titleId, optionnum, idx, value } = e.currentTarget.dataset;
      let jsonToAnswer = JSON.parse(JSON.stringify(this.jsonToAnswer));
      let answerRemark = value || e.detail.value;

      this.titleList.map(item => {
        if (item.titleId == titleId) {
          (item.optionList || []).map(opt => {
            if (opt.optionNum == optionnum && opt.haveRemarkFrame == 1) {
              opt.extInputList[idx].value = e.detail.value;
            }
          });
        }
      });
      if (jsonToAnswer) {
        // 查询已有答案中的位置
        const findIndex = jsonToAnswer.findIndex(
          item => item.titleId == titleId && item.optionNum == optionnum
        );
        if (findIndex > -1) {
          if (!jsonToAnswer[findIndex].extInputList) {
            jsonToAnswer[findIndex].extInputList = [];
          }
          jsonToAnswer[findIndex].extInputList[idx] = Utils.filterEmoji(
            answerRemark
          );
          jsonToAnswer[findIndex].answerContent = jsonToAnswer[
            findIndex
          ].extInputList.join(";");
        }
      }
      this.jsonToAnswer = jsonToAnswer;
      this.temJsonToAnswer = jsonToAnswer;
    },
    // 改变多选题
    checkboxChange(e) {
      this.onCheckChange(e);
    },
    
    // 处理二级选项文本框输入
    bindSecondAnswerChange(e) {
      if(this.record === '1') return;
      
      const { titleId, optionnum } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      let jsonToAnswer = JSON.parse(JSON.stringify(this.jsonToAnswer));
      
      // 处理单选题和多选题的不同情况
      this.titleList.forEach(item => {
        if (item.titleId == titleId) {
          item.optionList.forEach(opt => {
            if (opt.optionNum == optionnum) {
              opt.secondAnswerContent = value;
            }
          });
        }
      });
      
      // 查询已有答案中的位置
      const findIndex = jsonToAnswer.findIndex(
        item => item.titleId == titleId && item.optionNum == optionnum
      );
      
      if (findIndex > -1) {
        jsonToAnswer[findIndex].secondAnswerContent = Utils.filterEmoji(value);
      }
      
      this.jsonToAnswer = jsonToAnswer;
      this.temJsonToAnswer = jsonToAnswer;
    },
    
    // 处理二级选项复选框变更
    secondCheckboxChange(e) {
      if(this.record === '1') return;
      
      const { titleId, parentOption, secOption } = e.currentTarget.dataset;
      
      // 在titleList中找到对应的选项，并更新它的secondCheckedMap
      this.titleList.forEach(item => {
        if (item.titleId == titleId) {
          item.optionList.forEach(opt => {
            if (opt.optionNum == parentOption) {
              if (!opt.secondCheckedMap) {
                opt.secondCheckedMap = {};
              }
              // 切换复选框状态
              opt.secondCheckedMap[secOption] = !opt.secondCheckedMap[secOption];
              
              // 更新jsonToAnswer
              const checkedValues = [];
              for (const key in opt.secondCheckedMap) {
                if (opt.secondCheckedMap[key]) {
                  checkedValues.push(key);
                }
              }
              
              // 更新jsonToAnswer
              const findIndex = this.jsonToAnswer.findIndex(
                item => item.titleId == titleId && item.optionNum == parentOption
              );
              
              if (findIndex > -1) {
                this.jsonToAnswer[findIndex].secondAnswerContent = checkedValues.join(',');
              }
            }
          });
        }
      });
    },
    
    // 处理多值填空题的输入
    bindMultipleValueBlankChange(e) {
      if(this.record === '1') return;
      
      const { titleId, idx } = e.currentTarget.dataset;
      const value = e.detail.value;
      
      // 更新titleList中的值
      this.titleList.forEach(item => {
        if (item.titleId == titleId && item.questionsType === '18') {
          if (!item.blankValues) {
            item.blankValues = [];
          }
          item.blankValues[idx] = value;
          
          // 更新answerContent，用逗号分隔多个填空值
          item.answerContent = item.blankValues.join(',');
        }
      });
      
      // 更新jsonToAnswer
      let jsonToAnswer = [...this.jsonToAnswer];
      const findIndex = jsonToAnswer.findIndex(item => item.titleId == titleId);
      
      if (findIndex > -1) {
        jsonToAnswer[findIndex].answerContent = this.titleList.find(item => item.titleId == titleId).answerContent;
      } else {
        const answerContent = this.titleList.find(item => item.titleId == titleId).answerContent;
        jsonToAnswer.push({ titleId, answerContent });
      }
      
      this.jsonToAnswer = jsonToAnswer;
    },
    
    showOptions(e) {
      const { titleId, questionsType, value } = e.currentTarget.dataset;
      const titleList = this.titleList;
      (titleList || []).map(item => {
        if (item.titleId == titleId) {
          item.showAnction = true;
          let isChange = false;
          // wepy.showActionSheet({
          //   itemList: item.optionList.map(v => v.optionContent),
          // }).then((res)=>{
          //   const optionNum = item.optionList[res.tapIndex].optionNum;
          //   this.jsonToAnswer.map((item2, key) => {
          //     if (item2.titleId == titleId && !Array.isArray(optionNum)) {
          //       isChange = true;
          //       this.jsonToAnswer[key].optionNum = `${optionNum}`
          //       this.$apply();
          //     }
          //   })
          //   if(!isChange){
          //     if(questionsType == 8) {
          //       this.jsonToAnswer = this.jsonToAnswer.filter((obj) => obj && obj.titleId && obj.titleId != titleId);
          //       optionNum && optionNum.map((item1) => {
          //         this.jsonToAnswer.push({ titleId, optionNum:`${optionNum}` });
          //       })
          //     } else {
          //       this.jsonToAnswer.push({ titleId, optionNum:`${optionNum}`});
          //     }
          //   }
          //   item.showValue = item.optionList[res.tapIndex].optionContent;
          //   item.isHide = false;
          //   this.$apply();
          // })
        }
      });
    },
    cancelAnction(e) {
      const titleId = e.currentTarget.dataset.titleId;
      this.titleList.map(item => {
        if (item.titleId == titleId) {
          item.showAnction = false;
        }
      });
    },

    bindValueChange(e) {
      this.onValueChange(e);
    },

    async submit() {
      if (this.isDisabled) {
        return false;
      }
      this.jsonToAnswer = this.jsonToAnswer.filter(
        item => item.answerContent != ""
      );
      // vilidator,验证必输
      const answerArr = [];
      (this.jsonToAnswer || []).map(item => {
        answerArr.push(item.titleId);
      });
      let explanationBumber = 0;
      (this.titleList || []).some((item, index) => {
        if (item.questionsType == 5) {
          explanationBumber++;
        }
        if (!answerArr.includes(item.titleId) && item.required == 1) {
          // 添加特殊校验 id=13 时判断是否确诊遗传病，如果填无则正常校验，选有则跳过之后的所有选项校验
          if (this.id == "13" && index > 18 && this.isSpecialDis == true) {
            return false;
          }

          this.toptip =
            explanationBumber > 0
              ? `第${explanationBumber}段第${item.titleNum}题为必答题`
              : `第${item.titleNum}题为必答题`;
          this.scrollView = `title-${index}`;
          this.$apply();
          return true;
        }
      });
      // 验证多选题
      this.titleList &&
        this.titleList.map((item, index) => {
          if (item.questionsType == 1) {
            let arr = [];
            this.jsonToAnswer &&
              this.jsonToAnswer.map(item1 => {
                if (item1.titleId == item.titleId) {
                  arr.push(item1);
                }
              });
            if (arr.length > item.maxAnswer) {
              this.toptip = `多选题选项超过最大限制`;
              this.scrollView = `title-${index}`;
              this.$apply();
              return;
            }
          }
          if (item.questionsType == 9) {
            this.jsonToAnswer &&
              this.jsonToAnswer.map(item1 => {
                if (item1.titleId == item.titleId) {
                  if (
                    !/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(item1.answerContent)
                  ) {
                    this.toptip = "手机号码格式错误";
                    this.scrollView = `title-${index}`;
                    this.$apply();
                    return;
                  }
                }
              });
          }
          if (item.questionsType == 13) {
            this.jsonToAnswer &&
              this.jsonToAnswer.map(item1 => {
                if (item1.titleId == item.titleId) {
                  if (
                    !/^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/.test(
                      item1.answerContent
                    )
                  ) {
                    this.toptip = "请输入2-8位合法姓名";
                    this.scrollView = `title-${index}`;
                    this.$apply();
                    return;
                  }
                }
              });
          }
          if (item.questionsType == 10) {
            this.jsonToAnswer &&
              this.jsonToAnswer.map(item1 => {
                if (item1.titleId == item.titleId) {
                  const test = validator.idCard(item1.answerContent);
                  if (!test.ret) {
                    this.toptip = "身份证号码格式错误";
                    this.scrollView = `title-${index}`;
                    this.$apply();
                    return;
                  }
                }
              });
          }
          if (item.questionsType == 0) {
            this.jsonToAnswer &&
              this.jsonToAnswer.forEach(item1 => {
                if (item1.extInputList) {
                  delete item1.extInputList;
                }
              });
          }
        });
      const errTimer = setTimeout(() => {
        this.toptip = "";
        this.$apply();
        clearTimeout(errTimer);
      }, 2000);
      if (this.toptip) {
        return false;
      }
      this.isDisabled = true;
      let param = this.orderId
        ? {
            examId: this.id,
            jsonToAnswer: JSON.stringify(this.jsonToAnswer),
            orderId: this.orderId
          }
        : {
            examId: this.id,
            jsonToAnswer: JSON.stringify(this.jsonToAnswer)
          };
      if (this.id == 9) {
        const extFields = JSON.stringify({
          advice: this.advice,
          doctorName: this.doctorName
        });
        param.extFields = extFields;
        param.pacsDeptName = this.pacsDeptName;
        param.type = this.type;
        param.pascDoctorName = this.pascDoctorName;
      }
      if (this.id == 25) {
        param.questionUserId = this.questionUserId || '';
      }
      const { code, data = {} } = await Api.saveQuestion(param);
      this.isDisabled = false;
      if (code === 0) {
        wepy.showToast({
          title: "提交成功",
          icon: "success"
        });
        const timer = setTimeout(() => {
          clearTimeout(timer);
          const _currentPages = getCurrentPages();
          if (_currentPages.length > 1) {
            wepy.navigateBack({ delta: 1 });
          } else {
            wepy.redirectTo({
              url: "/pages/home/<USER>"
            });
          }
        }, 2000);
        this.$apply();
      }
    },

    toggleSurveyModel(e) {
      this.showModel = !this.showModel;
    },

    /**
     * 查看文件
     */
    viewFile(e) {
      const { url } = e.currentTarget.dataset;
      if (!url) return;

      // 判断文件类型
      if (url.toLowerCase().includes('.pdf')) {
        // PDF文件，使用微信内置浏览器打开
        wx.downloadFile({
          url: url,
          success: function (res) {
            const filePath = res.tempFilePath;
            wx.openDocument({
              filePath: filePath,
              success: function (res) {
                console.log('打开PDF成功');
              },
              fail: function (err) {
                console.error('打开PDF失败:', err);
                wx.showToast({
                  title: '无法打开PDF文件',
                  icon: 'none'
                });
              }
            });
          },
          fail: function (err) {
            console.error('下载PDF失败:', err);
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 图片文件，使用预览
        wx.previewImage({
          current: url,
          urls: [url]
        });
      }
    }
  };
  async onRemarkChange(e, echo) {
    const { titleId, optionnum, value } = e ? e.currentTarget.dataset : echo;

    let jsonToAnswer = JSON.parse(JSON.stringify(this.jsonToAnswer));
    let answerRemark = value || e.detail.value;
    if (jsonToAnswer) {
      // 查询已有答案中的位置
      const findIndex = jsonToAnswer.findIndex(
        item => item.titleId == titleId && item.optionNum == optionnum
      );
      if (findIndex > -1) {
        jsonToAnswer[findIndex].answerRemark = Utils.filterEmoji(answerRemark);
      }
    }
    this.jsonToAnswer = jsonToAnswer;
    this.temJsonToAnswer = jsonToAnswer;
  }
  async onCheckChange(e, echo) {
    const { curOption, titleId } = e ? e.currentTarget.dataset : echo;
    const titleList = this.titleList;
    let jsonToAnswer = [...this.jsonToAnswer];
    jsonToAnswer = [...jsonToAnswer.filter(itm => itm.titleId !== titleId)];
    const temJsonToAnswer = this.temJsonToAnswer;
    // 处理分组 optionGroup（互斥项）
    titleList.forEach(item => {
      if (item.titleId == titleId) {
        const isOptionGroup =
          item.optionList.filter(
            item1 => item1.optionNum == curOption && item1.optionGroup
          ).length > 0;
        // isOptionGroup参数表示是否当前勾选的是互斥项
        item.optionList.forEach(item2 => {
          // 当前操作的多选框
          if (item2.optionNum == curOption) {
            item2.checked = !item2.checked;
            if (!item2.checked) {
              // 取消勾选时删除本地存储的answerRemark
              const findIndex = temJsonToAnswer.findIndex(
                subitem => subitem.optionNum == item2.optionNum
              );
              if (findIndex > -1) {
                delete temJsonToAnswer[findIndex].answerRemark;
              }
            }
          } else if (isOptionGroup) {
            item2.checked = false;
          } else if (item2.optionGroup) {
            item2.checked = false;
          }
        });
      }
    });

    const temTitle = titleList.filter(item => item.titleId == titleId);
    temTitle[0].optionList.forEach(listItem => {
      if (listItem.checked) {
        const obj = this.temJsonToAnswer.find(
          subitem => subitem.optionNum == listItem.optionNum
        );
        // 将多选题的附加文本拼接进数组里
        let param = { titleId, optionNum: listItem.optionNum };
        if (obj) {
          param.answerRemark = obj.answerRemark;
        }
        jsonToAnswer.push(param);
      }
    });
    this.jsonToAnswer = [...jsonToAnswer];
    this.titleList = titleList;
  }

  async onValueChange(e, echo) {
    if(this.record === '1') return
    let dataset = {};
    let optionNum = "";
    if (e) {
      // 输入改变
      dataset = e.currentTarget.dataset;
      optionNum = dataset.value || e.detail.value;
      const { titleId, questionsType } = dataset;
      this.titleList.map(item => {
        if (item.titleId == titleId) {
          item.answerContent = e.detail.value;
        }
      });
    } else {
      // 答案回显
      dataset = echo;
      optionNum = echo.value;
    }

    const titleId = dataset.titleId;
    const questionsType = dataset.questionsType;
    const region = (dataset.questionsRegion || []).join("");

    // 问卷 id=13 时判断是否确诊遗传病
    if (this.id == "13" && titleId == 214) {
      const option = (
        this.titleList[this.startExpandIdx].optionList || []
      ).find(item => item.optionNum == optionNum);
      if (option.optionContent == "无") {
        this.isSpecialDis = false;
      } else {
        this.isSpecialDis = true;
      }
      this.$apply();
    }

    let jsonToAnswer = this.jsonToAnswer;
    let isChange = false;
    if (questionsType == 3) {
      this.score[`${titleId}`] = dataset.num;
    }
    // 判断所传入的titleid，如果不是多选 且 是选过选项再做修改的对其中选项做修改
    jsonToAnswer &&
      jsonToAnswer.map((item, key) => {
        if (item.titleId == titleId && !Array.isArray(optionNum)) {
          // 当更换单选题的选项时，将answerRemark属性删除
          if (questionsType == 0) {
            if (item.optionNum != optionNum) {
              delete item.answerRemark;
            }
          }
          isChange = true;
          if (
            ["2", "6", "9", "10", "11", "12", "13", "14"].includes(
              questionsType
            )
          ) {
            // 如果是填空题，答案字段传answerContent
            if (questionsType == 11) {
              jsonToAnswer[key].answerContent = `${Utils.filterEmoji(
                region + optionNum
              )}`;
            } else {
              jsonToAnswer[key].answerContent = `${Utils.filterEmoji(
                optionNum
              )}`;
            }
          } else {
            jsonToAnswer[key].optionNum = `${optionNum}`;
          }
          this.$apply();
        }
      });
    // 如果是多选或者  是其他题型但是没有选过选项或者填过答案的
    if (!isChange) {
      if (
        ["2", "6", "9", "10", "11", "12", "13", "14"].includes(questionsType)
      ) {
        // 如果是填空题，答案字段传answerContent
        if (questionsType == 11) {
          jsonToAnswer.push({
            titleId,
            answerContent: `${Utils.filterEmoji(region + optionNum)}`
          });
        } else {
          jsonToAnswer.push({
            titleId,
            answerContent: `${Utils.filterEmoji(optionNum)}`
          });
        }
      } else {
        jsonToAnswer.push({ titleId, optionNum: `${optionNum}` });
      }
    }

    // 特殊处理单选需要填写附加选项的问题
    let titleList = this.titleList;
    if (questionsType == 0) {
      titleList.map(item => {
        if (item.titleId == titleId) {
          item.optionList.map(item2 => {
            item2.checked = false;
            if (item2.optionNum == optionNum) {
              item2.checked = true;
            }
          });
        }
      });
    }
    this.jsonToAnswer = jsonToAnswer;
    this.$apply();
  }
  // 处理二级选项文本框输入
  async bindSecondAnswerChange(e) {
    if(this.record === '1') return;
    
    const { titleId, optionnum } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    let jsonToAnswer = JSON.parse(JSON.stringify(this.jsonToAnswer));
    
    // 处理单选题和多选题的不同情况
    this.titleList.forEach(item => {
      if (item.titleId == titleId) {
        item.optionList.forEach(opt => {
          if (opt.optionNum == optionnum) {
            opt.secondAnswerContent = value;
          }
        });
      }
    });
    
    // 查询已有答案中的位置
    const findIndex = jsonToAnswer.findIndex(
      item => item.titleId == titleId && item.optionNum == optionnum
    );
    
    if (findIndex > -1) {
      jsonToAnswer[findIndex].secondAnswerContent = Utils.filterEmoji(value);
    }
    
    this.jsonToAnswer = jsonToAnswer;
    this.temJsonToAnswer = jsonToAnswer;
  }

  // 处理二级选项复选框变更
  secondCheckboxChange(e) {
    if(this.record === '1') return;
    
    const { titleId, parentOption, secOption } = e.currentTarget.dataset;
    
    // 在titleList中找到对应的选项，并更新它的secondCheckedMap
    this.titleList.forEach(item => {
      if (item.titleId == titleId) {
        item.optionList.forEach(opt => {
          if (opt.optionNum == parentOption) {
            if (!opt.secondCheckedMap) {
              opt.secondCheckedMap = {};
            }
            // 切换复选框状态
            opt.secondCheckedMap[secOption] = !opt.secondCheckedMap[secOption];
            
            // 更新jsonToAnswer
            const checkedValues = [];
            for (const key in opt.secondCheckedMap) {
              if (opt.secondCheckedMap[key]) {
                checkedValues.push(key);
              }
            }
            
            // 更新jsonToAnswer
            const findIndex = this.jsonToAnswer.findIndex(
              item => item.titleId == titleId && item.optionNum == parentOption
            );
            
            if (findIndex > -1) {
              this.jsonToAnswer[findIndex].secondAnswerContent = checkedValues.join(',');
            }
          }
        });
      }
    });
  }
  
  // 处理填空题中的{val}替换为下划线输入框
  processMultipleValueBlank(titleList) {
    titleList.forEach(item => {
      if (item.questionsType === '18') {
        let content = '';
        // 如果有选项列表且第一项存在
        if (item.optionList && item.optionList.length > 0) {
          content = item.optionList[0].optionContent;
          
          // 查找此题对应的答案
          let answerValues = [];
          if (item.answerContent) {
            // 已经有回显的答案
            answerValues = item.answerContent.split(',');
          } else {
            // 查找jsonToAnswer中的答案
            const answer = this.jsonToAnswer.find(ans => ans.titleId === item.titleId);
            if (answer && answer.answerContent) {
              answerValues = answer.answerContent.split(',');
            }
          }
          
          let valIndex = 0;
          // 替换{val}为带下划线的输入区域
          const richContent = content.replace(/{val}/g, match => {
            const value = answerValues[valIndex] || '';
            const result = `<span class="blank-underline">${value}</span>`;
            valIndex++;
            return result;
          });
          
          // 保存处理后的富文本内容
          item.richContent = richContent;
        }
      }
    });
    return titleList;
  }
  
  // 获取答案
  async getSurveyAnswer() {
    const { code, data = {} } = await Api.getSurveyAnswer({
      questionUserId: this.questionUserId,
      orderId: this.orderId,
      id: this.id
    });
    if (this.id == 25) {
      this.record = '';
    }
    if(code === 0){
      try {
        data.examDesc = (data.examDesc || '').split('\n');
      } catch (error) {
        data.examDesc = [data.examDesc];
      }
      this.quesInfo = data || {};
      (data.titleList || []).map((item, idx) => {
        // 如果是打分题，倒序
        if (item.questionsType == 3) {
          item.optionList.reverse();
        }
        // 如果是下拉单选和多选，增加是否折叠参数isHide
        if (item.questionsType == 7 || item.questionsType == 8) {
          item.isHide = true;
        }
        // id=13 && 如果是单选题
        if (item.questionsType == 0) {
          (item.optionList || []).map(opt => {
            if (opt.frameDefaultText) {
              const extInputList = opt.frameDefaultText.split(";");
              let extData = (extInputList || []).map(ext => {
                return {
                  label: ext,
                  value: ""
                };
              });
              opt.extInputList = extData;
            }
          });

          if (item.titleId == 214) {
            this.startExpandIdx = idx;
          }
        }

        // id=13 && 如果是地址，默认长沙
        if (this.id == 13 && item.questionsType == 11) {
          item.region = ["湖南省", "长沙市", "岳麓区"];
        }

        // 如果是辖区，二维码带入地址则默认辖区
        if (item.questionsType == 17) {
          if (this.urlAddress) {
            item.answerContent = this.urlAddress;
            const { titleId, answerContent = this.urlAddress } = item;
            this.jsonToAnswer.push({ titleId, answerContent });
          } else {
            item.region = ["湖南省", "长沙市", "岳麓区"];
            const { titleId, answerContent = item.region.join("") } = item;
            this.jsonToAnswer.push({ titleId, answerContent });
          }
        }
      });
      this.titleList = data.titleList || [];
    }
    let sameTimeId = "";
    const tempTempList = JSON.parse(JSON.stringify(this.titleList));
    (data.titleList || []).map(item => {
      if (item.required == "1") {
        // 取第必填项的提交时间
        sameTimeId =
          item.answerContentList[item.answerContentList.length - 1].sameTimeId;
      }
    });
    (data.titleList || []).map((item, key) => {
      (item.answerContentList || []).map(ans => {
        if (sameTimeId !== ans.sameTimeId) {
          return;
        }
        // 取上一次提交的答案
        if (item.questionsType === "1") {
          // 多选单独处理
          this.onCheckChange("", {
            curOption: ans.optionNum,
            titleId: item.titleId
          });
        } else {
          this.onValueChange("", {
            titleId: item.titleId,
            questionsType: item.questionsType,
            value: ans.optionNum || ans.answerContent
          });
        }
        if (ans.answerRemark) {
          this.onRemarkChange("", {
            titleId: item.titleId,
            optionnum: ans.optionNum,
            value: ans.answerRemark
          });
        }
        (this.titleList[key].optionList || []).map((opt, idx) => {
          if (opt.optionNum === ans.optionNum) {
            // 取上一次提交的答案
            if (item.questionsType == 3) {
              this.score[`${item.titleId}`] = idx + 1;
            } else if (["0", "1"].indexOf(item.questionsType) > -1) {
              opt.checked = true;
            }
            opt.haveRemarkFrame =
              tempTempList[key].optionList[idx].haveRemarkFrame;
            if (`${ans.answerRemark}`.length) {
              opt.answerRemark = ans.answerRemark;
            }
          }
        });
        item.optionList = this.titleList[key].optionList;
        if (`${ans.answerContent}`.length) {
          item.answerContent = ans.answerContent;
        }
      });
    });
    this.titleList = data.titleList;
    this.emptyConfig.show = false;
    this.$apply();
  }

  // 处理填空题（带占位符）的输入
  bindPlaceholderBlankChange(e) {
    if(this.record === '1') return;
    
    const { titleId, idx } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    // 更新titleList中的值
    this.titleList.forEach(item => {
      if (item.titleId == titleId && item.questionsType === '18') {
        if (!item.blankValues) {
          item.blankValues = [];
        }
        item.blankValues[idx] = value;
        
        // 更新answerContent，用逗号分隔多个填空值
        item.answerContent = item.blankValues.join(',');
        
        // 更新formattedContent
        this.updateFormattedContent(item);
      }
    });
    
    // 更新jsonToAnswer
    let jsonToAnswer = [...this.jsonToAnswer];
    const findIndex = jsonToAnswer.findIndex(item => item.titleId == titleId);
    
    if (findIndex > -1) {
      jsonToAnswer[findIndex].answerContent = this.titleList.find(item => item.titleId == titleId).answerContent;
    } else {
      const answerContent = this.titleList.find(item => item.titleId == titleId).answerContent;
      jsonToAnswer.push({ titleId, answerContent });
    }
    
    this.jsonToAnswer = jsonToAnswer;
  }
  
  // 更新格式化的内容，将{val}替换为带有下划线的文本
  updateFormattedContent(item) {
    if (item.questionsType === '18' && item.optionList && item.optionList.length > 0) {
      const content = item.optionList[0].optionContent;
      const blankValues = item.blankValues || [];
      
      let valIndex = 0;
      // 替换{val}为带下划线的输入区域
      const formattedContent = content.replace(/{val}/g, () => {
        const value = blankValues[valIndex] || '';
        const result = `<span class="blank-underline">${value}</span>`;
        valIndex++;
        return result;
      });
      
      // 保存处理后的富文本内容
      item.formattedContent = formattedContent;
    }
  }

  /**
   * 获取问卷详情
   */
  async getSurveyDetail() {
    this.emptyConfig.show = true;
    wx.showLoading({ title: "加载中" });
    try {
      // 入参checkSubmit传值"yes"时会返回haveSubmit字段，1代表填写过、0代表未填写过，
      let apiMethod = this.record === '1' ? Api.getSurveyAnswer : Api.getSurveyDetail;
      const { code, data = {} } = await apiMethod({
        questionUserId: this.questionUserId,
        orderId: this.orderId,
        id: this.id,
        examId: this.id,
        checkSubmit: 'yes'
      });
      
      if (code === 0) {
        try {
          data.examDesc = (data.examDesc || '').split('\n');
        } catch (error) {
          data.examDesc = [data.examDesc];
        }
        this.quesInfo = data || {};
        
        // 处理题目列表
        (data.titleList || []).map((item, idx) => {
          // 如果是打分题，倒序
          if (item.questionsType == 3) {
            item.optionList.reverse();
          }
          // 如果是下拉单选和多选，增加是否折叠参数isHide
          if (item.questionsType == 7 || item.questionsType == 8) {
            item.isHide = true;
          }
          // id=13 && 如果是单选题
          if (item.questionsType == 0) {
            (item.optionList || []).map(opt => {
              if (opt.frameDefaultText) {
                const extInputList = opt.frameDefaultText.split(";");
                let extData = (extInputList || []).map(ext => {
                  return {
                    label: ext,
                    value: ""
                  };
                });
                opt.extInputList = extData;
              }
              
              // 处理二级复选框
              if (opt.optionType === '2' && opt.secondOptionContent) {
                opt.secondOptionList = opt.secondOptionContent.split(',');
                opt.secondCheckedMap = {};
              }

              // 处理二级多值填空
              if (opt.optionType === '3' && opt.secondOptionContent) {
                opt.secondContentParts = this.parseSecondFillBlankContent(opt.secondOptionContent);
              }
            });

            if (item.titleId == 214) {
              this.startExpandIdx = idx;
            }
          }
          
          // 如果是多选题，处理二级选项
          if (item.questionsType == 1) {
            (item.optionList || []).map(opt => {
              // 处理二级复选框
              if (opt.optionType === '2' && opt.secondOptionContent) {
                opt.secondOptionList = opt.secondOptionContent.split(',');
                opt.secondCheckedMap = {};
              }

              // 处理二级多值填空
              if (opt.optionType === '3' && opt.secondOptionContent) {
                opt.secondContentParts = this.parseSecondFillBlankContent(opt.secondOptionContent);
              }
            });
          }

          // id=13 && 如果是地址，默认长沙
          if (this.id == 13 && item.questionsType == 11) {
            item.region = ["湖南省", "长沙市", "岳麓区"];
          }

          // 如果是辖区，二维码带入地址则默认辖区
          if (item.questionsType == 17) {
            if (this.urlAddress) {
              item.answerContent = this.urlAddress;
              const { titleId, answerContent = this.urlAddress } = item;
              this.jsonToAnswer.push({ titleId, answerContent });
            } else {
              item.region = ["湖南省", "长沙市", "岳麓区"];
              const { titleId, answerContent = item.region.join("") } = item;
              this.jsonToAnswer.push({ titleId, answerContent });
            }
          }
          
          // 处理多值填空题（带占位符）
          if (item.questionsType === '18') {
            // 如果有选项列表且第一项存在
            if (item.optionList && item.optionList.length > 0) {
              const content = item.optionList[0].optionContent;
              
              // 计算需要的空格数量
              const valCount = (content.match(/{val}/g) || []).length;
              
              // 初始化空白值数组
              item.blankValues = new Array(valCount).fill('');
              
              // 查找此题对应的答案
              if (item.answerContent) {
                // 已经有回显的答案
                const answerValues = item.answerContent.split(',');
                item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
              } else if (item.answerContentList && item.answerContentList.length > 0) {
                // 从answerContentList获取答案
                const answer = item.answerContentList[0];
                if (answer && answer.answerContent) {
                  const answerValues = answer.answerContent.split(',');
                  item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
                  item.answerContent = answer.answerContent;
                }
              } else {
                // 查找jsonToAnswer中的答案
                const answer = this.jsonToAnswer.find(ans => ans.titleId === item.titleId);
                if (answer && answer.answerContent) {
                  const answerValues = answer.answerContent.split(',');
                  item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
                }
              }
              
              // 更新格式化的内容
              this.updateFormattedContent(item);
            }
          }
        });
        
        this.titleList = data.titleList || [];
        this.emptyConfig.show = false;

        // 处理附件显示（所有情况下都需要处理）
        if (data.titleList) {
          data.titleList.forEach(item => {
            // 处理附件显示
            if (item.fileFlg === '1' && item.answerContentList && item.answerContentList.length > 0) {
              const answerWithFile = item.answerContentList.find(ans => ans.filePath);
              if (answerWithFile && answerWithFile.filePath) {
                console.log('找到附件数据:', item.titleNum, answerWithFile.filePath);
                const filePaths = answerWithFile.filePath.split(',');
                item.uploadedFiles = filePaths.map(path => {
                  const fileName = path.split('/').pop();
                  const fileType = path.toLowerCase().includes('.pdf') ? 'pdf' : 'image';
                  return {
                    url: path,
                    type: fileType,
                    fileName: fileName
                  };
                });
                console.log('处理后的附件:', item.uploadedFiles);
              }
            }
          });
        }

        // 如果是回显数据，进行选中状态和二级选项的处理
        if (this.record === '1' && data.titleList) {
          data.titleList.forEach(item => {

            if (item.answerContentList && item.optionList) {
              // 处理单选和多选题的选中状态和二级选项
              if (item.questionsType === '0' || item.questionsType === '1') {
                item.optionList.forEach(opt => {
                  const answer = item.answerContentList.find(ans => ans.optionNum === opt.optionNum);
                  if (answer) {
                    opt.checked = true;
                    if (answer.secondAnswerContent) {
                      opt.secondAnswerContent = answer.secondAnswerContent;
                      
                      // 如果是二级复选框
                      if (opt.optionType === '2') {
                        const checkedValues = answer.secondAnswerContent.split(',');
                        opt.secondCheckedMap = {};
                        checkedValues.forEach(val => {
                          opt.secondCheckedMap[val] = true;
                        });
                      }

                      // 如果是二级多值填空
                      if (opt.optionType === '3') {
                        const answerValues = answer.secondAnswerContent.split(',');
                        if (opt.secondContentParts) {
                          let valueIndex = 0;
                          opt.secondContentParts.forEach(part => {
                            if (part.isInput && valueIndex < answerValues.length) {
                              part.value = answerValues[valueIndex];
                              valueIndex++;
                            }
                          });
                        }
                      }
                    }
                  }
                });
              }
              
              // 处理多值填空题答案
              if (item.questionsType === '18' && item.answerContentList.length > 0) {
                const answer = item.answerContentList[0];
                if (answer && answer.answerContent) {
                  item.answerContent = answer.answerContent;
                  
                  // 重新处理多值填空题的显示
                  if (item.optionList && item.optionList.length > 0) {
                    const content = item.optionList[0].optionContent;
                    
                    // 计算需要的空格数量
                    const valCount = (content.match(/{val}/g) || []).length;
                    
                    // 初始化并设置回显值
                    const answerValues = item.answerContent.split(',');
                    item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
                    
                    // 更新格式化的内容
                    this.updateFormattedContent(item);
                  }
                }
              }
            }
          });
        }
        
        this.$apply();
      }
    } catch (error) {
      console.error(error);
    } finally {
      wx.hideLoading();
    }
  }

  // 解析二级多值填空内容（与问卷填写页面保持一致）
  parseSecondFillBlankContent(content) {
    if (!content) return []

    const parts = []
    let currentIndex = 0
    let inputIndex = 0

    const regex = /\{val\}/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index)
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          })
        }
      }

      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      })

      inputIndex++
      currentIndex++
      lastIndex = regex.lastIndex
    }

    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex)
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        })
      }
    }

    return parts
  }
}
</script>
