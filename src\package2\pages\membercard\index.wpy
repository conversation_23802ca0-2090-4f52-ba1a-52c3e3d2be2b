<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml" />
<script>
import wepy from "wepy";
import md5 from "md5";
import { DOMAIN, REQUEST_QUERY } from "@/config/constant";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import WxsUtils from "../../../wxs/utils.wxs";
import * as Api from "./api";

export default class Search extends wepy.page {
  config = {
    navigationBarTitleText: "我的会员卡",
    navigationBarBackgroundColor: "#fff",
  };

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    // 挂号记录列表
    patientConfig: {
      infoShow: false,
      show: false,
      initUser: {},
    },
    currentPatient: {},
    orderList: [],
  };

  wxs = {
    WxsUtils: WxsUtils,
  };

  components = {
    outpatient: Outpatient,
    empty: Empty,
  };

  props = {};

  onShow() {
    this.$broadcast("outpatient-get-patient");
  }

  events = {
    "outpatient-change-user": function (item = {}) {
      if (item) {
        this.patientConfig.infoShow = true;
        this.currentPatient = item;
        this.getOrderList(item);
      }
    },
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    toBind() {
      wepy.navigateTo({
        url: `/package2/pages/membercard/bind/index`,
      });
    },
  };

  async getOrderList(item) {
    const { code, data = {} } = await Api.vipList({
      patId: item.patInNo,
    });
    if (code !== 0) {
      this.orderList = [];
      this.$apply();
      return;
    }

    this.orderList = data.list || [];
    this.$apply();
  }
}
</script>
