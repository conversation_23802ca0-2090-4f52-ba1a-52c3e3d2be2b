<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';
  import Outpatient from '@/components/outpatient/index';
  import { getFormatDate } from '@/utils/utils';
  import Empty from '@/components/empty/index';
  import * as Api from './api';
  
  export default class payMerge extends wepy.page {
    config = {
      navigationBarTitleText: '快速交费',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      'outpatient': Outpatient,
      'empty': Empty,
    };

    onLoad(options) {
    }

    onShow(){
      const { patientId = '' } = this.$wxpage.options;
      this.patientId = patientId;
      this.$broadcast('outpatient-get-patient', { patientId });
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      patientConfig: {
        infoShow: false,
        show: false,
        initUser: {},
      },
      emptyConfig: {
        show: true,
      },
      login: false,
      patient: {},
      pid: '',
      grid: '',
      patientId: '',
      defaultUser: {},
      todayDate: getFormatDate(0, '-'),
      dateTypeValue: 1,
      dateTypeList: [
        { value: 1, name: '当天', beginDate: getFormatDate(0, '-') },
        { value: 2, name: '三个月内', beginDate: getFormatDate(-90, '-') },
        { value: 3, name: '一年内', beginDate: getFormatDate(-365, '-') },
      ],
      // 待缴费汇总列表
      waitPayOrderList: [],
      urlMap: {
        'outpatient': '/pages/treat/untreatlist/index',
        'inpatient': '/pages/inhosp/amount/index',
        // 'take_register': '/pages/takeno/index/index',
        'ivf': '/package1/pages/reproductcenter/amount/index'
      }
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(e) {
        const { type } = e.currentTarget.dataset;
        const url = this.urlMap[type];
        if (!url) return;
        wepy.navigateTo({ 
          url: `${url}?pid=${this.pid}&patientId=${this.patientId}&grid=${this.grid}&patCardNo=${this.grid}&dateType=${this.dateTypeValue}`
        });
      },
      changeDateRange(item = {}) {
        const { value = 1, beginDate } = item;
        this.dateTypeValue = value;
        const patient = { patientId: this.patientId || '', patCardNo: this.grid || '', patHisNo: this.pid || '' };
        this.getWaitPayOrderList(patient, value)
        this.$apply();
      },
    };

    events = {
      'outpatient-change-user': function (item = {}) {
        if(item){
          this.patientConfig.infoShow = true;
          this.changeUser(item);
        }
      },
    };

    formatMoney (moneyString = '0', mark = 100) {
      var moneyNumber = parseFloat(moneyString);
      if (typeof moneyNumber === 'number' && typeof mark === 'number') {
        return parseFloat(moneyNumber / mark).toFixed(2);
      }
      return 0;
    };

    async changeUser(item = {}) {
      this.getWaitPayOrderList(item);
    };

    /**
     * 获取待缴费汇总列表
     */
    async getWaitPayOrderList(item = {}, dateTypeValue) {
      wepy.showLoading({
        title: '加载中',
        mask: true,
      });
      this.waitPayOrderList = [];
      // FIXME: 切换就诊人后，之前的选中信息没有初始化
      const { patientId = '', patCardNo: grid = '', patHisNo: pid = '', } = item;
      dateTypeValue = dateTypeValue || this.dateTypeValue;
      this.pid = pid;
      this.grid = grid;
      this.patientId = patientId;
      const { beginDate } = this.dateTypeList[dateTypeValue - 1] || {};
      const param = { patHisNo: pid, patCardNo: grid, beginDate };
      this.emptyConfig.show = true;
      const { code, data = {} } = await Api.getWaitPayOrderList(param);
      wepy.hideLoading();
      if(code == 0){
        this.waitPayOrderList = data || [];
        this.waitPayOrderList.map(item=>{
          item.totalFee = this.formatMoney(item.totalFee,100);
        })
        if(this.waitPayOrderList.length > 0){
          this.emptyConfig.show = false;
        }
        this.$apply();
      }
    };


  }
</script>
