<view class="p-page">
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{item.statusIcon}}"
          @tap="bindGoDetail({{item}})"
        >
          <view class="item-main">
            <view class="main-tit">
              <view class="item-icon">
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/list-{{item.statusIcon}}.png"></image>
              </view>
              <text>{{item.status == "S" ? '缴费成功' : '缴费失败'}}</text>
              <text class="unit-label" wx:if="{{item.refundStatus == 1 || item.refundStatus == 2 || item.refundStatus == 3}}">有退款</text>
            </view>
          </view>
          <view class="item-extra">
            <view class="main-txt">就诊人：{{item.patientName}}</view>
            <view class="main-txt">缴费时间：{{item.payedTime}}</view>
            <view class="extra-tit">￥{{item.totalFee}}</view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>