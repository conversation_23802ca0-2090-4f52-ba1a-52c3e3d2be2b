<template>
  <view class="comm-tabs">
    <block wx:for="{{tabs}}" wx:key="{{index}}">
      <view class="tabs-item {{active == index && 'active'}}" @tap="onChange('{{index}}')">
        <image class="tabs-item-icon" src="{{active == index ? item.activeIcon : item.icon}}" />
        <text>{{item.title}}</text>
      </view>
    </block>
  </view>
</template>

<script>
import wepy from 'wepy';
export default class Tabs extends wepy.component {
  props = {
    active: {
      type: Number,
      default: 0,
      twoWay: true,
    }
  };

  data = {
    tabs: [
      {
        title: '直播',
        icon: 'REPLACE_EHOSPITAL_DOMAIN/live-unactive.png',
        activeIcon: 'REPLACE_EHOSPITAL_DOMAIN/live-active.png'
      },
      {
        title: '视频',
        icon: 'REPLACE_EHOSPITAL_DOMAIN/video-unactive.png',
        activeIcon: 'REPLACE_EHOSPITAL_DOMAIN/video-active.png'
      },
      {
        title: '关注',
        icon: 'REPLACE_EHOSPITAL_DOMAIN/concern-unactive.png',
        activeIcon: 'REPLACE_EHOSPITAL_DOMAIN/concern-active.png'
      }
    ],
  };

  components = {};

  methods = {
    onChange(index) {
      console.log('index', index);
      if (index == 2) {
        // TODO: 跳转到关注页面
        wepy.navigateTo({ url: '/package1/pages/live/concern/index?type=concern' });
        
      } else {
        this.active = index;
      }
    }
  };
}
</script>

<style lang='less'>
@import '../../../../resources/style/mixins';
.comm-tabs {
  display: flex;
  padding: 44rpx 0 20rpx;
  font-size:30rpx;
  color:#B6B6B6;
  line-height:42rpx;
  .tabs-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .tabs-item-icon {
      height: 70rpx;
      width: 70rpx;
      margin-bottom: 8rpx;
    }
    &.active {
      color: @hc-color-primary;
      &::after {
        content: ' ';
        position: absolute;
        bottom: -20rpx;
        height: 4rpx;
        width: 100rpx;
        background: @hc-color-primary;
        left: 50%;
        transform: translateX(-50%);
        z-index: 99;
      }
    }
  }
}
</style>