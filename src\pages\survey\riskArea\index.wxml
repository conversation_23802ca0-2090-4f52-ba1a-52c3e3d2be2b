<view class="p-page">
  <view class="m-risk" wx:if="{{riskArea.end_update_time}}">
    <view class="risk-top">
      <image class="top-bg" mode="widthFix" src="REPLACE_STATIC_DOMAIN/riskArea/top_bg.png" />
      <view class="title">全国中高风险疫情地区</view>
      <view class="desc">数据来源：国务院客户端小程序，具体以实情况为准</view>
    </view>

    <!-- 高风险等级地区 -->
    <view class="risk-wrap">
      <view class="head-wrap">
        <view class="title-wrap">
          <image class="title-icon" mode="widthFix" src="REPLACE_STATIC_DOMAIN/riskArea/alert.png" />
          <view class="title">高风险等级地区</view>
          <view class="num-wrap color-high">{{riskArea.hcount}}</view>
          个
        </view>
        <view class="desc">截至：{{riskArea.end_update_time}}</view>
      </view>

      <view
        class="content-wrap"
        wx:for="{{riskArea.highlist}}"
      >
        <view class="risk-item" data-item="{{item}}">
          <view class="risk-info">
            <div class="title-wrap">
              <view class="title">{{item.area_name}}</view>
              <image
                class="risk-icon"
                mode="widthFix"
                src="REPLACE_STATIC_DOMAIN/riskArea/risk_high.png"
              />
            </div>
            <view
              wx:for="{{item.communitys}}"
              wx:for-item="itemCell"
            >
              <view class="desc">{{itemCell}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 中风险等级地区 -->
    <view class="risk-wrap mt-48">
      <view class="head-wrap">
        <view class="title-wrap">
          <image class="title-icon" mode="widthFix" src="REPLACE_STATIC_DOMAIN/riskArea/warning.png" />
          <view class="title">中风险等级地区</view>
          <view class="num-wrap color-mid">{{riskArea.mcount}}</view>
          个
        </view>
      </view>

      <view
        class="content-wrap"
        wx:for="{{riskArea.middlelist}}"
      >
        <view class="risk-item" data-item="{{item}}">
          <view class="risk-info">
            <div class="title-wrap">
              <view class="title">{{item.area_name}}</view>
              <image
                class="risk-icon"
                mode="widthFix"
                src="REPLACE_STATIC_DOMAIN/riskArea/risk_mid.png"
              />
            </div>
            <view
              wx:for="{{item.communitys}}"
              wx:for-item="itemCell"
            >
              <view class="desc">{{itemCell}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <block wx:if="{{!riskArea.end_update_time}}">
    <empty :config.sync="emptyConfig">
      <block slot="text">暂无数据</block>
    </empty>
  </block>
</view>
