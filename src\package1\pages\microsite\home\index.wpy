<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  import NavBar from "@/components/navbar/index";


  export default class Home extends wepy.page {
    config = {
      navigationBarTitleText: '医院信息',
      navigationStyle: 'custom',
      usingComponents: {
        "wxparser": "plugin://wxparserPlugin/wxparser"
      }
    };

    components = {
      "nav-bar": NavBar,
    };

    onLoad(options) {
      this.getHisInfo();
      this.getCharacterList();
    }

    data = {
      hisInfo: {},
      hisImgBannerHeight: 0, // 图片占位高度
      hisImgHeight: 0, // 图片高度
      // 特色业务
      characterList: []
    };

    methods = {
      setImgSize(e){
        const { width, height } = e.detail;
        const basScale = 750 / width;
        const imgHeight = height * basScale;
        this.hisImgHeight = imgHeight;
        this.hisImgBannerHeight = imgHeight - 136;
      },
      makePhoneCall(e) {
        const { phone } = e.target.dataset;
        if(phone){
          wepy.makePhoneCall({ phoneNumber: phone });
        }
      },
      navigateTo(e) {
        const { url, type } = e.currentTarget.dataset;
        if (url) {
          if (type == '1') {
            wepy.navigateTo({
              url: `/pages/webview/index?pid=&weburl=${url}`
            });
          } else {
            wepy.navigateTo({ url });
          }
          return false;
        } else {
          wepy.showModal({
            title: '提示',
            content: '功能开发中，敬请期待...',
            showCancel: false,
            confirmText: '确定',
          });
          return;
        }
      },
      preview(url){
        if(url){
          wepy.previewImage({
            urls: [url],
          });
        }
      },
      goBack() {
        this.$back(1);
      }
    };

    async getHisInfo() {
      const { data = {}, code } = await Api.getHisInfo();
      if(code == 0){
        const { address } = data;
        const ad = address.split('】');
        this.hisInfo = data;
        this.hisInfo.address = ad[1];
        this.$apply();
      }
    };

    async getCharacterList() {
      const { data = {}, code } = await Api.getCharacterList();
      if(code == 0){
        this.characterList = data;
        this.$apply();
      }
    }
  }
</script>
