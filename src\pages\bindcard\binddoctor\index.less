@import "../../../resources/style/mixins";

page{
}

.p-page{
  padding: 20rpx 30rpx;

  .tips {
    text-align: center;
    font-size: 44rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }


  .m-nocard{
    margin: 0 20rpx;
    background-color: #fff;
    padding: 96rpx 0 67rpx;
    display: flex;
    justify-content: center;
    .nocard-btn{
      padding: 22rpx 30rpx;
      font-size: 32rpx;
      color: @hc-color-primary;
      box-sizing: border-box;
      border-radius: 10rpx;
      border: 2rpx solid @hc-color-primary;
    }
  }
}


.primary-btn{
  background: @hc-color-primary;
  color: #fff;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  margin-top: 50rpx;
  margin-bottom: 22rpx;
}

.button-block {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
}

.defalult-btn{
  background: #fff;
  color: #000;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
  margin: 40rpx 0;
  padding: 20rpx 0;
}
