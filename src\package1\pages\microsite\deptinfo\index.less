@import "../../../../resources/style/mixins";

page{
}

.m-deptinfo{
  margin: 24rpx 24rpx 16rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
}

.m-deptname{
  font-size: 34rpx;
  color: @hc-color-title;
  font-weight: bold;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #e5e5e5;
  text-align: center;
}

.m-lr-line{
  display: flex;
  padding-top: 24rpx;
  font-size: 32rpx;
  &:first-child{
    padding: 0;
  }

  .line-lt{
    color: rgba(0, 0, 0, 0.40);
    padding-right: 96rpx;
  }
  .line-rt{
    flex: 1;
    color: @hc-color-title;
  }
}
.info-title{
  position: relative;
  padding-left: 12rpx;
  margin-bottom: 24rpx;
  color: #2D2D2D;
  font-size: 28rpx;
  font-weight: 600;
  &::before{
    content: '';
    position: absolute;
    display: inline-block;
    width: 5rpx;
    height: 27rpx;
    border-radius: 100rpx;
    background: #2D666F;
    top: 7rpx;
    left: 0;
  }
}

.m-blockinfo{
  padding: 30rpx 0;
}
