@import "../../resources/style/mixins";

.wgt-user-box{
  position: relative;
  z-index: 1;
  padding: 30rpx;
  background-color: #fff;
  //margin: 20rpx;
  //box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.05);
  //border-radius: 4rpx;
}
.wgt-user-main{
  display: flex;
  align-items: center;
}
.wgt-user-main-info{
  display: flex;
  align-items: center;
  flex: 1;
}
.wgt-user-main-info-tit{
  font-size: 37rpx;
  color:@hc-color-title;
}
.wgt-user-main-info-label{
  flex: 1;
}
.wgt-user-main-btn{
  padding: 0 25rpx;
  font-size: 24rpx;
  height: 52rpx;
  line-height: 52rpx;
  color:@hc-color-primary;
  border:2rpx solid @hc-color-primary;
  border-radius: 999rpx;
}
.wgt-user-extra{
  margin-top: 20rpx;
  font-size: 28rpx;
  color:@hc-color-text;
  .textBreak();
}


//就诊人切换弹窗，需要抽离组件出去
.wgt-user-pop-box{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -999;
  visibility: hidden;
  transform: translateY(-1000%);
}
.wgt-user-pop-mask{
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background .3s;
}
.wgt-user-pop{
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  max-height: 90%;
  overflow-y: auto;
  background-color: #fff;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  transform: translateY(100%);
  transition: transform .3s 0.1s;
}
&.active{
  z-index: 999;
  visibility: visible;
  transform: translateY(0);
  .wgt-user-pop-mask{
    background-color: rgba(0, 0, 0, .6);
  }
  .wgt-user-pop {
    transform: translateY(0%);
  }
}
.wgt-user-pop-title{
  padding: 30rpx;
  font-size: 30rpx;
  color:@hc-color-title;
  text-align: center;
  border-bottom:2rpx solid @hc-color-border;
}
.wgt-user-pop-list{
  margin: 0 30rpx;
}
.wgt-user-pop-list-item{
  position: relative;
  padding: 25rpx 0;
  border-top:2rpx solid @hc-color-border;
  padding-right: 120rpx;
  &:first-child{
    border-top: none;
  }
}
.wgt-user-pop-list-item-main{
  display: flex;
  align-items: center;
  flex-direction: row;
}
.wgt-user-pop-list-item-name{
  font-size: 37rpx;
  color:@hc-color-title;
}
.wgt-user-pop-list-item-label{
  margin-left: 20rpx;
}
.wgt-user-pop-list-item-num{
  margin-top: 10rpx;
  font-size: 28rpx;
  color:@hc-color-text;
  .textBreak();
}
.wgt-user-pop-list-item-ipt{
  display: none;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 38rpx;
  height: 27rpx;
  image{
    width: 100%;
    height: 100%;
    vertical-align: top;
  }
  &.active{
    display: block;
  }
}
.wgt-user-pop-opt{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20rpx 0;
  border-top:2rpx solid @hc-color-border;
}
.wgt-user-pop-opt-item{
  font-size: 34rpx;
  text-align: center;
  flex: 1;
  color:@hc-color-primary;
  border-left:2rpx solid @hc-color-border;
  &:first-child{
    border-left:none;
  }
}
.wgt-user-pop-list-item{
  &.active{
    .wgt-user-pop-list-item-ipt{
      display: block;
    }
  }
}
.wgt-user-pop-close{
  position: absolute;
  right: 0;
  top:0;
  padding: 30rpx;
  width: 36rpx;
  height: 36rpx;
  background: url('REPLACE_IMG_DOMAIN/his-miniapp/icon/common/close.png') no-repeat 50% 50%;
  background-size: 36rpx 36rpx;
  z-index: 9;
}
