<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import { PRIMARY_COLOR } from "@/config/constant";
import Outpatient from "@/components/outpatient/index";
import Empty from "@/components/empty/index";
import Table from "@/components/table/index";
import WxsUtils from "../../../../wxs/utils.wxs";
import * as Utils from "@/utils/utils";
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "住院申请结算",
    navigationBarBackgroundColor: '#fff',
  };
  components = {
    outpatient: Outpatient,
    empty: Empty,
    table: Table
  };
  wxs = {
    WxsUtils: WxsUtils
  };
  data = {
    // url参数
    options: {},
    // 空就诊人列表提醒
    emptyNotice: true,
    // 就诊人配置
    outpatientConfig: {
      infoShow: false,
      show: false,
      initUser: {}
    },
    // 就诊人列表
    outpatient: {},
    // 当前就诊人信息
    currentPatient: {},
    inHospitalBill: [],
    tableHeader: ["住院号", "住院科室", "住院费用总计", "应退费金额"],
    columnKey: ["zyh", "ryksmc", "zyzfy", "yjjye"],
    patCardNo: "",
    patHisNo: "",
    newData: [],
    src: "../img/union.png"
  };
  onLoad() {}
  onShow() {
    const { patientId = "" } = this.$wxpage.options;
    this.options = this.$wxpage.options;
    this.$broadcast("outpatient-get-patient", { patientId });
  }
  // 获取住院人信息
  async getPatientInfo() {
    const { patientId } = this.options;
    if (!patientId) {
      return;
    }
    const { code, data = {}, msg } = await Api.getPatientInfo({ patientId });
    if (code !== 0) return;
    this.currentPatient = data;
    this.getOutHospitalPendingList(data.patHisNo, data.patCardNo);
    this.patCardNo = data.patCardNo;
    this.patHisNo = data.patHisNo;
    this.$apply();
  }
  events = {
    "outpatient-change-user": function(activePatient) {
      if (activePatient) {
        this.options = activePatient;
        this.outpatientConfig.infoShow = true;
        this.$apply();
      }
      this.getPatientInfo();
    }
  };
  methods = {
    navigateTo() {
      // console.log(this.patHisNo, this.patCardNo);
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/list/index?patCardNo=${
          this.patCardNo
        }&patHisNo=${this.patHisNo}`
      });
    },
    async onItemClick(item = {}) {
      // console.log(this.patHisNo, this.patCardNo);
      // 如果是已申请退费的，则弹窗提示 1 已提交，0 未提交
      if (item.sqzt === "1") {
        const { data = {}, code } = await Api.getTipsText({
          profileKey: "getAlertNoteProfile_osaNotSqNotice"
        });
        wx.showModal({
          title: "提示",
          content: data.profileValue || "该条记录已提交过，请勿重复提交",
          showCancel: false,
          confirmText: "确定",
          confirmColor: PRIMARY_COLOR
        });
        return;
      }

      // 如果需要补缴费用的，则弹窗提示，需要补缴费用 1 需要，0 不需要
      if (item.dbj === "1") {
        const { data = {}, code } = await Api.getTipsText({
          profileKey: "getAlertNoteProfile_osaDbjNotice"
        });
        wx.showModal({
          title: "提示",
          content: data.profileValue || "请先补缴费用",
          showCancel: false,
          confirmText: "确定",
          confirmColor: PRIMARY_COLOR
        });
        return;
      }

      const { patientId, patientName, patHisNo, idType } = this.options;

      const billInfo = {
        ...item,
        patientId,
        patientName,
        patHisNo,
        idType,
        zyzfy: item.newzyzfy,
        yjjye: item.newyjjye
      };
      wepy.navigateTo({
        url: `/package1/pages/hospitalbilling/settlementdetail/index?billInfo=${JSON.stringify(
          billInfo
        )}&patientId=${patientId}&jzid=${item.jzid}&patCardNo=${this.patCardNo}`
      });
    }
  };

  async getOutHospitalPendingList(patHisNo, patCardNo) {
    const param = {
      patHisNo: patHisNo, //: 423590,
      patCardNo: patCardNo //: 10262715,
    };
    const { data = [], code } = await Api.getOutHospitalPendingList(param);
    if (code === 0 || code === 1) {
      const newData = data.map(item => {
        return {
          ...item,
          zyzfy: Utils.formatMoney(item.zyzfy),
          yjjye: Utils.formatMoney(item.yjjye),
          newzyzfy: item.zyzfy,
          newyjjye: item.yjjye
        };
      });
      this.newData = data;
      this.inHospitalBill = newData;
      this.$apply();
    }
  }
}
</script>
