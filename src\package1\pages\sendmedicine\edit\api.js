import { post } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

//获取地址详情
export const getAddressDetail = (param) => post('/api/address/addressdetail', param);

//添加地址
export const addAddress = (param) => post('/api/address/addaddress', param);

//修改地址
export const modifyAddress = (param) => post('/api/address/modifyaddress', param);

//省市区查询
export const getCity = (param) => post('/api/address/childcitylist', param);

