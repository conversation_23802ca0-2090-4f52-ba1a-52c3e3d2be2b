import { post } from '@/utils/request';
/**
 * 进入直播间
 */
export const enterLive = (param) => post('/api/ehis/liveEvent/enterLive', param);

/**
 * 退出直播间
 */
export const quitLive = (param) => post('/api/ehis/liveEvent/quitLive', param);

/**
 * 点赞
 */
export const addLike = (param) => post('/api/ehis/liveEvent/like', param);

/**
 * 发送聊天信息
 */
export const sendMsg = (param) => post('/api/ehis/liveEvent/sendMsg', param, true, false);

/**
 * 直播的弹幕，患者列表等等，需要实时更新的信息
 */
export const getLiveRealTimeInfo = (param) => post('/api/ehis/doctorLive/live', param, false);

