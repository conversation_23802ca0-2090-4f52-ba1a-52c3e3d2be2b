<style lang="less"></style>
<template lang="wxml">
  <view class="m-list">
    <view class="list-tit">就诊信息</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.patientName}}">
        <view class="item-label">就诊人</view>
        <view class="item-value">{{detailData.patientName}}</view>
      </view>
      <!-- <view class="list-item" wx:if="{{detailData.patCardNo}}">
        <view class="item-label">就诊卡号</view>
        <view class="item-value">{{detailData.patCardNo}}</view>
      </view> -->
      <view class="list-item" wx:if="{{detailData.queCode}}">
        <view class="item-label">排队号</view>
        <view class="item-value">{{detailData.queCode}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.visitPosition}}">
        <view class="item-label">科室位置</view>
        <view class="item-value">{{detailData.visitPosition}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.visitTime}}">
        <view class="item-label">就诊时段</view>
        <view class="item-value">{{detailData.visitTime}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.hisName}}">
        <view class="item-label">医院名称</view>
        <view class="item-value">{{detailData.hisName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.deptName}}">
        <view class="item-label">就诊科室</view>
        <view class="item-value">{{detailData.deptName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.doctorName}}">
        <view class="item-label">医生姓名</view>
        <view class="item-value">{{detailData.doctorName}}</view>
      </view>
      <view class="list-item" wx:if="{{detailData.doctorTitle}}">
        <view class="item-label">医生职称</view>
        <view class="item-value">{{detailData.doctorTitle}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import wepy from 'wepy';
  import * as Utils from '@/utils/utils';

  export default class BasicDetail extends wepy.component {
    data = {
    };

    components = {};

    props = {
      detailData: {
        type:Object,
        default:{},
      }
    };

    onLoad(options) {
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
    };
  }
</script>
