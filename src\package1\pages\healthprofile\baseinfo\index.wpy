<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import TopTip from '@/components/toptip/index';
  import { validator } from '@/utils/utils';
  import * as Api from './api';

  export default class BindCard extends wepy.page {
    config = {
      navigationBarTitleText: '基本信息',
    };

    components = {
      toptip: TopTip,
    };
    
    onLoad(options) {
      this.options = options;
      const { sex = 'F', pid = '', healthId = '', type = '' } = options || {};
      this.sex = sex;
      this.pid = pid;
      this.healthId = healthId;
      this.type = type;

      this.patientName = '';
      this.idNo = '';
      this.profession= '';
      this.education= '';
      this.nation= '';
      this.address= '';
      this.weight= '';
      this.height = '';
      this.baseinfo = {};

      this.getBaseInfo(options);
    }

    onShow() {
      this.cardList = [];
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      isDisabledBaseInfo: false,
      basePatientInfo: {},
      options: {},
      placeholderColor: '',
      errorColor: 'red',
      errorElement: {}, // 发生错误的元素
      hasErr: true, // 是否存在校验错误
      toptip: '',
      type: '',
      patientName: '',
      idNo: '',
      patientAddress: '',
      pid: '',
      baseinfo: {},
      sex: 'F',
      healthId: '',
      isUpdateInfo: false,
    };

    async getBaseInfo(options = {}){
      let patientInfo = {};
      if (this.sex === 'F') {
        patientInfo = this.$parent.femalInfo || {};
      } else {
        patientInfo = this.$parent.manInfo || {};
      }
      console.log('patientInfo', patientInfo)
      const { basicInfo = {}, pid = '', healthId = '' } = patientInfo;
      this.baseinfo = basicInfo;
      // 设置值
      console.log('test', basicInfo)
      if (basicInfo.name || basicInfo.idNo) {
        this.name = basicInfo.name || '';
        this.idNo = basicInfo.idNo || '';
        this.profession = basicInfo.profession || '';
        this.education = basicInfo.education || '';
        this.nation = basicInfo.nation || '';
        this.address = basicInfo.address || '';
        this.weight = basicInfo.weight || '';
        this.height = basicInfo.height || '';
        this.healthId = healthId || '';
        this.baseinfo = basicInfo;
        this.isDisabledBaseInfo = true;
        this.isUpdateInfo = true;
      } else {
        // 查询用户信息
        this.getPatientInfo(this.options);
      }
      // this.getPatientInfo(options);
      this.$apply();
    }

    async getPatientInfo(options = {}) {
      const { patientId = '' } = this.$parent.profilePatientInfo || {};
      const { code, data = {} } = await Api.getUserInfo({ patientId, idFullFlag: 1 });
      const { patientName = '', patientFullIdNo: idNo = '', patHisNo = '' } = data;
      if (!patientName || !idNo || !patHisNo) {
        return false;
      }
      const { data: patientRelationInfo = {} } = await Api.getPatientInfo({ patName: patientName, idNo, patHisNo, qryType: 2 });
      const { itemList: [ { items = [] } ] = [] } = patientRelationInfo;
      if (items.length <= 0) {
        return false;
      }
      const { sex = 'F' } = options;
      const familyMemberCode = sex === 'F' ? 'JT01' : 'JT02';
      items.forEach((item) => {
        if (item.familyMemberCode === familyMemberCode && item.patName && item.idNo) {
          this.name = item.patName || '';
          this.idNo = item.idNo || '';
          this.profession = item.jobName || '',
          this.education = item.educationName || '';
          this.nation = item.nationName || '';
          this.address = item.address || '';
          this.baseinfo = { ...this.baseinfo, idNo: item.idNo, name: item.patName,
            education: item.educationName || '', profession: item.jobName || '', nation: item.nationName || '', address: item.address || ''  };
          this.isDisabledBaseInfo = true;
        }
      });
      this.$apply();
    }

    getFormData(){
      const { name = '', idNo = '', profession = '', education = '', nation = '', address = '', weight = '',
       height = '', pid = '', sex = '', healthId = '' } = this;
      return {
        name,
        idNo,
        profession,
        education,
        nation,
        address,
        weight,
        height,
        pid,
        patHisNo: pid,
        sex,
        healthId,
      };
    }

    validator(id){
        console.log('开始调用validator', id)
        const validate = {
          name: {
            regexp: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,8}$/,
            errTip: '请输入2-8位合法姓名',
          },
          idNo: {
            regexp: (() => {
              const regexp = validator.idCard;
              if(typeof regexp === 'function'){
                return (val) => regexp(val.idNo);
              } else {
                return /^\S+$/;
              }
            })(),
            errTip: '请输入18位身份证',
          },
          // patientAddress: {
          //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          //   errTip: '请输入有效的住址',
          // },
          // address: {
          //   regexp: /^[\u4e00-\u9fa5-_a-zA-Z0-9]+$/,
          //   errTip: '请输入有效的住址',
          // },
          patientMobile: {
            regexp: /^1\d{10}$/,
            errTip: '请输入正确的手机号',
          },
          pid: {
            regexp: /^\d{3,8}/,
            errTip: '请输入正确pid号'
          }
        };


        const value = this.getFormData();

        let hasErr = false;
        for(let o in value){
          const obj = validate[o];
          if(obj && obj.regexp){
            let thisErr = false;
            if(typeof obj.regexp === 'function'){
              const retObj = obj.regexp(value);
              console.log('retObj', retObj)
              if(!retObj.ret){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            } else {
              if(typeof obj.regexp.test === 'function' && !obj.regexp.test(value[o])){
                hasErr = true;
                thisErr = true;
                if(id && id == o){
                  this.errorElement[id] = true;
                }
              }
            }
            if((!id && hasErr) || (obj.errTarget && obj.errTarget == id && thisErr)){ // 提交时弹框提示
              this.errorElement[obj.errTarget || o] = true;
              this.toptip = obj.errTip || '';
              const errTimer = setTimeout(() => {
                this.toptip = '';
                this.$apply();
                clearTimeout(errTimer);
              }, 2000);
              break;
            }
          }
        }
        return hasErr;
      }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(url) {
        let { qryType = 2 } = this;
        // 跳转带参数 绑定本人或他人
        const jumpUrl = `${url}?qryType=${this.cardList.length > 0 ? 2 : 1}`
        wepy.navigateTo({ url: jumpUrl });
      },

      inputTrigger(e){
        const { id } = e.currentTarget;
        const { value } = e.detail;
        this[id] = value;
      },
    };

    async formSubmit(e){
      let value = this.getFormData();
      console.log('formData', value)
      this.hasErr = this.validator();
      if(this.hasErr){
        return false;
      }
      let param = param = {
        pid: value.pid || '',
        sex: value.sex || '',
        healthId: value.healthId || '',
        basicInfo: value,
      };
      const { code, data = {}, msg } = await (this.type === 'update' ? Api.updateHealthRecord : Api.addHealthRecord)(param);
      // const { idTypes = [], isNewCard, patCards = [], patientTypes = [], relationTypes = [] } = this.hisConfig
      if (code == 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1000,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    }
  }
</script>
