<!--  -->
<template>
  <cover-view class="comm-chat-box" scroll-top="{{chatScroll}}" style="overflow-y: scroll;">
    <cover-view id="live-chat-container">
      <block wx:for="{{list}}" wx:key="{{index}}">
        <cover-view class="chat-item-box">
          <cover-view class="chat-item">
            <cover-image class="item-avatar" src="{{item.avatarUrl}}"/>
            <cover-view class="item-content">{{item.content}}</cover-view>
          </cover-view>
        </cover-view>
      </block>
    </cover-view>
  </cover-view>
</template>

<script>
import wepy from 'wepy';
export default class ChatMsg extends wepy.component {
  props = {
    list: {
      type: Array,
      default: []
    }
  };
  data = {
    chatScroll: 0
  };
  onLoad() {
    this.scrollToBottom();
  }
  watch = {
    list(newVal) {
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }
  };
  scrollToBottom() {
    try {
      this.createSelectorQuery()
        .select('#live-chat-container')
        .boundingClientRect(rect => {
          this.chatScroll = rect.height.toFixed(0);
          this.$apply();
        })
        .exec();
    } catch (error) {
      console.log(error)
    }
  }
}
</script>

<style lang='less'>
// 必须固定高度，scroll-top才会生效
.comm-chat-box {
  position: absolute;
  left: 30rpx;
  bottom: 120rpx;
  width: 580rpx;
  height: 400rpx;
  overflow-x: hidden;
  .chat-item-box {
    max-width: 90%;
    margin-bottom: 20rpx;
    color: #ffffff;
    font-size: 30rpx;
    .chat-item {
      display: inline-flex;
      align-items: center;
      padding: 6rpx;
      background: rgba(0, 0, 0, 0.25);
      border-radius: 50rpx;
      .item-avatar {
        display: inline-block;
        height: 48rpx;
        width: 48rpx;
        margin-right: 10rpx;
        border-radius: 50%;
      }
      .item-content {
        flex: 1;
        padding-right: 15rpx;
        word-break: break-all;
        white-space: normal;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }
}
</style>