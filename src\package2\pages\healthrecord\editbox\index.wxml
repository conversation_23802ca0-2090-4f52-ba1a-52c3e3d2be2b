<view class="p-page">
  <view class="page-content" wx:if="{{type !== '6'}}">
    <block wx:if="{{type === '1'}}">
      <view class="content-box {{isEdit ? 'box-border' : ''}}">
        <view class="title {{!isEdit ? 'require' : ''}}">职业</view>
        <input wx:if="{{!isEdit}}" placeholder="请输入职业" id="career" value="{{basicMsg.career}}" @input="inputValue" maxlength="10" />
        <text wx:else class="box-content">{{basicMsg.career}}</text>
      </view>
      <view class="content-box {{isEdit ? 'box-border' : ''}}">
        <view class="title {{!isEdit ? 'require' : ''}}">学历</view>
        <input wx:if="{{!isEdit}}" placeholder="请输入学历" id="education" value="{{basicMsg.education}}" @input="inputValue" maxlength="10" />
        <text wx:else class="box-content">{{basicMsg.education}}</text>
      </view>
      <view class="content-box {{isEdit ? 'box-border' : ''}}">
        <view class="title {{!isEdit ? 'require' : ''}}">民族</view>
        <input wx:if="{{!isEdit}}" placeholder="请输入民族" id="nation" value="{{basicMsg.nation}}" @input="inputValue" maxlength="10" />
        <text wx:else class="box-content">{{basicMsg.nation}}</text>
      </view>
      <view class="content-box {{isEdit ? 'box-border' : ''}}">
        <view class="title {{!isEdit ? 'require' : ''}}">身高(cm)</view>
        <input wx:if="{{!isEdit}}" placeholder="请输入身高" id="height" value="{{basicMsg.height}}" @input="inputValue" type="number" />
        <text wx:else class="box-content">{{basicMsg.height}}</text>
      </view>
      <view class="content-box">
        <view class="title {{!isEdit ? 'require' : ''}}">体重(kg)</view>
        <input wx:if="{{!isEdit}}" placeholder="请输入体重" id="weight" value="{{basicMsg.weight}}" @input="inputValue" type="number" />
        <text wx:else class="box-content">{{basicMsg.weight}}</text>
      </view>
    </block>
    <block wx:if="{{type !== '1' && type !== '6'}}">
      <view class="content-box">
        <textarea wx:if="{{!hasFamilyMsg || !isEdit}}" class="textarea-box" placeholder="{{placeholder}}" maxlength="500" value="{{familyValue}}" @input="textareaValue" />
        <view wx:else class="textarea-box no-border">
          <view class="box-tip">{{familyMsgDate}}</view>
          {{familyValue}}
        </view>
      </view>
    </block>
  </view>
  <view class="img-content {{currentPicCount !== picCount ? 'edit-box' : ''}}" wx:if="{{type === '6'}}">
    <view class="content" wx:if="{{tempFilePaths.length > 0}}">
      <view class="content-view" wx:for="{{tempFilePaths}}" wx:key="index" wx:for-item="item">
        <view class="time">{{item.date}} ({{item.reportsPath.length}}张)</view>
        <view class="img-box-content">
          <view class="img-box" wx:for="{{item.reportsPath}}" wx:for-item="itm" wx:key="ind" >
            <image class="img" src="{{itm}}" bindlongpress="longTapImg" data-url="{{itm}}" @tap="viewImage" data-path="{{item}}"  />
          </view>
        </view>
      </view>
    </view>
    <block wx:if="{{type === '6' && tempFilePaths.length === 0}}">
      <empty>
        <view slot="main-text">您暂未添加任何检查检验报告</view> 
        <view slot="text">上传在院外所做的报告，便于医生尽快了解病情</view> 
      </empty> 
    </block>
    <view class="update-btn btn" wx:if="{{type === '6'}}" @tap="updateImg">+ 上传外院检查报告</view>
  </view>
  
  
  <view class="btn-box">
    <block wx:if="{{type === '1'}}">
      <view wx:if="{{!hasBsicMsg || !isEdit}}" class="save-btn btn" @tap="saveMsg">保存</view>
      <view wx:if="{{!hasBsicMsg || !isEdit}}" class="cancel-btn btn" @tap="cancel">取消</view>
      <view wx:if="{{hasBsicMsg  && isEdit}}" class="edit-btn btn" @tap="editMsg">编辑</view>
    </block>
    <block wx:if="{{type !== '6' && type !== '1'}}">
      <view wx:if="{{!hasFamilyMsg || !isEdit}}" class="save-btn btn" @tap="saveMsg">保存</view>
      <view wx:if="{{!hasFamilyMsg || !isEdit}}" class="cancel-btn btn" @tap="cancel">取消</view>
      <view wx:if="{{isEdit && hasFamilyMsg}}" class="edit-btn btn" @tap="editMsg">编辑</view>
    </block>
    
  </view>
  <view class="bottom-btn" wx:if="{{currentPicCount !== picCount}}">
    <view class="save" @tap="saveImg">保存</view>
    <view class="cancel" @tap="cancel">取消</view>
  <view>
</view>