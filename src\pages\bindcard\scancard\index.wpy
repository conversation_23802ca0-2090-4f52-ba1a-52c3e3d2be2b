<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
  import wepy from 'wepy';
  import { DOMAIN, PRIMARY_COLOR } from '@/config/constant';
  import { uploadFile, login } from "@/utils/request";
  import Message from '@/components/message/index';
  import * as Api from './api';

  export default class ScanCard extends wepy.page {
    config = {
      navigationBarTitleText: '上传证件',
      navigationBarBackgroundColor: '#fff',
    };

    components = {
      message: Message,
    };

    onShow() {
      const login_access_token = wepy.getStorageSync('login_access_token') || '';
      if(!login_access_token){
        login();
      }
      this.getCardList();
    }

    onLoad(options){
      const { isNewCard = 0, isNoCard = 0, qryType = 2, isScan = 0 } = options;
      this.isNewCard = isNewCard;
      this.isNoCard = isNoCard;
      this.qryType = qryType;
      this.isScan = isScan;
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      DOMAIN,
      imgWidth: 0,
      imgHeight: 0,
      isNewCard: 0,
      isNoCard: 0,
      uploadProgress: '0%',
      idCardPath: '',
      idCardToken: '',
      cardInfo: {},
      qryType: '',
      isScan: 0,
    };
    // 检测是否已登录
    async getCardList(){
      const { data = {} } = await Api.getCardList();
      const { cardList = [] } = data;
      this.cardList = cardList;
      let isLogin = false;
      if (cardList.length > 0) {
        cardList.forEach((item) => {
          if (item.relationType == 1) {
            isLogin = true;
          }
        })
      }
      if(!isLogin && this.qryType != 1){
        const showModalRes = await wepy.showModal({
          title: '提示',
          content: '请登录后再操作',
          showCancel: false,
          confirmText: '确定',
          confirmColor: PRIMARY_COLOR,
        });
        if (showModalRes.confirm) {
          wepy.redirectTo({
            url: `/pages/bindcard/index/index?isScan=${this.isScan}`
          });
        }
      }
      this.$apply();
    }
    async readCardInfo(url){ // 读取照片数据
      wepy.showLoading({ title: '正在读取', mask: true });
      const { code, data = {}, msg } = await Api.readCardInfo({ url, cardType: 0 });
      wepy.hideLoading();
      if(code == 0){
        console.log(data);
        this.$preload('cardInfo', data);
        this.$navigate('/pages/bindcard/queryuserinfo/index', {
          ...data, isNewCard: this.isNewCard, isNoCard: this.isNoCard, idCardPath: this.idCardPath, idCardToken: this.idCardToken, qryType: this.qryType, isScan: this.isScan
        });
      }
    }
    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(url) {
        wepy.navigateTo({ url });
      },
      setImgSize(e){
        const { width, height } = e.detail;
        const basScale = 560 / 346;
        let scale = width >= (height * basScale) ? (560 / width) : (346 / height);
        scale = scale > 1 ? 1 : scale;
        this.imgWidth = width * scale;
        this.imgHeight = height * scale;
      },
      async bindSelIdCardPic(){
        const img = await wepy.chooseImage({
          count: 1
        });
        if(img && img.tempFilePaths && img.tempFilePaths[0]){
          this.idCardPath = img.tempFilePaths[0];
          this.imgWidth = 0;
          this.imgHeight = 0;
          this.$apply();
        }
      },
      async uploadCardImg(){

        const _ts = this;
        if(!this.idCardPath){
          wepy.showToast({
            title: '请选择证件照片',
            icon: 'none',
          });
          return false;
        }
        // wepy.showLoading({ title: this.uploadProgress, mask: true });
        wepy.showLoading({ mask: true });

        console.log('this.idCardPath', this.idCardPath)

        const uploadRes = await uploadFile(
          "/api/files/uploadpic",
          // '/hisServiceNew',
          this.idCardPath,
        );

        wepy.hideLoading();

        const data = uploadRes || JSON.parse(uploadRes);

        if(data.code == 0){
          this.uploadProgress = '0%';
          this.$apply();
          const dataRoot = data.data || {};
          if(dataRoot.url){
            _ts.readCardInfo(dataRoot.url);
            this.idCardToken = dataRoot.token;
          }
        } else {
          this.uploadProgress = '0%';
          this.$apply();
          wepy.showModal({
            title: '提示',
            content: data.msg || '上传失败',
            showCancel: false,
          });
        }

        // const uploadTask = wx.uploadFile({
        //   url: `${DOMAIN}/api/files/uploadpic`,
        //   filePath: this.idCardPath,
        //   name: 'upfile',
        //   formData: { serviceType: 'idcardocr', isPte: 'true', expiredSeconds: 30 },
        //   header: {
        //     "content-type": "application/x-www-form-urlencoded",
        //   },
        //   success: ({ data = '{}' }) => {
        //     try {
        //       data = JSON.parse(data);
        //     } catch(e) {
        //       if (data.indexOf('413') > 0) {
        //         data = { code: -1, msg: '图片过大，请上传不超过5M的图片' };
        //       } else {
        //         data = { code: -1, msg: '上传失败' };
        //       }
        //     }
        //     if(data.code == 0){
        //       this.uploadProgress = '0%';
        //       wepy.hideLoading();
        //       this.$apply();
        //       const dataRoot = data.data || {};
        //       if(dataRoot.url){
        //         _ts.readCardInfo(dataRoot.url);
        //         this.idCardToken = dataRoot.token;
        //       }
        //     } else {
        //       wepy.hideLoading();
        //       this.uploadProgress = '0%';
        //       this.$apply();
        //       wepy.showModal({
        //         title: '提示',
        //         content: data.msg || '上传失败',
        //         showCancel: false,
        //       });
        //     }
        //   },
        //   fail: (res) => {
        //     wepy.hideLoading();
        //     this.uploadProgress = '0%';
        //     this.$apply();
        //     wepy.showModal({
        //       title: '提示',
        //       content: '上传失败',
        //       showCancel: false,
        //     });
        //   }
        // });
        // uploadTask.onProgressUpdate((res) => {
        //   this.uploadProgress = res.progress;
        //   this.$apply();
        // });

      },
    };

  }
</script>
