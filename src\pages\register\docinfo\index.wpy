<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import {
  APPOINTMENT_REG_PAY,
  TODAY_REG_PAY,
  PRIMARY_COLOR
} from "@/config/constant";
import { getTimeSlot } from "@/utils/utils";
import Empty from "@/components/empty/index";
import WxsUtils from "../../../wxs/utils.wxs";
import BottomLogo from '@/components/bottomlogo/index';
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "医生主页",
    navigationBarBackgroundColor: '#fff',
    usingComponents: {
      wxparser: "plugin://wxparserPlugin/wxparser"
    }
  };

  components = {
    empty: Empty,
    "bottom-logo": BottomLogo
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  onShow() {
    const {
      subHisId = "",
      deptId = "",
      doctorId = "",
      scheduleDate = "",
      doctorRemark = "",
      positName,
      areaCode = ""
    } = this.$wxpage.options;
    this.doctorRemark = doctorRemark;
    this.positName = positName;
    this.areaCode = areaCode;
    this.doctorDetail({ subHisId, deptId, doctorId });
    this.dateScheduleList({ subHisId, deptId, doctorId, scheduleDate });
    if (this.activeSourceItem) {
      this.getConfirm(this.activeSourceItem);
    }
  }

  data = {
    
    areaCode: "",
    positName: "",
    // 互联网门诊科室ID
    internetList: ["0100", "0095", "0097", "0098"],
    // 是否时互联网门诊
    isInternet: false,
    // 医生信息详情
    docDetail: {},
    // 排班日期
    scheduleList: [],
    // 按日期获取医生排班列表
    scheduleDoctorList: {},
    // 日期补空位数组
    nullDateArr: [],
    // 选中日期，默认为false
    selectedDay: "",
    // 选中日期对应星期几，默认为false
    selectedWeekDay: "",
    // 日期展开状态
    expandDate: false,
    // 顶部tab，0：挂号，1：介绍
    tabIndex: 0,
    // 显示确认挂号弹窗
    showConfirm: false,
    // 确认挂号页面弹窗信息
    confirmData: {},
    // 选中就诊人信息
    activePatient: {},
    // 是否有过选中的号源，用于添加就诊人返回时刷新就诊人列表
    activeSourceItem: null,
    //停诊险
    parkedRisk: false,
    //医疗商业保险
    medicalSecure: false,
    //院区
    doctorRemark: "",
    //挂号地点确认弹窗
    areaShowConfirm: false,
    //上午剩余号源
    firstTotal: 0,
    //下午挂号列表
    secondTotal: 0
  };

  methods = {
    bindAddFav() {
      this.addFav();
    },
    bindCancelFav() {
      this.cancelFav();
    },
    bindChangeDate(item) {
      this.changeScheduleDoctorList(item);
    },
    bindChangeExpandDate(nextStatus) {
      this.expandDate = nextStatus;
    },
    /**
     * 修改tab状态（挂号、介绍）
     */
    bindChangeTabIndex(index) {
      if (this.tabIndex === index) {
        return;
      }
      this.tabIndex = index;
    },
    bindConfirmReg(item) {
      if (item.leftSource == 0 && this.docDetail.doctorLevel !== 9) {
        return;
      }
      this.activeSourceItem = item;
      // 现场挂号需要弹窗确认，互联网挂号不需要（按日期才需要，按医生挂号没有院区字段）
      if (this.scheduleDoctorList.visitAdress) {
        if (item.leftSource == 0 && this.docDetail.doctorLevel === 9) {
          // console.log(this.firstTotal, "ssadsadas", this.secondTotal);
          // 上午还有号源的时候不能点击
          if (
            item.scheduleId === this.scheduleDoctorList.itemList[0].scheduleId
          ) {
            if (this.firstTotal !== 0) {
              return;
            }
          }
          //下午午还有号源的时候不能点击
          if (
            item.scheduleId !== this.scheduleDoctorList.itemList[0].scheduleId
          ) {
            if (this.secondTotal !== 0) {
              return;
            }
          }
          this.getConfirm(item);
        } else {
          this.getConfirm(this.activeSourceItem);
        }
      } else {
        this.getConfirm(item);
      }
    },
    bindToggleConfirm(nextStatus) {
      this.toggleConfirm(nextStatus);
    },
    bindChangePatient(item) {
      this.changeActivePatient(item);
    },
    async bindRegisterConfirm() {
      // this.toggleConfirm(false);
      //测试完善信息屏蔽
      const {
        idNo,
        idType,
        patientName,
        relationType,
        patHisNo,
        patientId
      } = this.activePatient;
      // 获取当前挂号患者信息，判断 address 字段是否有值，如果没有就去完善信息，有就直接挂号
      const { data = {} } = await Api.getRelationList({
        idNo,
        patName: patientName,
        patHisNo,
        qryType: 1
      });
      const { items = [], resultCode = "" } = data;
      if (
        items.length > 0 &&
        resultCode == 0
      ) {
        const userInfo = items[0];
        // if (userInfo && userInfo.address) {
          this.generatorOrder();
        // } else {
        //   const query = JSON.stringify(this.$wxpage.options);
        //   const {
        //     subHisId = "",
        //     deptId = "",
        //     doctorId = "",
        //     scheduleDate = "",
        //     doctorRemark = "",
        //     positName = "",
        //     areaCode = ""
        //   } = query;
        //   const fromQuery = `subHisId=${subHisId}&deptId=${deptId}&doctorId=${doctorId}&scheduleDate=${scheduleDate}&doctorRemark=${doctorRemark}&positName=${positName}&areaCode=${areaCode}`;
        //   wepy.navigateTo({
        //     url: `/package1/pages/completeinfo/index?isNewCard=0&idNo=${idNo}&idType=${idType}&patientName=${patientName}&qryType=${relationType}&pid=${patHisNo}&address=&isScan=0&query=${fromQuery}`
        //   });
        // }
      }
    },
    // 走候补流程
    async bindAlternateConfirm() {
      const { deptId, doctorId } = this.docDetail;
      const { patCardNo, patHisNo } = this.activePatient;
      const { scheduleId } = this.activeSourceItem;
      const ids = scheduleId.split("|");
      const { code, data } = await Api.confirmWatiQueue({
        deptId,
        doctorId,
        scheduleDate: this.selectedDay,
        patCardNo,
        scheduleTypeId: ids[0],
        regConfigId: ids[1]
      });
      const { resultCode, resultMessage, serialNumber, waitId } = data;
      if (resultCode === "0") {
        // 成功跳转
        wepy.navigateTo({
          url: `/pages/register/alternatedetail/index?patHisNo=${patHisNo}&waitId=${waitId}`
        });
      } else {
        // 失败提示
        wepy.showModal({
          title: "候补失败",
          content: resultMessage,
          showCancel: false,
          confirmText: "确认",
          confirmColor: "#3ecdb5"
        });
      }
    },
    bindGoBindUser() {
      const {
        idNo,
        patientName,
        patHisNo,
        patientId
      } = this.activePatient;
      console.log(this.activePatient, '======243')
      //this.toggleConfirm(false);
      wepy.navigateTo({    
        // url: "/pages/bindcard/queryuserinfo/index"
        url: `/pages/usercenter/queryrelation/index?patientName=${patientName}&patHisNo=${patHisNo}&idNo=${idNo}&patientId=${patientId}`
        // url: "/pages/usercenter/userlist/index"
      });
    },
    //停诊险
    bindChangeCheck(flg) {
      if (flg == 1) {
        this.parkedRisk = !this.parkedRisk;
      } else {
        this.medicalSecure = !this.medicalSecure;
      }
    },
    // // 挂号确认
    // agree(flag) {
    //   this.areaShowConfirm = !this.areaShowConfirm;
    //   if (flag) {
    //     this.getConfirm(this.activeSourceItem);
    //   }
    // }
  };

  /**
   * 添加收藏
   */
  async addFav() {
    const { subHisId = "", deptId = "", doctorId = "" } = this.docDetail || {};
    const { code, data = {}, msg } = await Api.addFav({
      subHisId,
      deptId,
      doctorId
    });
    if (code !== 0) {
      return;
    }
    wepy.showToast({
      title: "收藏成功",
      icon: "success",
      duration: 1500,
      mask: true
    });
    this.docDetail.favoriteStatus = 1;
    this.$apply();
  }

  /**
   * 取消收藏
   */
  async cancelFav() {
    const { subHisId = "", deptId = "", doctorId = "" } = this.docDetail || {};
    const { code, data = {}, msg } = await Api.cancelFav({
      subHisId,
      deptId,
      doctorId
    });
    if (code !== 0) {
      return;
    }
    wepy.showToast({
      title: "取消收藏成功",
      icon: "success",
      duration: 1500,
      mask: true
    });
    this.docDetail.favoriteStatus = 0;
    this.$apply();
  }

  /**
   * 切换确认挂号弹窗状态
   * @param nextStatus
   */
  toggleConfirm(nextStatus) {
    this.showConfirm = nextStatus;
  }

  /**
   * 获取医生信息
   */
  async doctorDetail(item = {}) {
    const { subHisId = "", deptId = "", doctorId = "" } = item;
    const param = { subHisId, deptId, doctorId };
    const { code, data = {}, msg } = await Api.doctorDetail(param);
    if (code !== 0) {
      return;
    }
    data.doctorSummary = (data.doctorSummary || "").replace(
      /<[^>]*>|<\/[^>]*>/gm,
      ""
    );
    this.docDetail = data;
    if (this.internetList.indexOf(data.deptId) != -1) {
      this.isInternet = true;
    }
    this.$apply();
  }

  /**
   * 获取排班日期
   */
  async dateScheduleList(item = {}) {
    const {
      subHisId = "",
      deptId = "",
      doctorId = "",
      scheduleDate: nextDate = ""
    } = item;
    const param = {
      subHisId,
      deptId,
      doctorId,
      scheduleDate: nextDate,
      transParam: this.areaCode
        ? JSON.stringify({ areaCode: this.areaCode })
        : ""
    };
    const { code, data = {}, msg } = await Api.dateScheduleList(param);
    if (code !== 0) {
      return;
    }
    const { scheduleList = [] } = data;
    const weekMap = { 一: 0, 二: 1, 三: 2, 四: 3, 五: 4, 六: 5, 日: 6 };
    // 获取当前日期到周一有几天
    const nullDateCount =
      scheduleList[0] && scheduleList[0].weekDate
        ? weekMap[scheduleList[0].weekDate] || 0
        : 0;
    // 生成一个长度为差距天数的数组
    this.nullDateArr = [1, 2, 3, 4, 5, 6, 7].slice(0, nullDateCount);
    scheduleList.forEach(item => {
      item.date = `${item.scheduleDate.split('-')[1]}/${item.scheduleDate.split('-')[2]}`
    })
    this.scheduleList = scheduleList

    // 如果带日期进入医生排班页面，优先使用传递日期
    if (nextDate) {
      this.changeScheduleDoctorList({
        subHisId,
        deptId,
        doctorId,
        scheduleDate: nextDate
      });
    } else {
      // 获取初始选中日期，保存选中日期，并获取选中日期的排班列表
      this.expandDate = true;
      for (let i = 0; i < scheduleList.length; i++) {
        const { selected, scheduleDate } = scheduleList[i];

        if (selected) {
          this.changeScheduleDoctorList({
            subHisId,
            deptId,
            doctorId,
            scheduleDate
          });
          break;
        }
      }
    }
    this.$apply();
  }

  /**
   * 切换排班日期，获取排班医生列表
   * @param item
   */
  async changeScheduleDoctorList(item = {}) {
    const {
      subHisId = "",
      deptId = "",
      doctorId = "",
      scheduleDate: nextDate = ""
    } = item;
    if (this.selectedDay === nextDate) {
      return;
    }
    this.selectedDay = nextDate;

    const { code, data = {}, msg } = await Api.scheduleList({
      subHisId,
      deptId,
      doctorId,
      scheduleDate: nextDate,
      transParam: this.areaCode
        ? JSON.stringify({ areaCode: this.areaCode })
        : ""
    });
    if (code !== 0) {
      this.scheduleDoctorList.itemList = [];
      this.$apply();
      return;
    }
    const { scheduleDate } = data;
    // 重新校验日期，确保数据与日期一致
    if (this.selectedDay !== scheduleDate) {
      return;
    }

    this.scheduleDoctorList = data;
    this.$apply();
    if (data.itemList.length > 0) {
      let firstCount = 0;
      let secondCount = 0;
      const firstDoctorList = data.itemList.filter(
        i => i.scheduleId === data.itemList[0].scheduleId
      );
      const sencondDoctorList = data.itemList.filter(
        i => i.scheduleId !== data.itemList[0].scheduleId
      );
      firstDoctorList.map(i => (firstCount = i.leftSource + firstCount));
      sencondDoctorList.map(i => (secondCount = i.leftSource + secondCount));
      this.firstTotal = firstCount;
      this.secondTotal = secondCount;
      this.$apply();
    }
  }

  /**
   * 获取确认挂号信息
   * @param item
   */
  async getConfirm(item) {
    const {
      scheduleId = "",
      visitPeriod = "",
      visitBeginTime = "",
      visitEndTime = ""
    } = item;
    const { subHisId = "", deptId = "", doctorId = "" } = this.$wxpage.options;
    const { selectedDay: scheduleDate = "" } = this;

    const { code, data = {}, msg } = await Api.registerConfirm({
      subHisId,
      deptId,
      doctorId,
      scheduleDate,
      scheduleId,
      visitPeriod,
      visitBeginTime,
      visitEndTime
    });
    if (code !== 0) {
      return;
    }
    this.confirmData = data;
    if (await this.initActivePatient()) {
      this.toggleConfirm(true);
    }
    this.$apply();
  }

  /**
   * 获取初始激活就诊人
   */
  async initActivePatient() {
    const { patientList = [] } = this.confirmData || {};
    // if (patientList.length <= 0) {
    //   return false;
    // }
    for (let i = 0; i < patientList.length; i++) {
      // 如果是互联网门诊，就只显示登录的就诊人
      if (this.isInternet) {
        if (patientList[i].relationType == 1) {
          this.activePatient = patientList[i];
          break;
        }
      } else {
        // if (patientList[i].isDefault == 1) {
        this.activePatient = patientList[0];
        break;
        // }
      }
    }
    if (Object.keys(this.activePatient).length == 0) {
      const showModalRes = await wepy.showModal({
        title: "提示",
        content: "请登录后再操作",
        showCancel: false,
        confirmText: "确定",
        confirmColor: PRIMARY_COLOR
      });
      if (showModalRes.confirm) {
        wepy.reLaunch({
          url: "/pages/bindcard/index/index"
        });
      }
      return false;
    }
    return true;
  }

  /**
   * 切换就诊人
   * @param item
   */
  changeActivePatient(item) {
    if (this.activePatient.patientId == item.patientId) {
      return;
    }
    this.activePatient = item;
  }

  /**
   * 锁号
   */
  async generatorOrder() {
    let payFlag;
    const { patientId } = this.activePatient;
    if (!patientId) {
      return false;
    }
    const { subHisId = "", deptId = "", doctorId = "" } = this.$wxpage.options;
    const { scheduleId = "" } = this.activeSourceItem;
    const {
      scheduleDate = "",
      visitPeriod = "",
      visitBeginTime = "",
      visitEndTime = "",
      registerType = ""
    } = this.confirmData;
    if (
      (registerType === "1" && APPOINTMENT_REG_PAY) ||
      (registerType === "2" && TODAY_REG_PAY)
    ) {
      payFlag = 1;
    } else {
      payFlag = 2;
    }
    const { code, data = {}, msg } = await Api.generatorOrder({
      subHisId,
      deptId,
      doctorId,
      scheduleDate,
      scheduleId,
      visitPeriod,
      visitBeginTime,
      visitEndTime,
      patientId,
      payFlag
    });
    if (code !== 0) {
      return;
    }
    const { orderId = "", type = "" } = data;
    this.registerPayOrder({ orderId, type });
    this.$apply();
  }

  /**
   * 获取支付页展示信息
   */
  getBizContent(item = {}) {
    const time = getTimeSlot(item);

    return [
      { key: "费用类型", value: item.registerTypeName },
      { key: "就诊科室", value: item.deptName },
      { key: "医生名称", value: item.doctorName },
      { key: "就诊日期", value: `${item.scheduleDate} ${item.visitWeekName}` },
      { key: "就诊时段", value: time },
      { key: "就诊人", value: item.patientName },
      { key: "就诊卡号", value: item.patCardNo }
    ];
  }

  /**
   * 挂号支付下单
   */
  async registerPayOrder(item = {}) {
    let regType;
    let bizContent;
    const { orderId = "", type = "" } = item;
    const { patientName = "", patCardNo = "" } = this.activePatient;
    regType = type == 1 ? "YYGH" : "DBGH";
    try {
      bizContent = JSON.stringify(
        this.getBizContent({
          ...this.confirmData,
          patientName,
          patCardNo
        }) || []
      );
    } catch (e) {
      bizContent = "[]";
    }

    const { code, data = {}, msg } = await Api.registerPayOrder({
      orderId,
      bizContent
    });
    if (code !== 0) {
      return;
    }
    const { payOrderId = "" } = data;
    wepy.navigateTo({
      url: `/pages/pay/index?payOrderId=${payOrderId}&orderId=${orderId}&type=${regType}`
    });
  }
}
</script>
