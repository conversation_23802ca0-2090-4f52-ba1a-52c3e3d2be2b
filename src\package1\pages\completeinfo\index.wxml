<view class="container">
  <view class="patInfo-card">
    <view class="patInfo-card-top">
      <view class="patInfo-card-info">
        <view class="info-name">{{ dataInfo.patName || dataInfo.patientName }}</view>
      </view>
      <view class="patInfo-card-other">
        {{ dataInfo.patSex }}   {{ dataInfo.patBirth }}
      </view>
      <view class="patInfo-card-other">证件号码：{{ dataInfo.idNo || idNo }}</view>
      <!-- <view class="patInfo-card-other">手机号码：{{ patientMobile }}</view> -->
    </view>
  </view>
  <form bindsubmit="formSubmit" report-submit='true'>
    <view class="form-container">
      <view class="patInfo-list">
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">籍贯</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AF" value="{{indexAF}}" range="{{natArrList}}">
              <view class="picker">
                <input class="picker-info" disabled type="text" value="{{natArrList[indexAF]}}"
                  placeholder-style="{{ dataInfo.nativeName ? '': ''}}"
                  placeholder="{{dataInfo.nativeName || '请选择籍贯'}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">职业</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AG" value="{{indexAG}}" range="{{jobArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.jobName || '请选择职业'}}"
                  placeholder-style="{{ dataInfo.jobName ? '': ''}}" value="{{jobArrList[indexAG]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">民族</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AC" value="{{indexAC}}" range="{{nationArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.nationName || '请选择民族'}}"
                  placeholder-style="{{ dataInfo.nationName ? '': ''}}" value="{{nationArrList[indexAC]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">文化程度</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="ZC" value="{{indexZC}}" range="{{culArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.educationName || '请输入文化程度'}}"
                  placeholder-style="{{ dataInfo.educationName ? '': ''}}" value="{{culArrList[indexZC]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">邮政编码</text>
          </view>
          <view class="listitem-body">
            <input @input="inputTrigger" id="postalCode" type="number" placeholder-style="{{ dataInfo.postalCode ? '': ''}}"
              placeholder="请输入邮政编码" value="{{dataInfo.postalCode}}" maxlength="6" />
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">身份证地址</text>
          </view>
          <view class="listitem-body">
            <input type="text" @input="inputTrigger" id="idCardAddress" placeholder-style="{{ dataInfo.idCardAddress ? '': ''}}"
              placeholder="{{'请输入身份证地址'}}" value="{{dataInfo.idCardAddress}}" />
          </view>
        </view>
        <view class="patInfo-listitem no-after">
          <view class="listitem-head">
            <text class="list-title require">通讯地址</text>
          </view>
          <view class="listitem-body mb-32">
            <picker mode="region" bindchange="bindRegionChange" value="{{dataInfo.areaCode || []}}">
              <view class="picker picker-wrap" style="color:{{dataInfo.areaCode && dataInfo.areaCode.length > 0?'':'#bbb'}}">
                <block wx:if="{{dataInfo.areaCode && dataInfo.areaCode.length > 0}}">
                  <view class="picker-info">{{dataInfo.areaName[0]}}{{dataInfo.areaName[1]}}{{dataInfo.areaName[2]}}</view>
                </block>
                <block wx:else>
                  <view class="picker-info placeholder-text">请选择所在省市区/县</view>
                </block>
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
          <view class="listitem-body">
            <input type="text" @input="inputTrigger" id="address" placeholder-style="{{ dataInfo.address ? '': ''}}"
              placeholder="{{'请输入详细地址，具体到街道小区'}}" value="{{dataInfo.address}}" />
          </view>
        </view>
      </view>
    </view>
    <view class="patInfo-btn">
      <button class="binduser-btn_line" formType="submit">提交</button>
    </view>
  </form>
</view>

<toptip :toptip.sync="toptip" />