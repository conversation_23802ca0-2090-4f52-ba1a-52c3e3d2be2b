<view class="p-page">
  <block wx:if="{{orderList.length > 0}}">
    <view class="m-list">
      <block
        wx:for="{{orderList || []}}"
        wx:key="index"
      >
        <view
          class="list-item list-item-{{WxsUtils.convertListStatus(item.status)}}"
          @tap="bindGoDetail({{item}})"
        >
          <view class="item-main">
            <view class="main-tit">
              <view class="item-icon">
                <image class="item-status" mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/list-{{WxsUtils.convertListStatus(item.status)}}.png"></image>
              </view>
              <text>{{item.status === 'S' ? '缴费成功' : item.status === 'F' ? '缴费失败' : '缴费异常'}}</text>
              <text class="unit-label" wx:if="{{item.refundStatus == 1 || item.refundStatus == 2 || item.refundStatus == 3}}">有退款</text>
              <text wx:if="{{item.xcxSamplePrefectFlag == '1'}}" class="unit-label {{item.sampleReadyFlag == '1' ? 'isComplete' : 'unComplete' }}">样本信息</text>
              <text wx:if="{{item.xcxSamplePrefectFlag == '1'}}" class="unit-label  {{item.sjzReadyFlag == '1' ? 'isComplete' : 'unComplete' }}">受检者信息</text>
            </view>
          </view>
          <view class="item-extra">
            <view class="main-txt">就诊人：{{item.patientName}}</view>
            <view class="main-txt">缴费时间：{{item.payedTime}}</view>
            <view class="extra-tit">支付金额：<text class="fee">￥{{WxsUtils.formatMoney(item.totalRealFee,100)}}</text></view>
          </view>
        </view>
      </block>
    </view>
  </block>
  <block wx:else>
    <empty>
      <block slot="text">暂未查询到相关信息</block>
    </empty>
  </block>
</view>