<view class="p-page">
  <view class="page-body">
    <view class="card-panel-title">请输入被推荐病友信息：</view>
    <form bindsubmit="formSubmit" report-submit='true'>
      <view class="bindcard-list">
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title"><text class="color-red">*</text>病友姓名</text>
          </view>
          <view class="listitem-body">
            <input
              class="m-content {{errorElement.patientName ? 'o-error' : ''}}" placeholder="请输入病友姓名" 
              cursor-spacing="{{CURSOR_SPACING}}" placeholder-style="color:{{errorElement.patientName ? errorColor : placeholderColor}}"
              maxlength="8" id="patientName" @input="inputTrigger" @focus="resetThisError" value="{{options.name}}"
            />
          </view>
        </view>
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title"><text class="color-red">*</text>病友手机号</text>
          </view>
          <view class="listitem-body">
            <input class="m-content {{errorElement.patientMobile ? 'o-error' : ''}}" type="number" placeholder="请输入病友手机号码"
              cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError" disabled="{{isReadOnlyMobile}}"
              placeholder-style="color:{{errorElement.patientMobile ? errorColor : placeholderColor}}" value="{{queryPatientMobile}}"
              id="patientMobile" maxlength="11" @input="inputTrigger"
            />
          </view>
        </view>
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title">身份证</text>
          </view>
          <view class="listitem-body">
            <input class="m-content {{errorElement.medicalCode ? 'o-error' : ''}}" type="number" placeholder="请输入病友身份证"
              cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError"
              placeholder-style="color:{{errorElement.medicalCode ? errorColor : placeholderColor}}" value="{{queryMedicalCode}}"
              id="medicalCode" maxlength="18" @input="inputTrigger"
            />
          </view>
        </view>
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title">备注信息</text>
          </view>
          <view class="listitem-body">
            <input
              class="m-content {{errorElement.patientAddress ? 'o-error' : ''}}" type="text" placeholder="请输入备注信息"
              maxlength="80" cursor="60" cursor-spacing="{{CURSOR_SPACING}}"
              placeholder-style="color:{{errorElement.patientAddress ? errorColor : placeholderColor}}"
              id="patientAddress" @input="inputTrigger" @focus="resetThisError" value="{{options.address}}"
            />
          </view>
        </view>
      </view>
    </form>
  </view>
  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit" @tap="getAuth">立即推荐</button>
    <view class="binduser-btn_line cancel-btn" @tap="goBack">取消</view>
  </view>
</view>
