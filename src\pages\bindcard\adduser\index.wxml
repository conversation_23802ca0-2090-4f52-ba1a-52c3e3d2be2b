<view class="container">
  <form bindsubmit="formSubmit" report-submit='true'>
    <view class="form-container">
      <view class="patInfo-list">
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title require">姓名</text>
          </view>
          <view class="listitem-body {{patientName ? 'default-bg' : 'border-bg'}}">
            <input @input="inputTrigger" id="{{patientName}}" type="number" placeholder="请输入手机号码" value="{{patientName}}" maxlength="11" disabled="{{mobileDisabled}}" class="{{mobileDisabled ? 'disabled' : ''}}" />
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title require">身份证号</text>
          </view>
          <view class="listitem-body {{idNo ? 'default-bg' : 'border-bg'}}">
            <input @input="inputTrigger" id="{{idNo}}" type="number" placeholder="请输入手机号码" value="{{idNo}}" maxlength="18" disabled="{{mobileDisabled}}" class="{{mobileDisabled ? 'disabled' : ''}}" />
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title require">手机号码</text>
          </view>
          <view class="listitem-body {{patientMobile ? 'default-bg' : 'border-bg'}}">
            <input @input="inputTrigger" id="patientMobile" type="number" placeholder="请输入手机号码" value="{{patientMobile}}" maxlength="11" disabled="{{mobileDisabled}}" class="{{mobileDisabled ? 'disabled' : ''}}" />
          </view>
        </view>
        <!--<block wx:if="{{idType != 1}}">
          <view class="patInfo-listitem">
            <view class="listitem-head">
              <view class="list-title require">性别</view>
            </view>
            <view class="listitem-body no-bg">
              <view class="m-content">
                <radio-group  @change="inputTrigger" id="patSex">
                  <label class="binduser-radio">
                    <radio value="男" checked="{{dataInfo.patSex == '男'}}" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">男</text>
                  </label>
                  <label class="binduser-radio">
                    <radio value="女" checked="{{dataInfo.patSex == '女'}}" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">女</text>
                  </label>
                </radio-group>
              </view>
            </view>
          </view>
          <view class="patInfo-listitem">
            <view class="listitem-head">
              <view class="list-title require">出生日期</view>
            </view>
            <picker
              class="listitem-body" mode="date"
              @change="inputTrigger" id="birthday"
            >
              <input
                class="m-content {{errorElement.birthday ? 'o-error' : ''}}" placeholder="请选择出生日期" disabled
                placeholder-style="color:{{errorElement.birthday ? errorColor : placeholderColor}}" value="{{dataInfo.birthday || birthday}}"
              />
            </picker>
          </view>
        </block>-->
        <!--<view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title require">婚姻状况（非助孕病人可以选择未说明的婚姻状况）</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="ZR" value="{{indexZR}}" range="{{marrArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled
                  value="{{marrArrList[indexZR]}}" placeholder="请选择婚姻状况" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>-->
        <view class="patInfo-listitem no-after">
          <view class="listitem-head">
            <text class="list-title require">家庭成员关系</text>
          </view>
          <view class="listitem-body border-bg">
            <picker bindchange="pickerChange" id="JT" value="{{indexJT}}" range="{{famArrList}}" range-key="familyTypeName">
              <view class="picker">
                <input class="picker-info" type="text" disabled
                  placeholder-style="{{ dataInfo.familyMemberName ? '': ''}}" value="{{famArrList[indexJT].familyTypeName || dataInfo.familyMemberName}}"
                  placeholder="请选择家庭成员关系" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view>
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">籍贯</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AF" value="{{indexAF}}" range="{{natArrList}}">
              <view class="picker">
                <input class="picker-info" disabled type="text" value="{{natArrList[indexAF]}}"
                  placeholder-style="{{ dataInfo.nativeName ? '': ''}}"
                  placeholder="{{dataInfo.nativeName || '请选择籍贯'}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view> -->
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">职业</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AG" value="{{indexAG}}" range="{{jobArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.jobName || '请选择职业'}}"
                  placeholder-style="{{ dataInfo.jobName ? '': ''}}" value="{{jobArrList[indexAG]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view> -->
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">民族</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="AC" value="{{indexAC}}" range="{{nationArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.nationName || '请选择民族'}}"
                  placeholder-style="{{ dataInfo.nationName ? '': ''}}" value="{{nationArrList[indexAC]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view> -->
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">文化程度</text>
          </view>
          <view class="listitem-body">
            <picker bindchange="pickerChange" id="ZC" value="{{indexZC}}" range="{{culArrList}}">
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.educationName || '请输入文化程度'}}"
                  placeholder-style="{{ dataInfo.educationName ? '': ''}}" value="{{culArrList[indexZC]}}" />
                <view class="item-arrow"></view>
              </view>
            </picker>
          </view>
        </view> -->
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">邮政编码</text>
          </view>
          <view class="listitem-body">
            <input @input="inputTrigger" id="postalCode" type="number" placeholder-style="{{ dataInfo.postalCode ? '': ''}}"
              placeholder="请输入邮政编码" value="{{dataInfo.postalCode}}" maxlength="6" />
          </view>
        </view> -->
        <!-- <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">身份证地址</text>
          </view>
          <view class="listitem-body">
            <input type="text" @input="inputTrigger" id="idCardAddress" placeholder-style="{{ dataInfo.idCardAddress ? '': ''}}"
              placeholder="{{'请输入身份证地址'}}" value="{{dataInfo.idCardAddress}}" />
          </view>
        </view> -->
        <view class="patInfo-listitem no-after">
          <view class="listitem-head">
            <text class="list-title require">家庭住址</text>
          </view>
          <view class="listitem-body border-bg">
            <input type="text" @input="inputTrigger" id="address" placeholder-style="{{ dataInfo.address ? '': ''}}"
              placeholder="{{'请输入家庭住址'}}" value="{{dataInfo.address}}" />
          </view>
        </view>
      </view>
    </view>

    <!--<block wx:if="{{(indexJT == 0 || indexJT == 1) && indexZR == 1}}">
      <view class="form-container">
        <view class="patInfo-part">
          <text class="list-title">配偶信息</text>
        </view>
        <view class="patInfo-list">
          <view class="patInfo-listitem">
            <view class="listitem-head">
              <text class="list-title require">配偶姓名</text>
            </view>
            <view class="listitem-body">
              <input id="spouseName" @input="inputTrigger" type="text" placeholder-style="" placeholder="请输入配偶姓名" maxlength="20" value="{{spouseName || spouseInfo.patName}}" disabled="{{spouseInfo.patName}}" />
            </view>
          </view>
          <view class="patInfo-listitem">
            <block wx:if="{{isOtherType == '1' || isShowSpouseInfo}}">
              <picker class="listitem-head" range="{{hisConfig.idTypes}}" range-key="dictValue" bindchange="changeIdType('spouse')" style="display: block;" data-prop="{{hisConfig.idTypes.length > 1 ? 'idTypes' : ''}}">
                <view class="list-title require {{hisConfig.idTypes.length > 1 ? 'list-title_select' : ''}}">{{hisConfig.idTypes[spouseInfo.idTypesIdx].dictValue}}</view>
              </picker>
            </block>
            <block wx:if="{{isOtherType == '0' && !isShowSpouseInfo}}">
              <view class="listitem-head">
                <text class="list-title require">身份证</text>
              </view>
            </block>
            <view class="listitem-body">
              <input id="spouseIdNo" @input="inputTrigger" maxlength="{{hisConfig.idTypes[spouseInfo.idTypesIdx].dictKey == 1 ? 18 : -1 }}" placeholder-style="" type="idcard" placeholder="请输入配偶{{hisConfig.idTypes[spouseInfo.idTypesIdx].dictValue}}号码" value="{{spouseIdNo || spouseInfo.idNo}}" disabled="{{spouseInfo.idNo}}" />
            </view>
          </view>

          <view wx:if="{{spouseInfo.idTypesIdx != 0}}" class="patInfo-listitem">
            <view class="listitem-head">
              <view class="list-title">配偶出生日期</view>
            </view>
            <picker
              class="listitem-body" mode="date"
              @change="inputTrigger" id="spouseBirthday"
            >
              <input
                class="m-content {{errorElement.birthday ? 'o-error' : ''}}" placeholder="请选择出生日期" disabled
                placeholder-style="color:{{errorElement.birthday ? errorColor : placeholderColor}}" value="{{spouseBirthday || spouseInfo.patBirth}}"
              />
            </picker>
          </view>
          <view class="patInfo-listitem no-after">
            <view class="listitem-head">
              <text class="list-title require">手机号码</text>
            </view>
            <view class="listitem-body">
              <input id="spousePhone" @input="inputTrigger" type="number" placeholder="请输入配偶手机号码" placeholder-style="" value="{{spousePhone || spouseInfo.telephone}}" disabled="{{spouseInfo.telephone}}"
                maxlength="11" />
            </view>
          </view>
          <view class="item">
            <view class="item-main">
              <view class="main-title">{{item.name}}</view>
              <view class="main-text">{{item.desc}}</view>
            </view>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </block>-->

    <block>
      <view class="form-container">
        <view wx:if="{{memberList.length}}" class="member-title">家庭成员信息</view>
        <block wx:for="{{memberList}}" wx:for-item="itm" wx:for-index="idx" wx:key="idx">
          <view class="patInfo-part" wx:if="{{itm.memberName}}">
            <text class="list-title">{{itm.memberName}}信息</text>
          </view>
          <view class="patInfo-list">
            <view class="patInfo-listitem">
              <view class="listitem-head">
                <text class="list-title require">成员关系</text>
                <view class="m-delete-member" @tap="removeMemberItem({{idx}})">删除</view>
              </view>
              <view class="listitem-body border-bg">
                <picker bindchange="bindMemberCode({{itm}})" value="{{itm.familyMemberCodeIndex}}" range="{{famArrList}}" range-key="familyTypeName">
                  <view class="picker">
                    <input class="picker-info" type="text" disabled value="{{famArrList[itm.familyMemberCodeIndex].familyTypeName}}"
                      placeholder-style="{{ itm.familyMemberName ? '': ''}}"
                      placeholder="请选择家庭成员关系" />
                    <view class="item-arrow"></view>
                  </view>
                </picker>
              </view>
            </view>
            <view class="patInfo-listitem">
              <view class="listitem-head">
                <text class="list-title require">成员姓名</text>
              </view>
              <view class="listitem-body border-bg">
                <input @input="inputTriggerMember" data-id="name" data-index="{{idx}}" type="text" placeholder-style="" placeholder="请输入{{itm.memberName}}姓名" maxlength="20" />
              </view>
            </view>
            <view class="patInfo-listitem">
              <block wx:if="{{isOtherType == '1'}}">
                <picker class="listitem-head" range="{{hisConfig.idTypes}}" range-key="dictValue" bindchange="changeIdType('{{idx}}')" style="display: block;" data-prop="{{hisConfig.idTypes.length > 1 ? 'idTypes' : ''}}">
                  <!-- <text class="list-title">身份证号码</text> -->
                  <!-- 身份证 -->
                  <view class="list-title require {{hisConfig.idTypes.length > 1 ? 'list-title_select' : ''}}">{{hisConfig.idTypes[itm.idTypesIdx || 0].dictValue}}</view>
                </picker>
              </block>
              <block wx:if="{{isOtherType == '0'}}">
                <view class="listitem-head">
                  <text class="list-title require">身份证</text>
                </view>
              </block>
              <view class="listitem-body border-bg">
                <input  @input="inputTriggerMember" data-id="idNo" maxlength="{{hisConfig.idTypes[itm.idTypesIdx].dictKey == 1 ? 18 : -1 }}" data-index="{{idx}}" placeholder-style="" type="idcard" placeholder="请输入{{itm.memberName}}{{hisConfig.idTypes[itm.idTypesIdx || 0].dictValue}}号码" />
              </view>
            </view>
            <block wx:if="{{itm.idTypesIdx > 0}}">
              <view class="patInfo-listitem">
                <view class="listitem-head">
                  <view class="list-title">{{itm.memberName}}性别</view>
                </view>
                <view class="listitem-body border-bg">
                  <view class="m-content">
                    <radio-group  @change="inputTriggerMember" data-id="patSex" data-index="{{idx}}">
                      <label class="binduser-radio">
                        <radio value="男" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">男</text>
                      </label>
                      <label class="binduser-radio">
                        <radio value="女" class="binduser-radio_object" color="#3ECDB5" /><text class="binduser-radio_text">女</text>
                      </label>
                    </radio-group>
                  </view>
                </view>
              </view>
              <view class="patInfo-listitem">
                <view class="listitem-head">
                  <view class="list-title">{{itm.memberName}}出生日期</view>
                </view>
                <picker
                  class="listitem-body border-bg" mode="date"
                  @change="inputTriggerMember" data-id="birthday" data-index="{{idx}}"
                >
                  <input
                    class="m-content {{errorElement.birthday ? 'o-error' : ''}}" placeholder="请选择出生日期" disabled
                    placeholder-style="color:{{errorElement.birthday ? errorColor : placeholderColor}}" value="{{itm.birthday}}"
                  />
                </picker>
              </view>
            </block>
            <view class="patInfo-listitem">
              <view class="listitem-head">
                <text class="list-title require">手机号码</text>
              </view>
              <view class="listitem-body border-bg">
                <input  @input="inputTriggerMember" data-id="patientMobile" data-index="{{idx}}" type="number" placeholder="请输入{{itm.memberName}}手机号码" placeholder-style=""
                  maxlength="11" />
              </view>
            </view>
            
          </view>
        </block>
      </view>
    </block>
    <view class="patInfo-tips">
      <text class="list-title require">本院实行实名就诊，请如实填写相关信息以便完成绑定。遗传类问题请添加其他家庭成员信息。
        <!-- <view wx:if="{{qryType != 1}}">遗传类问题请添加其他家庭成员信息。</view> -->
      </text>
    </view>
    <view class="patInfo-btn">
      <button class="binduser-btn_line" formType="submit">立即绑定</button>
    </view>
    <!--<view class="add-member" @tap="addMember">添加其他家庭成员信息</view>-->
    <bottom-logo class="add-user-bottom" />
  </form>
</view>

<toptip :toptip.sync="toptip" />

