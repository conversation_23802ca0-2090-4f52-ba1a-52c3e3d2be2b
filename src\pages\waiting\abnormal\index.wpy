<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import { TYPE_MAP } from '@/config/constant';
  import * as Api from './api';
  import WxsUtils from '../../../wxs/utils.wxs';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '操作繁忙',
    };

    data = {
      // 页面参数
      options: {},
      // 订单详情
      orderDetail: {},
    };

    components = {};

    wxs = {
      WxsUtils: WxsUtils,
    };

    onLoad(options) {
      this.getDetail(options);
    }

    onUnload() {
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      bindRetryOrder(){
        this.retryOrder();
      }
    };

    events = {};

    async getDetail(item = {}) {
      const { orderId, type } = item;

      const { code, data = {} } = await Api.getOrderDetail(type, { orderId });
      if (code !== 0) {
        return;
      }
      this.orderDetail = data;
      this.$apply();
    };

    async retryOrder() {
      const { orderId, type } = this.$wxpage.options;

      const { code, data = {}, msg } = await Api.manualNotify({
        orderId,
        bizType: TYPE_MAP[type] || 'default',
      });
      if (code !== 0) {
        return;
      }
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?orderId=${orderId}&type=${type}&from=abnormal&time=15`
      });
    };
  }
</script>
