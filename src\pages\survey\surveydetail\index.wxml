<import src="../template/template.wxml" />
<block >
  <view class="p-page">
    <scroll-view scroll-y style="height: {{scrollHeight}}px;" bindscroll="scroll" scroll-into-view="{{scrollView}}">
      <view class="survey-head" wx:if="{{!hideHeader}}">
        <view class="survey-title">{{quesInfo.examTitle}}</view>
        <view class="survey-time">有效期：{{quesInfo.beginTime}} 至{{quesInfo.endTime}}</view>
        <view class="survey-des">
          <view wx:for="{{quesInfo.examDesc}}" wx:key="index" style="text-indent: 4ch;">{{item}}</view>
          <view class="publish-unit">发布单位：{{quesInfo.releaseCompany}}</view>
          <view class="publish-unit text-link" wx:if="{{id == 13}}" bindtap="toggleSurveyModel">查看详情</view>
        </view>
        <view class="survey-target">
          <view class="dept" wx:if="{{quesInfo.deptName}}">评价科室：<text>{{quesInfo.deptName}}</text></view>
          <view class="doc" wx:if="{{quesInfo.doctorName}}">评价医生：<text>{{quesInfo.doctorName}}</text></view>
        </view>
      </view>
      <view class="survey-body">
        <block wx:for="{{titleList}}" wx:for-index="subjectListIdx" wx:key="key" wx:for-item="question">
          <block wx:if="{{(id == '13' && (subjectListIdx <= startExpandIdx || ( subjectListIdx > startExpandIdx && isSpecialDis === false))) || id != '13'}}">
            <!-- 单选题 -->
            <block wx:if="{{question.questionsType == '0'}}">
              <template is="individual" data="{{individual: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 下拉单选题 -->
            <!-- <block wx:if="{{question.questionsType == '7'}}">
              <template is="selectRadio" data="{{selectRadio: question}}" />
            </block>
            <!-- 多选题 -->
            <block wx:if="{{question.questionsType == '1'}}">
              <template is="multiple" data="{{multiple: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 下拉多选题 -->
            <!-- <block wx:if="{{question.questionsType == '8'}}">
              <template is="checkboxRadio" data="{{checkboxRadio: question}}" />
            </block> -->
            <!-- 文本框多行填空 -->
            <block wx:if="{{question.questionsType == '2'}}">
              <template is="multipleBlank" data="{{multipleBlank: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 星星打分 -->
            <block wx:if="{{question.questionsType == '3'}}">
              <template is="starScore" data="{{starScore: question, score, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 段落说明 -->
            <block wx:if="{{question.questionsType == '5'}}">
              <template is="explanation" data="{{explanation: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 签名 -->
            <block wx:if="{{question.questionsType == '6'}}">
              <template is="sign" data="{{sign: question, index: subjectListIdx, readonly: record}}"/>
            </block>
            <!-- 文本框单行填空 -->
            <block wx:if="{{question.questionsType == '12'}}">
              <template is="singleBlank" data="{{singleBlank: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 手机号 -->
            <block wx:if="{{question.questionsType == '9'}}">
              <template is="mobileArea" data="{{mobileArea: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 姓名 -->
            <block wx:if="{{question.questionsType == '13'}}">
              <template is="patName" data="{{patName: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 身份证 -->
            <block wx:if="{{question.questionsType == '10'}}">
              <template is="idNumber" data="{{idNumber: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 地址 -->
            <block wx:if="{{question.questionsType == '11'}}">
              <template is="addressArea" data="{{addressArea: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 日期选择 -->
            <block wx:if="{{question.questionsType == '14'}}">
              <template is="dateSelect" data="{{dateSelect: question, index: subjectListIdx, readonly: record}}" />
            </block>
            <!-- 新冠风险地区显示 -->
            <!-- <block wx:if="{{question.questionsType == '15'}}">
              <template is="riskArea" data="{{riskArea: question, index: subjectListIdx}}" />
            </block>-->
            <!-- 辖区 -->
            <block wx:if="{{question.questionsType == '17'}}">
              <template is="regionArea" data="{{regionArea: question, index: subjectListIdx, urlAddress: urlAddress, readonly: record}}" />
            </block>
            <!-- 填空题（多值填空） -->
            <block wx:if="{{question.questionsType == '18'}}">
              <template is="placeholderBlank" data="{{placeholderBlank: question, index: subjectListIdx, readonly: record}}" />
            </block>
          </block>
        </block>
      </view>
      <block wx:if="{{record !== '1'}}">
        <button class="survey-btn" bindtap="submit">提交</button>
      </block>
    </view>
  </scroll-view>
</block>
<block wx:if="{{quesInfo.status != 1 || !quesInfo.flag}}">
  <empty :config.sync="emptyConfig">
    <block slot="text">暂无可填写的问卷</block>
  </empty>
</block>
<toptip :toptip.sync="toptip" />
<!-- 弹窗 -->
<block wx:if="{{showModel}}">
  <view class="desc-modal-mask">
    <view class="desc-modal">
      <view class="desc-title">长沙市健康民生项目—遗传性罕见病综合防控项目告知书</view>
      <scroll-view class="desc-content" scroll-y>
        <text decode="{{true}}" space="{{true}}">{{modelContent}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle1}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent1}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle2}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent2}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle3}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent3}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle4}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle41}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent411}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent412}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle42}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent42}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle43}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent43}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle44}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle5}}</text>
        <text decode="{{true}}" space="{{true}}">{{modelContent5}}</text>
        <text class="desc-content-title" decode="{{true}}" space="{{true}}">{{modelTitle6}}</text>
      </scroll-view>
      <view class="desc-footer">
        <view class="agree" @tap="toggleSurveyModel">关闭</view>
      </view>
    </view>
  </view>
</block>
