@import "../../../../resources/style/mixins";

page {
  background-color: @hc-color-bg;
}
.p-page {
  position: relative;
  height: 100vh;
}

.m-date-box{
  margin:46rpx 24rpx;
  border-radius: 8rpx;
  box-shadow: 0px 1rpx 4rpx 0px rgba(0, 0, 0, 0.12);
}
.m-date-range {
  padding: 8rpx 0;
  display: flex;
  background-color: #3F969D;
  border-radius: 8rpx 8rpx 0 0;
  .m-date-item {
    text-align: center;
    flex: 1;
    font-size: 26rpx;
    color: @hc-color-white;
    font-weight: 600;
    padding: 0 24rpx;
    align-items: center;
    justify-content: center;
    border-right: 2rpx solid rgba(0, 0, 0, 0.08);
    &:last-child{
      border: none;
    }
  }
}

.m-tab {
  background-color: @hc-color-white;
  border-radius: 0 0 8rpx 8rpx;
  max-height: calc(100vh - 450rpx);
  overflow-y: auto;
  .m-tab-list {
    display: flex;
    border-top: 2rpx solid #e5e5e5;
    .list-item {
      display: flex;
      flex: 1;
      padding: 16rpx;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: @hc-color-text;
      height: 104rpx;
      word-break: break-all;
      border-right: 2rpx solid rgba(0, 0, 0, 0.08);
      &:last-child{
        border: none;
      }
    }
  }
}

.page-btn{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 64rpx;
  margin: 0 40rpx;
}

.torecommend {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 36rpx;
  color: @hc-color-white;
  border-radius: 76rpx;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
}

.bg-color {
  background: @hc-color-bg;
}

.color-red {
  color: red;
}

.card {
  padding: 40rpx 24rpx;
  background: #fff;
  .card-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.40);
    .item-tit {
      font-size: 40rpx;
      font-weight: 600;
      color: @hc-color-title;
    }
  }
}

.afterscan-operbtnbox {
  position: fixed;
  bottom: 40rpx;
  width: 100vw;
}

.binduser-btn_line {
  margin: 40rpx 32rpx;
  background: @hc-color-primary;
  color: @hc-color-white;
  border-radius: 10rpx;
  text-align: center;
  font-size: 36rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: @hc-color-text;
}
.empty-info {
  font-size: 28rpx;
  color: @hc-color-info;
  margin-top: 16rpx;
}

button::after {
  border: none;
}

.imgbox{
  text-align: center;
  margin-top: 40rpx;
  margin-bottom: 120rpx;
  image{
    border-radius: 16rpx;
  }
}

.pregnancy-img{
  width: 100%;
  text-align: center;
  padding-bottom: 10vh;
  image{
    width: 96%;
    height: 200rpx;
  }
}