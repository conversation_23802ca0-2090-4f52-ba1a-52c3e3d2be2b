<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Api from "./api";
export default class InhospIndex extends wepy.page {
  config = {
    navigationBarTitleText: "出院结算告知书",
    navigationBarBackgroundColor: '#fff',
  };
  components = {};
  data = {
    billInfo: "",
    isChecked: false,
    jzid: "",
    patCardNo: ""
  };
  onLoad(options) {
    this.billInfo = options.billInfo || "";
    this.jzid = options.jzid || "";
    this.patCardNo = options.patCardNo || "";
  }
  onShow() {}

  methods = {
    cancel() {
      console.log(this.isChecked);
      wepy.navigateBack({ delta: 1 });
    },
    agree() {
      if (this.isChecked) {
        wepy.navigateTo({
          url: `/package1/pages/hospitalbilling/apply/index?billInfo=${
            this.billInfo
          }&jzid=${this.jzid}&patCardNo=${this.patCardNo}`
        });
        return;
      }
    },
    checkboxChange(e) {
      this.isChecked = !this.isChecked;
    }
  };
}
</script>
