<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import Empty from "@/components/empty/index";
import NavTab from "@/components/navtab/index";
import * as Api from "./api";

export default class List extends wepy.page {
  config = {
    navigationBarTitleText: "",
    navigationBarBackgroundColor: '#fff'
  };

  components = {};

  onLoad(options) {
    this.getType(options);
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    // tab类型
    navTabType: "dynamic",
    holeContent: "",
    nowpackage: "",
    emptyConfig: {
      show: true
    },
    packageList: [
      {
        id: "1",
        projectId: "130396",
        listName: "基础管理套餐",
        profileKey: "getAlertNoteProfile_payConsult_oneyear",
      },
      {
        id: "2",
        projectId: "130397",
        listName: "半年管理套餐",
        profileKey: "getAlertNoteProfile_payConsult_halfyear",
       
      }
    ]
  };

  getType(options) {
    this.packageList.forEach(item => {
      if (item.projectId === options.vipCode) {
        this.nowpackage = item;
        this.$apply();
        wx.setNavigationBarTitle({
          title: this.nowpackage.listName //页面切换，更换页面标题
        });
        this.getProfile(this.nowpackage.profileKey);
      }
    });
  }
  async getProfile(profileKey) {
    const { code, data = {}, msg } = await Api.getNoteProfile({ profileKey });
    if (code == 0) {
      const contentText = data.profileValue;
      console.log(contentText, "contentText");
      this.holeContent = contentText;
      this.$apply();
    }
  }
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    // 获取弹框提示语
    onApply() {
      wx.navigateBack({
        delta: 1
      });
    }
  };
}
</script>
