<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from 'wepy';
import TopTip from '@/components/toptip/index';
import * as Api from './api';
import md5 from "md5";

export default class UpdatePatientinfo extends wepy.page {
  config = {
    navigationBarTitleText: '验证信息',
    navigationBarBackgroundColor: '#fff',
  };
  data = {
    leftTime: 60,
    phone: '',
    toptip: '',
    validateCode: '',
    isSendValidate: false,
    errorElement: {}, // 发生错误的元素
    hasErr: true, // 是否存在校验错
    smsCode: '',
    option: {},
  };
  components = {
    toptip: TopTip,
  };
  clock() {
    this.clockTimer = setTimeout(() => {
      let { leftTime } = this;
      --leftTime;
      if (leftTime <= 0) {
        this.isSendValidate = false;
        this.$apply();
      } else {
        this.leftTime = leftTime;
        this.clock();
        this.$apply();
      }
    }, 1000);
  }
  validator(id) {
    const validate = {
      // phone: {
      //   regexp: /^1\d{10}$/,
      //   errTip: '请输入正确的手机号'
      // },
      validateCode: {
        regexp: /^\d{6}$/,
        errTip: '请输入正确的验证码'
      }
    };

    const value = {};
    value.phone = this.phone;
    value.validateCode = this.validateCode;

    let hasErr = false;
    for (let o in value) {
      const obj = validate[o];
      if (obj && obj.regexp) {
        let thisErr = false;
        if (typeof obj.regexp === 'function') {
          const retObj = obj.regexp(value);
          if (!retObj.ret) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        } else {
          if (
            typeof obj.regexp.test === 'function' &&
            !obj.regexp.test(value[o])
          ) {
            hasErr = true;
            thisErr = true;
            if (id && id == o) {
              this.errorElement[id] = true;
            }
          }
        }
        if (
          (!id && hasErr) ||
          (obj.errTarget && obj.errTarget == id && thisErr)
        ) {
          // 提交时弹框提示
          this.errorElement[obj.errTarget || o] = true;
          this.toptip = obj.errTip || '';
          const errTimer = setTimeout(() => {
            this.toptip = '';
            this.$apply();
            clearTimeout(errTimer);
          }, 2000);
          break;
        }
      }
    }

    return hasErr;
  }
  async bindCard() {
    const param = {
      relationType: 5,
      patientType: 0,
      isNewCard: '1',
      patientName: this.option.patName || "",
      patientMobile: this.option.telephone || "",
      patCardType: 21,
      idNo: this.option.idNo,
      patientAddress: this.option.patAddress || "",
      familyTypeId: this.option.familyTypeId
    };
    // return
    const { code, data = {}, msg } = await Api.bindPatient(param);
    if (code == 0) {

      wx.showToast({
        title: "绑定成功",
        icon: "success",
        duration: 2000,
        success: () => {
          wepy.redirectTo({ url: "/pages/usercenter/userlist/index" });
        }
      });
    }
  }
  async pushData() {
    wepy.showLoading({ title: '加载中', mask: true });
    const value = {};
    value.phone = this.phone;
    value.code = this.validateCode;
    // value.type = 1;
    // value.grid = this.option.grid;
    value.msgKey = md5(this.phone+'2023msg')
    const registerRes = await Api.checkMsgAndValidate(value);
    if (registerRes.code == 0 && registerRes.data.resultCode == 0) {
      wepy.hideLoading();
      wepy.showToast({
        title: '验证成功',
        icon: 'success'
      });
      const timer = setTimeout(() => {
        clearTimeout(timer);
        // wepy.navigateTo({
        //   url: `/pages/bindcard/adduser/index?telephone=${this.option.telephone}&relationType=${this.option.relationType }&patName=${this.option.patName }&patSex=${this.option.patSex }&patBirth=${this.option.patBirth || '2020-02-02'}&idNo=${this.option.idNo }&familyMemberName=${this.option.familyMemberName }&marryDes=${this.option.marryDes}&idType=${this.option.idType}&isNewCard=0&pid=${this.option.pid}&isCode=1&patAddress=${this.option.patAddress}`,
        //  });
        this.bindCard()
      }, 2000);
    } else if (registerRes.code == 0 && registerRes.data.resultCode != 0) {
      wepy.showModal({
        title: "温馨提示",
        content: registerRes.data.resultMessage,
        showCancel: false
      });
    }
  }
  methods = {
    userIO(e) {
      const { id } = e.currentTarget;
      const { value } = e.detail;
      this[id] = value;
    },
    resetThisError(e) {
      const { id } = e.currentTarget;
      this.errorElement[id] = false;
    },
    validator(e) {
      const { id } = e.currentTarget;
      this.hasErr = this.validator(id);
    },
    async getValidate() {
      if (this.errorElement['phone'] || !this.phone) {
        this.toptip = '请填写正确的手机号码';
        const errTimer = setTimeout(() => {
          this.toptip = '';
          this.$apply();
          clearTimeout(errTimer);
        }, 2000);
        return '';
      }
      const getValidateRes = await Api.sendMsgAndValidate({ phone: this.phone,  msgKey: md5(this.phone+'2023') });
      if (getValidateRes.code == 0 && getValidateRes.data.resultCode == 0) {
        wepy.showToast({
          title: '发送成功',
          icon: 'success'
        });
        this.isSendValidate = true;
        this.clock();
      }
    },
    submitData() {
      const hasErr = this.validator();
      if (hasErr) {
        return false;
      }
      this.pushData();
    }
  };
  onLoad(options){
    this.phone = options.telephone;
    this.option = options;
  }
  onUnload() {
    this.clockTimer && clearInterval(this.clockTimer);
  }
}
</script>