@import "../../../resources/style/mixins";

page {
  background-color: @hc-color-white;
  min-height: 100vh;
}

.notice-detail {
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
  font-size: 28rpx;

  .title {
    padding: 15rpx 0;
    text-align: center;
    font-size: 36rpx;
    color: @hc-color-title;
  }

  .date {
    padding: 15rpx 0;
    text-align: center;
    font-size: 24rpx;
    color: @hc-color-info;
  }

  .content {
    padding: 20rpx 0;
    text-align: left;
    text-indent: 2em;
    color: @hc-color-text;
    word-break: break-all;
    word-wrap: break-word;
  }

  .btn-area {
    width: 100%;
    padding: 30rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-flow: column nowrap;

    .btn {
      background-color: @hc-color-primary;
      color: @hc-color-white;
      font-size: 28rpx;
      &::after{
        display: none;
      }
    }
    .isReceipt{
      border: 2rpx solid @hc-color-border;
      color: @hc-color-text;
      background-color: transparent;
    }
  }
}