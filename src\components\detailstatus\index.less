@import "../../resources/style/mixins";

.wgt-detailstatus{
  padding: 48rpx 24rpx;
  
  &.wgt-detailstatus-success{
    .wgt-detailstatus-bd-tit{
      color: #3F969D;
    }
    .icon{
      background-color: #3F969D;
    }
  }
  &.wgt-detailstatus-fail{
    .wgt-detailstatus-bd-tit{
      color: #C55D5D;
    }
    .icon{
      background-color: rgba(197, 93, 93, 1);
    }
  }
  &.wgt-detailstatus-abnormal{
    .wgt-detailstatus-bd-tit{
      color: #E7AA35;
    }
    .icon{
      background-color: #E7AA35;
    }
  }
  &.wgt-detailstatus-lock{
    .wgt-detailstatus-bd-tit{
      color: #3F969D;
    }
    .wgt-detailstatus-bd-timer{
      color: rgba(12, 12, 12, 0.70);
      font-size: 40rpx;
      font-weight: 600;
    }
    .icon{
      background-color: #3F969D;
    }
  }
  &.wgt-detailstatus-cancel{
    .icon{
      background-color: rgba(0, 0, 0, .5);
    }
  }
  &.wgt-detailstatus-wait{
  }
}
.wgt-detailstatus-bd{
  display: flex;
  align-items: center;
}
.wgt-detailstatus-bd-icon{
  width: 64rpx;
  height: 64rpx;
  .icon{
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  image{
    vertical-align: top;
    width: 100%;
    height: 100%;
  }
}
.wgt-detailstatus-bd-tit{
  margin-left: 15rpx;
  font-size: 48rpx;
  font-weight: 600;
  color:rgba(0, 0, 0, 0.50);
}
.wgt-detailstatus-bd-label{
  margin-left: 15rpx;
  display: inline-block;
  color: rgba(0, 0, 0, 0.40);
  font-size: 20rpx;
  text-align: center;
  padding: 4rpx 16rpx;
  vertical-align: middle;
  border-radius: 38rpx;
  background-color: rgba(0, 0, 0, 0.04);
}
.wgt-detailstatus-bd-timer{
  flex: 1;
  color: #fff;
  font-size: 50rpx;
  text-align: right;
}
.wgt-detailstatus-ft{
  font-size: 28rpx;
  color:rgba(0, 0, 0, 0.50);
  margin-top: 8rpx;
}
