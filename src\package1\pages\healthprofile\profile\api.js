import { post } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

export const getProfileInfo = (param) => post('/api/healthRecord/queryHealthRecord', param);

export const  patientinfo = (param) => post('/api/user/patientinfo', param);

export const  queryRelationPatients = (param) => post(`/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`, param);
// 查询病史
export const queryDiseaseList = (param) => post('/api/healthRecord/queryDiseaseList', param);