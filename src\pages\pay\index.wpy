<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
  import wepy from 'wepy';
  import WxsUtils from '../../wxs/utils.wxs';
  import * as Api from './api';

  export default class PageWidget extends wepy.page {
    config = {
      navigationBarTitleText: '收银台',
      navigationBarBackgroundColor: '#fff'
    };

    wxs = {
      WxsUtils: WxsUtils,
    };

    components = {};

    onLoad(options = {}) {
      this.options = options;
      const { payOrderId = '' } = options;
      this.cashier({ payOrderId });
    }

    onUnload() {
      this.leftTimer && clearTimeout(this.leftTimer);
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      // 订单参数信息
      orderInfo: {},
      // 剩余时间大于0
      leftTimeFlag: false,
      // 剩余支付时间
      leftTime: '00:00',
      // 防重复提交
      isSubmit: false,
      options: {},
      productList: []
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      goBack() {
        wepy.navigateBack({
          delta: 1
        });
      },
      /**
       * 显示全部功能切换
       * @param item
       */
      bindChoosePayMode(item){
        this.choosePayMode(item);
      },
    };

    events = {};

    getLeftTime(time = 0) {
      if(this.leftTimer){
        clearTimeout(this.leftTimer);
      }
      if (time <= 0) {
        this.leftTimer && clearTimeout(this.leftTimer);
        this.leftTimeFlag = false;
        this.leftTime = '00:00';
        this.$apply();
        return;
      }

      const minute = (`00${Math.floor(time / 60)}`).substr(-2);
      const second = (`00${Math.floor(time % 60)}`).substr(-2);

      this.leftTime = `${minute}:${second}`;
      this.leftTimeFlag = true;
      this.leftTimer = setTimeout(() => {
        this.getLeftTime(--time);
      }, 1000);
      this.$apply();
    };

    /**
     * 获取支付参数信息
     */
    async cashier(item = {}) {
      const { payOrderId = '' } = item;
      const { code, data = {}, msg } = await Api.cashier({ payOrderId });
      try {
        data.goodsName = JSON.parse(data.goodsName || '[]');
      } catch (e) {
        data.goodsName = [];
      }
      if(this.options.type === 'SMKD'){
        this.productList = data.goodsName.filter(v => v.key === "项目名称")[0].value;
        data.goodsName = data.goodsName.filter(v => v.key !== "项目名称");
      }
      
      if(data.showLeftTime === 1){
        this.getLeftTime(data.leftPayTime || 0);
      }
      this.orderInfo = data;
      this.$apply();
    }

    /**
     * 选择支付方式
     * @param e
     */
    async choosePayMode(e) {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true; // 防止重复提交

      const { formId = '' } = e.detail;
      Api.saveFormId({ formId });
      const item = e.detail.target.dataset['mode'];
      const { payMode = '' } = item;
      const { payOrderId = '' } = this.orderInfo;
      const { code, data = {}, msg } = await Api.choosePayMode({ payOrderId, payMode });
      const { payParameter = {} } = data;
      switch (payMode) {
        // 微信支付
        case 'weixin_wap':
          this.chooseWeChatWapPay(payParameter);
          break;
        // 微信小程序支付
        case 'weixin_miniapp':
          this.chooseWeChatWapPay(payParameter);
          break;
        // 微付通
        case 'swiftpass_weixin_wap':
          this.chooseWeChatWapPay(payParameter);
          break;
        // 预付卡支付
        case 'hospital_prepaidCard':
          this.choosePrepay(payParameter);
          break;
        // 0元支付
        case 'hospital_zero':
          this.chooseZeroPay(payParameter);
          break;
        // 医保0元支付
        case 'weixin_medicareOnline_zero':
          this.chooseZeroPay(payParameter);
          break;
        default:
          this.chooseUndefinedMode();
          break;
      }

      this.isSubmit = false; // 防止重复提交

      this.$apply();
    }

    /**
     * 未知的支付方式
     */
    chooseUndefinedMode() {
      wepy.showModal({
        title: '提示',
        content: '未知的支付方式',
        showCancel: false,
      });
    }

    /**
     * 微信wap支付
     * @param item
     */
    async chooseWeChatWapPay(item) {
      let requestPaymentRes;
      try {
        requestPaymentRes = await wepy.requestPayment({
          'timeStamp': item.timeStamp,
          'nonceStr': item.nonceStr,
          'package': item.package,
          'signType': item.signType || 'MD5',
          'paySign': item.paySign,
        });
      } catch (e) {
        requestPaymentRes = e;
      }
      if (requestPaymentRes.errMsg == 'requestPayment:fail cancel') {
        // 取消支付
      } else if (requestPaymentRes.errMsg == 'requestPayment:ok') {
        // 支付成功
        const { orderId = '', type = '', id = '', regType = '' } = this.options;
        wepy.redirectTo({
          url: `/pages/waiting/waiting/index?type=${type}&orderId=${orderId}&id=${id}&regType=${regType}`,
        });
      } else {
        // 其他未支付成功情况
      }
      console.log(requestPaymentRes);
    }

    /**
     * 预付卡支付
     */
    choosePrepay() {
      const { orderId = '', type = '' } = this.options;
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?type=${type}&orderId=${orderId}`,
      });
    }

    /**
     * 0元支付
     */
    chooseZeroPay() {
      const { orderId = '', type = '' } = this.options;
      wepy.redirectTo({
        url: `/pages/waiting/waiting/index?type=${type}&orderId=${orderId}`,
      });
    }
  }
</script>
