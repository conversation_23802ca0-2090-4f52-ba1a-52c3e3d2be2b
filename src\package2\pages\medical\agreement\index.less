
.medical-modal {
  text-align: justify;
  padding: 30rpx;
  color: #414040;
  background-color: #F4F5F8;
  font-size: 32rpx;
  // height: 100vh;

  .modal-title {
    text-align: center;
    margin-bottom: 30rpx;
    font-size: 34rpx;
    font-weight: 600;
  }

  .modal-first-content{
    text-indent: 2em;
  }
}


.agreement_endcontent {
  margin-top: 30rpx;
  border-radius: 16rpx;

  .agreement_tips {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.4);
    display: flex;

  }

  .button {
    border-radius: 10rpx;
    font-size: 34rpx;
    margin-top: 30rpx;
  }
  checkbox{
    transform: scale(0.8);
  }

  .disable {
    color: rgba(0, 0, 0, 0.4);
    background: rgba(0, 0, 0, 0.04);
  }

  .confirm {
    background: #3ECEB6;
    color: #FFFFFF;
  }
}