<view class="p-page">

  <view class="m-card">
    <view class="card-info">
      <view class="info-main">
        <view class="main-name">
          <view class="name">{{userInfo.patientName}}</view>
          <view class="status" wx:if="{{userInfo.isDefault == 1}}">默认</view>
        </view>
      </view>
      <view class="info-extra">病历号：{{userInfo.idNo}}</view><!-- 协商后台考虑无卡情况 -->
    </view>
  </view>

  <view class="m-title">就诊人信息</view>
  <view class="m-qrcode">
    <image wx:if="{{!!userInfo.patientFullIdNo}}"
      mode="widthFix" class="qrimage" 
      src="https://wechat.jiahuiyiyuan.com/barcode?msg={{userInfo.patientFullIdNo}}&type=code128&mw=.60"
    />
  </view>

  <view class="m-userinfo">
    <!-- <view class="userinfo-item" wx:if="{{userInfo.patientSex}}">
      <view class="item-tit">性别</view>
      <view class="item-txt">{{userInfo.patientSex === 'M' ? '男' : '女'}}</view>
    </view> -->
    <view class="userinfo-item" wx:if="{{userInfo.birthday}}">
      <view class="item-tit">出生日期</view>
      <view class="item-txt">{{userInfo.birthday}}</view>
    </view>
    <view class="userinfo-item" wx:if="{{userInfo.idNo}}">
      <view class="item-tit">{{userInfo.idTypeName || '证件'}}</view>
      <view class="item-txt">{{userInfo.idNo}}</view>
    </view>
    <view class="userinfo-item" wx:if="{{userInfo.parentIdNo}}">
      <view class="item-tit">监护人{{userInfo.parentIdTypeName || '证件'}}</view>
      <view class="item-txt">{{userInfo.parentIdNo}}</view>
    </view>
    <!-- <view class="userinfo-item">
      <view class="item-tit">手机号</view>
      <view class="item-txt">{{userInfo.patientMobile}}</view>
    </view> -->
    <view class="userinfo-item" wx:if="{{userInfo.patientAddress}}">
      <view class="item-tit">地址</view>
      <view class="item-txt">{{userInfo.areaName}}{{userInfo.patientAddress}}</view>
    </view>
  </view>

  <block wx:if="{{dataInfo && isShowEditInfo}}">
    <form bindsubmit="formSubmit" report-submit='true'>
      <view class="patInfo-list">
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">籍贯</text>
          </view>
          <view class="listitem-body">
            <!-- <picker bindchange="pickerChange" id="AF" value="{{indexAF}}" range="{{natArrList}}"> -->
              <view class="picker">
                <input class="picker-info" disabled type="text" value="{{natArrList[indexAF]}}"
                  placeholder-style="{{ dataInfo.nativeName ? '': ''}}"
                  placeholder="{{dataInfo.nativeName || ''}}" />
                  <!-- 请选择籍贯 -->
                <!-- <view class="item-arrow"></view> -->
              </view>
            <!-- </picker> -->
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">职业</text>
          </view>
          <view class="listitem-body">
            <!-- <picker bindchange="pickerChange" id="AG" value="{{indexAG}}" range="{{jobArrList}}"> -->
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.jobName || ''}}"
                  placeholder-style="{{ dataInfo.jobName ? '': ''}}" value="{{jobArrList[indexAG]}}" />
                  <!-- 请选择职业 -->
                <!-- <view class="item-arrow"></view> -->
              </view>
            <!-- </picker> -->
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">民族</text>
          </view>
          <view class="listitem-body">
            <!-- <picker bindchange="pickerChange" id="AC" value="{{indexAC}}" range="{{nationArrList}}"> -->
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.nationName || ''}}"
                  placeholder-style="{{ dataInfo.nationName ? '': ''}}" value="{{nationArrList[indexAC]}}" />
                  <!-- 请选择民族 -->
                <!-- <view class="item-arrow"></view> -->
              </view>
            <!-- </picker> -->
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">文化程度</text>
          </view>
          <view class="listitem-body">
            <!-- <picker bindchange="pickerChange" id="ZC" value="{{indexZC}}" range="{{culArrList}}"> -->
              <view class="picker">
                <input class="picker-info" type="text" disabled placeholder="{{dataInfo.educationName || ''}}"
                  placeholder-style="{{ dataInfo.educationName ? '': ''}}" value="{{culArrList[indexZC]}}" />
                  <!-- 请选择文化程度 -->
                <!-- <view class="item-arrow"></view> -->
              </view>
            <!-- </picker> -->
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">邮政编码</text>
          </view>
          <view class="listitem-body">
            <input bindinput="inputTrigger" id="postalCode" type="number" placeholder="" value="{{dataInfo.postalCode}}" disabled maxlength="6"  />
            <!-- 请输入邮政编码 -->
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">身份证地址</text>
          </view>
          <view class="listitem-body">
            <text wx:if="{{dataInfo.idCardAddress}}">{{dataInfo.idCardAddress}}</text>
            <block wx:else>
              <input type="text" @input="inputTrigger" id="idCardAddress" placeholder-style="{{ dataInfo.idCardAddress ? '': ''}}"
              placeholder="" disabled value="{{dataInfo.idCardAddress}}"  />
              <!-- 请输入身份证地址 -->
            </block>
          </view>
        </view>
        <view class="patInfo-listitem">
          <view class="listitem-head">
            <text class="list-title">通讯地址</text>
          </view>
          <view class="listitem-body">
            <text wx:if="{{dataInfo.address}}">{{dataInfo.areaName}} {{dataInfo.address}}</text>
            <block wx:else>
              <input type="text" @input="inputTrigger" id="address" placeholder-style="{{ dataInfo.address ? '': ''}}"
              placeholder="" disabled value="{{dataInfo.areaName}} {{dataInfo.address}}"  />
              <!-- 请输入通讯地址 -->
            </block>
          </view>
        </view>
      </view>
      <!-- <view class="patInfo-btn">
        <button class="binduser-btn_line" formType="submit">确认修改</button>
      </view> -->
    </form>
  </block>

  <!-- 修改信息 -->
  <view class="m-edit-block" wx:if="{{dataInfo}}">
    <view @tap="toggleEdit" class="m-edit-title">
      {{isShowEditInfo ? '收起基本信息' : '查看基本信息'}}
      <!-- <view class="item-arrow"></view> -->
    </view>
  </view>

  <view class="m-list {{!isShowEditInfo ? 'abs' : ''}}">
    <view class="m-btn">
      <block wx:if="{{relationType != 1}}">
        <view class="btn-item default-btn" @tap="unBind">删除就诊人</view>
      </block>
      <block wx:else>
        <view class="btn-item default-btn" @tap="unBind">退出</view>
      </block>
    </view>
  </view>
  

</view>
