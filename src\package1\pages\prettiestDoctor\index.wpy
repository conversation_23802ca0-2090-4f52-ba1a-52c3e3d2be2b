<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import * as Api from './api';
  //import DoctorItem from './doctorItem/index'

  export default class WebView extends wepy.page {
    config = {
      navigationBarTitleText: '最美医生评选活动',
    };

    components = {
     // doctorItem: DoctorItem
    };

    onLoad(options){
      this.getDeptAndDoctor();
      this.checkPatient('init');
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      deptList: '',
      selectData:[],
      judge:true,
      selectArr:[],
    };

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      voteEvent(){
        this.checkPatient('submit');
      },
        unfoldEvent(){
        this.judge = false;
      },
      checkValue(e){
        if(e.detail.value[0]){
          let selectData = e.detail.value[0].split('/');
          this.selectArr.push({id:selectData[0],dept:selectData[1]});
        }else{
          for(let i = 0;i<this.selectArr.length;i++){
            if(e.target.dataset.id == this.selectArr[i].id){
                this.selectArr.splice(i,1);
            } 
          }
        }
      }
    };

    events = {
      selectData(data){
        this.selectData=data;
      }
    }

    async checkPatient(val){
      const {code,data} = await Api.checkPatient({voteGroup:'most_beautiful_doctor_20200724'});
      if(code == 0 ){
        if( val != 'init'){
          this.commit();
        }
      }else{
          this.judge=false;
      }
    }

    async commit(){
      let doctorIds = [];
      let otherDoctorIds = [];
      let allIds = [];
      for(let i = 0;i<this.selectArr.length;i++){
        if(this.selectArr[i].dept == '生殖中心'){
          doctorIds.push(this.selectArr[i].id);
        }
         if(this.selectArr[i].dept == '其他科室'){
           otherDoctorIds.push(this.selectArr[i].id);
         }
      }
       if(doctorIds.length>7){
          wx.showModal({
             title:'提示',
            content:'生殖中心最多选7人！',
            duration:1000,
               confirmColor:'#3eceb5',
               showCancel:false,
            });
            return false;
         }
         if(otherDoctorIds.length>3){
            wx.showModal({
               title:'提示',
               content:'其他科室最多选3人！',
               duration:1000,
                confirmColor:'#3eceb5',
               showCancel:false,
            });
            return false;
         }
       allIds = doctorIds.concat(otherDoctorIds);

      let param={
          voteGroup:'most_beautiful_doctor_20200724',
          voteDoctorIdArray:allIds
      }
      const {code,data} = await Api.commit(param);
      if(code == 0){
       // wx.setStorageSync('vote',this.selectArr);
      //  this.getDeptAndDoctor();
        this.judge = false;
        wx.showModal({
              title:'提示',
              content:'投票成功！',
              duration:1000,
              confirmColor:'#3eceb5',
              showCancel:false,
           });
      }else{
        wx.showToast({title:'投票失败',duration:1000,icon:'none'});
      }
    }

    async getDeptAndDoctor() {
      const { code, data = {} } = await Api.getDeptAndDoctor({voteGroup:'most_beautiful_doctor_20200724'});
      if (code !== 0) {
        return;
      }
      let newArr=[];
      for(let i = 0;i<data.length;i++){
        data[i].check = false;
        newArr.push(data[i]);
      }
      this.deptList = newArr || [];
      this.$apply();
    }
  }
</script>
