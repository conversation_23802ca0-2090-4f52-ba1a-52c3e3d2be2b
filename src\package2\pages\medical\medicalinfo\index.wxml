<view class="medical">
  <view class="medical-container">
    <block wx:if="{{userInfo.length}}">
      <view class="medical-info" style="padding: 20rpx 30rpx">
        <view class="radio-title">{{medicalCopyTypeList[medicalCopyType]}}</view>
        <view
          wx:if="{{userInfo.length > 1}}"
          style="margin-bottom: 10rpx;color:#989898;font-size:28rpx"
        >查询到您有多条信息，请选择需复印的那条</view>
        <view class="picker-li" wx:if="{{userInfo.length > 1}}">
          <view style="margin-right: 20rpx">
            请选择{{medicalCopyType === '1'?'入院日期':'周期数'}}
            <text style="color:red">></text>
          </view>
          <picker
            range="{{userInfo}}"
            range-key="{{medicalCopyType === '1' ? 'ryrq' : 'zqxx'}}"
            bindchange="bindPickerChangeVal"
          >
            <view class="rt">
              <text>{{medicalCopyType === '1' ? userInfo[selectInfoIdx].ryrq : userInfo[selectInfoIdx].zqxx}}</text>
              <text class="arrow"></text>
            </view>
          </picker>
        </view>
        <view class="medical-content">
          <view class="info-li" wx:if="{{userInfo[selectInfoIdx].zyh && medicalCopyType === '1'}}">
            <view class="info-key">住院号：</view>
            <view>{{userInfo[selectInfoIdx].zyh}}</view>
          </view>
          <view class="info-li" wx:if="{{userInfo[selectInfoIdx].ryrq && medicalCopyType === '1'}}">
            <view class="info-key">入院日期：</view>
            <view>{{userInfo[selectInfoIdx].ryrq}}</view>
          </view>
          <view class="info-li" wx:if="{{userInfo[selectInfoIdx].ryks && medicalCopyType === '1'}}">
            <view class="info-key">入院科室：</view>
            <view>{{userInfo[selectInfoIdx].ryks}}</view>
          </view>
          <view class="info-li" wx:if="{{userInfo[selectInfoIdx].zqxx && medicalCopyType !== '1'}}">
            <view class="info-key">周期信息</view>
            <view>{{userInfo[selectInfoIdx].zqxx}}</view>
          </view>
          <view class="info-li" wx:if="{{userInfo[selectInfoIdx].zljd && medicalCopyType !== '1'}}">
            <view class="info-key">诊疗阶段：</view>
            <view>{{userInfo[selectInfoIdx].zljd}}</view>
          </view>
        </view>
      </view>
    </block>
    <view class="medical-info">
      <view class="info-li">
        <view>
          <text class="color-red">*</text>复印用途
        </view>
        <picker range="{{wayList}}" bindchange="bindPickerChange">
          <input type="text" disabled value="{{copyPurpose}}" placeholder="请选择复印用途" />
        </picker>
      </view>
      <view class="info-li">
        <view>
          <text class="color-red">*</text>复印份数
        </view>
        <view class="number-info">
          <view class="btn-li" @tap="reduce">
            <view class="reduce-btn" />
          </view>
          <view class="number">{{copyNum}}</view>
          <view class="btn-li" @tap="add">
            <view class="add-btn" />
          </view>
        </view>
      </view>
      <view class="check-group">
        <view>
          <text class="color-red">*</text>复印资料（可多选）
        </view>
        <checkbox-group data-id="copyContent" bindchange="checkValue">
          <label
            class="radio-label"
            wx:for="{{ medicalCopyType == 1 ? copyZYContentArray : copyOthContentArray}}"
            wx:key="{{item}}"
          >
            <view class="weui-cell__hd">
              <checkbox value="{{item.value}}" checked="{{item.isCheck}}" class="radio-item" color="#3ECDB5" />
            </view>
            <view class="weui-cell__bd">{{item.value}}</view>
          </label>
        </checkbox-group>
      </view>
      <view class="check-group">
        <view>
          <text class="color-red">*</text>复印对象（可多选）
        </view>
        <checkbox-group data-id="copyObject" bindchange="checkValue">
          <label class="radio-label" wx:for="{{sexRadioArray}}" wx:key="{{item.value}}">
            <view class="weui-cell__hd">
              <checkbox value="{{item.value}}" checked="{{item.isCheck}}" class="radio-item" color="#3ECDB5" />
            </view>
            <view class="weui-cell__bd">{{item.value}}</view>
          </label>
        </checkbox-group>
      </view>
      <view class="other-li">
        <view>备注：</view>
        <textarea
          auto-height
          value="{{remark}}"
          placeholder="若有其他情况请补充说明，如复印资料"
          placeholder-style="color: rgba(0, 0, 0, 0.2)"
          name="textarea"
          maxlength="100"
          @input="inputTriggerOtherContent"
          @blur="inputTriggerOtherContent"
        />
      </view>
    </view>

    <view class="form-info">
      <view class="form-item form-between">
        <view>
          <text style="color: #FF613B">*</text>请选择领取方式
        </view>
        <radio-group bindchange="postRadioChange('receiveType')" class="radio-group">
          <label class="radio-label" wx:for="{{radioArray}}" wx:key="{{item.value}}">
            <view class="weui-cell__hd">
              <radio value="{{item.value}}" checked="{{receiveType == item.value}}" class="radio-item" color="#3ECDB5" />
            </view>
            <view class="weui-cell__bd">{{item.label}}</view>
          </label>
        </radio-group>
      </view>
      <block wx:if="{{receiveType == 1}}">
        <view class="address-title">
          <text style="color: #FF613B;margin-right:5rpx">*</text>
          <text style="font-size:28rpx">(必填)请输入病历邮寄地址（快递费到付）</text>
        </view>
        <view class="form-item">
          <view class="label">收件人姓名</view>
          <input
            placeholder="请输入收件人姓名"
            @input="onAddressInputValue"
            data-key="userName"
            value="{{addresseeInfo.userName}}"
            placeholder-class="input-placeholder"
          />
        </view>
        <view class="form-item">
          <view class="label">手机号码</view>
          <input
            type="number"
            placeholder="请输入手机号码"
            @input="onAddressInputValue"
            data-key="mobile"
            value="{{addresseeInfo.mobile}}"
            placeholder-class="input-placeholder"
            maxlength="11"
          />
        </view>
        <view class="form-item">
          <view class="label">所在地区</view>
          <picker
            mode="region"
            bindchange="bindRegionChange"
            value="{{region}}"
            custom-item="{{customItem}}"
            style="flex: 1"
          >
            <view class="picker">
              <text
                wx:if="{{addresseeInfo.provinceName}}"
              >{{addresseeInfo.provinceName}}/{{addresseeInfo.cityName }}/{{addresseeInfo.areaName }}</text>
              <text wx:else class="input-placeholder">请选择所在地区</text>
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
        <view class="form-item">
          <view class="label">详细地址</view>
          <textarea
            placeholder="请输入详细地址"
            auto-height="true"
            @input="onAddressInputValue"
            data-key="addressDetail"
            value="{{addresseeInfo.addressDetail}}"
            placeholder-class="input-placeholder"
          />
        </view>
      </block>
    </view>

    <view class="btn">
      <view @tap="submit">提交申请</view>
    </view>
  </view>
</view>
