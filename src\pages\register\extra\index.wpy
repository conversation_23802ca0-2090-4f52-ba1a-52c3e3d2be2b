<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";
import * as Api from "./api";
import * as Utils from "@/utils/utils";


export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "遗传咨询"
  };

  onLoad(options) {
    const { deptId = '', deptName = '', deptType = '', note = '', skill = '', areaCode = '' } = options;
    this.areaCode = areaCode;
    this.skill = skill;
    console.log(skill);
    this.deptId = deptId,
    this.deptName = deptName,
    this.deptType = deptType,
    wx.setNavigationBarTitle({
      title: deptName
    })
    this.note = note;
    this.getList(deptId);
    // this.getNoteProfile();
    this.$apply();
  }

  // data中的数据，可以直接通过  this.xxx 获取，如：this.text
  data = {
    areaCode: '',
    list: [
      { name: '产前诊断及胎儿医学咨询', id: 1 },
      { name: '染色体异常咨询', id: 2 },
      { name: '基因诊断咨询', id: 3 },
      { name: '婚姻生育咨询', id: 4 },
      { name: '遗传胚胎检测结果咨询', id: 5 },
      { name: '肿瘤遗传咨询', id: 6 },
    ],
    activeIndex: 1,
    deptId: '',
    deptName: '',
    deptType: '',

    //顶部提示语
    note: '',
    skill: ''
  };

  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    goDetail(id){
      this.activeIndex = id;
      this.goDoc(id);
    }
  };
  async goDoc(id) {
    const { code = '', data = {} } = await Api.getRegisterProfile({ profileId: id, deptId: this.deptId });
    if (code == 0) {
      const { doctorStrNo = '' } = data;
      wepy.navigateTo({
        url: `/pages/register/doclist/index?${Utils.jsonToQueryString({
          deptId: this.deptId,
          deptName: this.deptName,
          deptType: this.deptType,
          doctorStrNo: JSON.stringify(doctorStrNo),
          profileId: id,
          skill: this.skill,
          areaCode: this.areaCode || '',
        })}`
      });
    }
  };
  async getList(deptId) {
    const  { code = '', data = [] } = await Api.getList({ deptNo:deptId });
    if (code == 0) {
      this.list = data;
      this.$apply();
    }
  };
  //获取顶部提示语
  async getNoteProfile() {
    const { code, data = {}, msg } = await Api.getNoteProfile({ profileKey: 'getNoteProfile_selectRegSourceTop' });
    if (code == 0) {
      this.note = data.profileValue;
      this.$apply();
    }
  }
}
</script>
