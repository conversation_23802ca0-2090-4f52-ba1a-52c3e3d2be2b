@import "../../../../resources/style/mixins";
.guidelines{
  .report{
    width: 690rpx;
    margin: 30rpx auto;
    box-sizing: border-box;
    padding: 0 30rpx;
    border-radius: 8rpx;
    background-color: @hc-color-white;
    .report-item{
      padding: 15rpx 0;
      font-size: 28rpx;
      &+.report-item{
        border-top: 2rpx dashed @hc-color-border;
      }
    }
    .name-age{
      color: @hc-color-title;
      .item{
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-flow: row nowrap;
        .name,.age{
          flex: 1;
          .val{
            color: @hc-color-text;
          }
        }
      }
    }
    .title{
      color: @hc-color-title;
      font-size: 28rpx;
    }
    .entrust{
      .table{
        margin-top: 15rpx;
        font-size: 24rpx;
        border: 2rpx solid @hc-color-border;
        .th,.tr{
          display: flex;
          justify-content: space-between;
          align-items: stretch;
          flex-flow: row nowrap;
          &+.tr{
            border-top: 2rpx solid @hc-color-border;
          }
          .td{
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15rpx 10rpx;
            border-right: 2rpx solid @hc-color-border;
          }
          .content{
            flex: 4;
          }
          .gender{
            flex: 1;
          }
          .status{
            flex: 2;
          }
          .result{
            flex: 4;
          }
        }
        .th{
          color: @hc-color-title;
        }
        .tr{
          color: @hc-color-text;
        }
        .tr-empty{
          padding: 30rpx;
          justify-content: center;
        }
      }
    }
    .tips-content{
      text-indent: 2rem;
      color: @hc-color-text;
    }
  }
}