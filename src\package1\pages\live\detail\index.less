@import '../../../../resources/style/mixins.less';

.header {
  height: 420rpx;
  background: linear-gradient(
    99deg,
    rgba(92, 92, 92, 1) 0%,
    rgba(47, 47, 47, 1) 100%
  );
  color: #ffffff;
  font-size: 34rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
  .header-title {
    font-size: 42rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 42rpx;
    line-height: 59rpx;
  }
}

.content {
  padding: 0 30rpx;
  background: #ffffff;
  .content-title {
    height: 48rpx;
    font-size: 34rpx;
    font-weight: bold;
    color: rgba(45, 45, 45, 1);
    line-height: 48rpx;
    padding: 30rpx 0 20rpx;
  }
  .divider {
    width: 100%;
    height: 1rpx;
    background: #D8D8D8;
  }
  .intro-box {
    padding: 30rpx 0;
    font-size: 32rpx;
    color: #2d2d2d;
    .intro-title {
      margin-bottom: 14rpx;
      font-weight: bold;
    }
    .intro-des {
      font-size: 30rpx;
      min-height: 92rpx;
      font-size: 30rpx;
      color: #666666;
      line-height: 46rpx;
    }
  }
}

.bottom-btn {
  height: 94rpx;
  background: rgba(62, 206, 182, 1);
  box-shadow: 0rpx -3rpx 6rpx 0rpx rgba(0, 0, 0, 0.07);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #ffffff;
}
