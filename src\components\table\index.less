@import '../../resources/style/mixins';
.inhosp-table {
  margin: 40rpx 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;

  .title {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    font-size: 28rpx;
    margin-bottom: 32rpx;
  }

  .table {
    border: 2rpx solid rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
    border-radius: 16rpx;
    font-size: 24rpx;

    .table-title {
      display: flex;
      height: 68rpx;
      line-height: 68rpx;
      text-align: center;
      background-color: rgba(0, 0, 0, 0.04);
      > view {
        flex: 1;
      }
    }

    .table-content {
      display: flex;
      padding: 16rpx 0;
      text-align: center;
      align-items: center;
      > view {
        flex: 1;
      }
      &:not(:last-child) {
        box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.06);
      }
    }
  }
}
