<view class="p-page">
  <view class="refund-info">
    <view>退费金额（元）</view>
    <view>{{billInfo.yjjye}}</view>
  </view>
  <detail-item title="住院信息" :dataSource.sync="hospitalInfo" />
  <view class="tips">*请填写住院人本人名下有效的银行卡信息，若账号与户名不一致都将导致医院后续转账失败，影响结算，请认真核对。</view>
  <view class="form-info">
    <view class="title">
      <text style="color: #FF613B">*</text>
      请填写住院人银行卡信息备用
    </view>
    <view class="form-item">
      <view class="label">户名</view>
      <view>{{billInfo.patientName}}</view>
    </view>
    <view class="form-item">
      <view class="label">账号</view>
      <input placeholder="请输入账号" @input="onCardInputValue" data-key="bankCardNumber" value="{{bankCardInfo.bankCardNumber}}" placeholder-class="input-placeholder"/>
    </view>
    <view class="form-item">
      <view class="label">开户行</view>
        <input placeholder="请输入开户行" @input="onCardInputValue" data-key="bankName" value="{{bankCardInfo.bankName}}" placeholder-class="input-placeholder"/>
    </view>
  </view>
  <view class="form-info img-choose-box">
    <view class="title">
      <text style="color: #FF613B">*</text>
      请拍照上传全部住院缴费押金条
    </view>
    <view class="choose-img">
      <view class="img-item" wx:for="{{hospitalDepositSlip}}" wx:for-item="item" wx:for-index="index">
        <image src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/close.png" class="delete" @tap="delete({{item}})"/>
        <image src="{{item}}" @tap="previewImg({{item}})" />
      </view>
      <image
        wx:if="{{hospitalDepositSlip.length < 9}}"
        src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/upload-icon.png"
        class="img-item"
        @tap="onChooseImage"
      />
    </view>
  </view>
  <view class="tips">*若需邮件发票则请填写以下邮寄信息，邮费到付。若无需邮寄发票的可不填，后续可持身份证去收费窗口补打发票。</view>
  <view class="form-info">
    <view class="title">发票邮寄</view>
    <view class="form-item form-between">
      <view>
        <text style="color: #FF613B">*</text>
        是否办理结算发票邮寄
      </view>
      <radio-group bindchange="postRadioChange" class="radio-group">
        <label class="radio-label" wx:for="{{radioArray}}" wx:key="{{item.value}}">
          <view class="weui-cell__hd">
            <radio value="{{item.value}}" checked="{{invoiceMail === item.value}}" class="radio-item" color="#3ECDB5" />
          </view>
          <view class="weui-cell__bd">{{item.label}}</view>
        </label>
      </radio-group>
    </view>
    <block wx:if="{{invoiceMail === 1}}">
      <view class="form-item form-between">
        <view>邮寄方式</view>
        <radio-group bindchange="bindExpressChange" class="radio-group">
          <label class="radio-label" wx:for="{{expressArray}}" wx:key="{{item.value}}">
            <view class="weui-cell__hd">
              <radio value="{{item.value}}" checked="{{addresseeInfo.expressType == item.value}}" class="radio-item" color="#3ECDB5" />
            </view>
            <view class="weui-cell__bd">{{item.label}}</view>
          </label>
        </radio-group>
      </view>
      <view class="form-item">
        <view class="label">收件人姓名</view>
        <input placeholder="请输入收件人姓名" @input="onAddressInputValue" data-key="userName" value="{{addresseeInfo.userName}}" placeholder-class="input-placeholder"/>
      </view>
      <view class="form-item">
        <view class="label">手机号码</view>
        <input placeholder="请输入手机号码" @input="onAddressInputValue" data-key="mobile" value="{{addresseeInfo.mobile}}" placeholder-class="input-placeholder" maxlength="11"/>
      </view>
      <view class="form-item">
        <view class="label">所在地区</view>
        <picker mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="{{customItem}}" style="flex: 1">
          <view class="picker">
            <text wx:if="{{addresseeInfo.provinceName}}">{{addresseeInfo.provinceName}}/{{addresseeInfo.cityName }}/{{addresseeInfo.areaName }}</text>
            <text wx:else class="input-placeholder">请选择所在地区</text>
          <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
      <view class="form-item">
        <view class="label">详细地址</view>
        <textarea placeholder="请输入详细地址" auto-height="true" @input="onAddressInputValue" data-key="addressDetail" value="{{addresseeInfo.addressDetail}}" placeholder-class="input-placeholder"/>
      </view>
    </block>
  </view>
  <view class="btn">
    <view @tap="submit">出院结算</view>
  </view>
</view>
