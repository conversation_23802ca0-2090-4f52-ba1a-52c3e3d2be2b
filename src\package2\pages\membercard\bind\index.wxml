<view class="p-page">
  <outpatient :config.sync="patientConfig" :patient.sync="patient" :login.sync="login"></outpatient>
  <view class="page-body">
    <view class="card-panel-title">请输入会员卡信息：</view>
    <form bindsubmit="formSubmit" report-submit='true'>
      <view class="bindcard-list">
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title"><text class="color-red">*</text>卡号</text>
          </view>
          <view class="listitem-body">
            <input
              class="m-content {{errorElement.vipCardNo ? 'o-error' : ''}}" placeholder="请输入会员卡号" 
              cursor-spacing="{{CURSOR_SPACING}}" placeholder-style="color:{{errorElement.vipCardNo ? errorColor : placeholderColor}}"
              maxlength="7" id="vipCardNo" @input="inputTrigger" @focus="resetThisError" value="{{vipCardNo}}"
            />
          </view>
        </view>
        <view class="bindcard-listitem">
          <view class="listitem-head">
            <text class="list-title"><text class="color-red">*</text>密码</text>
          </view>
          <view class="listitem-body">
            <input class="m-content {{errorElement.vipPassWord ? 'o-error' : ''}}" type="password" placeholder="请输入会员卡密码"
              cursor-spacing="{{CURSOR_SPACING}}" @focus="resetThisError"
              placeholder-style="color:{{errorElement.vipPassWord ? errorColor : placeholderColor}}" value="{{vipPassWord}}"
              id="vipPassWord" maxlength="6" @input="inputTrigger"
            />
          </view>
        </view>
      </view>
    </form>
  </view>
  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit" @tap="bind">立即绑定</button>
    <view class="binduser-btn_line cancel-btn" @tap="goBack">取消</view>
  </view>
</view>
<toptip :toptip.sync="toptip" />
