<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" ></template>

<script>
import wepy from 'wepy';
import * as Api from './api';
import WxsUtils from "../../../../wxs/utils.wxs";
import { REQUEST_QUERY } from "@/config/constant";
import { validator as validatorFun } from "@/utils/utils";
import { uploadFile } from "@/utils/request";

export default class PageWidget extends wepy.page {
	config = {
		navigationBarTitleText: '完善样本信息',
	}

  components = {
  };

  wxs = {
    WxsUtils: WxsUtils
  };

  data={
    moreArray: [
      {
        value: 'FSH',
        name: 'FSH',
        checked: false
      },
      {
        value: 'LH',
        name: 'LH',
        checked: false
      },
      {
        value: '睾酮',
        name: '睾酮',
        checked: false
      },
      {
        value: 'E2',
        name: 'E2',
        checked: false
      },
      {
        value: 'PRL',
        name: 'PRL',
        checked: false
      },
      {
        value: 'INHB',
        name: 'INHB',
        checked: false
      }
    ],
    lessArray: [
      {
        value: 'FSH',
        name: 'FSH',
        checked: false
      },
      {
        value: 'LH',
        name: 'LH',
        checked: false
      },
      {
        value: '睾酮',
        name: '睾酮',
        checked: false
      },
      {
        value: 'E2',
        name: 'E2',
        checked: false
      },
      {
        value: 'PRL',
        name: 'PRL',
        checked: false
      },
      {
        value: 'INHB',
        name: 'INHB',
        checked: false
      }
    ],
    tempFilePaths: [],
    sampleNumber: '',
    expressnumber: '',
    files: [],
    xbtemplateJson: {},
    field1: `生育史:孕 (0) 次，产 (0) 次，流产 (0) 次。
    双方不良孕产详述 (含孕周、是否做过遗传学检测、病理等) :
    现存子女详述(含年龄、健康状况、遗传学检测等) :
    有无腮腺炎：       手术情况：     自身免疫药：      药物服用史：       睾丸大小:      
    有无B超检测结果：    `,
    field2: [],
    field3: [],
    orderId: '',
    productId: '',
    deptId: '',
    deptName: '',
    docname: '',
    perfectInfoDetail: {},
    patientName: '',
    idNumber: '',
    mobile: ''
  }

  onLoad(options) {
    const { orderId, productId, deptId, sampleNumber = '', deptName, patientName, idNumber, mobile, docname } = options;
    this.productId = productId;
    this.orderId = orderId;
    this.deptId = deptId;
    this.deptName = deptName;
    this.docname = docname;
    this.mobile = mobile;
    this.patientName = patientName;
    this.idNumber = idNumber;
    if(sampleNumber){
      this.handleDetail(sampleNumber);
    }
  }

	onShow() {}

  methods = {
    cancle() {
      wepy.navigateBack({
        delta: 1
      });
    },
    checkboxChange(e) {
      const { id } = e.currentTarget.dataset;
      const { value } = e.detail;
      this[id] = value;
      this.$apply();
    },
    inputNumber(e) {
      const { id } = e.currentTarget.dataset;
      const { value } = e.detail;
      this[id] = value;
      this.$apply();
    },
    scanCode() {
      if(JSON.stringify(this.perfectInfoDetail) !== "{}" && this.perfectInfoDetail.id) return;
      wx.scanCode({
        success: (res) => {
          console.log(res.result);
          this.sampleNumber = res.result;
          this.$apply();
        }
      })
    },
    longTapImg(e) {
      const { url } = e.currentTarget.dataset;
      wx.showActionSheet({
        itemList: ['保存到手机', '删除'],
        success: (res) => {
          const actionList = ['save', 'delete'];
          this.bindItemTap({ action: actionList[res.tapIndex] }, url);
        },
        fail: (res) => {
          console.log(res.errMsg)
        }
      });
    },
    viewImage(e) {
      const { path, url } = e.currentTarget.dataset;
      wepy.previewImage({
        urls: this.tempFilePaths || [],
        current: url || '',
      });
    },
    async updateImg() {
      let that = this;
      const { errMsg = "", tempFilePaths = [] } = await wepy.chooseImage({
        count: 9 - (this.tempFilePaths.length || 0),
        sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"]
      });
      if (errMsg == "chooseImage:ok") {
        const tempFilePath = tempFilePaths;
        wepy.showLoading({ title: "发送中...", mask: true });
        tempFilePaths.forEach(v => {
          this.pushImg(v);
        })
      }
    },
  }

  async handleDetail(sampleNumber) {
    const { code, data = {}, msg } = await Api.getBySamplenumber({ id: sampleNumber });
    this.perfectInfoDetail = data;
    this.expressnumber = data.expressnumber || '';
    this.sampleNumber = data.sampleNumber || '';
    const informObj = data.informedConsentForm ? JSON.parse(data.informedConsentForm) : {};
    this.field1 = informObj.field1 ? informObj.field1 : this.field1;
    this.field2 = informObj.field2.split(',');
    this.field3 = informObj.field3.split(',');
    this.field2.forEach(v => {
      this.moreArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    this.field3.forEach(v => {
      this.lessArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    this.tempFilePaths = data.files ? data.files.split(',') : [];
    this.$apply();
  }

  async checkunique(sampleNumber) {
    const { code, data = {}, msg } = await Api.checkunique({sampleNumber});
    if(code == 0){
      this.perfectInfo()
    }
  }

  async perfectInfo() {
    const { orderId, sampleNumber, expressnumber, field1, field2, field3, productId, tempFilePaths, deptId, deptName, patientName, idNumber, mobile, docname } = this.data;
    const params = {
      orderId,
      productId,
      sampleNumber,
      expressnumber,
      institutionId: deptId,
      submitHospital: deptId,
      submitDoctor: docname,
      sjzName: patientName,
      sjzIdNum: idNumber,
      sjzPhone: mobile,
      informedConsentForm: JSON.stringify({
        field1,
        field2: field2.length ? field2.join(',') : '',
        field3: field3.length ? field3.join(',') : ''
      }),
      files: tempFilePaths.length ? tempFilePaths.join(',') : ''
    }

    if(JSON.stringify(this.perfectInfoDetail) !== "{}" && this.perfectInfoDetail.id){
      params.id = this.perfectInfoDetail.id;
    }
    const { code, data = {}, msg } = await Api.perfectSample(params);
    if(code === 0) {
      wepy.navigateBack({
        delta: 1
      });
      wx.showToast({
        title: "完善样本成功",
        icon: "none",
        duration: 1400
      });
     
    }
  }

  confirm() {
    const { orderId, sampleNumber, expressnumber, field1, field2, field3, productId, tempFilePaths, deptId, deptName } = this.data;
    
    if(!sampleNumber){
      wx.showToast({
        title: "样本编号不能为空",
        icon: "none",
        duration: 1400
      });
      return
    }
    if(!this.perfectInfoDetail.id){
      this.checkunique(sampleNumber);
      return;
    }
    this.perfectInfo()
    
  }

  bindItemTap(item = {}, url) {
    const { action = '' } = item;
    if (!url) {
      wepy.showModal({
        title: '提示',
        content: '请选择有效图片',
        showCancel: false,
      });
      return;
    }
    if (action === 'save') {
      this.saveImage(url);
    } else if (action === 'delete') {
      this.deleteImg(url);
    }
  }
  async saveImage(url) {
  // 保存图片
    wepy.showLoading({ title: '保存中', mask: true });
      wx.downloadFile({
        url: url || '',
        success: (res) => {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: (res) => {
              wepy.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 1000,
                success: () => {
                }
              });
            },
            fail: (res) => {
              wepy.hideLoading();
              if (res.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                // this.getSetting();
                return;
              }
              wepy.showModal({
                title: '提示',
                content: '保存图片失败',
                showCancel: false,
              });
            }
          })
        },
        fail:function(res){
          wepy.hideLoading();
          wepy.showModal({
            title: '提示',
            content: '保存图片失败',
            showCancel: false,
          });
        }
      });
  }
  deleteImg(url) {
    this.tempFilePaths = this.tempFilePaths.filter(v => v !== url);
    this.$apply();
  }

  async pushImg(v) {
    const { code, data } = await uploadFile(
      "/api/files/uploadpic",
      v
    );
    if (code === 0) {
      this.tempFilePaths.push(data.url);
      
      this.$apply();
    } else {
      wx.showToast({
        title: "图片上传失败",
        icon: "none",
        duration: 1400
      });
    }
  }
}
</script>
		
