import { post, postBy<PERSON><PERSON> } from '@/utils/request';
import { REQUEST_QUERY } from '@/config/constant';

export const addHealthRecord = (param) => postByJson('/api/healthRecord/addHealthRecord', param);

export const updateHealthRecord = (param) => postByJson('/api/healthRecord/updateHealthRecord', param);

export const getUserInfo = (param) => post('/api/user/patientinfo', param);

export const getPatientInfo = (param) => post(`/api/customize/register/queryRelationPatients?_route=h${REQUEST_QUERY.platformId}`, param);
