@import "../../../../resources/style/mixins";

page{
  position: relative;
  font-size: 30rpx;
  line-height: 1.5;
  color: @hc-color-title;
  background-color: @hc-color-bg;
  font-family: PingFangSC-Regular, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}


.m-tips{
  padding: 20rpx;
  color: @hc-color-title;
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 4rx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.02);
}

.desc-modal-mask{
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  .desc-modal{
    width: 670rpx;
    border-radius: 8rpx;
    background-color: @hc-color-white;
    .desc-title{
      text-align: center;
      height: 48rpx;
      padding: 40rpx 0 32rpx;
      font-size: 34rpx;
      font-weight: 500;
      color: @hc-color-primary;
    }
    .desc-content{
      width: 100%;
      max-height: 700rpx;
      min-height: 200rpx;
      box-sizing: border-box;
      padding: 0 30rpx 30rpx;
      color: @hc-color-text;
    }
    .phone{
      color: @hc-color-assist;
    }
    .desc-footer{
      height: 96rpx;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      border-top: 2rpx solid @hc-color-border;
      >view{
        flex: 1;
        font-size: 34rpx;
        font-weight: 500;
        color: @hc-color-gray;
        text-align: center;
        padding: 24rpx 0;
        &+view{
          border-left: 2rpx solid @hc-color-border;
        }
      }
      .agree{
        color: @hc-color-primary;
      }
    }
  }
}

.main{
  padding: 20rpx;
  .list{
    margin-top: 20rpx;
    .title-box{
      display: flex;
      margin-bottom: 20rpx;
      image{
        display: flex;
        width: 50rpx;
        height: 50rpx;
        margin-right: 20rpx;
      }
      .list-title{
        font-size: 32rpx;
        line-height: 50rpx;
      }
    }
    .table{
      border-top: 2rpx solid #ccc;
      border-left: 2rpx solid #ccc;
      background: #fff;
      .tr{
        display: flex;
        text-align: center;
        width: 100%;
        .th{
          font-weight: bold;
          font-size: 24rpx;
          line-height: 32rpx;
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
        }
        .td{
          border-right: 2rpx solid #ccc;
          border-bottom: 2rpx solid #ccc;
          width: 100%;
          padding: 10rpx 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.address-box{
  margin: 0 auto;
  padding: 20rpx;
  .adress-list{
    .item-card{
      padding: 20rpx;
      margin: 10rpx;
      border-radius: 10rpx;
      background: #Fff;
      .card-content{
        border-bottom: 2rpx solid #ccc;
        padding-bottom: 10rpx;
        .content-head{
          display: flex;
          .head-name{
            font-weight: bold;
            margin-right: 20rpx;
          }
          .head-phone{
            font-weight: bold;
            font-size: 28rpx;
          }
        }
        .content-addr{
          color: #ccc;
        }
      }
      .card-bottom{
        display: flex;
        align-items: center;
        margin-top: 10rpx;
        .check{
          flex: 1;
        }
        .btn{
          padding: 10rpx 20rpx;
          margin: 10rpx;
          border: 2rpx solid #ccc;
          border-radius: 10rpx;
        }
      }
    }
  }
}

.m-adduser{
  padding: 49rpx 37rpx 48rpx 154rpx;
  background: url("REPLACE_IMG_DOMAIN/his-miniapp/icon/common/icon-add.png") no-repeat 30rpx center;
  background-size: 98rpx;
  background-color: #fff;
  margin: 10rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.02);
  position: relative;
  
  &:after{
    content: ' ';
    position: absolute;
    right: 30rpx;
    top: 50%;
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #C7C7CC;
    border-bottom: 5rpx solid #C7C7CC;
    transform: translate(-8rpx, -50%) rotate(-45deg);
  }

  .add-title{
    font-size: 34rpx;
  }
  .add-text{
    font-size: 28rpx;
    color: @hc-color-text;
  }

}

.btn-box{
  margin-top: 10rpx;
  display: flex;
  padding: 20rpx;
  justify-content: space-around;
  .btn{
    padding: 20rpx 50rpx;
    border: 2rpx solid #000;
    font-size: 32rpx;
    border-radius: 10rpx;
  }
  .cancel{
    background: #fff;
  }
  .sure{
    background: @hc-color-primary;
    border: none;
  }
}
.wgt-user-box{
  position: relative;
  z-index: 1;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 4rpx 0 rgba(0,0,0,0.05);
}
.wgt-user-main{
  display: flex;
  align-items: center;
}
.wgt-user-main-info{
  display: flex;
  align-items: center;
  flex: 1;
}
.wgt-user-main-info-tit{
  font-size: 37rpx;
  color:@hc-color-title;
}
.wgt-user-main-info-label{
  margin-left: 20rpx;
  flex: 1;
  color:@hc-color-text;
  font-size: 28rpx;
}