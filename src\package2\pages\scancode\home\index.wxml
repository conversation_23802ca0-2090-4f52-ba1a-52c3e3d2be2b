<view class="p-page {{showPop ? 'hiddenScroll' : ''}}">
  <view class="tips">温馨提示：请根据医生的建议，选择对应的检测项目进行开单缴费。</view>
  <block wx:if="{{showcontent}}">
    <view class="header">
      <view class="header_title">
        <text>请将条形码对准扫码单</text>
        <image
          class="s.header_icon"
          src="https://wechat.jiahuiyiyuan.com/showfiles/publicfile/A92B09499B82DB3C71C29ADC94E6BE4A.png"
          @tap="openSetting"
        />
      </view>
      <view wx:if="{{!hideCamera}}">
        <camera
          class="header_camera"
          mode="scanCode"
          resolution="high"
          device-position="back"
          flash="off"
          frame-size="large"
          binderror="onCameraError"
          bindscancode="onCameraScanCode"
        />
      </view>
    </view>
    <view class="p-patient-info" wx:if="{{!patientInfo.patientId}}">
      <view class="info-item">
        <view class="label required">受检者姓名</view>
        <input class="info-ipt" placeholder="请输入姓名" maxlength="20" id="patientName" value="{{myInfo.patientName}}" @input="inputTrigger" />
      </view>
      <view class="info-item">
        <view class="label required">证件号码</view>
        <input class="info-ipt" placeholder="请输入证件号码" id="idNumber" value="{{myInfo.idNumber}}" @input="inputTrigger" />
      </view>
      <view class="info-item">
        <view class="label required">受检者手机号</view>
        <input class="info-ipt" placeholder="请输入手机号" maxlength="11" type="number" id="mobile" value="{{myInfo.mobile}}" @input="inputTrigger" />
      </view>
      <view class="info-item">
        <view class="label">样本编号</view>
        <input class="info-ipt" placeholder="请输入或扫描条形码" id="sampleNumber" value="{{myInfo.sampleNumber}}" @input="inputTrigger" />
      </view>
    </view>
    <view class="p-content">
      <view class="content-title">请选择需要检测的项目</view>
      <view class="content-group">
        <view class="box-list" wx:for="{{productList}}" wx:for-index="idx" wx:key="productId">
          <view class="group-left">
            <image wx:if="{{item.selected}}" src="/resources/images/select-on.png" @tap="selctItem({{idx}})" />
            <image wx:else src="/resources/images/select.png" @tap="selctItem({{idx}})" />
            <text class="left-title">
              <block wx:if="{{item.parproductName}}">{{item.parproductName}}—</block>{{item.productName}}
            </text>
            <text class="product-content" wx:if="{{item.childReleaseFlag == '1' || item.releaseFlag == 1}}" @tap="introduction({{item.childReleaseFlag == '1' ? item.childIntroduction : item.introduction}})">产品介绍</text>
          </view>
          <view class="group-right">{{WxsUtils.formatMoney(item.productprice,100)}}元</view>
        </view>
        <view class="box-list">
          <view class="group-left">
          <image wx:if="{{otherSelected}}" src="/resources/images/select-on.png" @tap="selOther({{false}})" />
          <image wx:else src="/resources/images/select.png" @tap="selOther({{true}})" />
          <text class="left-title">其他<text class="small-title">（若以上无可选项目，可勾选此项）</text></text>
        </view>
        </view>
      </view>
      <block wx:if="{{otherSelected}}">
        <view class="content-title">其他项目金额</view>
        <view class="list-item">
          <input type="digit" placeholder="请输入金额" value="{{otherFee}}" @input="feeInputValue" />
        </view>
        <view class="content-title">备注</view>
        <view class="list-item">
          <input placeholder="请备注其他项目名称"  value="{{otherName}}" @input="nameInputValue" />
        </view>
      </block>
      <view wx:if="{{productObj.reportMailingType === '2'}}" class="msg">注：检测需支付报告快递费用{{WxsUtils.formatMoney(productObj.reportMailingFees, 100)}}元</view>
    </view>
    <view class="p-content pay-type-content" wx:if="{{productObj.xcxOutpatientPaymentFlag == 1}}">
      <picker	header-text="切换支付方式" range="{{payTypeArray}}" value="0" bindchange="bindChangePop">
        <view class="picker">
          <view class="title content-title">支付方式</view>
          <view class="pay-type {{payTypeIndex === null ? 'empty' : ''}}">
            <image class="pay-type-icon" src="{{payTypeIndex === 1 ? '/resources/images/wechatPay.png' : payTypeIndex === 2 ? '/resources/images/unlinePay.png' : '' }}" />
            {{payType}}
            <view class="arrow-icon"></view>
          </view>
        </view>
      </picker>
      <view class="pic-content" wx:if="{{payTypeIndex === 2}}">
        <view class="content-title">上传门诊缴费凭证</view>
        <view class="content-list">
          <view class="content">
            <view class="content-view">
              <image wx:if="{{tempFilePaths.length > 0}}" wx:for="{{tempFilePaths}}" wx:key="index" wx:for-item="item" class="img-item" src="{{item}}" bindlongpress="longTapImg" data-url="{{item}}" @tap="viewImage" data-path="{{item}}"  />
              <image
                wx:if="{{tempFilePaths.length < 3}}"
                src="REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/upload-icon.png"
                class="img-item"
                @tap="updateImg"
              />
            </view>
          </view>
         
          <view wx:if="{{tempFilePaths.length === 0}}">限制最多上传3张，在确认支付前可以查看和删除</view>
        </view>
      </view>
    </view>
    <view class="p-btn-box">
      <view class="confirm-btn" @tap="confirm">确认</view>
    </view>
  </block>
  <empty :config.sync="emptyConfig">
    <block slot="text">{{errorText || '暂无可检测项目，请联系医生处理'}}</block>
  </empty>
  <navigator url="/package2/pages/scancode/recordlist/index" hover-class="none" class="record">扫码缴费记录</navigator>
  <view class="pop-view {{showPop ? 'active' : ''}}">
    <view class="wgt-user-pop-mask" @tap="bindClosePop" />
    <view class="pop-content">
      <view class="pop-top">
        <view class="pop-title">切换支付方式</view>
        <image class="delete-icon" src="/resources/images/delete.png" @tap="bindClosePop" />
      </view>
      <picker-view indicator-style="height: 50px;" style="width: 100%; height: 120px;" bindchange="bindChangePop" value="0">
        <picker-view-column>
          <view wx:for="{{payTypeArray}}" wx:key="payIndex" style="line-height: 50px; text-align: center;">{{item}}</view>
        </picker-view-column>
      </picker-view>
      <view class="pop-btn">
        <view class="btn" @tap="confirmType">确认切换</view>
      </view>
    </view>
  </view>
</view>


