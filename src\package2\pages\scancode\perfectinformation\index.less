@import "../../../../resources/style/mixins";

.p-page{
  .page-content{
    margin-bottom: 16rpx;
    padding: 32rpx 24rpx;
    background: #fff;
    .content-title{
      font-weight: bold;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
    }
    .content-list{
      padding: 24rpx 0;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
      &:last-child{
        padding-bottom: 0;
        border: none;
      }
      &.list-code{
        display: flex;
        align-items: center;
      }
      &.disabled{
        background-color: #eee;
      }
    }
    .list-title{
      position: relative;
      padding-left: 16rpx;
      font-size: 32rpx;
      width: 208rpx;
      color: rgba(0, 0, 0, 0.9);
      &.requird::before{
        content: "*";
        position: absolute;
        left: 0;
        color: red;
      }
    }
    .list-info{
      flex: 1;
      width: 100%;
      color: #000;
      &.disabled{
        background: rgba(0, 0, 0, 0.05);
      }
    }
    .camera-box{
      width: 100rpx;
      height: 46rpx;
    }
    .info-camera{
      width: 46rpx;
      height: 46rpx;
    }
    .content-area{
      margin: 24rpx 0;
      padding: 32rpx;
      border-radius: 12rpx;
      background: linear-gradient(90deg, rgba(48, 161, 166, 0.1) 0%, rgba(47, 132, 139, 0.1) 100%);
      .name{
        margin-bottom: 16rpx;
        font-weight: bold;
        font-size: 40rpx;
      }
      .area-name, .area-msg{
        font-size: 26rpx;
      }
    }
    .content-textarea{
      margin: 24rpx 0;
      padding: 24rpx;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.7);
      border: 1rpx solid rgba(0, 0, 0, 0.1);
      border-radius: 8rpx;
    }

    .check-content{
      margin: 24rpx 0;
      .check-title{
        margin-bottom: 16rpx;
      }
      .check-list{
        .check-list-group, .weui-cell{
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }
        .weui-cell{
          margin: 14rpx 14rpx 0 0;
        }
      }
    }
    .content-list{
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
      .img-item{
        margin-right: 24rpx;
        flex: 0 0 196rpx;
        width: 216rpx;
        height: 216rpx;
        &:nth-child(3n){
          margin: 0;
        }
      }
    }
  }
  .btn-box{
    padding: 64rpx 40rpx 64rpx 40rpx;
    .btn{
      width: 100%;
      height: 100rpx;
      text-align: center;
      line-height: 100rpx;
      font-weight: bold;
      font-size: 34rpx;
      border-radius: 76rpx;
      background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      &.confirm{
        margin-bottom: 24rpx;
        color: #fff;
      }
      &.cancel{
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.7);
      }
    }
  }
}
