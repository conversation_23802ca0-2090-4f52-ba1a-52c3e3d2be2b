<style lang="less" src="./index.less" />
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';

  export default class Pop extends wepy.component {
    props = {
      config: {
        type: Object,
        twoWay: true,
        default: {
          show: false,
        }
      }
    };

    onLoad(options) {
    }

    data = {
    };

    methods = {
      bindClose(){
        this.config.show = false;
      }
    };
  }
</script>
