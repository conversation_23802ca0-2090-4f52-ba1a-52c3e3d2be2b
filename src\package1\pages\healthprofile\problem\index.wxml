<form bindsubmit="formSubmit" report-submit='true'>
  <view class="m-cells">
      <view class="m-cell {{currentTab == '1' ? 'active' : ''}}" data-current='1' @tap="clickTab">有问题</view>
      <view class="m-boxs {{currentTab == '1' ? '' : 'displayNone'}}">
        <block wx:for="{{ diseaseList }}" wx:for-index="idx" wx:key="idx">
          <view class="u-box {{item.isChecked ? 'active' : ''}}"
            @tap="selectBoxs({{idx}})">{{ item.dictValue }}</view>
        </block>
      </view>
  </view>

  <view class="m-cells">
      <view class="m-cell {{currentTab == '0' ? 'active' : ''}}" data-current='0' @tap="clickTab">没有问题</view>
      <view class="m-cell {{currentTab == '2' ? 'active' : ''}}" data-current='2' @tap="clickTab">不清楚</view>
  </view>

  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">保存</button>
  </view>
</form>

<toptip :toptip.sync="toptip"/>