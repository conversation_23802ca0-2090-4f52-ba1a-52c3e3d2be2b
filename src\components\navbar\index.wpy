<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>
<script>
import wepy from 'wepy';
export default class NavTop extends wepy.component {
  props = {
    isBack: {
      type: String,
      default: '1'  
    },
    bgColor: {
      type: String,
      default: '#fff'
    },
    color: {
      type: String,
      default: 'black'
    },
  };
  data = {
    navBarHeight: 0, //导航栏高度
    menuRight: 0, // 胶囊距右方间距（方保持左、右间距一致）
    menuBotton: 0,
    menuHeight: 0,
  };
  components = {};
  onLoad(options) {
    console.log(this.bgColor, '=====28')
    const res = wepy.getSystemInfoSync();
    // 胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    this.navBarHeight = (menuButtonInfo.top - res.statusBarHeight) * 2 + menuButtonInfo.height + res.statusBarHeight;
    this.menuBotton = menuButtonInfo.top - res.statusBarHeight;
    this.menuRight = res.screenWidth - menuButtonInfo.right;
    this.menuHeight = menuButtonInfo.height;
    this.$apply();
  }
  // 交互事件，必须放methods中，如tap触发的方法
  methods = {
    backTo() {
      wepy.navigateBack({
        delta: 1
      });
    },
    goHome(){
      wepy.reLaunch({
        url: '/pages/home/<USER>'
      });
    }
  };
}
</script>