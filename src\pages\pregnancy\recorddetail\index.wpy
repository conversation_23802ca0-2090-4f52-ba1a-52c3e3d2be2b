<style lang="less" src="./index.less"></style>
<template lang="wxml" src="./index.wxml"></template>

<script>
import wepy from "wepy";

import { TYPE_MAP, CODE_TYPE } from "@/config/constant";
import DetailStatus from "@/components/detailstatus/index";
import * as Utils from "@/utils/utils";
import * as Api from "./api";

export default class PageWidget extends wepy.page {
  config = {
    navigationBarTitleText: "申请详情"
  };

  data = {
    //详情
    detailData: {},
    // 顶部状态配置
    statusConfig: {},
    imageObj: {
      1: [],
      2: [],
      3: []
    },
    tuoMingmanIdNo: "",
    tuoMingWonmentIdNo: ""
  };

  components = {
    "detail-status": DetailStatus
  };

  onLoad(options) {
    this.orderDetail(options);
  }

  events = {
    "set-navigationbar-color": param => {
      wepy.setNavigationBarColor(param);
    }
  };

  methods = {
    actionSheetTap(index = 0, phototype = "idCard") {
      wx.showActionSheet({
        itemList: ["删除"],
        success: res => {
          this.removeItem({ phototype, index });
        },
        fail: res => {
          console.log(res.errMsg);
        }
      });
    },
    previewImage(item, phototype) {
      let objIndex;
      if (phototype === "idCard") {
        objIndex = 1;
      }
      if (phototype === "marry") {
        objIndex = 2;
      }
      if (phototype === "other") {
        objIndex = 3;
      }
      const imageList = this.imageObj[objIndex] || [];
      const imageUrlList = imageList.map(item => {
        return item.filePath || "";
      });
      wepy.previewImage({
        urls: imageUrlList || [],
        current: item.filePath || ""
      });
    },
    canelApply() {
      wx.showModal({
        content: "确定取消申请吗？",
        success: res => {
          if (res.confirm) {
            this.canelApplyProject();
          } else {
            return;
          }
        }
      });
    },
    retrayApply() {
      wx.showModal({
        content: "确定重新申请吗？",
        success: res => {
          if (res.confirm) {
            this.retryApplyProject();
          } else {
            return;
          }
        }
      });
    }
  };

  async retryApplyProject() {
    const params = {
      projectId: this.detailData.projectId || "",
      projectName: this.detailData.projectName || "",
      pid: this.detailData.pid || "",
      manGrid: this.detailData.manGrid || "",
      manName: this.detailData.manName || "",
      manAge: this.detailData.manAge || "",
      manIdNo: this.detailData.manIdNo || "",
      manMobile: this.detailData.manMobile || "",
      womanGrid: this.detailData.womanGrid || "",
      womanName: this.detailData.womanName || "",
      womanAge: this.detailData.womanAge || "",
      womanIdNo: this.detailData.womanIdNo || "",
      womanMobile: this.detailData.womanMobile || "",
      address: this.detailData.address || "",
      ivfNo: this.detailData.ivfNo || "",
      doctorName: this.detailData.doctorName || "",
      idCardImg: this.detailData.idCardImg || "",
      marriageCertificateImg: this.detailData.marriageCertificateImg || "",
      otherIntroduceName: this.detailData.otherIntroduceName || "",
      otherIntroduceImg: this.detailData.otherIntroduceImg || "",
      applicantName: this.detailData.applicantName || ""
    };
    console.log("111");
    wx.redirectTo({
      url: `/pages/pregnancy/material/index?title=${params.projectName}&pid=${
        params.pid
      }&name=${params.applicantName}&otherIntroduce=${
        params.otherIntroduceName
      }&projectId=${params.projectId}&idCardImg=${
        params.idCardImg
      }&marriageCertificateImg=${
        params.marriageCertificateImg
      }&otherIntroduceImg=${params.otherIntroduceImg}`
    });
  }

  async canelApplyProject() {
    const { code, data } = await Api.cancleApply({ id: this.detailData.id });
    if (code === 0) {
      wepy.showToast({
        title: "取消申请成功",
        icon: "success"
      });
      setTimeout(() => {
        wx.redirectTo({
          url: "/pages/pregnancy/projectlist/index"
        });
      }, 1000);
    } else {
      return;
    }
  }

  async orderDetail(item = {}) {
    const { code, data } = await Api.orderDetail({ id: item.id });
    if (code !== 0) {
      return;
    }
    const idCardImgArr = data.idCardImg.split(",");
    const idCardImg = idCardImgArr.map(i => {
      return { filePath: i };
    });
    const marryImgArr = data.marriageCertificateImg.split(",");
    const marryImg = marryImgArr.map(i => {
      return { filePath: i };
    });
    const otherImgArr = data.otherIntroduceImg.split(",");
    const otherImg = otherImgArr.map(i => {
      return { filePath: i };
    });
    this.imageObj[1] = idCardImg;
    this.imageObj[2] = marryImg;
    this.imageObj[3] = otherImg;
    this.detailData = data;
    this.tuoMingmanIdNo = Utils.tuoMing(data.manIdNo, 4, -4);
    this.tuoMingWonmentIdNo = Utils.tuoMing(data.womanIdNo, 4, -4);

    this.statusConfig = this.getStatus() || {};
    this.$apply();
  }

  /**
   * 获取订单描述文案
   */
  getStatus() {
    const { status } = this.detailData;
    let stsObj = {};

    // 需要转成map
    if (status == "2") {
      stsObj = {
        statusName: "初审通过复审中",
        text:
          "您提交的爱心助孕项目申请已通过初审，工作人员正进行资格复审中，请留意医院公众号消息通知。"
      };
    } else if (status == "0") {
      stsObj = {
        statusName: "初审待审核",
        text:
          "您已成功提交爱心助孕项目申请，工作人员会在1-7个工作日内初审完毕，初审通过后还需要进行复审，请留意医院公众号消息。"
      };
    } else if (status == "1") {
      stsObj = {
        statusName: "初审通过待签字",
        text: "您提交的爱心助孕项目申请已通过初审"
      };
    } else if (status == "3") {
      stsObj = {
        statusName: "初审不通过",
        text: `您提交的爱心助孕申请项目申请初审不通过，请核对信息后重新提交，若有疑问可联系医院工作人员。`
      };
    } else if (status == "4") {
      stsObj = {
        statusName: "复审通过",
        text: "您提交的爱心助孕项目申请已通过审核"
      };
    } else if (status == "5") {
      stsObj = {
        statusName: "复审不通过",
        text:
          "您提交的爱心助孕申请项目申请复审不通过，请核对信息后重新提交，若有疑问可联系医院工作人员。"
      };
    } else if (status == "6") {
      stsObj = {
        statusName: "申请已取消",
        text: "您的爱心助孕项目申请已取消，可重新申请"
      };
    } else {
      stsObj = {
        statusName: "申请异常",
        text: `操作超时，请咨询医院窗口为您处理。`
      };
    }
    return {
      ...stsObj,
      status
    };
  }
}
</script>
