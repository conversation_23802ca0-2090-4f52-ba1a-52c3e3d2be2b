{"name": "p242", "version": "0.0.1", "main": "dist/app.js", "scripts": {"dev": "cross-env NODE_ENV=develop API_ENV='' wepy build --watch", "dev:u": "cross-env NODE_ENV=develop API_ENV='u' wepy build --watch", "dev:s": "cross-env NODE_ENV=develop API_ENV='s' wepy build --watch", "build:s": "cross-env NODE_ENV=production API_ENV='s' wepy build --no-cache", "build:u": "cross-env NODE_ENV=production API_ENV='u' wepy build --no-cache", "build:weapp": "cross-env NODE_ENV=production API_ENV='' wepy build --no-cache", "clean": "find ./dist -maxdepth 1 -not -name 'project.config.json' -not -name 'dist' | xargs rm -rf", "test": "echo \"Error: no test specified\" && exit 1"}, "wepy": {"module-a": false, "./src/components/list": "./src/components/wepy-list.wpy"}, "author": "", "license": "MIT", "dependencies": {"@haici/monitor-his-wepy": "^1.0.3", "@haici/request-filter": "^1.0.16", "@haici/request-wepy": "^1.0.4", "jsencrypt": "^3.2.1", "moment": "^2.23.0", "promise-polyfill": "^7.1.1", "wepy": "1.7.2-alpha3", "wepy-async-function": "1.4.6", "wepy-com-toast": "^1.0.2", "wepy-plugin-img2base64": "^1.0.7", "wepy-plugin-replace": "^1.5.10", "wxmp-rsa": "^2.1.0", "blueimp-md5": "^2.18.0"}, "devDependencies": {"@haici/scripts": "^1.0.3-beta.3", "babel-eslint": "^7.2.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "cross-env": "^5.1.3", "less": "^3.8.1", "less-plugin-autoprefix": "^2.0.0", "wepy-compiler-babel": "^1.5.1", "wepy-compiler-less": "^1.3.10", "wepy-plugin-uglifyjs": "^1.3.7"}}