@import "../../../resources/style/mixins";

page {
  height: 100%;
}

.p-page {
  height: 100%;
}
.empty-content{
  width: 100%;
  height: 100vh;
  text-align: center;
  line-height: 100vh;
  font-weight: bold;
}

.m-list {
  // margin-top: 20rpx;
  // margin-bottom: 20rpx;
  margin: 30rpx;
  background-color: #fff;

  &.list-all-box {
    padding-top: 0;
    border-top: 1rpx solid @hc-color-border;
  }

  .list-item {
    display: block;
    margin-left: 30rpx;

    border-bottom: 1rpx solid @hc-color-border;
  }

  .item-box {
    display: flex;
    padding: 30rpx 30rpx 30rpx 0;
    align-items: center;

  }

  .item-other {
    margin-top: -10rpx;
    font-size: 28rpx;
    color: @hc-color-warn;
    padding-bottom: 20rpx;
  }

  .other-date {
    &:after {
      display: inline;
      content: '、';
    }

    &:last-child {
      &:after {
        content: '';
      }
    }
  }

  .list-item {}

  .item-hd {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 119rpx;
    height: 133rpx;
    margin-right: 30rpx;
    border-radius: 12rpx;
    overflow: hidden;
    text-align: center;

    image {
      vertical-align: top;
    }
  }

  .item-bd {
    flex: 1;
    overflow: hidden;
  }

  .bd-info {
    display: flex;
  }

  .info-lt {
    flex: 1;
  }

  .lt-title {
    color: @hc-color-title;
    font-size: 36rpx;
    font-weight: 600;
  }

  .lt-text {
    color: @hc-color-text;
    font-size: 30rpx;
    margin-top: 5rpx;
  }

  .info-rt {
    margin-left: 24rpx;
    text-align: right;
  }

  .rt-num {
    font-size: 28rpx;
    color: @hc-color-title;
    margin-right: 10rpx;
  }

  .unit-label {
    display: inline-block;
    background-color: @hc-color-warn;
    border-radius: 100rpx;
    line-height: 40rpx;
    white-space: nowrap;
    padding: 0 12rpx;
    color: #fff;
    font-size: 28rpx;

    .label-disabled {}
  }

  .bd-text {
    padding-top: 8rpx;
    color: #aaa;
    font-size: 28rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .bd-extra {
    position: relative;
    padding-top: 5rpx;
    color: #aaa;
    font-size: 24rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    &.active {
      padding-right: 30rpx;

      &:after {
        position: absolute;
        border-radius: 50%;
        content: '';
        width: 16rpx;
        height: 16rpx;
        background-color: #F76260;
        top: 12rpx;
        right: 0;
      }
    }
  }

  .list-item.no-extra {

    .lt-text {
      margin-top: 20rpx;
    }

    .bd-info {
      align-items: center;
    }
  }
}

.tips {
  font-size: 26rpx;
  color: @hc-color-text;
  margin: 30rpx;
  word-break: break-all;
}

.bottom-css {
  position: absolute;
  width: 100%;
  bottom: 50rpx;
  text-align: center;
  color: #989898;
  font-size: 30rpx;
}