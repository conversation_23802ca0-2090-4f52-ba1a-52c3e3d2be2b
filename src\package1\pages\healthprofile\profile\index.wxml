<view class="container">
  <!-- <view class="m-tab">
    <view class="unit-tab">
      <view class="unit-tab-li tab-li {{tabIndex === 0 ? 'active' : ''}}" @tap="bindChangeTabIndex({{0}})">生殖档案
      </view>
      <view class="unit-tab-li tab-li {{tabIndex === 1 ? 'active' : ''}}" @tap="bindChangeTabIndex({{1}})">遗传档案
      </view>
    </view>
  </view> -->

  <view hidden="{{tabIndex !== 0}}">
    <view class="m-profile-card">
      <view class="m-profile-title">
        <view class="m-title-icon women">
          ♀
          <!-- <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/p242/women.png' /> -->
        </view>
        <view class="m-title-content">女方健康信息</view>
      </view>
      <view class="m-list">
        <view class="list-item" @tap="jumppage('baseinfo', {{femalInfo}})">
          <view class="item-main">
            <view class="main-tit">基本信息</view>
            <view wx:if="{{femalInfo.basicInfo}}" class="main-txt">
              <text>{{femalInfo.basicInfo.name || '暂无姓名'}}/</text>
              <text>{{femalInfo.basicInfo.idNo || '暂无证件号码'}}/</text>
              <text>{{femalInfo.basicInfo.profession || '暂无职业'}}/</text>
              <text>{{femalInfo.basicInfo.nation || '暂无民族'}}/</text>
              <block wx:if="{{femalInfo.basicInfo.height}}"><text>{{femalInfo.basicInfo.height}}cm/</text></block>
              <block wx:if="{{!femalInfo.basicInfo.height}}"><text>暂无身高/</text></block>
              <block wx:if="{{femalInfo.basicInfo.weight}}"><text>{{femalInfo.basicInfo.weight}}kg/</text></block>
              <block wx:if="{{!femalInfo.basicInfo.weight}}"><text>暂无体重/</text></block>
              <text>{{femalInfo.basicInfo.address || '暂无地址'}}/</text>
              <text>{{femalInfo.basicInfo.education || '暂无教育程度'}}</text>
            </view>
            <view wx:if="{{!femalInfo.basicInfo}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('marriage', {{femalInfo}})">
          <view class="item-main">
            <view class="main-tit">婚育史</view>
            <view wx:if="{{femalInfo.marriageRecord}}" class="main-txt">
              <block>怀孕{{femalInfo.marriageRecord.hyjc || 0}}次/</block>
              <block>生育{{femalInfo.marriageRecord.syjc || 0}}次/</block>
              <block>早产{{femalInfo.marriageRecord.zcjc || 0}}次</block>
            </view>
            <view wx:if="{{!femalInfo.marriageRecord}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('menstrual', {{femalInfo}})">
          <view class="item-main">
            <view class="main-tit">月经史</view>
            <!-- <view class="main-txt">周期28-31天/正常</view> -->
            <view wx:if="{{femalInfo.periodRecord}}" class="main-txt">
              <block>周期{{femalInfo.periodRecord.yjzq || 0}}</block>
            </view>
            <view wx:if="{{!femalInfo.periodRecord}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('problem', {{femalInfo}})">
          <view class="item-main">
            <view class="main-tit">妇科是否有问题</view>
            <view wx:if="{{femalInfo.questionStatus == '1'}}" class="main-txt">
              <block wx:if="{{diseaseF.length > 0}}" wx:for="{{diseaseF}}" wx:for-index="idx" wx:key="idx">
                <text>{{item}}{{idx != diseaseF.length - 1 ? '/' : ''}}</text>
              </block>
            </view>
            <block wx:if="{{femalInfo.questionStatus == '0'}}">
              <view class="main-txt">
                <text>没有问题</text>
              </view>
            </block>
            <block wx:if="{{femalInfo.questionStatus == '2'}}">
              <view class="main-txt">
                <text>不清楚</text>
              </view>
            </block>
            <view wx:if="{{!femalInfo.questionStatus ||  femalInfo.questionStatus == 'null'}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('history', {{femalInfo}})">
          <view class="item-main">
            <view class="main-tit">既往病史</view>
            <view class="main-txt">
              <block wx:if="{{showHistoryF.length > 0}}" wx:for="{{showHistoryF}}" wx:for-index="idx" wx:key="idx"> {{item}}{{idx != showHistoryF.length - 1 ? '/' : ''}}</block>
            </view>
            <view wx:if="{{showHistoryF == 0}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>

    <view class="m-profile-card">
      <view class="m-profile-title">
        <view class="m-title-icon man">
          ♂
          <!-- <image mode="widthFix" src='REPLACE_IMG_DOMAIN/his-miniapp/p242/women.png' /> -->
        </view>
        <view class="m-title-content">男方健康信息</view>
      </view>
      <view class="m-list">
        <view class="list-item" @tap="jumppage('baseinfo', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">基本信息</view>
            <view wx:if="{{manInfo.basicInfo}}" class="main-txt">
              <text>{{manInfo.basicInfo.name || '暂无姓名'}}/</text>
              <text>{{manInfo.basicInfo.idNo || '暂无证件号码'}}/</text>
              <text>{{manInfo.basicInfo.profession || '暂无职业'}}/</text>
              <text>{{manInfo.basicInfo.nation || '暂无民族'}}/</text>
              <block wx:if="{{manInfo.basicInfo.height}}"><text>{{manInfo.basicInfo.height}}cm/</text></block>
              <block wx:if="{{!manInfo.basicInfo.height}}"><text>暂无身高/</text></block>
              <block wx:if="{{manInfo.basicInfo.weight}}"><text>{{manInfo.basicInfo.weight}}kg/</text></block>
              <block wx:if="{{!manInfo.basicInfo.weight}}"><text>暂无体重/</text></block>
              <text>{{manInfo.basicInfo.address || '暂无地址'}}/</text>
              <text>{{manInfo.basicInfo.education || '暂无教育程度'}}</text>
            </view>
            <view wx:if="{{!manInfo.basicInfo}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('marriage', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">婚育史</view>

            <view wx:if="{{manInfo.marriageRecord}}" class="main-txt">
              <block>
                <text>{{manInfo.marriageRecord.hyzk || '暂无婚姻状况'}}/</text>
                <text>{{manInfo.marriageRecord.sys || '暂无生育史'}}/</text>
                <text>婚龄{{manInfo.marriageRecord.hl || ''}}年</text>
              </block>
            </view>
            <view wx:if="{{!manInfo.marriageRecord}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('problem', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">小蝌蚪是否有问题</view>
            <view wx:if="{{manInfo.questionStatus == '1'}}" class="main-txt">
              <block wx:if="{{diseaseM.length > 0}}" wx:for="{{diseaseM}}" wx:for-index="idx" wx:key="idx">
                <text>{{item}}{{idx != diseaseM.length - 1 ? '/' : ''}}</text>
              </block>
            </view>
            <block wx:if="{{manInfo.questionStatus == '0'}}">
              <view class="main-txt">
                <block>没有问题</block>
              </view>
            </block>
            <block wx:if="{{manInfo.questionStatus == '2'}}">
              <view class="main-txt">
                <block>不清楚</block>
              </view>
            </block>
            <view wx:if="{{!manInfo.questionStatus || manInfo.questionStatus == 'null'}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
        <view class="list-item" @tap="jumppage('history', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">既往病史</view>
            <view class="main-txt">
              <block wx:if="{{showHistoryM.length > 0}}" wx:for="{{showHistoryM}}" wx:for-index="idx" wx:key="idx"> {{item}}{{idx != showHistoryM.length - 1 ? '/' : ''}}</block>
            </view>
            <view wx:if="{{showHistoryM.length == 0}}" class="main-txt">未填写</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>

    <view class="m-profile-card">
      <view class ="m-list">
        <view class="list-item"  @tap="jumppage('description', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">病情描述</view>
            <view class="main-txt">{{description || '请详细描述基本病情，以便医生更准确分析'}}</view>
          </view>
          <view>
              <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="m-profile-card">
      <view class ="m-list">
        <view class="list-item"  @tap="jumppage('record', {{manInfo}})">
          <view class="item-main">
            <view class="main-tit">就诊记录</view>
            <view class="main-txt">咨询及治疗过程中的相关记录</view>
          </view>
          <view>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view hidden="{{tabIndex !== 1}}">
    遗传档案
  </view>
</view>
