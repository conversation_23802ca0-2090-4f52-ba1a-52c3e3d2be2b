@import "../../../resources/style/mixins";

page {
}

.p-page {
  padding-bottom: 130rpx;
}

.m-info{
  padding: 60rpx 0 20rpx;
  text-align: center;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  .item-tit{
    margin-bottom: 16rpx;
    font-size: 40rpx;
    color: #fff;
    font-weight: 600;
  }
  .item-txt{
    color:@hc-color-white;
    font-size: 28rpx;
  }
}

.m-table{
  .table{
    background-color: #fff;
    margin-top: 20rpx;
    border-top:4rpx solid @hc-color-primary;
  }
  .tb-extra{
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom:2rpx solid @hc-color-border;
  }
  .extra-tit{
    flex: 1;
    font-size: 34rpx;
    color:@hc-color-title;
  }
  .extra-txt{
    font-size: 28rpx;
    color:@hc-color-text;
  }
  .extra-num{
    font-size: 36rpx;
    color:@hc-color-title;
    margin-left: 20rpx;
  }
  .tb-head,.tb-body{

  }
  .tb-head{

  }
  .tb-body{

  }
  .head-tr,.body-tr{
    display: flex;
    align-items: center;
    border-bottom:2rpx solid #e5e5e5;
    padding: 0 15rpx;
  }
 
  .body-td{
    .textBreak();
    &.td-1{
      flex-basis: 20%;
    }
    &.td-2{
      flex-basis: 40%;
    }
    &.td-center{
      text-align: center;
    }
    &.td-right{
      text-align: right;
    }
  }
  .head-td{
    padding: 15rpx;
    color:@hc-color-text;
    font-size: 25rpx;
  }
  .body-td{
    padding: 20rpx 15rpx;
    color:@hc-color-title;
    font-size: 30rpx;
  }
}

.top-tips {
  width: 100%;
  box-sizing: border-box;
  padding: 12rpx 32rpx;
  color: @hc-color-text;
  font-size: 26rpx;
  // background-color: @hc-color-white;
}

.order-list {
  padding: 32rpx;
  background-color: #fff;

  .o-item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    box-sizing: border-box;
    background-color: @hc-color-white;
    border-radius: 24rpx;
    padding: 32rpx;
    align-items: flex-start;
    margin-bottom: 24rpx;

    & + .o-item {
      // border-top: 2rpx solid @hc-color-border;
    }

    .lf-check {
      padding-right: 32rpx;
      font-size: 24rpx;
      image {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .content {
      flex: 1;

      .con-tit {
        font-size: 36rpx;
        font-weight: 600;
        color: @hc-color-title;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        margin-bottom: 8rpx;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .dept-doct,
      .patient-date {
        color: @hc-color-text;
        font-size: 28rpx;
        text {
          padding-right: 20rpx;
        }
      }
    }

    .min-total {
      color: @hc-color-assist;
      font-size: 28rpx;
      white-space: nowrap;
      font-weight: 600;
      margin-bottom: 8rpx;
      // padding: 0 30rpx;
    }

    .rt-arrow {
      min-width: 8rpx;
      width: 16rpx;
      height: 16rpx;
      border-right: 4rpx solid rgba(0, 0, 0, 0.4);
      border-bottom: 4rpx solid rgba(0, 0, 0, 0.4);
      transform: translateX(-8rpx) rotate(-135deg);
      animation: right 0.15s linear forwards;
    }
    .down {
      animation: down 0.15s linear forwards;
    }
    @keyframes right {
      from {
        transform: translateX(-8rpx) rotate(45deg);
      }
      to {
        transform: translateX(-8rpx) rotate(-135deg);
      }
    }
    @keyframes down {
      from {
        transform: translateX(-8rpx) rotate(-135deg);
      }
      to {
        transform: translateX(-8rpx) rotate(45deg);
      }
    }
  }
  .o-detail {
    background: #fff;
    border-radius: 0 0 24rpx 24rpx;
    background-color: @hc-color-white;
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 4rpx;
    .table-container {
    }
    .t-r {
      &:first-child {
        border-bottom: 2rpx solid @hc-color-border;
      }
      box-sizing: border-box;
      padding: 0 16rpx;
      display: flex;
      flex-flow: row nowrap;
      align-items: stretch;
      justify-content: space-between;
      .t-hd {
        padding: 8rpx 0;
        color: @hc-color-title;
        font-size: 20rpx;
        font-weight: 600;
        border-right: 2rpx solid @hc-color-border;
        &:last-child{
          border: none;
        }
      }
      .t-rd {
        padding: 16rpx 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: @hc-color-text;
        border-right: 2rpx solid @hc-color-border;
        &:last-child{
          border: none;
        }
      }
      .project {
        width: 40%;
        justify-content: flex-start;
      }
      .count-unit {
        width: 26%;
        text-align: center;
      }
      .money {
        width: 26%;
        text-align: right;
        justify-content: flex-end;
      }
    }
  }
}

.m-pay {
  display: flex;
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 9;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  padding: 24rpx;

  .pay-main {
    .all-check {
      padding-right: 20rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      font-weight: 600;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
    }
  }

  .main-txt {
    font-size: 28rpx;
    font-weight: 600;
    color: #000;
  }

  .main-num {
    color: #D2962B;
    font-size: 28rpx;
  }

  .pay-btn {
    margin-right: 48rpx;
    line-height: 88rpx;
    padding: 0 80rpx;
    z-index: 9;
    width: 88rpx;
    height: 88rpx;
    border-radius: 76rpx;
    background-color: #3F969D;
    font-size: 28rpx;
    font-weight: 500;
    color: @hc-color-white;

  }
}
