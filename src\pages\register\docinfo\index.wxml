<view class="p-page">
  <view class="m-info">
    <view class="info-box">
      <view class="info">
        <view class="info-hd">
          <image
            mode="widthFix"
            src="{{docDetail.doctorImg || (docDetail.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}"
            alt=""
          ></image>
        </view>
        <view class="info-bd">
          <view class="bd-tit">
            <view class="tit-lt">{{docDetail.doctorName}}</view>
            <view class="abs-icon-box">
              <view class="abs-icon">
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/share.png"></image>
                <button class="share-btn" open-type="share">分享</button>
              </view>
              <block wx:if="{{docDetail.favoriteStatus == 0}}">
                <view class="abs-icon" @tap="bindAddFav">
                  <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/collect.png"></image>
                </view>
              </block>
              <block wx:else>
                <view class="abs-icon" @tap="bindCancelFav">
                  <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/images/reg-collect-on.png"></image>
                </view>
              </block>
              <!--<view class="abs-icon" @tap="bindQrCode"><image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/reg-qrcode.png"></image></view>-->
            </view>
          </view>
          <view class="bd-txt">{{docDetail.deptName}} | {{docDetail.doctorTitle}}</view>
        </view>
      </view>
      <view class="info-des">
        {{docDetail.doctorSkill || '暂未添加'}}
      </view>
    </view>
    <!-- <view class="info-bg">
      <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/docinfo-bg.png" alt="" />
    </view> -->
    <view class="m-tab">
      <view class="unit-tab">
        <view
          class="unit-tab-li tab-li {{tabIndex === 0 ? 'active' : ''}}"
          @tap="bindChangeTabIndex({{0}})"
        >服务
        </view>
        <view
          class="unit-tab-li tab-li {{tabIndex === 1 ? 'active' : ''}}"
          @tap="bindChangeTabIndex({{1}})"
        >介绍
        </view>
      </view>
    </view>
  </view>

  <view class="date-container" hidden="{{tabIndex === 1}}">
    <block wx:if="{{scheduleList.length > 0}}">
      <view class="m-date-info">
        <view class="info-title">医生排班</view>
        <view class="m-date-rt"  @tap="bindChangeExpandDate({{!expandDate}})">
          <view class="info-lt">{{selectedDay}} 星期{{WxsUtils.getWeekDay(selectedDay)}}</view>
            <view
              class="info-rt {{expandDate === false ? 'active' : ''}}"
             
            >
              <!--<text class="rt-arrow bottom"></text>-->
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/arrow.png" class="arrow-icon bottom" />
            </view>
            <view
              class="info-rt {{expandDate === true ? 'active' : ''}}"
              
            >
              <!--<text class="rt-arrow top"></text>-->
              <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/arrow.png" class="arrow-icon top" />
            </view>
        </view>
      </view>

      <view class="m-date {{expandDate === true ? 'active' : ''}}">
        <view class="m-date-box">
          <!-- 使用wx:for循环渲染所有日期项，通过CSS自动换行 -->
          <view 
            class="date-box-list {{selectedDay === false ? (item.selected === true ? 'active' : '') : (selectedDay == item.scheduleDate ? 'active' : '')}}"
            wx:for="{{scheduleList}}"
            wx:key="index"
            @tap="bindChangeDate({{item}})">
            <view class="date-item">{{item.date}}</view>
            <view class="week-item">周{{item.weekDate}}</view>
            <view class="source-item {{item.status == 1 ? 'on' : ''}}">{{item.status == 1 ? '有号' : '暂未排号'}}</view>
          </view>
        </view>
      </view>


      <!--<view
        class="m-date {{expandDate === true ? 'active' : ''}}"
      >
        <view class="date-box">
          <view class="week">
            <view class="week-item">一</view>
            <view class="week-item">二</view>
            <view class="week-item">三</view>
            <view class="week-item">四</view>
            <view class="week-item">五</view>
            <view class="week-item">六</view>
            <view class="week-item">日</view>
          </view>
          <view class="date">
            <block
              wx:for="{{nullDateArr}}"
              wx:key="index"
            >
              <view class="date-item">
                <view class="item-box"></view>
              </view>
            </block>
            <block
              wx:for="{{scheduleList}}"
              wx:key="index"
            >
              <view
                class="date-item {{item.status == 1 ? 'on' : ''}} {{selectedDay === false ? (item.selected === true ? 'active' : '') : (selectedDay == item.scheduleDate ? 'active' : '')}}"
                @tap="bindChangeDate({{item}})"
              >
                <view class="item-box">
                  <view class="item-day">{{item.monthDay}}</view>
                  <view class="item-status {{item.status == 1 ? 'on' : ''}}">{{item.status == 1 ? '可约' : '无号'}}</view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>-->
    </block>


    <view class="m-list">
      <block wx:if="{{scheduleDoctorList.itemList.length > 0}}">
        <block
          wx:for="{{scheduleDoctorList.itemList}}"
          wx:key="index"
        >
          <view
            class="list-item"
            wx:if="{{item.status == 1}}"
            @tap="bindConfirmReg({{item}})"
          >
            <view class="item">
              <view class="item-hd">
                <!-- <text class="circle {{item.leftSource > 0 ? 'can' : 'can-not' }}">{{item.leftSource > 0 ? '可预约' : '已满诊' }}</text> -->
                <text class="{{item.leftSource > 0 ? 'exist' : 'no-exist'}}">{{item.visitBeginTime + '-' + item.visitEndTime}}</text>
                <!-- {{WxsUtils.getVisitTime('','',item.visitPeriod, item.visitBeginTime,item.visitEndTime)}} -->
              </view>
              <view class="item-bd">
                <!-- <text wx:if="{{item.leftSource < 1 && docDetail.doctorLevel === 9}}" class="has-wait">申请候补</text> -->
                 <text wx:if="{{firstTotal === 0 && docDetail.doctorLevel === 9 && item.scheduleId === scheduleDoctorList.itemList[0].scheduleId }}" class="has-wait">申请候补</text>
                <text wx:if="{{secondTotal === 0 && docDetail.doctorLevel === 9 && item.scheduleId !== scheduleDoctorList.itemList[0].scheduleId }}" class="has-wait">申请候补</text>
                
                <block wx:if="{{item.leftSource > 0}}">
                  <!-- <text class="exist">剩{{item.leftSource}}<text class="exit-total">/总{{item.totalSource}}</text></text> -->
                  <text class="money">¥{{WxsUtils.formatMoney(item.registerFee, 100)}}</text>
                  <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/arrow.png" class="arrow-icon" />
                </block>
                <block wx:else>
                  <text class="no-exist">已无号</text>
                </block>
              </view>
              <!-- <view class="item-ft"></view> -->
            </view>
          </view>
          <view class="list-item disabled" wx:if="{{item.status != 1}}">
            <view class="item">
              <view class="item-hd">{{WxsUtils.getVisitTime('','',item.visitPeriod, item.visitBeginTime,
                item.visitEndTime)}}
              </view>
              <view class="item-bd">已无号</view>
             <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/arrow.png" class="arrow-icon" />
            </view>
          </view>
        </block>
      </block>
      <block wx:else>
        <empty>
          <block slot="text">医生暂未排此天班次</block>
        </empty>
      </block>
    </view>
  </view>

  <view hidden="{{tabIndex === 0}}">
    <view class="m-intro">
      <view class="intro-box">
        <view class="intro-item">
          <view class="item-tit">擅长方向</view>
          <view class="item-text">{{docDetail.doctorSkill || '暂未添加'}}</view>
        </view>
        <view class="intro-item">
          <view class="item-tit">个人介绍</view>
          <view class="item-text">
            <block wx:if="{{docDetail.doctorSummary}}">
              <wxparser rich-text="{{docDetail.doctorSummary}}" />
            </block>
            <block wx:if="{{!docDetail.doctorSummary}}">暂未添加</block>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 挂号弹窗 -->
<view class="m-reg-docinfo-popup {{showConfirm ? 'active' : ''}}">
  <view class="reg-docinfo-popup-mask" @tap="bindToggleConfirm({{false}})"></view>
  <view class="reg-docinfo-popup">
    <view class="popup-form-box">
      <view class="popup-form">
        <!--<view class="popup-item opt">
          <view class="opt-tit">
            <block wx:if="{{confirmData.patientList.length == 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-title">请点击下方加号添加就诊人</text>
            </block>
            <block wx:if="{{confirmData.patientList.length > 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-title">请选择就诊人</text>
            </block>
          </view>
        </view>-->
        <view class="popup-item info">
          <view class="info-hd">
            <image
              mode="widthFix"
              src="{{confirmData.doctorImg || (confirmData.sex === 'F' ? 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-f.png' : 'REPLACE_IMG_DOMAIN/his-miniapp/images/doctor-m.png')}}"
              alt="" />
          </view>
          <view class="info-bd">
            <view class="info-bd-item">
              <!-- <view class="info-bd-label">医师：</view> -->
              <view class="bd-item-title" >{{confirmData.doctorName}}</view>
              <view>{{confirmData.doctorTitle}}</view>
            </view>
            <view class="info-bd-small">
              <!--<view class="info-bd-label">费用：</view>-->
              <view class="info-bd-text">
                ¥{{WxsUtils.formatMoney(confirmData.totalFee, 100)}}
              </view>
            </view>
            <view class="info-bd-small">
              <!--<view class="info-bd-label">时段：</view>-->
              <view>
                {{WxsUtils.getVisitTime(confirmData.scheduleDate,confirmData.visitWeekName,confirmData.visitPeriod,
                confirmData.visitBeginTime,
                confirmData.visitEndTime)}}
              </view>
            </view>
          </view>
        </view>
        <view class="popup-item opt">
          <view class="opt-tit">
            <!-- <block wx:if="{{confirmData.patientList.length == 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-text">请点击下方加号添加就诊人</text>
            </block>
            <block wx:if="{{confirmData.patientList.length > 0 && !isInternet}}">
              <text class="unit-color-title unit-fs-text">请选择就诊人</text>
            </block> -->
            <block wx:if="{{activePatient.patCardNo}}">
              <view class="unit-color-title">
                <text class="title">请选择就诊人</text> 
                {{activePatient.patientName}}  {{activePatient.patCardNo}}
              </view>
            </block>
          </view>
          <view class="opt-list" wx:if="{{!isInternet}}">
            <block
              wx:for="{{confirmData.patientList}}"
              wx:key="index"
            >
              <view
                class="list-box unit-label {{activePatient.patientId === item.patientId ? 'active' : ''}}"
                @tap="bindChangePatient({{item}})"
              >
                <view class="unit-label-text">{{WxsUtils.sliceString(item.patientName,-2)}}</view>
              </view>
            </block>
            <block wx:if="{{confirmData.leftBindNum > 0}}">
              <view class="list-box opt-list-item-add" @tap.stop="bindGoBindUser">
                <view>+</view>
              </view>
              <!-- <image
                class="opt-list-item opt-list-item-add"
                @tap.stop="bindGoBindUser"
                src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/icon-smalladd.png"
                mode="widthFix"
              /> -->
            </block>
          </view>
        </view>
        <view class="popup-item opt unit-none">
          <view class="opt-tit">
            <view class="unit-color-title unit-fs-text">是否为初诊</view>
          </view>
          <view class="opt-list">
            <view class="opt-list-item unit-label">
              <view class="unit-label-text">初诊</view>
            </view>
            <view class="opt-list-item unit-label active">
              <view class="unit-label-text">复诊</view>
            </view>
          </view>
        </view>
        <!-- <view class="popup-item opt ad-docinfo">
          <view class="list-item" @tap="bindChangeCheck(1)">
            <view class="item-bd">
              <view class="bd-main">
                <view class="main-tit">停诊险<view class="help-icon"></view></view>
                <view class="main-txt">挂号未出诊，最高赔付100元</view>
              </view>
              <view class="bd-extra">￥10</view>
            </view>
            <view class="item-ft">
              <block wx:if="{{parkedRisk}}">
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-on.png"></image>
              </block>
              <block wx:else>
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-off.png"></image>
              </block>
            </view>
          </view>
          <view class="list-item" @tap="bindChangeCheck(2)">
            <view class="item-bd">
              <view class="bd-main">
                <view class="main-tit">医疗商业保险<view class="help-icon"></view></view>
                <view class="main-txt">医疗商业保险，最高保障10万元</view>
              </view>
              <view class="bd-extra">￥1</view>
            </view>
            <view class="item-ft">
              <block wx:if="{{medicalSecure}}">
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-on.png"></image>
              </block>
              <block wx:else>
                <image mode="widthFix" src="REPLACE_IMG_DOMAIN/his-miniapp/icon/common/checkbox-off.png"></image>
              </block>
            </view>
          </view>
        </view> -->
      </view>
    </view>
    <view class="popup-bottom-btn">
      <block wx:if="{{activeSourceItem.leftSource === 0 && docDetail.doctorLevel === 9}}">
        <view
          class="popup-submit {{activePatient.patientId ? '' : 'disabled'}}"
          @tap="bindAlternateConfirm">确认候补
        </view>
      </block>
      <block wx:else>
        <view
          class="popup-submit {{activePatient.patientId ? '' : 'disabled'}}"
          @tap="bindRegisterConfirm">确认预约
        </view>
      </block>
    </view>
    
  </view>
  <view class="m-top-tips">
    <i class="iconfont tips-icon">&#xe7d3;</i>
    <view class="tips-text">请先添加或选择就诊人</view>
  </view>
  
</view>
<!--<bottom-logo />-->