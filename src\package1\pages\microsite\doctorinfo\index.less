@import "../../../../resources/style/mixins";

page{
}

.p-page{
  
}

.m-info {
  position: relative;
  overflow: hidden;
  padding: 40rpx 18rpx;
  background: url('REPLACE_IMG_DOMAIN/his-miniapp/images/top-bg.png');
  .info-box{
    position: relative;
    z-index: 1;
  }
  .info{
    position: relative;
    display: flex;
    align-items: center;
  }
  .info-bg{
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    image{
      width: 100%;
      vertical-align: top;
    }
  }
  .info-hd {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 124rpx;
    height: 124rpx;
    overflow: hidden;
    border-radius: 50%;
    flex:0 0 1;
    
    image {
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }
  .info-bd {
    margin-left: 30rpx;
    flex: 1;
    overflow: hidden;
    color: #fff
  }
  .bd-tit {
    display: flex;
    align-items: center;
    
  }
  .tit-lt {
    flex: 1;
    font-size: 48rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .tit-rt {
    display: flex;
  }
  .rt-icon {
    font-size: 56rpx;
    line-height: 1;
    margin-left: 30rpx;
    
    &.active{
      color: @hc-color-warn;
    }
  }
  .bd-txt {
    font-size: 28rpx;
    margin-top: 10rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .info-des {
    font-size: 26rpx;
    margin-top: 20rpx;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .abs-icon-box{
    display: flex;
    position: absolute;
    right: 0;
    top: 0;
  }
  .abs-icon{
    position: relative;
    width: 56rpx;
    height: 56rpx;
    margin-left: 30rpx;
    
    image{
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
    .share-btn{
      position: absolute;
      left: 0;
      top: 0;
      width: 56rpx;
      height: 56rpx;
      z-index: 1;
      opacity: 0;
    }
  }
}

//介绍
.m-intro {
  &.active {
    display: block;
  }
  .intro-box {
    margin: 24rpx 24rpx 16rpx;
  }
  .intro-tit {
    text-align: center;
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .intro-item {
    margin-bottom: 16rpx;
    padding: 24rpx;
    border-radius: 8rpx;
    background-color: #fff;
  }
  .item-tit {
    font-size: 28rpx;
    color: @hc-color-title;
    font-weight: bold;
  }
  .item-text {
    font-size: 28rpx;
    color: @hc-color-text;
    margin-top: 24rpx;
    line-height: 1.5;
  }
}