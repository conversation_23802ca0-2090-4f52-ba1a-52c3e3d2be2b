@import '../../../../resources/style/mixins.less';
@outZIndex: 999;
@inZIndex: 99;
page {
  height: 100%;
  background: #424242;
  .title {
    padding: 227rpx 30rpx 45rpx;
    font-size: 42rpx;
    font-weight: 600;
    color: #ffffff;
    line-height: 59rpx;
    text-align: center;
  }
  .describe {
    text-align: center;
    font-size: 34rpx;
    color: #ffffff;
    line-height: 48rpx;
    margin-bottom: 20rpx;
  }
  .card-label {
    margin-left: 30rpx;
    margin-bottom: 15rpx;
    height: 42rpx;
    font-size: 30rpx;
    color: @hc-color-text;
    line-height: 42rpx;
  }
  .card {
    margin: 0 auto;
    width: 690rpx;
    background: #ffffff;
    border-radius: 10rpx;
    font-size: 22rpx;
    .card-body {
      height: 330rpx;
      width: 100%;
      position: relative;
      color: #ffffff;
      .body-tag {
        width: 145rpx;
        height: 56rpx;
        border-top-left-radius: 10rpx;
        background: #F5A623;
        position: absolute;
        z-index: @outZIndex;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow:0rpx 2rpx 4rpx 0rpx rgba(0,0,0,0.24);
        .body-tag-trigle {
          position: absolute;
          z-index: @outZIndex;
          right: -24rpx;
          width: 0;
          height: 0;
          border-top: 56rpx solid #F5A623;
          border-right: 24rpx solid transparent;
        }
      }
      .body-poster {
        height: 100%;
        width: 100%;
        border-top-left-radius: 10rpx;
        border-top-right-radius: 10rpx;
      }
      .body-name {
        position: absolute;
        z-index: @outZIndex;
        left: 15rpx;
        bottom: 10rpx;
        text-shadow:0rpx 2rpx 1rpx rgba(0,0,0,0.19);
      }
      .wgt-empty-box {
        padding-top: 0;
      }
    }
    .card-footer {
      padding: 27rpx;
      .item-title {
        width: 100%;
        font-size: 26rpx;
        color: #2D2D2D;
        margin-bottom: 17rpx;
        .ellipsis();
      }
      .item-tags-box {
        display: flex;
        .item-tag {
          padding: 8rpx 40rpx;
          color: #EB9773;
          border-radius: 20rpx;
          background: #FDF5ED;
          margin-right: 20rpx;
        }
      }
    }
  }
  .refresh {
    margin-top: 30rpx;
    height: 42rpx;
    font-size: 30rpx;
    color: @hc-color-text;
    line-height: 42rpx;
    width: 100%;
    text-align: center;
  }
  .close {
    height: 70rpx;
    width: 70rpx;
    margin-top: 96rpx;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }
}
