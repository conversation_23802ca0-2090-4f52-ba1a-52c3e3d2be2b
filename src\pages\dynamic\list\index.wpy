<style lang="less" src="./index.less" ></style>
<template lang="wxml" src="./index.wxml" />

<script>
  import wepy from 'wepy';
  import Empty from '@/components/empty/index';
  import * as Api from './api';

  export default class List extends wepy.page {
    config = {
      navigationBarTitleText:'',
      // enablePullDownRefresh: true,
      navigationBarBackgroundColor: '#fff',  
    };

    components = {
      'empty': Empty,
    };

    onLoad(options) {
      wx.setNavigationBarTitle({
        title: options.title
      })
      this.singleId = options.typeId;
      this.getArticList(1,options.typeId);
    }

    onPullDownRefresh(){
      const { currentPage, endPageIndex } = this.artic;
      if (currentPage * 1 < endPageIndex * 1) {
        this.getArticList((currentPage * 1) + 1,this.singleId);
      }
      wx.stopPullDownRefresh();
    }

    // data中的数据，可以直接通过  this.xxx 获取，如：this.text
    data = {
      emptyConfig: {
        show: true,
      },
      artic: {},
      singleId:'',
    };

    async getArticList(pageNum = 1,singleId) {
      console.log(singleId)
      const { code, data = {} } = await Api.getArticList({typeId:singleId, pageNum: 999 });
      if (code == 0 && data.recordList && data.recordList.length > 0) {
        this.artic = data;
        // const { recordList = [] } = this.artic;
        // const { recordList: _recordList = [] } = data;
        // this.artic = data;
        // this.artic.recordList = [...recordList, ..._recordList];
        this.emptyConfig.show = false;
      } else {
        this.artic = {};
        this.emptyConfig.show = true;
      }
      this.$apply();
    }

    // 交互事件，必须放methods中，如tap触发的方法
    methods = {
      navigateTo(item) {
        let url = '';
        if(item.articleId){
          url = `/pages/dynamic/info/index?articleId=${item.articleId}`;
        }else{
          url = `/pages/dynamic/secondindex/index?typeId=${item.typeId}&title=${item.typeName || item.title}`;
        }
        wepy.navigateTo({ url });
      },
    };

    events = {
    };

  }
</script>
