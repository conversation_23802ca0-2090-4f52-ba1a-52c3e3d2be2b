<form bindsubmit="formSubmit" report-submit='true'>
  <view class="patInfo-list">
    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text wx:if="{{sex == 'F'}}" class="list-title">女方婚姻状况</text>
        <text wx:if="{{sex == 'M'}}" class="list-title">男方婚姻状况</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('hyzk')" value="{{hyzkIndex}}" range="{{hyzkList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择婚姻状况" placeholder-style="{{ hyzk ? '': ''}}"
              value="{{hyzk}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">婚龄</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('hl')" value="{{hl}}" range="{{hlList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择婚龄" placeholder-style="{{ hl ? '': ''}}"
              value="{{hl}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

    <view wx:if="{{sex == 'M'}}" class="patInfo-listitem">
      <view class="listitem-head">
        <text class="list-title">生育史</text>
      </view>
      <view class="listitem-body">
        <picker bindchange="pickerChange('sys')" value="{{sysIndex}}" range="{{sysList}}">
          <view class="picker">
            <input class="picker-info" type="text" disabled placeholder="请选择生育史" placeholder-style="{{ sys ? '': ''}}"
              value="{{sys}}" />
            <view class="item-arrow"></view>
          </view>
        </picker>
      </view>
    </view>

    <block wx:if="{{sex == 'F'}}">
      <view class="patInfo-listitem">
        <view class="listitem-head">
          <text class="list-title">性生活</text>
        </view>
        <view class="listitem-body">
          <picker bindchange="pickerChange('xsh')" value="{{xshIndex}}" range="{{xshList}}">
            <view class="picker">
              <input class="picker-info" type="text" disabled placeholder="性生活是否正常"
                placeholder-style="{{ xsh ? '': ''}}" value="{{xsh}}" />
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
      </view>

      <view class="patInfo-listitem">
        <view class="listitem-head">
          <text class="list-title">怀孕几次</text>
        </view>
        <view class="listitem-body">
          <picker bindchange="pickerChange('hyjc')" value="{{hyjc}}" range="{{hyjcList}}">
            <view class="picker">
              <input class="picker-info" type="text" disabled placeholder="请选择怀孕次数"
                placeholder-style="{{ hyjc ? '': ''}}" value="{{hyjc}}" />
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
      </view>

      <view class="patInfo-listitem">
        <view class="listitem-head">
          <text class="list-title">生育几次</text>
        </view>
        <view class="listitem-body">
          <picker bindchange="pickerChange('syjc')" value="{{syjc}}" range="{{syjcList}}">
            <view class="picker">
              <input class="picker-info" type="text" disabled placeholder="请选择生育次数"
                placeholder-style="{{ syjc ? '': ''}}" value="{{syjc}}" />
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
      </view>

      <view class="patInfo-listitem">
        <view class="listitem-head">
          <text class="list-title">早产几次</text>
        </view>
        <view class="listitem-body">
          <picker bindchange="pickerChange('zcjc')" value="{{zcjc}}" range="{{zcjcList}}">
            <view class="picker">
              <input class="picker-info" type="text" disabled placeholder="请选择早产次数"
                placeholder-style="{{ zcjc ? '': ''}}" value="{{zcjc}}" />
              <view class="item-arrow"></view>
            </view>
          </picker>
        </view>
      </view>
    </block>

  </view>

  <view class="afterscan-operbtnbox">
    <button class="binduser-btn_line" formType="submit">保存</button>
  </view>
</form>

<toptip :toptip.sync="toptip" />