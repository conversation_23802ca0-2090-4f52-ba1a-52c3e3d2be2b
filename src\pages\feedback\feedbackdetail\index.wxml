<view class="p-page">
  <nav-bar bgColor="#fff" isBack="1" color="#000">反馈详情</nav-bar>
  <view class="page-top">
    <view class="status">
      <image src="REPLACE_IMG_DOMAIN/his-miniapp/images/success.png" />
      <view>{{detailData.replyContent ? '已回复' : '提交成功'}}</view>
    </view>
    <view class="tips" wx:if="{{!detailData.replyContent}}">您的意见反馈已提交，工作人员将会进行核实处理，届时将根据实际情况，给予信息回复或致电联系。</view>
    <view class="tips" wx:else>您的意见反馈已收到，工作人员已进行了回复，您可在页面下方查看回复内容。</view>
  </view>
  <view class="m-list">
    <view class="list-title">反馈内容</view>
    <view class="list">
      <view class="list-item" wx:if="{{detailData.feedbackType}}">
        <view class="item-label">反馈类型</view>
        <view class="item-value" wx:if="{{detailData.feedbackType === '1'}}">投诉</view>
        <view class="item-value" wx:if="{{detailData.feedbackType === '2'}}">建议</view>
      </view>
      <view class="list-item" wx:if="{{detailData.feedbackObject}}">
        <view class="item-label">反馈对象</view>
        <view class="item-value">{{detailData.feedbackObject}}</view>
      </view>
      <view class="list-item content-list">
        <view class="item-label">反馈内容</view>
        <view class="item-value">{{detailData.feedbackContent}}</view>
        <view class="m-upload-list">
          <block wx:for="{{imageObj}}" wx:key="index">
            <view class="m-upload-item" @longpress="actionSheetTap({{index}}, '')" >
              <image mode="widthFix" class="m-upload-image"  src="{{item}}" @tap="previewImage({{item}})"/>
            </view>
          </block>
        </view>
      </view>
    </view>
  </view>
  <view class="m-list" wx:if="{{detailData.replyContent}}">
    <view class="list-title">反馈回复</view>
    <view class="m-list-retry">
      <view class="list-tit">反馈回复</view>
      <view class="list-retry-time">{{detailData.updateTime}}</view>
    </view>
    <view class="list">
      <view class="list-item">
        <view>{{detailData.replyContent}}</view>
      </view>
    </view>
  </view>
</view>