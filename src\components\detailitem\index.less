@import '../../resources/style/mixins';
.inhosp-info {
  background-color: #fff;
  padding: 32rpx;

  &.radius {
    border-radius: 24rpx;
  }

  .title {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    font-size: 28rpx;
    margin-bottom: 32rpx;
  }

  .info-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    &.flex-1 {
      > view {
        flex: 1;
      }
    }
    > view {
      font-size: 32rpx;
      &:first-child {
        color: rgba(0, 0, 0, 0.4);
      }
      &:last-child {
        color: rgba(0, 0, 0, 0.9);
      }
    }
    &:not(:last-child) {
      margin-bottom: 24rpx;
    }
  }
}
