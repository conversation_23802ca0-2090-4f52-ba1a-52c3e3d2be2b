<!--  -->
<template lang="wxml" src="./index.wxml" ></template>
<style lang='less' src="./index.less"></style>

<script>
import wepy from 'wepy';
import LiveListMixin from '@/mixins/live/listMixin';
import CardList from '../comm/cardList';
export default class LiveHome extends wepy.page {
  config = {
    navigationBarTitleText: '关注的直播'
  };

  data = {
    liveList: [1]
  };
  mixins = [LiveListMixin];
  components = {
    'card-list': CardList,
  };

  methods = {
    onListItemTap(info) {
      this.mixinListItemTap(info);
    }
  };

  events = {};

  watch = {};

  computed = {};

  onLoad() {}

  onShow() {
    this.mixinGetConcernLiveList();
  }
}
</script>
