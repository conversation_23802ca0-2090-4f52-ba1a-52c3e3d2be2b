@import "../../resources/style/mixins";

page {
  height: 100%;
}

.p-page {
  height: 100%;
}

.m-notice {
  .notice-item {
    position: relative;
    margin: 20rpx;
    background-color: #fff;
    border-radius: 4rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
  }

  .item-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }

  .item-txt {
    margin-top: 5rpx;
    font-size: 26rpx;
    color: @hc-color-text;
  }
}

.m-todo {
  .todo-item {
    display: flex;
    align-items: center;
    flex-direction: row;
    position: relative;
    margin: 20rpx;
    background-color: #fff;
    border-radius: 4rpx;
    padding: 25rpx 30rpx;
    box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.02);
    overflow: hidden;
  }
  .item-bd {
    flex: 1;
    margin-right: 30rpx;
    padding-left: 20rpx;
  }
  .bd-tit {
    font-size: 34rpx;
    color: @hc-color-title;
  }
  .bd-txt {
    font-size: 30rpx;
    color: @hc-color-text;
  }
  .item-ft {
    width: 17rpx;
    height: 17rpx;
    border-right: 5rpx solid #c7c7cc;
    border-bottom: 5rpx solid #c7c7cc;
    transform: translateX(-8rpx) rotate(-45deg);
  }
  .item-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 73rpx;
    height: 73rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.tip {
  position: absolute;
  bottom: 0rpx;
  right: 30%;
  height: 60rpx;
  padding: 10rpx 0;
  box-sizing: border-box;
  background: #505050;
  border-radius: 8px;
  font-size: 28rpx;
  color: #fff;
  text {
    display: inline-block;
    line-height: 40rpx;
    padding: 0 40rpx;
    &:not(:last-child) {
      border-right: 2rpx solid #fff;
    }
  }
  i {
    border: 20rpx solid transparent;
    border-bottom-color: #505050;
    position: absolute;
    bottom: 60rpx;
    right: 50rpx;
  }
}

// UI调整版本
.card {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 20rpx 32rpx;
  .card-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 500;
    padding: 32rpx;

    .left {
      display: flex;
      align-items: center;
      .icon {
        width: 32prx;
        height: 32rpx;
        image {
          padding: 9rpx;
          width: 32rpx;
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
          border-radius: 50%;
          box-sizing: border-box;
          &.link {
            background: @hc-color-link;
          }
          &.primary {
            background: @hc-color-primary;
          }
        }
      }
    }
    .right {
      display: flex;
      font-size: 24rpx;
      color: #3eceb6;
    }
  }
  .card-content {
    font-size: 32rpx;
    color: rgba(0, 0, 0, 0.7);
    margin: 0 32rpx;
    padding: 24rpx 0;
    border-top: 2rpx solid rgba(0, 0, 0, 0.08);

    .card-main {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-main-text {
        flex: 1;
        font-size: 32rpx;
        color: @hc-color-title;
        .ellipsisLn(1);
      }

      .unread {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background: #fc7f60;
      }
    }
  }
  .desc {
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.3);
    padding-top: 8rpx;
  }
}
