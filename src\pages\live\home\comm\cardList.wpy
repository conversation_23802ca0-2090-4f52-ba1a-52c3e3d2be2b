<!--  -->
<template>
  <view class="comm-card-list">
    <view wx:if="{{list.length == 0}}" class="empty-box">
      <empty>
        <text slot="text">暂无数据</text>
      </empty>
    </view>
    <block wx:if="{{list.length > 0}}" wx:for="{{list}}" wx:key="{{index}}">
      <view class="list-item" @tap="onCardTab('{{item}}')">
        <view class="item-body">
          <view class="body-tag {{tagsData[item.liveStatus].tagClass}}" wx:if="{{item.type === 'live'}}">
            {{tagsData[item.liveStatus].tagTitle}}
            <view class="body-tag-trigle"></view>
          </view>
          <image class="body-poster" src="{{item.banner || item.doctorImage}}" mode="aspectFill" />
          <!-- <view class="body-name">{{item.doctorName}} {{item.doctorLevel}}</view> -->
          <view class="body-name">{{item.doctorName}}</view>
          <view class="body-mask"  wx:if="{{item.type === 'live'}}">
            <block wx:if="{{item.liveStatus == 0}}">
              <view>开播时间</view>
              <view>{{item.liveTime}}</view>
            </block>
            <block wx:elif="{{item.liveStatus == 2 && item.isScreencap == 1}}">
              <image src="REPLACE_EHOSPITAL_DOMAIN/live-list-play.png" />
              <view>观看录像</view>
            </block>
            
          </view>
        </view>
        <view class="item-footer">
          <view class="item-title">{{item.name}}</view>
          <view class="item-tags-box">
            <view class="item-tag">{{item.liveType}}</view>
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import wepy from 'wepy';
import Empty from '@/components/empty/index';
export default class CardList extends wepy.component {
  props = {
    list: {
      type: Array,
      default: []
    }
  };

  data = {
    tagsData: {
      0: {tagClass: 'body-tag-waiting', tagTitle: '等待开播'},
      1: {tagClass: '', tagTitle: '直播中'},
      2: {tagClass: 'body-tag-end', tagTitle: '已结束'},
    },

  };

  components = {
    empty: Empty,
  };

  methods = {
    onCardTab(info) {
      this.$emit('onTap', info);
    },
  };

  events = {};
}
</script>

<style lang='less'>
@import '../../../../resources/style/mixins';
@waiting: #52C41A;
@end: #839BA3;
@outZIndex: 999;
@inZIndex: 99;
.comm-card-list {
  background: #F3F5F9;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 500rpx;
  font-size: 22rpx;
  .empty-box {
    width: 100%;
    .wgt-empty-box {
      padding-top: 100rpx;
    }
  }
  .list-item {
    width: 330rpx;
    background: rgba(255, 255, 255, 1);
    border-radius: 10rpx;
    border: 2rpx solid rgba(151, 151, 151, 0.32);
    margin-bottom: 30rpx;
    .item-body {
      height: 186rpx;
      width: 100%;
      position: relative;
      color: #ffffff;
      .body-tag {
        width: 93rpx;
        height: 37rpx;
        border-top-left-radius: 10rpx;
        background: #F5A623;
        position: absolute;
        z-index: @outZIndex;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow:0rpx 2rpx 4rpx 0rpx rgba(0,0,0,0.24);
        .body-tag-trigle {
          position: absolute;
          z-index: @outZIndex;
          right: -24rpx;
          width: 0;
          height: 0;
          border-top: 37rpx solid #F5A623;
          border-right: 24rpx solid transparent;
        }
        &.body-tag-waiting {
          background: @waiting;
          .body-tag-trigle {
            border-top-color: @waiting;
          }
        }
        &.body-tag-end {
          background: @end;
          .body-tag-trigle {
            border-top-color: @end;
          }
        }
      }
      .body-poster {
        height: 100%;
        width: 100%;
        border-top-left-radius: 10rpx;
        border-top-right-radius: 10rpx;
      }
      .body-name {
        position: absolute;
        z-index: @outZIndex;
        left: 15rpx;
        bottom: 10rpx;
        text-shadow:0rpx 2rpx 1rpx rgba(0,0,0,0.19);
      }
      .body-mask {
        border-top-left-radius: 10rpx;
        border-top-right-radius: 10rpx;
        position: absolute;
        z-index: @inZIndex;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background:rgba(0,0,0,0.45);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        line-height: 36rpx;
        image {
          height: 27rpx;
          width: 27rpx;
          margin-bottom: 10rpx;
        }
      }
    }
    .item-footer {
      padding: 12rpx;
      .item-title {
        width: 100%;
        font-size: 26rpx;
        color: #2D2D2D;
        margin-bottom: 10rpx;
        .ellipsis();
      }
      .item-tags-box {
        display: flex;
        .item-tag {
          padding: 6rpx 25rpx;
          color: #EB9773;
          border-radius: 20rpx;
          background: #FDF5ED;
          margin-right: 20rpx;
        }
      }
    }
  }
}
</style>