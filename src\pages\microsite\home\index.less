@import "../../../resources/style/mixins";

page {
  width: 100%;
  height: 100%;
  background-color: @hc-color-bg;
  overflow-y: hidden;
}

.p-page {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .wgt-navtab{
    z-index: 9;
  }
  .nav{
    .capsule-box{
      padding-left: 24rpx;
      font-size: 34rpx;
      text-align: left;
      font-weight: 600;
    }
  }
}

.g-main {
  position: relative;
  padding: 0 24rpx 110rpx;
  z-index: 1;
  top: -24rpx;
  
  // .g-main-box{
  //   height: calc(100vh - 450rpx);
  //   overflow-y: auto;
  // }
  .logo {
    position: absolute;
    top: -70rpx;
    left: 60rpx;
    z-index: 9;
  }
  .logo-img {
    width: 116rpx;
    height: 116rpx;
  }
}
.m-banner {
  height: 340rpx;
  .banner {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
  }
  .banner-image {
    width: 100%;
    height: 508rpx;
  }
}
.m-logo {
  position: relative;
  z-index: 1;
  height: 136rpx;
  position: relative;
  overflow: visible;

  .clip-bg {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    -webkit-clip-path: polygon(0 0, 100% 60%, 100% 100%, 0 100%);
    background-color: #fff;
  }

  .logo {
    position: absolute;
    width: 148rpx;
    height: 148rpx;
    border-radius: 50%;
    left: 30rpx;
    top: -74rpx;
    overflow: hidden;
  }
  .logo-img {
    width: 148rpx;
    height: 148rpx;
  }
}
.m-info {
  z-index: 1;
  padding: 0 30rpx;
  padding-top: 64rpx;
  background: linear-gradient(180deg, #FFF 0%, #FFF 100%);
  border-radius: 8rpx;
  box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
  
  .info-name {
    padding-top: 4rpx;
    font-size: 36rpx;
    color: @hc-color-title;
    position: relative;
    display: flex;

    .m-hisname {
      font-weight: bold;
      font-size: 40rpx;
    }

    .rt-icon {
      position: relative;
      font-size: 56rpx;
      line-height: 1;
      margin-top: 5rpx;
      float: right;

      &:last-child {
        margin-right: 20rpx;
      }

      &.wx {
        color: #43a53b;
      }
      &.zfb {
        color: #0097e5;
      }
    }
  }
  .unit-tag {
    display: inline-block;
    vertical-align: middle;
    font-size: 26rpx;
    border-radius: 100rpx;
    height: 40rpx;
    padding: 0 20rpx;
    line-height: 40rpx;
    text-align: center;
    background: rgba(62, 206, 182, 0.1);
    border-radius: 59rpx;
    color: #3eceb6;
    font-size: 20rpx;
  }
  .icon-qrcode {
    position: absolute;
    right: 0;
    width: 36rpx;
    height: 36rpx;
    top: 50%;
    margin-top: -18rpx;
  }
  .info-detail {
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
  }
  .detail-item {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.07);
    padding: 16rpx 0;
    &:last-child{
      border: none;
    }
  }
  .item-label {
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.9);
  }
  .item-text {
    flex: 1;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.9);
  }
  .icon-phone {
    width: 36rpx;
    height: 36rpx;
  }
  .icon-address {
    width: 36rpx;
    height: 36rpx;
  }
}
.m-media-box {
  padding: 24rpx;
  max-height: 276rpx;
  border-radius: 8rpx;
  background: #FFF;
  box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
  overflow: hidden;
}
.m-media {
  display: block;
  position: relative;
  z-index: 1;
  padding-bottom: 50rpx;
  color: @hc-color-title;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;

  .media-hd {
    display: flex;
    align-items: center;
    position: relative;
  }
  .hd-tit {
    flex: 1;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
    font-weight: bold;
  }
  .hd-extra {
    font-size: 24rpx;
    color: #2D666F;
  }
  .media-bd {
    font-size: 28rpx;
    color: @hc-color-text;
    &.article{
      margin-top: 16rpx;
    }
  }
}
.m-list {
  margin-top: 16rpx;
  border-radius: 8rpx;
  background: #FFF;
  box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
  .list-hd {
    padding: 32rpx;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
    font-weight: bold;
  }
  .list-bd {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .list-item {
    flex-basis: 25%;
    overflow: hidden;
    padding-bottom: 20rpx;
    text-align: center;
  }
  .item-icon {
    .fn-icon {
      width: 88rpx;
      height: 88rpx;
    }
  }
  .item-tit {
    text-align: center;
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.70);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.c-list {
  padding-bottom: 20rpx;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
  .list-hd {
    padding: 30rpx;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.90);
    font-weight: bold;
  }
  .list-bd {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 0 32rpx;
  }

  .list-item {
    flex: 1;
    box-sizing: border-box;
    border-radius: 16rpx;
    line-height: 100rpx;
    position: relative;
    background-image: url(REPLACE_IMG_DOMAIN/his-miniapp/icon/new/others/menu-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100rpx;
  }
  .list-item + .list-item {
    margin-left: 14rpx;
  }
  .item-icon {
    position: absolute;
    height: 100rpx;
    width: 100%;

    .fn-icon {
      // width: 100rpx;
      height: 100rpx;
    }
  }
  .item-tit {
    text-align: center;
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.ad-microsite {
  padding: 20rpx 0;
  overflow: hidden;
  .ad-content {
    color: @hc-color-warn;
    float: left;
    font-size: 32rpx;
  }
  .main-btn {
    padding: 0 25rpx;
    font-size: 24rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: @hc-color-primary;
    border: 2rpx solid @hc-color-primary;
    border-radius: 999rpx;
    float: right;
  }
}
