@import "../../../resources/style/mixins.less";

.m-risk {
  .color-high {
    color: #fc7f60;
  }

  .color-mid {
    color: #f9b64f;
  }

  .mt-48 {
    margin-top: 48rpx;
  }

  .top-bg {
    z-index: -1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: auto;
  }

  .risk-top {
    padding: 72rpx 32rpx 80rpx;

    .title {
      margin-bottom: 8rpx;
      width: 320rpx;
      line-height: 72rpx;
      font-size: 48rpx;
      font-weight: 600;
    }

    .desc {
      width: 380rpx;
      line-height: 30rpx;
      font-size: 20rpx;
    }
  }

  .risk-wrap {
    padding: 0 32rpx;

    .head-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 48rpx;
        font-weight: 600;
        font-size: 32rpx;

        .title-icon {
          width: 40rpx;
          height: auto;
          margin-right: 8rpx;
        }

        .num-wrap {
          margin: 0 8rpx;
          font-size: 32rpx;
        }
      }

      .desc {
        font-size: 22rpx;
      }
    }

    .content-wrap {
      margin-top: 26rpx;

      .risk-icon {
        width: 72rpx;
        height: auto;
      }

      .risk-item {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        padding: 32rpx;
        background: #fff;
        border-radius: 32rpx;

        .risk-info {
          display: flex;
          flex-direction: column;
          flex: 1;
          justify-content: center;

          .title-wrap {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .title {
            width: 82%;
            font-weight: 600;
            font-size: 32rpx;
            line-height: 48rpx;
          }

          .desc {
            margin-bottom: 16rpx;
            width: 82%;
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
