// config mixins
import wepy from 'wepy';
import { post } from '@/utils/request';

export default class LiveListMixin extends wepy.mixin {
  data = {
    liveList: [],
    hasMoreData: false,
    type: ''
  };
  onLoad(options = {}) {
    const { type } = options;
    this.type = type;
    // if (this.type && this.type === 'concern') {
    //   this.mixinGetConcernLiveList();
    // } else {
    //   this.mixinGetLiveList();
    // }
    
  }
  onShow() {
    // if (this.type && this.type === 'concern') {
    //   this.mixinGetConcernLiveList();
    // } else {
    //   this.mixinGetLiveList();
    // }
  }
  methods = {};
  mixinListItemTap(info = {}) {
    /**
     * TODO:
     * 1，直播中的直接跳转到直播播放页面
     * 2，未开播的跳转到等待页面
     * 3，已结束的跳转到结束页面
     */
    const { liveStatus, id, liveTime, type, isScreencap } = info;
    if (type === 'video' || type === 'VIDEO') {
      wepy.navigateTo({ url: `/package1/pages/live/video/index?id=${id}` });
      return;
    }
    let url = '/package1/pages/live/';
    switch (Number(liveStatus)) {
      case 0:
        // 待开始, 距开播时间大于5分钟时，前往详情页面；小于五分钟时，前往等待页面
        if (this.mixinTimeDiffWithNow(liveTime) > 5 * 60 * 1000) {
          url += `detail/index?id=${id}`;
        } else {
          url += `waiting/index?id=${id}`;
        }
        break;
      case 1:
        // 直播中
        url += `play/index?id=${id}`;
        break;
      case 2:
        // 已结束
        if (isScreencap == 1) {
          url = `/package1/pages/live/video/index?id=${id}`;
        } else {
          url += `end/index?id=${id}`;
        }
        break;
      default:
        break;
    }
    wepy.navigateTo({ url });
  }
  async mixinGetLiveList(params = {}) {
    const { code, data } = await post('/api/ehis/doctorLive/list', {
      type: 'live',
      ...params
    });
    if (code == 0) {
      // 查询第一页时要清空原来的数据
      const isFirstPage = !params.pageNum || params.pageNum == 1;
      this.liveList = isFirstPage
        ? data.recordList
        : [...this.liveList, ...data.recordList];
      this.hasMoreData = data.currentPage < data.pageCount;
      this.$apply();
    }
  }
  async mixinGetConcernLiveList() {
    const { code, data = [] } = await post('/api/ehis/doctorLive/myattentions');
    if (code == 0) {
      this.liveList = data || [];
      this.$apply();
    }
  }
  /**
   * 计算目标时间和当前时间的时间戳的差值
   * @param {*} number 
   */
  mixinTimeDiffWithNow(date) {
    const nowDate = new Date();
    const targetDate = new Date(date);
    return targetDate.getTime() - nowDate.getTime();
  }
}
